/*************************************************************
 *
 *  MathJax/jax/output/CommonHTML/fonts/TeX/SansSerif-Regular.js
 *
 *  Copyright (c) 2015-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

(function (CHTML) {

var font = 'MathJax_SansSerif';

CHTML.FONTDATA.FONTS[font] = {
  className: CHTML.FONTDATA.familyName(font),
  centerline: 250, ascent: 750, descent: 250,
  
  0x20: [0,0,250,0,0],               // SPACE
  0x21: [694,0,319,110,208],         // EXCLAMATION MARK
  0x22: [694,-471,500,32,325],       // QUOTATION MARK
  0x23: [694,194,833,56,777],        // NUMBER SIGN
  0x24: [750,56,500,44,444],         // DOLLAR SIGN
  0x25: [750,56,833,56,776],         // PERCENT SIGN
  0x26: [716,22,758,42,702],         // AMPERSAND
  0x27: [694,-471,278,89,188],       // APOSTROPHE
  0x28: [750,250,389,74,333],        // LEFT PARENTHESIS
  0x29: [750,250,389,55,314],        // RIGHT PARENTHESIS
  0x2A: [750,-306,500,63,436],       // ASTERISK
  0x2B: [583,82,778,56,722],         // PLUS SIGN
  0x2C: [98,125,278,89,188],         // COMMA
  0x2D: [259,-186,333,11,277],       // HYPHEN-MINUS
  0x2E: [98,0,278,90,188],           // FULL STOP
  0x2F: [750,250,500,56,445],        // SOLIDUS
  0x30: [678,22,500,39,460],         // DIGIT ZERO
  0x31: [678,0,500,83,430],          // DIGIT ONE
  0x32: [677,0,500,42,449],          // DIGIT TWO
  0x33: [678,22,500,42,457],         // DIGIT THREE
  0x34: [656,0,500,28,471],          // DIGIT FOUR
  0x35: [656,21,500,33,449],         // DIGIT FIVE
  0x36: [677,22,500,42,457],         // DIGIT SIX
  0x37: [656,11,500,42,457],         // DIGIT SEVEN
  0x38: [678,22,500,43,456],         // DIGIT EIGHT
  0x39: [677,22,500,42,457],         // DIGIT NINE
  0x3A: [444,0,278,90,188],          // COLON
  0x3B: [444,125,278,89,188],        // SEMICOLON
  0x3D: [370,-130,778,56,722],       // EQUALS SIGN
  0x3F: [704,0,472,55,416],          // QUESTION MARK
  0x40: [704,11,667,56,612],         // COMMERCIAL AT
  0x41: [694,0,667,28,638],          // LATIN CAPITAL LETTER A
  0x42: [694,0,667,90,610],          // LATIN CAPITAL LETTER B
  0x43: [705,11,639,59,587],         // LATIN CAPITAL LETTER C
  0x44: [694,0,722,88,666],          // LATIN CAPITAL LETTER D
  0x45: [691,0,597,86,554],          // LATIN CAPITAL LETTER E
  0x46: [691,0,569,86,526],          // LATIN CAPITAL LETTER F
  0x47: [704,11,667,59,599],         // LATIN CAPITAL LETTER G
  0x48: [694,0,708,86,621],          // LATIN CAPITAL LETTER H
  0x49: [694,0,278,87,191],          // LATIN CAPITAL LETTER I
  0x4A: [694,22,472,42,388],         // LATIN CAPITAL LETTER J
  0x4B: [694,0,694,88,651],          // LATIN CAPITAL LETTER K
  0x4C: [694,0,542,87,499],          // LATIN CAPITAL LETTER L
  0x4D: [694,0,875,92,782],          // LATIN CAPITAL LETTER M
  0x4E: [694,0,708,88,619],          // LATIN CAPITAL LETTER N
  0x4F: [715,22,736,55,680],         // LATIN CAPITAL LETTER O
  0x50: [694,0,639,88,583],          // LATIN CAPITAL LETTER P
  0x51: [715,125,736,55,680],        // LATIN CAPITAL LETTER Q
  0x52: [694,0,646,88,617],          // LATIN CAPITAL LETTER R
  0x53: [716,22,556,44,500],         // LATIN CAPITAL LETTER S
  0x54: [688,0,681,36,644],          // LATIN CAPITAL LETTER T
  0x55: [694,22,688,87,600],         // LATIN CAPITAL LETTER U
  0x56: [694,0,667,14,652],          // LATIN CAPITAL LETTER V
  0x57: [694,0,944,14,929],          // LATIN CAPITAL LETTER W
  0x58: [694,0,667,14,652],          // LATIN CAPITAL LETTER X
  0x59: [694,0,667,3,663],           // LATIN CAPITAL LETTER Y
  0x5A: [694,0,611,55,560],          // LATIN CAPITAL LETTER Z
  0x5B: [750,250,289,94,266],        // LEFT SQUARE BRACKET
  0x5D: [750,250,289,22,194],        // RIGHT SQUARE BRACKET
  0x5E: [694,-527,500,78,421],       // CIRCUMFLEX ACCENT
  0x5F: [-38,114,500,0,499],         // LOW LINE
  0x61: [460,10,481,38,407],         // LATIN SMALL LETTER A
  0x62: [694,11,517,75,482],         // LATIN SMALL LETTER B
  0x63: [460,10,444,34,415],         // LATIN SMALL LETTER C
  0x64: [694,10,517,33,441],         // LATIN SMALL LETTER D
  0x65: [461,10,444,28,415],         // LATIN SMALL LETTER E
  0x66: [705,0,306,27,347],          // LATIN SMALL LETTER F
  0x67: [455,206,500,28,485],        // LATIN SMALL LETTER G
  0x68: [694,0,517,73,443],          // LATIN SMALL LETTER H
  0x69: [680,0,239,67,171],          // LATIN SMALL LETTER I
  0x6A: [680,205,267,-59,192],       // LATIN SMALL LETTER J
  0x6B: [694,0,489,76,471],          // LATIN SMALL LETTER K
  0x6C: [694,0,239,74,164],          // LATIN SMALL LETTER L
  0x6D: [455,0,794,73,720],          // LATIN SMALL LETTER M
  0x6E: [455,0,517,73,443],          // LATIN SMALL LETTER N
  0x6F: [460,10,500,28,471],         // LATIN SMALL LETTER O
  0x70: [455,194,517,75,483],        // LATIN SMALL LETTER P
  0x71: [455,194,517,33,441],        // LATIN SMALL LETTER Q
  0x72: [455,0,342,74,327],          // LATIN SMALL LETTER R
  0x73: [460,10,383,28,360],         // LATIN SMALL LETTER S
  0x74: [571,10,361,18,333],         // LATIN SMALL LETTER T
  0x75: [444,10,517,73,443],         // LATIN SMALL LETTER U
  0x76: [444,0,461,14,446],          // LATIN SMALL LETTER V
  0x77: [444,0,683,14,668],          // LATIN SMALL LETTER W
  0x78: [444,0,461,0,460],           // LATIN SMALL LETTER X
  0x79: [444,204,461,14,446],        // LATIN SMALL LETTER Y
  0x7A: [444,0,435,28,402],          // LATIN SMALL LETTER Z
  0x7E: [327,-193,500,83,416],       // TILDE
  0xA0: [0,0,250,0,0],               // NO-BREAK SPACE
  0x131: [444,0,239,74,164],         // LATIN SMALL LETTER DOTLESS I
  0x237: [444,205,267,-59,192],      // LATIN SMALL LETTER DOTLESS J
  0x300: [694,-527,0,-417,-199],     // COMBINING GRAVE ACCENT
  0x301: [694,-527,0,-302,-84],      // COMBINING ACUTE ACCENT
  0x302: [694,-527,0,-422,-79],      // COMBINING CIRCUMFLEX ACCENT
  0x303: [677,-543,0,-417,-84],      // COMBINING TILDE
  0x304: [631,-552,0,-431,-70],      // COMBINING MACRON
  0x306: [694,-508,0,-427,-74],      // COMBINING BREVE
  0x307: [680,-576,0,-302,-198],     // COMBINING DOT ABOVE
  0x308: [680,-582,0,-397,-104],     // COMBINING DIAERESIS
  0x30A: [694,-527,0,-319,-99],      // COMBINING RING ABOVE
  0x30B: [694,-527,0,-399,-84],      // COMBINING DOUBLE ACUTE ACCENT
  0x30C: [654,-487,0,-422,-79],      // COMBINING CARON
  0x393: [691,0,542,87,499],         // GREEK CAPITAL LETTER GAMMA
  0x394: [694,0,833,42,790],         // GREEK CAPITAL LETTER DELTA
  0x398: [716,21,778,56,722],        // GREEK CAPITAL LETTER THETA
  0x39B: [694,0,611,28,582],         // GREEK CAPITAL LETTER LAMDA
  0x39E: [688,0,667,42,624],         // GREEK CAPITAL LETTER XI
  0x3A0: [691,0,708,86,621],         // GREEK CAPITAL LETTER PI
  0x3A3: [694,0,722,55,666],         // GREEK CAPITAL LETTER SIGMA
  0x3A5: [716,0,778,55,722],         // GREEK CAPITAL LETTER UPSILON
  0x3A6: [694,0,722,55,666],         // GREEK CAPITAL LETTER PHI
  0x3A8: [694,0,778,55,722],         // GREEK CAPITAL LETTER PSI
  0x3A9: [716,0,722,44,677],         // GREEK CAPITAL LETTER OMEGA
  0x2013: [312,-236,500,0,499],      // EN DASH
  0x2014: [312,-236,1000,0,999],     // EM DASH
  0x2018: [694,-471,278,90,189],     // LEFT SINGLE QUOTATION MARK
  0x2019: [694,-471,278,89,188],     // RIGHT SINGLE QUOTATION MARK
  0x201C: [694,-471,500,174,467],    // LEFT DOUBLE QUOTATION MARK
  0x201D: [694,-471,500,32,325]      // RIGHT DOUBLE QUOTATION MARK
};

CHTML.fontLoaded("TeX/"+font.substr(8));

})(MathJax.OutputJax.CommonHTML);
