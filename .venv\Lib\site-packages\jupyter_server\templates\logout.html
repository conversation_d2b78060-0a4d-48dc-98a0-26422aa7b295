{% extends "page.html" %}

{# This template is rendered in response to an authenticated request, so the
user is technically logged in. But when the user sees it, the cookie is
cleared by the Javascript, so we should render this as if the user was logged
out, without e.g. authentication tokens.
#}
{% set logged_in = False %}

{% block stylesheet %}
{% endblock %}

{% block site %}

<div id="jupyter-main-app" class="container">

  {% if message %}
  {% for key in message %}
  <div class="message {{key}}">
    {{message[key]}}
  </div>
  {% endfor %}
  {% endif %}

  {% if not login_available %}
  {% trans %}Proceed to the <a href="{{base_url}}">dashboard{% endtrans %}</a>.
  {% else %}
  {% trans %}Proceed to the <a href="{{base_url}}login">login page{% endtrans %}</a>.
  {% endif %}


  <div />

  {% endblock %}
