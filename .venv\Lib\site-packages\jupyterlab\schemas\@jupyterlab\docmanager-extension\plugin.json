{"title": "Document Manager", "description": "Document Manager settings.", "jupyter.lab.setting-icon": "ui-components:file", "jupyter.lab.setting-icon-label": "Document Manager", "jupyter.lab.transform": true, "jupyter.lab.menus": {"main": [{"id": "jp-mainmenu-file", "items": [{"command": "docmanager:clone", "rank": 2}, {"type": "separator", "rank": 4}, {"command": "docmanager:save", "rank": 4}, {"command": "docmanager:save-as", "rank": 4}, {"command": "docmanager:save-all", "rank": 4}, {"type": "separator", "rank": 5}, {"command": "docmanager:reload", "rank": 5}, {"command": "docmanager:restore-checkpoint", "rank": 5}, {"command": "docmanager:rename", "rank": 5}, {"command": "docmanager:duplicate", "rank": 5}]}, {"id": "jp-mainmenu-settings", "items": [{"type": "separator", "rank": 4}, {"command": "docmanager:toggle-autosave", "rank": 4}, {"type": "separator", "rank": 4}]}], "context": [{"command": "docmanager:rename", "selector": "[data-type=\"document-title\"]", "rank": 20}, {"command": "docmanager:duplicate", "selector": "[data-type=\"document-title\"]", "rank": 21}, {"command": "docmanager:delete", "selector": "[data-type=\"document-title\"]", "rank": 22}, {"command": "docmanager:clone", "selector": "[data-type=\"document-title\"]", "rank": 23}, {"command": "docmanager:show-in-file-browser", "selector": "[data-type=\"document-title\"]", "rank": 24}]}, "jupyter.lab.shortcuts": [{"command": "docmanager:save", "keys": ["Accel S"], "selector": "body"}, {"command": "docmanager:save-as", "keys": ["Accel Shift S"], "selector": "body"}], "properties": {"autosave": {"type": "boolean", "title": "Autosave Documents", "description": "Whether to autosave documents", "default": true}, "autosaveInterval": {"type": "number", "title": "Autosave Interval", "description": "Length of save interval in seconds", "default": 120}, "confirmClosingDocument": {"type": "boolean", "title": "Ask for confirmation to close a document", "description": "Whether to ask for confirmation to close a document or not.", "default": false}, "lastModifiedCheckMargin": {"type": "number", "title": "Margin for last modified timestamp check", "description": "Max acceptable difference, in milliseconds, between last modified timestamps on disk and client", "default": 500}, "defaultViewers": {"type": "object", "title": "Default Viewers", "default": {}, "description": "Overrides for the default viewers for file types", "properties": {}, "additionalProperties": {"type": "string"}}, "renameUntitledFileOnSave": {"type": "boolean", "title": "Rename Untitled File On First Save", "description": "Whether to prompt to rename untitled file on first manual save.", "default": true}, "maxNumberRecents": {"type": "number", "title": "Recent Items Number", "description": "Number of recently opened/closed files and directories to remember.", "default": 10, "minimum": 0}}, "additionalProperties": false, "type": "object"}