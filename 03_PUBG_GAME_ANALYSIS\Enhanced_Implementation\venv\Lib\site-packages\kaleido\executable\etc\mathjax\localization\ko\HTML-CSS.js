/*************************************************************
 *
 *  MathJax/localization/ko/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ko","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "%1 \uC6F9\uD3F0\uD2B8\uB97C \uBD88\uB7EC\uC624\uACE0 \uC788\uC2B5\uB2C8\uB2E4...",
          CantLoadWebFont: "%1 \uC6F9\uD3F0\uD2B8\uB97C \uBD88\uB7EC\uC62C \uC218 \uC5C6\uC2B5\uB2C8\uB2E4",
          FirefoxCantLoadWebFont: "\uD30C\uC774\uC5B4\uD3ED\uC2A4\uB294 \uC6D0\uACA9 \uD638\uC2A4\uD2B8\uB85C\uBD80\uD130 \uC6F9 \uD3F0\uD2B8\uB97C \uBD88\uB7EC\uC62C \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
          CantFindFontUsing: "%1\uB97C \uC0AC\uC6A9\uD55C \uC720\uD6A8\uD55C \uD3F0\uD2B8\uB97C \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
          WebFontsNotAvailable: "\uC6F9 \uD3F0\uD2B8 \uC0AC\uC6A9\uC774 \uBD88\uAC00\uD569\uB2C8\uB2E4. \uC774\uBBF8\uC9C0 \uD3F0\uD2B8\uB97C \uB300\uC2E0 \uC0AC\uC6A9\uD569\uB2C8\uB2E4."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ko/HTML-CSS.js");
