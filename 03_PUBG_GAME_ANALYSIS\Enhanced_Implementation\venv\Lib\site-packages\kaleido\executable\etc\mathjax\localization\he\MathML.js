/*************************************************************
 *
 *  MathJax/localization/he/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("he","MathML",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          BadMglyph: "\u05E8\u05DB\u05D9\u05D1 mglyph \u05D2\u05E8\u05D5\u05E2: %1",
          BadMglyphFont: "\u05D2\u05D5\u05E4\u05DF \u05D2\u05E8\u05D5\u05E2: %1",
          MathPlayer: "MathJax \u05DC\u05D0 \u05D4\u05E6\u05DC\u05D9\u05D7 \u05DC\u05D4\u05D2\u05D3\u05D9\u05E8 \u05D0\u05EA MathPlayer.\n\n\u05D0\u05DD MathPlayer th\u05E0\u05D5 \u05DE\u05D5\u05EA\u05E7\u05DF, \u05D9\u05E9 \u05DC\u05D4\u05EA\u05E7\u05D9\u05DF \u05D0\u05D5\u05EA\u05D5 \u05EA\u05D7\u05D9\u05DC\u05D4.\n\u05D0\u05D7\u05E8\u05EA, \u05D9\u05D9\u05EA\u05DB\u05DF \u05E9\u05D4\u05D2\u05D3\u05E8\u05D5\u05EA \u05D4\u05D0\u05D1\u05D8\u05D7\u05D4 \u05E9\u05DC\u05DA \u05DC\u05D0 \u05D9\u05D0\u05E4\u05E9\u05E8\u05D5 \u05DC\u05E8\u05DB\u05D9\u05D1\u05D9 ActiveX\n\u05DC\u05E8\u05D5\u05E5. \u05D9\u05E9 \u05DC\u05D4\u05E9\u05EA\u05DE\u05E9 \u05D1\u05D7\u05DC\u05D5\u05DF \u05D0\u05E4\u05E9\u05E8\u05D5\u05D9\u05D5\u05EA \u05D0\u05D9\u05E0\u05D8\u05E8\u05E0\u05D8 \u05EA\u05D7\u05EA \u05EA\u05E4\u05E8\u05D9\u05D8 \u05DB\u05DC\u05D9\u05DD, \u05DC\u05D1\u05D7\u05D5\u05E8 \u05D1\u05DC\u05E9\u05D5\u05E0\u05D9\u05EA \"\u05D0\u05D1\u05D8\u05D7\u05D4\", \u05D5\u05DC\u05DC\u05D7\u05D5\u05E5 \u05E2\u05DC \"\u05E8\u05DE\u05D4 \u05DE\u05D5\u05EA\u05D0\u05DE\u05EA \u05D0\u05D9\u05E9\u05D9\u05EA\". \u05E9\u05DD \u05D9\u05E9 \u05DC\u05D1\u05D3\u05D5\u05E7 \u05E9\u05D4\u05D0\u05E4\u05E9\u05E8\u05D5\u05D9\u05D5\u05EA\n\"\u05D4\u05E8\u05E6\u05E5 \u05D1\u05E7\u05E8\u05D9 ActiveX\" \u05D5\"\u05D4\u05EA\u05E0\u05D4\u05D2\u05D5\u05D9\u05D5\u05EA \u05D1\u05D9\u05E0\u05D0\u05E8\u05D9\u05D5\u05EA \u05D5\u05E1\u05E7\u05E8\u05D9\u05E4\u05D8\u05D9\u05DD\" \u05DE\u05D5\u05E4\u05E2\u05DC\u05D5\u05EA.\n\n\u05D1\u05DE\u05E6\u05D1 \u05D4\u05E0\u05D5\u05DB\u05D7\u05D9 \u05D9\u05D5\u05E4\u05D9\u05E2\u05D5 \u05D4\u05D5\u05D3\u05E2\u05D5\u05EA \u05E9\u05D2\u05D9\u05D0\u05D4 \u05D1\u05DE\u05E7\u05D5\u05DD \n\u05DE\u05EA\u05DE\u05D8\u05D9\u05E7\u05D4 \u05DE\u05E2\u05D5\u05E6\u05D1\u05EA.",
          CantCreateXMLParser: "MathJax \u05D0\u05D9\u05E0\u05D5 \u05D9\u05DB\u05D5\u05DC \u05DC\u05D9\u05E6\u05D5\u05E8 \u05DE\u05E4\u05E2\u05E0\u05D7 XML \u05E2\u05D1\u05D5\u05E8 MathML. \u05E0\u05D0 \u05DC\u05D1\u05D3\u05D5\u05E7\n\u05E9\u05D4\u05D2\u05D3\u05E8\u05EA \u05D4\u05D0\u05D1\u05D8\u05D7\u05D4 '\u05D1\u05E7\u05E8\u05D9 ActiveX \u05E9\u05DE\u05E1\u05D5\u05DE\u05E0\u05D9\u05DD \u05D1\u05EA\u05D5\u05E8 \u05D1\u05D8\u05D5\u05D7\u05D9\u05DD' \u05DE\u05D5\u05E4\u05E2\u05DC\u05EA\n(\u05D9\u05E9 \u05DC\u05D4\u05E9\u05EA\u05DE\u05E9 \u05D1\u05D0\u05E4\u05E9\u05E8\u05D5\u05D9\u05D5\u05EA \u05D0\u05D9\u05E0\u05D8\u05E8\u05E0\u05D8 \u05D1\u05EA\u05E4\u05E8\u05D9\u05D8 \u05DB\u05DC\u05D9\u05DD, \u05DC\u05E4\u05EA\u05D5\u05D7 \u05D0\u05EA \u05DC\u05E9\u05D5\u05E0\u05D9\u05EA \"\u05D0\u05D1\u05D8\u05D7\u05D4\",\n\u05D5\u05D0\u05D6 \u05DC\u05DC\u05D7\u05D5\u05E5 \u05E2\u05DC \"\u05E8\u05DE\u05D4 \u05DE\u05D5\u05EA\u05D0\u05DE\u05EA \u05D0\u05D9\u05E9\u05D9\u05EA\" \u05DB\u05D3\u05D9 \u05DC\u05D1\u05D3\u05D5\u05E7 \u05D0\u05EA \u05D6\u05D4).\n\n\u05DE\u05E9\u05D5\u05D5\u05D0\u05D5\u05EA MathML \u05DC\u05D0 \u05D9\u05E2\u05D5\u05D1\u05D3\u05D5 \u05E2\u05DC\u05BE\u05D9\u05D3\u05D9 MathJax.",
          UnknownNodeType: "\u05E1\u05D5\u05D2 \u05E6\u05D5\u05DE\u05EA \u05D1\u05DC\u05EA\u05D9\u05BE\u05D9\u05D5\u05D3\u05E2: %1",
          UnexpectedTextNode: "\u05E1\u05D5\u05D2 \u05E6\u05D5\u05DE\u05EA \u05D1\u05DC\u05EA\u05D9\u05BE\u05E6\u05E4\u05D5\u05D9: %1",
          ErrorParsingMathML: "\u05E9\u05D2\u05D9\u05D0\u05D4 \u05D1\u05E4\u05E2\u05E0\u05D5\u05D7 MathML",
          ParsingError: "\u05E9\u05D2\u05D9\u05D0\u05D4 \u05D1\u05E4\u05E2\u05E0\u05D5\u05D7 MathML\u200F: %1",
          MathMLSingleElement: "MathML \u05E6\u05E8\u05D9\u05DA \u05DC\u05D4\u05D9\u05D5\u05EA \u05DB\u05EA\u05D5\u05D1 \u05D1\u05D0\u05DC\u05DE\u05E0\u05D8 \u05D0\u05D7\u05D3",
          MathMLRootElement: "MathML \u05E6\u05E8\u05D9\u05DA \u05DC\u05D4\u05D9\u05DB\u05EA\u05D1 \u05D1\u05D0\u05DC\u05DE\u05E0\u05D8 \u003Cmath\u003E, \u05DC\u05D0 %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/he/MathML.js");
