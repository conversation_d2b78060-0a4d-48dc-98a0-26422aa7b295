/*************************************************************
 *
 *  MathJax/localization/mk/TeX.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("mk","TeX",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "\u0418\u043C\u0430\u0442\u0435 \u043D\u0435\u043F\u043E\u0442\u0440\u0435\u0431\u043D\u0430 \u043F\u043E\u0447\u0435\u0442\u043D\u0430 \u0437\u0430\u0433\u0440\u0430\u0434\u0430 \u0438\u043B\u0438 \u043D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \u0437\u0430\u0432\u0440\u0448\u043D\u0430",
          ExtraCloseMissingOpen: "\u0418\u043C\u0430\u0442\u0435 \u043D\u0435\u043F\u043E\u0442\u0440\u0435\u0431\u043D\u0430 \u0437\u0430\u0432\u0440\u0448\u043D\u0430 \u0437\u0430\u0433\u0440\u0430\u0434\u0430 \u0438\u043B\u0438 \u043D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \u043F\u043E\u0447\u0435\u0442\u043D\u0430",
          MissingLeftExtraRight: "\u041D\u0435\u043C\u0430\u0442\u0435 \u201E\\left\u201C \u0438\u043B\u0438 \u0438\u043C\u0430\u0442\u0435 \u0432\u0438\u0448\u043E\u043A \u201E\\right\u201C",
          MissingScript: "\u041D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0437\u0430 \u0433\u043E\u0440\u0435\u043D \u0438\u043B\u0438 \u0434\u043E\u043B\u0435\u043D \u0438\u043D\u0434\u0435\u043A\u0441",
          ExtraLeftMissingRight: "\u041D\u0435\u043C\u0430\u0442\u0435 \u201E\\right\u201C \u0438\u043B\u0438 \u0438\u043C\u0430\u0442\u0435 \u0432\u0438\u0448\u043E\u043A \u201E\\left\u201C",
          Misplaced: "\u041F\u043E\u0433\u0440\u0435\u0448\u043D\u043E \u043F\u043E\u0441\u0442\u0430\u0432\u0435\u043D %1",
          MissingOpenForSub: "\u041D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \u043F\u043E\u0447\u0435\u0442\u043D\u0430 \u0437\u0430\u0433\u0440\u0430\u0434\u0430 \u0437\u0430 \u0434\u043E\u043B\u0435\u043D \u0438\u043D\u0434\u0435\u043A\u0441",
          MissingOpenForSup: "\u041D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \u043F\u043E\u0447\u0435\u0442\u043D\u0430 \u0437\u0430\u0433\u0440\u0430\u0434\u0430 \u0437\u0430 \u0433\u043E\u0440\u0435\u043D \u0438\u043D\u0434\u0435\u043A\u0441",
          AmbiguousUseOf: "\u041D\u0435\u043F\u043E\u0458\u0430\u0441\u043D\u0435\u0442\u0430 \u0443\u043F\u043E\u0442\u0440\u0435\u0431\u0430 \u043D\u0430 %1",
          EnvBadEnd: "\\begin{%1} \u0437\u0430\u0431\u0440\u0448\u0438 \u0441\u043E \\end{%2}",
          EnvMissingEnd: "\u041D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \\end{%1}",
          MissingBoxFor: "\u041D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \u043A\u0443\u0442\u0438\u0458\u043A\u0430 \u0437\u0430 %1",
          MissingCloseBrace: "\u041D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \u0437\u0430\u0432\u0440\u0448\u043D\u0430 \u0437\u0430\u0433\u0440\u0430\u0434\u0430",
          UndefinedControlSequence: "\u041D\u0435\u043F\u0440\u0435\u043F\u043E\u0437\u043D\u0430\u0435\u043D\u0430 \u043A\u043E\u043D\u0442\u0440\u043E\u043B\u043D\u0430 \u043D\u0438\u0437\u0430 %1",
          DoubleExponent: "\u0414\u0432\u043E\u0435\u043D \u0433\u043E\u0440\u0435\u043D \u0438\u043D\u0434\u0435\u043A\u0441: \u043F\u043E\u0458\u0430\u0441\u043D\u0435\u0442\u0435 \u0441\u043E \u0437\u0430\u0433\u0440\u0430\u0434\u0438",
          DoubleSubscripts: "\u0414\u0432\u043E\u0435\u043D \u0434\u043E\u043B\u0435\u043D \u0438\u043D\u0434\u0435\u043A\u0441: \u043F\u043E\u0458\u0430\u0441\u043D\u0435\u0442\u0435 \u0441\u043E \u0437\u0430\u0433\u0440\u0430\u0434\u0438",
          DoubleExponentPrime: "\u041F\u0440\u0438\u043C\u043E\u0442 \u043F\u0440\u0430\u0432\u0438 \u0434\u0432\u043E\u0435\u043D \u0433\u043E\u0440\u0435\u043D \u0438\u043D\u0434\u0435\u043B\u043A\u0441: \u043F\u043E\u0458\u0430\u0441\u043D\u0435\u0442\u0435 \u0441\u043E \u0437\u0430\u0433\u0440\u0430\u0434\u0438",
          CantUseHash1: "\u041D\u0435 \u043C\u043E\u0436\u0435\u0442\u0435 \u0434\u0430 \u0433\u043E \u043A\u043E\u0440\u0438\u0441\u0442\u0438\u0442\u0435 \u043C\u0430\u043A\u0440\u043E\u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0430\u0440\u0441\u043A\u0438\u043E\u0442 \u0437\u043D\u0430\u043A \u201E#\u201C \u0432\u043E \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0438 \u0440\u0435\u0436\u0438\u043C",
          MisplacedMiddle: "%1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0431\u0438\u0434\u0435 \u201E\\left\u201C \u0438\u043B\u0438 \u201E\\right\u201C",
          MisplacedLimits: "%1 \u0441\u0435 \u0434\u043E\u043F\u0443\u0448\u0442\u0430 \u0441\u0430\u043C\u043E \u0437\u0430 \u043E\u043F\u0435\u0440\u0430\u0442\u043E\u0440\u0438",
          MisplacedMoveRoot: "%1 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0458\u0430\u0432\u0443\u0432\u0430 \u0441\u0430\u043C\u043E \u0432\u043E \u043A\u043E\u0440\u0435\u043D",
          MultipleCommand: "%1 \u0441\u0435 \u0458\u0430\u0432\u0443\u0432\u0430 \u043F\u043E\u0432\u0435\u045C\u0435\u043F\u0430\u0442\u0438",
          IntegerArg: "\u0410\u0440\u0433\u0443\u043C\u0435\u043D\u0442\u043E\u0442 \u0437\u0430 %1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0431\u0438\u0434\u0435 \u0446\u0435\u043B \u0431\u0440\u043E\u0458",
          NotMathMLToken: "%1 \u043D\u0435 \u0435 \u0448\u0438\u0444\u0440\u0435\u043D \u0435\u043B\u0435\u043C\u0435\u043D\u0442",
          InvalidMathMLAttr: "\u041D\u0435\u0432\u0430\u0436\u0435\u0447\u043A\u0438 MathML-\u0430\u0442\u0440\u0438\u0431\u0443\u0442: %1",
          UnknownAttrForElement: "%1 \u043D\u0435 \u0435 \u043F\u0440\u0438\u0437\u043D\u0430\u0435\u043D \u0430\u0442\u0440\u0438\u0431\u0443\u0442 \u0437\u0430 %2",
          MaxMacroSub1: "\u041D\u0430\u0434\u043C\u0438\u043D\u0430\u0442 \u0435 \u043D\u0430\u0458\u0433\u043E\u043B\u0435\u043C\u0438\u043E\u0442 \u0434\u043E\u043F\u0443\u0448\u0442\u0435\u043D \u0431\u0440\u043E\u0458 \u043D\u0430 \u043C\u0430\u043A\u0440\u043E\u0437\u0430\u043C\u0435\u043D\u0438 \u0432\u043E MathJax. \u0414\u0430 \u043D\u0435 \u0438\u043C\u0430 \u0440\u0435\u043A\u0443\u0440\u0437\u0438\u0432\u043D\u043E \u043F\u043E\u0432\u0438\u043A\u0443\u0432\u0430\u045A\u0435 \u043D\u0430 \u043C\u0430\u043A\u0440\u043E\u0437\u0430\u043C\u0435\u043D\u0430\u0442\u0430?",
          MaxMacroSub2: "\u041D\u0430\u0434\u043C\u0438\u043D\u0430\u0442 \u0435 \u043D\u0430\u0458\u0433\u043E\u043B\u0435\u043C\u0438\u043E\u0442 \u0434\u043E\u043F\u0443\u0448\u0442\u0435\u043D \u0431\u0440\u043E\u0458 \u043D\u0430 \u043C\u0430\u043A\u0440\u043E\u0437\u0430\u043C\u0435\u043D\u0438 \u0432\u043E MathJax. \u0414\u0430 \u043D\u0435 \u0438\u043C\u0430 \u0440\u0430\u043A\u0443\u0440\u0437\u0438\u0432\u043D\u0430 LaTeX-\u043E\u043A\u043E\u043B\u0438\u043D\u0430?",
          MissingArgFor: "\u041D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0437\u0430 %1",
          ExtraAlignTab: "\u041D\u0435\u043F\u043E\u0442\u0440\u0435\u0431\u0435\u043D \u0437\u043D\u0430\u043A \u0437\u0430 \u043F\u043E\u0440\u0430\u043C\u043D\u0443\u0432\u0430\u045A\u0435 \u0432\u043E \u0442\u0435\u043A\u0441\u0442\u043E\u0442 \u043D\u0430 \u201E\\cases\u201C",
          BracketMustBeDimension: "\u0417\u0430\u0433\u0440\u0430\u0434\u0435\u043D\u0438\u043E\u0442 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0437\u0430 %1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0431\u0438\u0434\u0435 \u0434\u0438\u043C\u0435\u043D\u0437\u0438\u0458\u0430",
          InvalidEnv: "\u041F\u043E\u0433\u0440\u0435\u0448\u043D\u043E \u0438\u043C\u0435 \u043D\u0430 \u043E\u043A\u043E\u043B\u0438\u043D\u0430\u0442\u0430 \u201E%1\u201C",
          UnknownEnv: "\u041D\u0435\u043F\u043E\u0437\u043D\u0430\u0442\u0430 \u043E\u043A\u043E\u043B\u0438\u043D\u0430 \u201E%1\u201C",
          ExtraCloseLooking: "\u041D\u0435\u043F\u043E\u0442\u0440\u0435\u0431\u043D\u0430 \u0437\u0430\u0432\u0440\u0448\u043D\u0430 \u0437\u0430\u0433\u0440\u0430\u0434\u0430 \u0432\u043E \u043F\u0440\u0435\u0431\u0430\u0440\u0443\u0432\u0430\u045A\u0435\u0442\u043E \u043D\u0430 %1",
          MissingCloseBracket: "\u041D\u0435 \u043C\u043E\u0436\u0435\u0432 \u0434\u0430 \u043D\u0430\u0458\u0434\u0430\u043C \u0437\u0430\u0432\u0440\u0448\u043D\u043E ']' \u0437\u0430 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442\u043E\u0442 \u0432\u043E %1",
          MissingOrUnrecognizedDelim: "\u041E\u0442\u0441\u0443\u0442\u0435\u043D \u0438\u043B\u0438 \u043D\u0435\u043F\u0440\u0435\u043F\u043E\u0437\u043D\u0430\u0435\u043D \u043E\u0434\u0434\u0435\u043B\u0443\u0432\u0430\u0447 \u0437\u0430 %1",
          MissingDimOrUnits: "\u041D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \u0434\u0438\u043C\u0435\u043D\u0437\u0438\u0458\u0430 \u0438\u043B\u0438 \u043D\u0435\u0458\u0437\u0438\u043D\u0438 \u0435\u0434\u0438\u043D\u0438\u0446\u0438 \u0437\u0430 %1",
          TokenNotFoundForCommand: "\u041D\u0435 \u043C\u043E\u0436\u0435\u0432 \u0434\u0430 \u0433\u043E \u043D\u0430\u0458\u0434\u0430\u043C %1 \u0437\u0430 %2",
          MathNotTerminated: "\u041C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0430\u0442\u0430 \u0441\u043E\u0434\u0440\u0436\u0438\u043D\u0430 \u043D\u0435 \u0435 \u0437\u0430\u0432\u0440\u0448\u0435\u043D\u0430 \u0432\u043E \u043F\u043E\u043B\u0435\u0442\u043E \u0437\u0430 \u0442\u0435\u043A\u0441\u0442",
          IllegalMacroParam: "\u041D\u0435\u0438\u0441\u043F\u0440\u0430\u0432\u0435\u043D \u043C\u0430\u043A\u0440\u043E\u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0430\u0440",
          MaxBufferSize: "\u041D\u0430\u0434\u043C\u0438\u043D\u0430\u0442\u0430 \u0435 \u0433\u043E\u043B\u0435\u043C\u0438\u043D\u0430\u0442\u0430 \u043D\u0430 \u0432\u043D\u0430\u0442\u0440\u0435\u0448\u043D\u0438\u043E\u0442 \u043C\u0435\u0453\u0443\u0441\u043A\u043B\u0430\u0434 \u043D\u0430 MathJax. \u0414\u0430 \u043D\u0435 \u0438\u043C\u0430 \u0440\u0435\u043A\u0443\u0440\u0437\u0438\u0432\u043D\u043E \u043C\u0430\u043A\u0440\u043E\u043F\u043E\u0432\u0438\u043A\u0443\u0432\u0430\u045A\u0435?",
          CommandNotAllowedInEnv: "%1 \u043D\u0435 \u0441\u0435 \u0434\u043E\u043F\u0443\u0448\u0442\u0430 \u0432\u043E \u043E\u043A\u043E\u043B\u0438\u043D\u0430\u0442\u0430 \u043D\u0430 %2",
          MultipleLabel: "\u041D\u0430\u0442\u043F\u0438\u0441\u043E\u0442 \u201E%1\u201C \u0435 \u0437\u0430\u0434\u0430\u0434\u0435\u043D \u043F\u043E\u0432\u0435\u045C\u0435\u043F\u0430\u0442\u0438",
          CommandAtTheBeginingOfLine: "%1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0434\u043E\u0458\u0434\u0435 \u043D\u0430 \u043F\u043E\u0447\u0435\u0442\u043E\u043A\u043E\u0442 \u043E\u0434 \u0440\u0435\u0434\u043E\u0442",
          IllegalAlign: "\u0423\u043A\u0430\u0436\u0430\u043D\u043E \u0435 \u043D\u0435\u0438\u0441\u043F\u0440\u0430\u0432\u0435\u043D\u043E \u043F\u043E\u0440\u0430\u043C\u043D\u0443\u0432\u0430\u045A\u0435 \u0432\u043E %1",
          BadMathStyleFor: "\u041D\u0435\u0438\u0441\u043F\u0440\u0430\u0432\u0435\u043D \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0438 \u0441\u0442\u0438\u043B \u0437\u0430 %1",
          PositiveIntegerArg: "\u0410\u0440\u0433\u0443\u043C\u0435\u043D\u0442\u043E\u0442 \u0437\u0430 %1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0431\u0438\u0434\u0435 \u043F\u043E\u0437\u0438\u0442\u0438\u0432\u0435\u043D \u0446\u0435\u043B \u0431\u0440\u043E\u0458",
          ErroneousNestingEq: "\u041F\u043E\u0433\u0440\u0435\u0448\u043D\u043E \u0432\u043C\u0435\u0442\u043D\u0430\u0442\u0438 \u0440\u0430\u0432\u0435\u043D\u0441\u043A\u0438 \u0441\u0442\u0440\u0443\u043A\u0442\u0443\u0440\u0438",
          MultlineRowsOneCol: "\u0420\u0435\u0434\u043E\u0432\u0438\u0442\u0435 \u0432\u043E \u0441\u043E\u0441\u0442\u0430\u0432 \u043D\u0430 \u043E\u043A\u043E\u043B\u0438\u043D\u0430\u0442\u0430 \u043D\u0430 %1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0438\u043C\u0430\u0430\u0442 \u0442\u043E\u0447\u043D\u043E \u043F\u043E \u0435\u0434\u043D\u0430 \u043A\u043E\u043B\u043E\u043D\u0430",
          MultipleBBoxProperty: "%1 \u0435 \u0443\u043A\u0430\u0436\u0430\u043D\u043E \u0434\u0432\u0430\u043F\u0430\u0442\u0438 \u0432\u043E %2",
          InvalidBBoxProperty: "\u201E%1\u201C \u043D\u0435 \u0435 \u0431\u043E\u0458\u0430, \u0434\u0438\u043C\u0435\u043D\u0437\u0438\u0458\u0430 \u0437\u0430 \u0441\u043B\u043E\u0436\u0443\u0432\u0430\u045A\u0435 \u0438\u043B\u0438 \u0441\u0442\u0438\u043B",
          ExtraEndMissingBegin: "\u041D\u0435\u043F\u043E\u0442\u0440\u0435\u0431\u043D\u043E %1 \u0438\u043B\u0438 \u043D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \\begingroup",
          GlobalNotFollowedBy: "%1 \u043D\u0435 \u0435 \u0434\u043E\u043F\u0443\u0448\u0442\u0435\u043D\u043E \u043E\u0434 \u201E\\let\u201C, \u201E\\def\u201C \u0438\u043B\u0438 \u201E\\newcommand\u201C",
          UndefinedColorModel: "\u0411\u043E\u0458\u043D\u0438\u043E\u0442 \u043C\u043E\u0434\u0435\u043B \u201E%1\u201C \u043D\u0435 \u0435 \u0437\u0430\u0434\u0430\u0434\u0435\u043D",
          ModelArg1: "\u0411\u043E\u0458\u043D\u0438\u0442\u0435 \u0432\u0440\u0435\u0434\u043D\u043E\u0441\u0442\u0438 \u0437\u0430 \u043C\u043E\u0434\u0435\u043B\u043E\u0442 %1 \u0442\u0440\u0435\u0431\u0430 \u0434\u0430 \u0438\u043C\u0430\u0430\u0442 \u043F\u043E \u0442\u0440\u0438 \u0431\u0440\u043E\u0458\u043A\u0438",
          InvalidDecimalNumber: "\u041D\u0435\u0432\u0430\u0436\u0435\u0447\u043A\u0438 \u0434\u0435\u0446\u0438\u043C\u0430\u043B\u0435\u043D \u0431\u0440\u043E\u0458",
          ModelArg2: "\u0411\u043E\u0458\u043D\u0438\u0442\u0435 \u0432\u0440\u0435\u0434\u043D\u043E\u0441\u0442\u0438 \u0437\u0430 \u043C\u043E\u0434\u0435\u043B\u043E\u0442 %1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0431\u0438\u0434\u0430\u0442 \u043F\u043E\u043C\u0435\u0453\u0443 %2 \u0438 %3",
          InvalidNumber: "\u041D\u0435\u0432\u0430\u0436\u0435\u0447\u043A\u0438 \u0431\u0440\u043E\u0458",
          NewextarrowArg1: "\u041F\u0440\u0432\u0438\u043E\u0442 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 %1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0438\u043C\u0435\u043D\u0443\u0432\u0430 \u043A\u043E\u043D\u0442\u0440\u043E\u043B\u043D\u0430 \u043D\u0438\u0437\u0430",
          NewextarrowArg2: "\u0412\u0442\u043E\u0440\u0438\u043E\u0442 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0437\u0430 %1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0441\u0435 \u0441\u043E\u0441\u0442\u043E\u0438 \u043E\u0434 \u0434\u0432\u0430 \u0446\u0435\u043B\u0438 \u0431\u0440\u043E\u0458\u0430 \u043E\u0434\u0434\u0435\u043B\u0435\u043D\u0438 \u0441\u043E \u0437\u0430\u043F\u0438\u0440\u043A\u0430",
          NewextarrowArg3: "\u0422\u0440\u0435\u0442\u0438\u043E\u0442 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0437\u0430 %1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0431\u0438\u0434\u0435 \u0443\u043D\u0438\u043A\u043E\u0434\u0435\u043D \u0431\u0440\u043E\u0435\u043D \u0437\u043D\u0430\u043A",
          NoClosingChar: "\u041D\u0435 \u043C\u043E\u0436\u0430\u043C \u0434\u0430 \u043D\u0430\u0458\u0434\u0430\u043C \u0437\u0430\u0432\u0440\u0448\u043D\u043E %1",
          IllegalControlSequenceName: "\u041D\u0435\u0438\u0441\u043F\u0440\u0430\u0432\u043D\u043E \u0438\u043C\u0435 \u043D\u0430 \u043A\u043E\u043D\u0442\u0440\u043E\u043B\u043D\u0430\u0442\u0430 \u043D\u0438\u0437\u0430 \u0437\u0430 %1",
          IllegalParamNumber: "\u041D\u0430 %1 \u043C\u0443 \u0435 \u0443\u043A\u0430\u0436\u0430\u043D \u043D\u0435\u0434\u043E\u043F\u0443\u0448\u0442\u0435\u043D \u0431\u0440\u043E\u0458 \u043D\u0430 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u0438",
          MissingCS: "%1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0435 \u043F\u0440\u043E\u0441\u043B\u0435\u0434\u0435\u043D \u043E\u0434 \u043A\u043E\u043D\u0442\u0440\u043E\u043B\u043D\u0430 \u043D\u0438\u0437\u0430",
          CantUseHash2: "\u041D\u0435\u0434\u043E\u043F\u0443\u0448\u0442\u0435\u043D\u0430 \u0443\u043F\u043E\u0442\u0440\u0435\u0431\u0430 \u043D\u0430 \u201E#\u201C \u0432\u043E \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0442 \u0437\u0430 %1",
          SequentialParam: "\u041F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u0438\u0442\u0435 \u0437\u0430 %1 \u043C\u043E\u0440\u0430 \u0434\u0430 \u0441\u0435 \u043D\u0443\u043C\u0435\u0440\u0438\u0440\u0430\u043D\u0438 \u043F\u043E\u0441\u043B\u0435\u0434\u043E\u0432\u0430\u0442\u0435\u043B\u043D\u043E",
          MissingReplacementString: "\u041D\u0435\u0434\u043E\u0441\u0442\u0430\u0441\u0443\u0432\u0430 \u0437\u0430\u043C\u0435\u043D\u0430 \u0437\u0430 \u043D\u0438\u0437\u0430\u0442\u0430 \u0432\u043E \u0437\u0430\u0434\u0430\u0434\u0435\u043D\u043E\u0442\u043E \u043D\u0430 %1",
          MismatchUseDef: "\u0423\u043F\u043E\u0442\u0440\u0435\u0431\u0430\u0442\u0430 \u043D\u0430 %1 \u043D\u0435 \u043E\u0434\u0433\u043E\u0432\u0430\u0440\u0430 \u043D\u0430 \u0437\u0430\u0434\u0430\u0434\u0435\u043D\u043E\u0442\u043E",
          RunawayArgument: "\u041D\u0435\u0441\u043A\u043B\u0430\u0434\u0435\u043D \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0437\u0430 %1?",
          NoClosingDelim: "\u041D\u0435 \u043C\u043E\u0436\u0430\u043C \u0434\u0430 \u0433\u043E \u043D\u0430\u0458\u0434\u0430\u043C \u0437\u0430\u0432\u0440\u0448\u043D\u0438\u043E\u0442 \u043E\u0434\u0434\u0435\u043B\u0443\u0432\u0430\u0447 \u0437\u0430 %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/mk/TeX.js");
