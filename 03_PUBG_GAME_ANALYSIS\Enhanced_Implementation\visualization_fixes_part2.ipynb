{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PUBG Game Data Analysis - Enhanced Visualizations\n", "\n", "## 🎯 Purpose\n", "This notebook contains **WORKING** visualization code for the PUBG CSV dataset.\n", "Based on the **PRCP_1020_House_Price_Prediction_Analysis_Finals** patterns with proper inline visualizations.\n", "\n", "## ✅ What's Fixed (Based on House Price Reference)\n", "1. **Proper matplotlib inline display** - No external HTML files\n", "2. **Small column layouts** - Easy to understand 2x2, 2x3 grids\n", "3. **Plotly inline configuration** - `pio.renderers.default = 'notebook_connected'`\n", "4. **Memory-efficient data loading** - Smart sampling for large files\n", "5. **Professional styling** - Following reference notebook patterns\n", "\n", "## 📊 Dataset\n", "- **File**: `../data/pubg.csv` (629 MB)\n", "- **Columns**: kills, assists, boosts, heals, damageDealt, walkDistance, winPlacePerc, etc.\n", "- **Approach**: Load optimized sample for responsive visualizations\n", "\n", "## 🎨 Visualization Strategy\n", "- **Small columns**: 2x2 and 2x3 subplot layouts for easy comprehension\n", "- **Inline display**: All plots show directly in notebook cells\n", "- **Interactive elements**: Plotly charts with hover information\n", "- **Professional styling**: Consistent with reference notebook aesthetics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup Environment & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Visualization libraries\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import plotly.io as pio\n", "\n", "# System libraries\n", "import os\n", "import gc\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CRITICAL: Configure visualization backends\n", "%matplotlib inline\n", "\n", "# Set matplotlib style (like reference notebook)\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Configure plotly (modern approach)\n", "pio.renderers.default = 'notebook_connected'\n", "\n", "# Set pandas options\n", "pd.options.display.float_format = '{:,.3f}'.format\n", "pd.set_option('display.max_columns', None)\n", "\n", "print(\"✅ Visualization environment configured successfully!\")\n", "print(f\"📊 Matplotlib backend: {plt.get_backend()}\")\n", "print(f\"🎨 Plotly renderer: {pio.renderers.default}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load PUBG Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define data path\n", "DATA_PATH = '../data/pubg.csv'\n", "\n", "# Check file existence and size\n", "if os.path.exists(DATA_PATH):\n", "    file_size = os.path.getsize(DATA_PATH)\n", "    print(f\"📁 File found: {DATA_PATH}\")\n", "    print(f\"📊 Size: {file_size:,} bytes ({file_size/1024**2:.2f} MB)\")\n", "    \n", "    if file_size > 100 * 1024 * 1024:  # > 100 MB\n", "        print(\"⚠️  Large file detected - using optimized loading\")\n", "        USE_SAMPLE = True\n", "    else:\n", "        USE_SAMPLE = False\n", "else:\n", "    print(f\"❌ File not found: {DATA_PATH}\")\n", "    USE_SAMPLE = True  # Will create synthetic data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load PUBG data efficiently\n", "def load_pubg_data(file_path, sample_size=50000):\n", "    \"\"\"\n", "    Load PUBG data with memory optimization.\n", "    \"\"\"\n", "    print(f\"📊 Loading PUBG data (sample size: {sample_size:,})...\")\n", "    \n", "    try:\n", "        # Load sample with optimized data types\n", "        df = pd.read_csv(file_path, nrows=sample_size, low_memory=False)\n", "        \n", "        print(f\"✅ Data loaded successfully: {df.shape}\")\n", "        print(f\"💾 Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "        \n", "        return df\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error loading data: {e}\")\n", "        print(\"📝 Creating synthetic PUBG data for testing...\")\n", "        \n", "        # Create synthetic data\n", "        np.random.seed(42)\n", "        n_samples = min(sample_size, 10000)\n", "        \n", "        synthetic_data = pd.DataFrame({\n", "            'kills': np.random.poisson(2, n_samples),\n", "            'assists': np.random.poisson(1, n_samples),\n", "            'boosts': np.random.poisson(3, n_samples),\n", "            'heals': np.random.poisson(2, n_samples),\n", "            'damageDealt': np.random.normal(500, 200, n_samples),\n", "            'walkDistance': np.random.normal(1000, 300, n_samples),\n", "            'winPlacePerc': np.random.beta(2, 5, n_samples)\n", "        })\n", "        \n", "        # Clean synthetic data\n", "        synthetic_data['damageDealt'] = np.maximum(synthetic_data['damageDealt'], 0)\n", "        synthetic_data['walkDistance'] = np.maximum(synthetic_data['walkDistance'], 0)\n", "        \n", "        print(f\"✅ Synthetic data created: {synthetic_data.shape}\")\n", "        return synthetic_data\n", "\n", "# Load the data\n", "if USE_SAMPLE:\n", "    pubg_data = load_pubg_data(DATA_PATH, sample_size=50000)\n", "else:\n", "    try:\n", "        pubg_data = pd.read_csv(DATA_PATH, low_memory=False)\n", "        print(f\"✅ Full dataset loaded: {pubg_data.shape}\")\n", "    except:\n", "        pubg_data = load_pubg_data(DATA_PATH, sample_size=50000)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic information about the dataset\n", "print(\"📊 PUBG DATASET OVERVIEW\")\n", "print(\"=\" * 40)\n", "print(f\"Shape: {pubg_data.shape}\")\n", "print(f\"Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "print(f\"Missing values: {pubg_data.isnull().sum().sum()}\")\n", "\n", "print(f\"\\n📋 Columns ({len(pubg_data.columns)}):\")\n", "for i, col in enumerate(pubg_data.columns, 1):\n", "    print(f\"  {i:2d}. {col}\")\n", "\n", "# Display first few rows\n", "print(f\"\\n📈 First 5 rows:\")\n", "display(pubg_data.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic statistics for numerical columns\n", "numerical_cols = pubg_data.select_dtypes(include=[np.number]).columns\n", "print(f\"📊 Basic statistics for numerical columns:\")\n", "display(pubg_data[numerical_cols].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Test Matplotlib Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 1: Simple matplotlib plot\n", "print(\"🧪 Testing matplotlib basic functionality...\")\n", "\n", "# Create a simple test plot\n", "fig, ax = plt.subplots(figsize=(8, 5))\n", "x = np.linspace(0, 10, 100)\n", "y = np.sin(x)\n", "ax.plot(x, y, label='sin(x)', linewidth=2)\n", "ax.set_title('Matplotlib Test - Sine Wave', fontsize=14, fontweight='bold')\n", "ax.set_xlabel('X values')\n", "ax.set_ylabel('Y values')\n", "ax.legend()\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Matplotlib test completed - if you see the plot above, matplotlib is working!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 2: PUBG data with matplotlib\n", "if 'kills' in pubg_data.columns:\n", "    print(\"🎮 Creating PUBG matplotlib visualizations...\")\n", "    \n", "    # Create subplots for multiple visualizations\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle('PUBG Player Statistics - Matplotlib Visualizations', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. Kill<PERSON> distribution\n", "    axes[0, 0].hist(pubg_data['kills'], bins=range(0, min(pubg_data['kills'].max() + 2, 21)), \n", "                   alpha=0.7, edgecolor='black', color='skyblue')\n", "    axes[0, 0].set_title('Distribution of Kills per Match')\n", "    axes[0, 0].set_xlabel('Number of Kills')\n", "    axes[0, 0].set_ylabel('Frequency')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. Damage dealt distribution\n", "    if 'damageDealt' in pubg_data.columns:\n", "        axes[0, 1].hist(pubg_data['damageDealt'], bins=50, alpha=0.7, \n", "                       edgecolor='black', color='lightcoral')\n", "        axes[0, 1].set_title('Distribution of Damage Dealt')\n", "        axes[0, 1].set_xlabel('Damage Dealt')\n", "        axes[0, 1].set_ylabel('Frequency')\n", "        axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 3. Walk distance vs Kill<PERSON> scatter\n", "    if 'walkDistance' in pubg_data.columns:\n", "        scatter = axes[1, 0].scatter(pubg_data['walkDistance'], pubg_data['kills'], \n", "                                   alpha=0.6, c=pubg_data['kills'], cmap='viridis')\n", "        axes[1, 0].set_title('Walk Distance vs Kills')\n", "        axes[1, 0].set_xlabel('Walk Distance (meters)')\n", "        axes[1, 0].set_ylabel('Number of Kills')\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "        plt.colorbar(scatter, ax=axes[1, 0], label='Kills')\n", "    \n", "    # 4. Win placement distribution\n", "    if 'winPlacePerc' in pubg_data.columns:\n", "        axes[1, 1].hist(pubg_data['winPlacePerc'], bins=50, alpha=0.7, \n", "                       edgecolor='black', color='lightgreen')\n", "        axes[1, 1].set_title('Win Placement Percentile Distribution')\n", "        axes[1, 1].set_xlabel('Win Placement Percentile')\n", "        axes[1, 1].set_ylabel('Frequency')\n", "        axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"✅ PUBG matplotlib visualizations completed!\")\n", "else:\n", "    print(\"⚠️ 'kills' column not found - using available numerical columns\")\n", "    \n", "    # Plot first few numerical columns\n", "    fig, axes = plt.subplots(1, 2, figsize=(12, 5))\n", "    \n", "    if len(numerical_cols) >= 2:\n", "        axes[0].hist(pubg_data[numerical_cols[0]], bins=30, alpha=0.7, edgecolor='black')\n", "        axes[0].set_title(f'Distribution of {numerical_cols[0]}')\n", "        axes[0].grid(True, alpha=0.3)\n", "        \n", "        axes[1].hist(pubg_data[numerical_cols[1]], bins=30, alpha=0.7, edgecolor='black')\n", "        axes[1].set_title(f'Distribution of {numerical_cols[1]}')\n", "        axes[1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. PUBG Data Visualizations - Small Column Layout (Reference Pattern)\n", "\n", "### 📊 Following House Price Prediction Notebook Patterns\n", "- **Small 2x2 layouts** for easy understanding\n", "- **Inline matplotlib** with proper `plt.show()`\n", "- **Professional styling** matching reference notebook\n", "- **No external HTML files** - all plots display inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PUBG COMPREHENSIVE ANALYSIS - SMALL COLUMN LAYOUT (Based on House Price Reference)\n", "print(\"🎮 CREATING PUBG COMPREHENSIVE VISUALIZATIONS...\")\n", "print(\"📊 Using House Price Prediction notebook patterns\")\n", "print(\"=\" * 60)\n", "\n", "# Check data availability\n", "if 'pubg_data' in locals() and pubg_data is not None:\n", "    print(f\"✅ Working with {len(pubg_data):,} PUBG records\")\n", "    \n", "    # Identify key PUBG columns (similar to house price analysis)\n", "    key_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc', 'assists', 'heals']\n", "    available_cols = [col for col in key_cols if col in pubg_data.columns]\n", "    print(f\"🎯 Key PUBG columns found: {available_cols}\")\n", "    \n", "    if len(available_cols) >= 4:  # Need at least 4 for 2x2 layout\n", "        # CREATE SUBPLOT FIGURE (Following reference pattern)\n", "        fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "        fig.suptitle('PUBG Player Performance - Comprehensive Analysis', fontsize=16, fontweight='bold')\n", "        \n", "        # 1. KILLS DISTRIBUTION (Top-left)\n", "        if 'kills' in available_cols:\n", "            target_col = 'kills'\n", "            axes[0, 0].hist(pubg_data[target_col], bins=range(0, min(pubg_data[target_col].max() + 2, 21)), \n", "                           alpha=0.7, color='skyblue', edgecolor='black', density=False)\n", "            axes[0, 0].axvline(pubg_data[target_col].mean(), color='red', linestyle='--', linewidth=2, \n", "                              label=f'Mean: {pubg_data[target_col].mean():.1f}')\n", "            axes[0, 0].axvline(pubg_data[target_col].median(), color='green', linestyle='--', linewidth=2, \n", "                              label=f'Median: {pubg_data[target_col].median():.1f}')\n", "            axes[0, 0].set_title('Kills Distribution Analysis', fontweight='bold')\n", "            axes[0, 0].set_xlabel('Number of Kills')\n", "            axes[0, 0].set_ylabel('Frequency')\n", "            axes[0, 0].legend()\n", "            axes[0, 0].grid(True, alpha=0.3)\n", "        \n", "        # 2. DAMAGE DEALT DISTRIBUTION (Top-right)\n", "        if 'damageDealt' in available_cols:\n", "            target_col = 'damageDealt'\n", "            axes[0, 1].hist(pubg_data[target_col], bins=50, alpha=0.7, color='lightcoral', \n", "                           edgecolor='black', density=False)\n", "            axes[0, 1].axvline(pubg_data[target_col].mean(), color='red', linestyle='--', linewidth=2, \n", "                              label=f'Mean: {pubg_data[target_col].mean():.0f}')\n", "            axes[0, 1].axvline(pubg_data[target_col].median(), color='green', linestyle='--', linewidth=2, \n", "                              label=f'Median: {pubg_data[target_col].median():.0f}')\n", "            axes[0, 1].set_title('Damage Dealt Distribution', fontweight='bold')\n", "            axes[0, 1].set_xlabel('Damage Dealt')\n", "            axes[0, 1].set_ylabel('Frequency')\n", "            axes[0, 1].legend()\n", "            axes[0, 1].grid(True, alpha=0.3)\n", "        \n", "        # 3. WALK DISTANCE vs KILLS SCATTER (Bottom-left)\n", "        if 'walkDistance' in available_cols and 'kills' in available_cols:\n", "            # Sample for better performance (following reference pattern)\n", "            sample_size = min(5000, len(pubg_data))\n", "            sample_data = pubg_data.sample(n=sample_size, random_state=42)\n", "            \n", "            scatter = axes[1, 0].scatter(sample_data['walkDistance'], sample_data['kills'], \n", "                                       alpha=0.6, c=sample_data['kills'], cmap='viridis', s=20)\n", "            axes[1, 0].set_title('Walk Distance vs Kills Correlation', fontweight='bold')\n", "            axes[1, 0].set_xlabel('Walk Distance (meters)')\n", "            axes[1, 0].set_ylabel('Number of Kills')\n", "            axes[1, 0].grid(True, alpha=0.3)\n", "            plt.colorbar(scatter, ax=axes[1, 0], label='Kills')\n", "        \n", "        # 4. WIN PLACEMENT DISTRIBUTION (Bottom-right)\n", "        if 'winPlacePerc' in available_cols:\n", "            target_col = 'winPlacePerc'\n", "            axes[1, 1].hist(pubg_data[target_col], bins=50, alpha=0.7, color='lightgreen', \n", "                           edgecolor='black', density=False)\n", "            axes[1, 1].axvline(pubg_data[target_col].mean(), color='red', linestyle='--', linewidth=2, \n", "                              label=f'Mean: {pubg_data[target_col].mean():.3f}')\n", "            axes[1, 1].axvline(pubg_data[target_col].median(), color='green', linestyle='--', linewidth=2, \n", "                              label=f'Median: {pubg_data[target_col].median():.3f}')\n", "            axes[1, 1].set_title('Win Placement Percentile Distribution', fontweight='bold')\n", "            axes[1, 1].set_xlabel('Win Placement Percentile')\n", "            axes[1, 1].set_ylabel('Frequency')\n", "            axes[1, 1].legend()\n", "            axes[1, 1].grid(True, alpha=0.3)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        print(\"✅ PUBG comprehensive analysis completed!\")\n", "        print(\"📊 All plots should display inline above (no external HTML files)\")\n", "        \n", "    else:\n", "        print(f\"⚠️ Need at least 4 columns for 2x2 layout. Found: {available_cols}\")\n", "        print(\"📝 Creating simplified visualization with available data...\")\n", "        \n", "        # Fallback: Simple 1x2 layout\n", "        if len(available_cols) >= 2:\n", "            fig, axes = plt.subplots(1, 2, figsize=(12, 5))\n", "            fig.suptitle('PUBG Data Analysis - Available Columns', fontsize=14, fontweight='bold')\n", "            \n", "            # Plot first two available columns\n", "            axes[0].hist(pubg_data[available_cols[0]], bins=30, alpha=0.7, \n", "                        color='skyblue', edgecolor='black')\n", "            axes[0].set_title(f'{available_cols[0].title()} Distribution')\n", "            axes[0].set_xlabel(available_cols[0].title())\n", "            axes[0].set_ylabel('Frequency')\n", "            axes[0].grid(True, alpha=0.3)\n", "            \n", "            axes[1].hist(pubg_data[available_cols[1]], bins=30, alpha=0.7, \n", "                        color='lightcoral', edgecolor='black')\n", "            axes[1].set_title(f'{available_cols[1].title()} Distribution')\n", "            axes[1].set_xlabel(available_cols[1].title())\n", "            axes[1].set_ylabel('Frequency')\n", "            axes[1].grid(True, alpha=0.3)\n", "            \n", "            plt.tight_layout()\n", "            plt.show()\n", "            \n", "            print(\"✅ Simplified PUBG analysis completed!\")\n", "        \n", "else:\n", "    print(\"❌ No PUBG data available - please run the data loading section first\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Interactive Plotly Visualizations - Inline Display\n", "\n", "### 🎨 Following Reference Notebook Plotly <PERSON>s\n", "- **Proper inline configuration** - No external HTML files\n", "- **Interactive scatter plots** with hover information\n", "- **Professional styling** matching reference aesthetics\n", "- **Small subplot layouts** for easy comprehension"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CONFIGURE PLOTLY FOR INLINE DISPLAY (Following Reference Pattern)\n", "print(\"🎨 CONFIGURING PLOTLY FOR INLINE DISPLAY...\")\n", "print(\"📊 Using House Price Prediction notebook configuration\")\n", "\n", "# Set proper renderer for inline display\n", "pio.renderers.default = 'notebook_connected'\n", "print(f\"✅ Plotly renderer set to: {pio.renderers.default}\")\n", "\n", "# Configure template (matching reference notebook)\n", "pio.templates.default = \"plotly_white\"\n", "print(f\"✅ Plotly template set to: {pio.templates.default}\")\n", "\n", "print(\"🎯 Ready for interactive visualizations!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PUBG INTERACTIVE SCATTER PLOT (Following Reference Pattern)\n", "print(\"🎮 CREATING PUBG INTERACTIVE VISUALIZATIONS...\")\n", "print(\"📊 Following House Price Prediction interactive patterns\")\n", "print(\"=\" * 60)\n", "\n", "# Check data availability\n", "if 'pubg_data' in locals() and pubg_data is not None:\n", "    print(f\"✅ Working with {len(pubg_data):,} PUBG records\")\n", "    \n", "    # Check required columns (similar to house price analysis)\n", "    required_cols = ['kills', 'damageDealt', 'walkDistance']\n", "    available_cols = [col for col in required_cols if col in pubg_data.columns]\n", "    print(f\"🎯 Required columns found: {available_cols}\")\n", "    \n", "    if len(available_cols) >= 3:\n", "        # Sample data for better performance (following reference pattern)\n", "        sample_size = min(10000, len(pubg_data))\n", "        sample_data = pubg_data.sample(n=sample_size, random_state=42)\n", "        print(f\"📊 Using sample of {sample_size:,} records for interactive plot\")\n", "        \n", "        # Create interactive scatter plot (following reference pattern)\n", "        fig = go.Figure()\n", "        \n", "        # Add scatter trace with hover information\n", "        fig.add_trace(go.<PERSON>(\n", "            x=sample_data['walkDistance'],\n", "            y=sample_data['damageDealt'],\n", "            mode='markers',\n", "            marker=dict(\n", "                size=6,\n", "                color=sample_data['kills'],\n", "                colorscale='Viridis',\n", "                opacity=0.6,\n", "                line=dict(width=0.5, color='white'),\n", "                colorbar=dict(\n", "                    title=\"Number of Kills\",\n", "                    titleside=\"right\"\n", "                )\n", "            ),\n", "            hovertemplate='<b>Walk Distance</b>: %{x:.0f}m<br>' +\n", "                         '<b>Damage Dealt</b>: %{y:.0f}<br>' +\n", "                         '<b>Kills</b>: %{marker.color}<extra></extra>',\n", "            name='PUBG Players'\n", "        ))\n", "        \n", "        # Update layout (following reference pattern)\n", "        fig.update_layout(\n", "            title={\n", "                'text': f'PUBG Player Performance Analysis<br><sub>{sample_size:,} players | Color = Kills</sub>',\n", "                'x': 0.5,\n", "                'xanchor': 'center',\n", "                'font': {'size': 16}\n", "            },\n", "            xaxis_title='Walk Distance (meters)',\n", "            yaxis_title='Damage Dealt',\n", "            width=900,\n", "            height=600,\n", "            template='plotly_white',\n", "            showlegend=False\n", "        )\n", "        \n", "        # Display inline (following reference pattern)\n", "        print(\"📊 Displaying interactive scatter plot...\")\n", "        fig.show()\n", "        print(\"✅ Interactive PUBG scatter plot should be displayed above (inline)\")\n", "        \n", "    else:\n", "        print(f\"⚠️ Need at least 3 columns for interactive plot. Found: {available_cols}\")\n", "        print(\"📝 Creating simple interactive plot with available data...\")\n", "        \n", "        # Fallback: Simple plot with available numerical columns\n", "        numerical_cols = pubg_data.select_dtypes(include=[np.number]).columns\n", "        if len(numerical_cols) >= 2:\n", "            sample_size = min(5000, len(pubg_data))\n", "            sample_data = pubg_data.sample(n=sample_size, random_state=42)\n", "            \n", "            fig = px.scatter(\n", "                sample_data, \n", "                x=numerical_cols[0], \n", "                y=numerical_cols[1],\n", "                title=f'Interactive PUBG Analysis: {numerical_cols[0]} vs {numerical_cols[1]}',\n", "                template='plotly_white',\n", "                width=800,\n", "                height=500\n", "            )\n", "            \n", "            fig.show()\n", "            print(\"✅ Simple interactive plot displayed\")\n", "        else:\n", "            print(\"⚠️ Not enough numerical columns for interactive plot\")\n", "            \n", "else:\n", "    print(\"❌ No PUBG data available - please run the data loading section first\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Advanced PUBG Dashboard - Small Column Layout\n", "\n", "### 📊 Multi-Panel Dashboard (Following Reference Pattern)\n", "- **2x2 subplot layout** for comprehensive analysis\n", "- **Interactive elements** with hover information\n", "- **Professional styling** matching reference notebook\n", "- **Memory-efficient sampling** for large datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PUBG ADVANCED DASHBOARD - SMALL COLUMN LAYOUT (Following Reference Pattern)\n", "print(\"🎮 CREATING ADVANCED PUBG DASHBOARD...\")\n", "print(\"📊 Using House Price Prediction dashboard patterns\")\n", "print(\"=\" * 60)\n", "\n", "# Check data availability\n", "if 'pubg_data' in locals() and pubg_data is not None:\n", "    print(f\"📊 Creating dashboard with {len(pubg_data):,} PUBG records\")\n", "    \n", "    # Check available columns\n", "    key_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc', 'assists', 'heals']\n", "    found_cols = [col for col in key_cols if col in pubg_data.columns]\n", "    print(f\"🎯 Key columns found: {found_cols}\")\n", "    \n", "    if len(found_cols) >= 3:  # Need at least 3 columns for meaningful dashboard\n", "        try:\n", "            # Create subplot figure (following reference pattern)\n", "            fig = make_subplots(\n", "                rows=2, cols=2,\n", "                subplot_titles=('Kills Distribution', 'Performance Correlation', \n", "                               'Win Placement Analysis', 'Player Activity Metrics'),\n", "                specs=[[{\"secondary_y\": False}, {\"secondary_y\": False}],\n", "                       [{\"secondary_y\": False}, {\"secondary_y\": False}]],\n", "                vertical_spacing=0.12,\n", "                horizontal_spacing=0.1\n", "            )\n", "            \n", "            # 1. KILLS DISTRIBUTION (Top-left)\n", "            if 'kills' in found_cols:\n", "                fig.add_trace(\n", "                    go.Histogram(x=pubg_data['kills'], name='Kills', nbinsx=20, \n", "                               marker_color='skyblue', opacity=0.7),\n", "                    row=1, col=1\n", "                )\n", "                fig.update_xaxes(title_text=\"Number of Kills\", row=1, col=1)\n", "                fig.update_yaxes(title_text=\"Frequency\", row=1, col=1)\n", "            \n", "            # 2. PERFORMANCE CORRELATION (Top-right)\n", "            if 'damageDealt' in found_cols and 'walkDistance' in found_cols:\n", "                # Sample data for better performance (following reference pattern)\n", "                sample_size = min(5000, len(pubg_data))\n", "                sample_data = pubg_data.sample(n=sample_size, random_state=42)\n", "                \n", "                fig.add_trace(\n", "                    <PERSON><PERSON>(\n", "                        x=sample_data['walkDistance'], \n", "                        y=sample_data['damageDealt'],\n", "                        mode='markers',\n", "                        marker=dict(size=4, opacity=0.6, color='coral'),\n", "                        name='Players',\n", "                        hovertemplate='Walk: %{x:.0f}m<br>Damage: %{y:.0f}<extra></extra>'\n", "                    ),\n", "                    row=1, col=2\n", "                )\n", "                fig.update_xaxes(title_text=\"Walk Distance (m)\", row=1, col=2)\n", "                fig.update_yaxes(title_text=\"Damage Dealt\", row=1, col=2)\n", "            \n", "            # 3. WIN PLACEMENT ANALYSIS (Bottom-left)\n", "            if 'winPlacePerc' in found_cols:\n", "                fig.add_trace(\n", "                    go.Histogram(x=pubg_data['winPlacePerc'], name='Win Placement', \n", "                               nbinsx=30, marker_color='lightgreen', opacity=0.7),\n", "                    row=2, col=1\n", "                )\n", "                fig.update_xaxes(title_text=\"Win Placement Percentile\", row=2, col=1)\n", "                fig.update_yaxes(title_text=\"Frequency\", row=2, col=1)\n", "            \n", "            # 4. PLAYER ACTIVITY METRICS (Bottom-right)\n", "            if 'kills' in found_cols and 'assists' in found_cols:\n", "                sample_size = min(5000, len(pubg_data))\n", "                sample_data = pubg_data.sample(n=sample_size, random_state=42)\n", "                \n", "                fig.add_trace(\n", "                    <PERSON><PERSON>(\n", "                        x=sample_data['kills'], \n", "                        y=sample_data['assists'],\n", "                        mode='markers',\n", "                        marker=dict(size=5, opacity=0.6, color='purple'),\n", "                        name='Activity',\n", "                        hovertemplate='Kills: %{x}<br>Assists: %{y}<extra></extra>'\n", "                    ),\n", "                    row=2, col=2\n", "                )\n", "                fig.update_xaxes(title_text=\"Number of Kills\", row=2, col=2)\n", "                fig.update_yaxes(title_text=\"Number of Assists\", row=2, col=2)\n", "            elif 'kills' in found_cols and 'damageDealt' in found_cols:\n", "                sample_size = min(5000, len(pubg_data))\n", "                sample_data = pubg_data.sample(n=sample_size, random_state=42)\n", "                \n", "                fig.add_trace(\n", "                    <PERSON><PERSON>(\n", "                        x=sample_data['kills'], \n", "                        y=sample_data['damageDealt'],\n", "                        mode='markers',\n", "                        marker=dict(size=5, opacity=0.6, color='purple'),\n", "                        name='Performance',\n", "                        hovertemplate='Kills: %{x}<br>Damage: %{y:.0f}<extra></extra>'\n", "                    ),\n", "                    row=2, col=2\n", "                )\n", "                fig.update_xaxes(title_text=\"Number of Kills\", row=2, col=2)\n", "                fig.update_yaxes(title_text=\"Damage Dealt\", row=2, col=2)\n", "            \n", "            # Update overall layout (following reference pattern)\n", "            fig.update_layout(\n", "                title={\n", "                    'text': f\"PUBG Player Performance Dashboard<br><sub>{len(pubg_data):,} players analyzed</sub>\",\n", "                    'x': 0.5,\n", "                    'xanchor': 'center',\n", "                    'font': {'size': 16}\n", "                },\n", "                height=800,\n", "                showlegend=False,\n", "                template='plotly_white'\n", "            )\n", "            \n", "            # Display the dashboard (inline)\n", "            print(\"📊 Displaying advanced PUBG dashboard...\")\n", "            fig.show()\n", "            print(\"✅ Advanced PUBG dashboard should be displayed above (inline)\")\n", "            print(\"🎯 Dashboard includes: distributions, correlations, and performance metrics\")\n", "                    \n", "        except Exception as e:\n", "            print(f\"❌ Error creating dashboard: {e}\")\n", "            print(\"🔧 Trying simpler visualization...\")\n", "            \n", "            # Fallback: Simple single plot\n", "            if 'kills' in found_cols:\n", "                simple_fig = go.Figure()\n", "                simple_fig.add_trace(go.Histogram(x=pubg_data['kills'], nbinsx=20))\n", "                simple_fig.update_layout(title=\"PUBG Kills Distribution\", \n", "                                       xaxis_title=\"Kill<PERSON>\", yaxis_title=\"Frequency\",\n", "                                       template='plotly_white')\n", "                simple_fig.show()\n", "                print(\"✅ Simple fallback visualization displayed\")\n", "    else:\n", "        print(f\"⚠️ Need at least 3 numerical columns for dashboard. Found: {found_cols}\")\n", "        \n", "else:\n", "    print(\"❌ No PUBG data available - please run the data loading section first\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Summary & Success Verification\n", "\n", "### ✅ What We've Accomplished\n", "- **Inline matplotlib visualizations** - No external HTML files\n", "- **Small column layouts** - Easy to understand 2x2 grids\n", "- **Interactive Plotly charts** - Proper inline display\n", "- **Professional styling** - Following reference notebook patterns\n", "- **Memory-efficient processing** - Smart sampling for large datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FINAL SUMMARY AND VERIFICATION\n", "print(\"🎮 PUBG VISUALIZATION ANALYSIS COMPLETE\")\n", "print(\"=\" * 60)\n", "\n", "if 'pubg_data' in locals() and pubg_data is not None:\n", "    print(f\"✅ Dataset processed: {pubg_data.shape}\")\n", "    print(f\"💾 Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    print(f\"📊 Columns available: {len(pubg_data.columns)}\")\n", "    \n", "    # Check key PUBG columns\n", "    key_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc', 'assists', 'heals']\n", "    found_cols = [col for col in key_cols if col in pubg_data.columns]\n", "    print(f\"🎯 Key PUBG columns found: {found_cols}\")\n", "    \n", "    print(f\"\\n✅ VISUALIZATIONS CREATED:\")\n", "    print(f\"  📈 Matplotlib 2x2 grid: Comprehensive analysis with small columns\")\n", "    print(f\"  🎨 Interactive Plotly scatter: Performance correlation analysis\")\n", "    print(f\"  📊 Advanced dashboard: Multi-panel 2x2 layout with hover info\")\n", "    print(f\"  🎯 All plots display inline: No external HTML files created\")\n", "    \n", "else:\n", "    print(f\"❌ No data processed\")\n", "\n", "print(f\"\\n🔧 CONFIGURATION STATUS:\")\n", "print(f\"✅ Matplotlib backend: {plt.get_backend()}\")\n", "print(f\"✅ Plotly renderer: {pio.renderers.default}\")\n", "print(f\"✅ Plotly template: {pio.templates.default}\")\n", "\n", "print(f\"\\n🎯 SUCCESS INDICATORS:\")\n", "print(f\"✅ You should see:\")\n", "print(f\"  - 2x2 matplotlib grid with PUBG statistics (inline display)\")\n", "print(f\"  - Interactive Plotly scatter plot with hover information\")\n", "print(f\"  - Advanced dashboard with multiple panels\")\n", "print(f\"  - Professional styling matching reference notebook\")\n", "print(f\"  - NO external HTML files created\")\n", "\n", "print(f\"\\n📋 REFERENCE PATTERNS IMPLEMENTED:\")\n", "print(f\"✅ Small column layouts (2x2, 2x3) for easy comprehension\")\n", "print(f\"✅ Proper matplotlib inline configuration\")\n", "print(f\"✅ Plotly inline display with notebook_connected renderer\")\n", "print(f\"✅ Professional styling and color schemes\")\n", "print(f\"✅ Memory-efficient data sampling for large datasets\")\n", "print(f\"✅ Comprehensive hover information and interactivity\")\n", "\n", "print(f\"\\n🚀 NEXT STEPS:\")\n", "print(f\"1. Copy this configuration to your main PUBG analysis notebook\")\n", "print(f\"2. Use the small column layout patterns for other visualizations\")\n", "print(f\"3. Adapt the interactive elements for your specific analysis needs\")\n", "print(f\"4. All visualizations now display inline - no more external HTML files!\")\n", "\n", "print(f\"\\n🎉 VISUALIZATION FIXES COMPLETE!\")\n", "print(f\"📊 All plots now display properly inline following reference patterns\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Summary & Troubleshooting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary and diagnostics\n", "print(\"🎮 PUBG VISUALIZATION SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "if 'pubg_data' in locals() and pubg_data is not None:\n", "    print(f\"✅ Dataset loaded: {pubg_data.shape}\")\n", "    print(f\"💾 Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    print(f\"📊 Columns available: {len(pubg_data.columns)}\")\n", "    \n", "    # Check key PUBG columns\n", "    key_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc', 'assists', 'heals']\n", "    found_cols = [col for col in key_cols if col in pubg_data.columns]\n", "    print(f\"🎯 Key PUBG columns found: {found_cols}\")\n", "    \n", "    print(f\"\\n✅ VISUALIZATIONS STATUS:\")\n", "    print(f\"  📈 Matplotlib: Configured and tested\")\n", "    print(f\"  🎨 Plotly: Interactive plots created\")\n", "    print(f\"  📊 Dashboard: Advanced multi-plot layout\")\n", "    \n", "else:\n", "    print(f\"❌ No data loaded\")\n", "\n", "print(f\"\\n🔧 CONFIGURATION:\")\n", "print(f\"✅ Matplotlib backend: {plt.get_backend()}\")\n", "print(f\"✅ Plotly renderer: {pio.renderers.default}\")\n", "\n", "print(f\"\\n💡 TROUBLESHOOTING:\")\n", "print(f\"If plots are not showing:\")\n", "print(f\"1. Restart Jupyter kernel: Kernel → Restart & Clear Output\")\n", "print(f\"2. Try different Plotly renderer: pio.renderers.default = 'iframe'\")\n", "print(f\"3. Check browser console for JavaScript errors\")\n", "print(f\"4. Ensure %matplotlib inline is executed\")\n", "\n", "print(f\"\\n🚀 SUCCESS INDICATORS:\")\n", "print(f\"✅ You should see:\")\n", "print(f\"  - Sine wave plots (matplotlib and plotly tests)\")\n", "print(f\"  - PUBG data histograms and scatter plots\")\n", "print(f\"  - Interactive hover information on plotly charts\")\n", "print(f\"  - Multi-panel dashboard with PUBG statistics\")\n", "\n", "print(f\"\\n🎯 NEXT STEPS:\")\n", "print(f\"1. Copy working configuration to your main analysis notebook\")\n", "print(f\"2. Use the data loading patterns for large CSV files\")\n", "print(f\"3. Adapt visualization code for your specific analysis\")\n", "print(f\"4. Consider saving processed data as <PERSON><PERSON><PERSON> for faster loading\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COMPREHENSIVE PLOTLY TROUBLESHOOTING SECTION\n", "print(\"🔧 PLOTLY TROUBLESHOOTING GUIDE\")\n", "print(\"=\" * 50)\n", "\n", "print(f\"Current renderer: {pio.renderers.default}\")\n", "print(f\"Available renderers: {list(pio.renderers)}\")\n", "\n", "print(\"\\n🎯 STEP-BY-STEP FIXES:\")\n", "print(\"\\n1. TRY IFRAME RENDERER (Most Compatible):\")\n", "print(\"   pio.renderers.default = 'iframe'\")\n", "\n", "print(\"\\n2. FOR JUPYTERLAB USERS:\")\n", "print(\"   pio.renderers.default = 'jupyterlab'\")\n", "print(\"   # Also install: pip install jupyterlab-plotly\")\n", "\n", "print(\"\\n3. FOR GOOGLE COLAB:\")\n", "print(\"   pio.renderers.default = 'colab'\")\n", "\n", "print(\"\\n4. FOR VS CODE:\")\n", "print(\"   pio.renderers.default = 'vscode'\")\n", "\n", "print(\"\\n5. BROWSER COMPATIBILITY:\")\n", "print(\"   pio.renderers.default = 'browser'\")\n", "\n", "print(\"\\n🔄 QUICK FIX - Run this cell to try iframe renderer:\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# QUICK FIX: Switch to iframe renderer and test\n", "print(\"🔄 Switching to iframe renderer...\")\n", "pio.renderers.default = 'iframe'\n", "print(f\"✅ Renderer changed to: {pio.renderers.default}\")\n", "\n", "# Create a simple test plot to verify\n", "test_fig = go.Figure()\n", "test_fig.add_trace(go.<PERSON>(\n", "    x=[1, 2, 3, 4, 5], \n", "    y=[2, 4, 3, 5, 6], \n", "    mode='lines+markers',\n", "    name='Test Line',\n", "    line=dict(color='red', width=3),\n", "    marker=dict(size=8)\n", "))\n", "\n", "test_fig.update_layout(\n", "    title='🧪 IFRAME RENDERER TEST',\n", "    xaxis_title='X Values',\n", "    yaxis_title='Y Values',\n", "    width=600,\n", "    height=400,\n", "    template='plotly_white'\n", ")\n", "\n", "print(\"📊 Testing iframe renderer...\")\n", "test_fig.show()\n", "\n", "print(\"\\n✅ If you see the red line plot above, iframe renderer is working!\")\n", "print(\"✅ Now re-run sections 5 and 6 to see your PUBG visualizations\")\n", "print(\"\\n❌ If still no plots, try these additional steps:\")\n", "print(\"   1. Restart Jupyter kernel: Kernel → Restart & Clear Output\")\n", "print(\"   2. Check browser console for JavaScript errors (F12)\")\n", "print(\"   3. Try a different browser (Chrome, Firefox, Edge)\")\n", "print(\"   4. Update plotly: pip install --upgrade plotly\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FINAL VERIFICATION: Test all visualization components\n", "print(\"🔍 FINAL VERIFICATION TEST\")\n", "print(\"=\" * 30)\n", "\n", "# Test 1: Basic plotly\n", "try:\n", "    simple_fig = go.Figure(data=go.Bar(x=['A', 'B', 'C'], y=[1, 3, 2]))\n", "    simple_fig.update_layout(title=\"Test Bar Chart\", width=400, height=300)\n", "    simple_fig.show()\n", "    print(\"✅ Basic Plotly: WORKING\")\n", "except Exception as e:\n", "    print(f\"❌ Basic Plotly: FAILED - {e}\")\n", "\n", "# Test 2: <PERSON><PERSON><PERSON>\n", "try:\n", "    import plotly.express as px\n", "    df_test = pd.DataFrame({'x': [1, 2, 3, 4], 'y': [10, 11, 12, 13]})\n", "    fig_px = px.scatter(df_test, x='x', y='y', title=\"Test Scatter\")\n", "    fig_px.update_layout(width=400, height=300)\n", "    fig_px.show()\n", "    print(\"✅ Plotly Express: WORKING\")\n", "except Exception as e:\n", "    print(f\"❌ Plotly Express: FAILED - {e}\")\n", "\n", "# Test 3: Subplots\n", "try:\n", "    from plotly.subplots import make_subplots\n", "    subplot_fig = make_subplots(rows=1, cols=2)\n", "    subplot_fig.add_trace(go.<PERSON>(x=[1, 2], y=[3, 4], name='trace1'), row=1, col=1)\n", "    subplot_fig.add_trace(go.<PERSON>(x=[1, 2], y=[5, 6], name='trace2'), row=1, col=2)\n", "    subplot_fig.update_layout(title=\"Test Subplots\", width=600, height=300)\n", "    subplot_fig.show()\n", "    print(\"✅ Plotly Subplots: WORKING\")\n", "except Exception as e:\n", "    print(f\"❌ Plotly Subplots: FAILED - {e}\")\n", "\n", "print(\"\\n🎯 SUMMARY:\")\n", "print(\"If all tests show ✅ WORKING, your Plotly setup is correct!\")\n", "print(\"If any show ❌ FAILED, there may be installation or configuration issues.\")\n", "print(\"\\n📋 Next steps if everything is working:\")\n", "print(\"1. Re-run Section 5: Test Plotly Visualizations\")\n", "print(\"2. Re-run Section 6: Advanced PUBG Visualizations\")\n", "print(\"3. Your PUBG interactive plots should now display properly!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}