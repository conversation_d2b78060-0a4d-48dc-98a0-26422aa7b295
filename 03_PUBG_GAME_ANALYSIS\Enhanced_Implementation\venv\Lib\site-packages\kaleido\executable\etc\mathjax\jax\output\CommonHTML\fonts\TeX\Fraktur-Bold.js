/*************************************************************
 *
 *  MathJax/jax/output/CommonHTML/fonts/TeX/Fraktur-Regular.js
 *
 *  Copyright (c) 2015-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

(function (CHTML) {

var font = 'MathJax_Fraktur-Bold';

CHTML.FONTDATA.FONTS[font] = {
  className: CHTML.FONTDATA.familyName(font),
  centerline: 259, ascent: 740, descent: 223,
  weight: 'bold',
  0x20: [0,0,250,0,0],               // SPACE
  0x21: [689,12,349,107,241],        // EXCLAMATION MARK
  0x22: [695,-432,254,10,231],       // QUOTATION MARK
  0x26: [696,16,871,44,839],         // AMPERSAND
  0x27: [695,-436,250,80,158],       // APOSTROPHE
  0x28: [737,186,459,134,347],       // LEFT PARENTHESIS
  0x29: [735,187,459,105,326],       // RIGHT PARENTHESIS
  0x2A: [692,-449,328,40,277],       // ASTERISK
  0x2B: [598,82,893,56,837],         // PLUS SIGN
  0x2C: [107,191,328,118,253],       // COMMA
  0x2D: [275,-236,893,54,833],       // HYPHEN-MINUS
  0x2E: [102,15,328,103,237],        // FULL STOP
  0x2F: [721,182,593,41,550],        // SOLIDUS
  0x30: [501,12,593,42,533],         // DIGIT ZERO
  0x31: [489,0,593,54,548],          // DIGIT ONE
  0x32: [491,-2,593,44,563],         // DIGIT TWO
  0x33: [487,193,593,31,523],        // DIGIT THREE
  0x34: [495,196,593,13,565],        // DIGIT FOUR
  0x35: [481,190,593,19,518],        // DIGIT FIVE
  0x36: [704,12,593,48,547],         // DIGIT SIX
  0x37: [479,197,593,54,591],        // DIGIT SEVEN
  0x38: [714,5,593,45,542],          // DIGIT EIGHT
  0x39: [487,195,593,29,549],        // DIGIT NINE
  0x3A: [457,12,255,57,197],         // COLON
  0x3B: [458,190,255,56,211],        // SEMICOLON
  0x3D: [343,-168,582,22,559],       // EQUALS SIGN
  0x3F: [697,14,428,40,422],         // QUESTION MARK
  0x41: [686,31,847,29,827],         // LATIN CAPITAL LETTER A
  0x42: [684,31,1044,57,965],        // LATIN CAPITAL LETTER B
  0x43: [676,32,723,72,726],         // LATIN CAPITAL LETTER C
  0x44: [683,29,982,31,896],         // LATIN CAPITAL LETTER D
  0x45: [686,29,783,74,728],         // LATIN CAPITAL LETTER E
  0x46: [684,146,722,17,727],        // LATIN CAPITAL LETTER F
  0x47: [687,29,927,74,844],         // LATIN CAPITAL LETTER G
  0x48: [683,126,851,6,752],         // LATIN CAPITAL LETTER H
  0x49: [681,25,655,32,623],         // LATIN CAPITAL LETTER I
  0x4A: [680,141,652,-8,616],        // LATIN CAPITAL LETTER J
  0x4B: [681,26,789,20,806],         // LATIN CAPITAL LETTER K
  0x4C: [683,28,786,30,764],         // LATIN CAPITAL LETTER L
  0x4D: [683,32,1239,27,1232],       // LATIN CAPITAL LETTER M
  0x4E: [679,30,983,26,973],         // LATIN CAPITAL LETTER N
  0x4F: [726,30,976,12,881],         // LATIN CAPITAL LETTER O
  0x50: [688,223,977,33,943],        // LATIN CAPITAL LETTER P
  0x51: [726,83,976,12,918],         // LATIN CAPITAL LETTER Q
  0x52: [688,28,978,31,978],         // LATIN CAPITAL LETTER R
  0x53: [685,31,978,82,905],         // LATIN CAPITAL LETTER S
  0x54: [686,30,790,31,802],         // LATIN CAPITAL LETTER T
  0x55: [688,39,851,18,871],         // LATIN CAPITAL LETTER U
  0x56: [685,29,982,25,966],         // LATIN CAPITAL LETTER V
  0x57: [683,30,1235,26,1240],       // LATIN CAPITAL LETTER W
  0x58: [681,35,849,32,835],         // LATIN CAPITAL LETTER X
  0x59: [688,214,984,34,878],        // LATIN CAPITAL LETTER Y
  0x5A: [677,148,711,-4,624],        // LATIN CAPITAL LETTER Z
  0x5B: [740,130,257,36,226],        // LEFT SQUARE BRACKET
  0x5D: [738,132,257,14,208],        // RIGHT SQUARE BRACKET
  0x5E: [734,-452,590,1,584],        // CIRCUMFLEX ACCENT
  0x61: [472,32,603,80,586],         // LATIN SMALL LETTER A
  0x62: [690,32,590,86,504],         // LATIN SMALL LETTER B
  0x63: [473,26,464,87,424],         // LATIN SMALL LETTER C
  0x64: [632,28,589,-1,511],         // LATIN SMALL LETTER D
  0x65: [471,27,472,81,428],         // LATIN SMALL LETTER E
  0x66: [687,222,388,35,372],        // LATIN SMALL LETTER F
  0x67: [472,208,595,17,541],        // LATIN SMALL LETTER G
  0x68: [687,207,615,89,507],        // LATIN SMALL LETTER H
  0x69: [686,25,331,3,327],          // LATIN SMALL LETTER I
  0x6A: [682,203,332,-19,238],       // LATIN SMALL LETTER J
  0x6B: [682,25,464,34,432],         // LATIN SMALL LETTER K
  0x6C: [681,24,337,100,312],        // LATIN SMALL LETTER L
  0x6D: [476,31,921,16,900],         // LATIN SMALL LETTER M
  0x6E: [473,28,654,5,608],          // LATIN SMALL LETTER N
  0x6F: [482,34,609,107,515],        // LATIN SMALL LETTER O
  0x70: [557,207,604,-1,519],        // LATIN SMALL LETTER P
  0x71: [485,211,596,87,515],        // LATIN SMALL LETTER Q
  0x72: [472,26,460,13,453],         // LATIN SMALL LETTER R
  0x73: [479,34,523,-23,481],        // LATIN SMALL LETTER S
  0x74: [648,27,393,43,407],         // LATIN SMALL LETTER T
  0x75: [472,32,589,9,603],          // LATIN SMALL LETTER U
  0x76: [546,27,604,56,507],         // LATIN SMALL LETTER V
  0x77: [549,32,918,55,815],         // LATIN SMALL LETTER W
  0x78: [471,188,459,8,441],         // LATIN SMALL LETTER X
  0x79: [557,221,589,60,512],        // LATIN SMALL LETTER Y
  0x7A: [471,214,461,-7,378],        // LATIN SMALL LETTER Z
  0xA0: [0,0,250,0,0],               // NO-BREAK SPACE
  0x2018: [708,-411,254,53,187],     // LEFT SINGLE QUOTATION MARK
  0x2019: [692,-394,254,58,193],      // RIGHT SINGLE QUOTATION MARK
  0xE301: [630,27,587,64,512],       // stix-MATHEMATICAL BOLD CAPITAL GAMMA SLASHED
  0xE302: [693,212,394,37,408],      // stix-capital Delta, Greek slashed
  0xE303: [681,219,387,36,384],      // stix-MATHEMATICAL BOLD CAPITAL DELTA SLASHED
  0xE304: [473,212,593,67,531],      // stix-capital Epsilon, Greek slashed
  0xE305: [684,27,393,33,387],       // stix-MATHEMATICAL BOLD CAPITAL EPSILON SLASHED
  0xE308: [679,220,981,32,875],      // stix-capital Eta, Greek slashed
  0xE309: [717,137,727,17,633]       // stix-MATHEMATICAL BOLD CAPITAL ETA SLASHED
};

CHTML.fontLoaded("TeX/"+font.substr(8));

})(MathJax.OutputJax.CommonHTML);
