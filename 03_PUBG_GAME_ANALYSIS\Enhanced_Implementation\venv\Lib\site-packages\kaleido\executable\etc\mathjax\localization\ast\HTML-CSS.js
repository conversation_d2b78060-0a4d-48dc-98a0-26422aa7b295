/*************************************************************
 *
 *  MathJax/localization/ast/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ast","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "Cargando la tipograf\u00EDa web %1",
          CantLoadWebFont: "Non pue cargase la tipograf\u00EDa web %1",
          FirefoxCantLoadWebFont: "Firefox nun pue cargar les tipograf\u00EDes web dende un sirvidor remotu",
          CantFindFontUsing: "Nun se pue atopar una tipograf\u00EDa v\u00E1lida usando %1",
          WebFontsNotAvailable: "Les tipograf\u00EDes web nun tan disponibles. Usando les tipograf\u00EDes d'imaxe nel so llugar"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ast/HTML-CSS.js");
