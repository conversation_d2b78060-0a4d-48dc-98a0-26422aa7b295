(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[9572],{19756:function(t){!function(e,n){true?t.exports=n():0}(this,(function(){"use strict";return function(t,e){var n=e.prototype,i=n.format;n.format=function(t){var e=this,n=this.$locale();if(!this.isValid())return i.bind(this)(t);var r=this.$utils(),s=(t||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,(function(t){switch(t){case"Q":return Math.ceil((e.$M+1)/3);case"Do":return n.ordinal(e.$D);case"gggg":return e.weekYear();case"GGGG":return e.isoWeekYear();case"wo":return n.ordinal(e.week(),"W");case"w":case"ww":return r.s(e.week(),"w"===t?1:2,"0");case"W":case"WW":return r.s(e.isoWeek(),"W"===t?1:2,"0");case"k":case"kk":return r.s(String(0===e.$H?24:e.$H),"k"===t?1:2,"0");case"X":return Math.floor(e.$d.getTime()/1e3);case"x":return e.$d.getTime();case"z":return"["+e.offsetName()+"]";case"zzz":return"["+e.offsetName("long")+"]";default:return t}}));return i.bind(this)(s)}}}))},90445:function(t){!function(e,n){true?t.exports=n():0}(this,(function(){"use strict";var t={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},e=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\d/,i=/\d\d/,r=/\d\d?/,s=/\d*[^-_:/,()\s\d]+/,a={},o=function(t){return(t=+t)+(t>68?1900:2e3)};var c=function(t){return function(e){this[t]=+e}},l=[/[+-]\d\d:?(\d\d)?|Z/,function(t){(this.zone||(this.zone={})).offset=function(t){if(!t)return 0;if("Z"===t)return 0;var e=t.match(/([+-]|\d\d)/g),n=60*e[1]+(+e[2]||0);return 0===n?0:"+"===e[0]?-n:n}(t)}],u=function(t){var e=a[t];return e&&(e.indexOf?e:e.s.concat(e.f))},d=function(t,e){var n,i=a.meridiem;if(i){for(var r=1;r<=24;r+=1)if(t.indexOf(i(r,0,e))>-1){n=r>12;break}}else n=t===(e?"pm":"PM");return n},f={A:[s,function(t){this.afternoon=d(t,!1)}],a:[s,function(t){this.afternoon=d(t,!0)}],Q:[n,function(t){this.month=3*(t-1)+1}],S:[n,function(t){this.milliseconds=100*+t}],SS:[i,function(t){this.milliseconds=10*+t}],SSS:[/\d{3}/,function(t){this.milliseconds=+t}],s:[r,c("seconds")],ss:[r,c("seconds")],m:[r,c("minutes")],mm:[r,c("minutes")],H:[r,c("hours")],h:[r,c("hours")],HH:[r,c("hours")],hh:[r,c("hours")],D:[r,c("day")],DD:[i,c("day")],Do:[s,function(t){var e=a.ordinal,n=t.match(/\d+/);if(this.day=n[0],e)for(var i=1;i<=31;i+=1)e(i).replace(/\[|\]/g,"")===t&&(this.day=i)}],w:[r,c("week")],ww:[i,c("week")],M:[r,c("month")],MM:[i,c("month")],MMM:[s,function(t){var e=u("months"),n=(u("monthsShort")||e.map((function(t){return t.slice(0,3)}))).indexOf(t)+1;if(n<1)throw new Error;this.month=n%12||n}],MMMM:[s,function(t){var e=u("months").indexOf(t)+1;if(e<1)throw new Error;this.month=e%12||e}],Y:[/[+-]?\d+/,c("year")],YY:[i,function(t){this.year=o(t)}],YYYY:[/\d{4}/,c("year")],Z:l,ZZ:l};function h(n){var i,r;i=n,r=a&&a.formats;for(var s=(n=i.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(e,n,i){var s=i&&i.toUpperCase();return n||r[i]||t[i]||r[s].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,(function(t,e,n){return e||n.slice(1)}))}))).match(e),o=s.length,c=0;c<o;c+=1){var l=s[c],u=f[l],d=u&&u[0],h=u&&u[1];s[c]=h?{regex:d,parser:h}:l.replace(/^\[|\]$/g,"")}return function(t){for(var e={},n=0,i=0;n<o;n+=1){var r=s[n];if("string"==typeof r)i+=r.length;else{var a=r.regex,c=r.parser,l=t.slice(i),u=a.exec(l)[0];c.call(e,u),t=t.replace(u,"")}}return function(t){var e=t.afternoon;if(void 0!==e){var n=t.hours;e?n<12&&(t.hours+=12):12===n&&(t.hours=0),delete t.afternoon}}(e),e}}return function(t,e,n){n.p.customParseFormat=!0,t&&t.parseTwoDigitYear&&(o=t.parseTwoDigitYear);var i=e.prototype,r=i.parse;i.parse=function(t){var e=t.date,i=t.utc,s=t.args;this.$u=i;var o=s[1];if("string"==typeof o){var c=!0===s[2],l=!0===s[3],u=c||l,d=s[2];l&&(d=s[2]),a=this.$locale(),!c&&d&&(a=n.Ls[d]),this.$d=function(t,e,n,i){try{if(["x","X"].indexOf(e)>-1)return new Date(("X"===e?1e3:1)*t);var r=h(e)(t),s=r.year,a=r.month,o=r.day,c=r.hours,l=r.minutes,u=r.seconds,d=r.milliseconds,f=r.zone,k=r.week,y=new Date,m=o||(s||a?1:y.getDate()),p=s||y.getFullYear(),g=0;s&&!a||(g=a>0?a-1:y.getMonth());var v,b=c||0,T=l||0,x=u||0,w=d||0;return f?new Date(Date.UTC(p,g,m,b,T,x,w+60*f.offset*1e3)):n?new Date(Date.UTC(p,g,m,b,T,x,w)):(v=new Date(p,g,m,b,T,x,w),k&&(v=i(v).week(k).toDate()),v)}catch(t){return new Date("")}}(e,o,i,n),this.init(),d&&!0!==d&&(this.$L=this.locale(d).$L),u&&e!=this.format(o)&&(this.$d=new Date("")),a={}}else if(o instanceof Array)for(var f=o.length,k=1;k<=f;k+=1){s[1]=o[k-1];var y=n.apply(this,s);if(y.isValid()){this.$d=y.$d,this.$L=y.$L,this.init();break}k===f&&(this.$d=new Date(""))}else r.call(this,t)}}}))},90694:function(t){!function(e,n){true?t.exports=n():0}(this,(function(){"use strict";var t="day";return function(e,n,i){var r=function(e){return e.add(4-e.isoWeekday(),t)},s=n.prototype;s.isoWeekYear=function(){return r(this).year()},s.isoWeek=function(e){if(!this.$utils().u(e))return this.add(7*(e-this.isoWeek()),t);var n,s,a,o,c=r(this),l=(n=this.isoWeekYear(),s=this.$u,a=(s?i.utc:i)().year(n).startOf("year"),o=4-a.isoWeekday(),a.isoWeekday()>4&&(o+=7),a.add(o,t));return c.diff(l,"week")+1},s.isoWeekday=function(t){return this.$utils().u(t)?this.day()||7:this.day(this.day()%7?t:t-7)};var a=s.startOf;s.startOf=function(t,e){var n=this.$utils(),i=!!n.u(e)||e;return"isoweek"===n.p(t)?i?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):a.bind(this)(t,e)}}}))},87191:(t,e,n)=>{"use strict";n.d(e,{diagram:()=>Nt});var i=n(96049);var r=n(75905);var s=n(16750);var a=n(74353);var o=n.n(a);var c=n(90694);var l=n.n(c);var u=n(90445);var d=n.n(u);var f=n(19756);var h=n.n(f);var k=n(24982);var y=function(){var t=(0,r.K2)((function(t,e,n,i){for(n=n||{},i=t.length;i--;n[t[i]]=e);return n}),"o"),e=[6,8,10,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,38,40],n=[1,26],i=[1,27],s=[1,28],a=[1,29],o=[1,30],c=[1,31],l=[1,32],u=[1,33],d=[1,34],f=[1,9],h=[1,10],k=[1,11],y=[1,12],m=[1,13],p=[1,14],g=[1,15],v=[1,16],b=[1,19],T=[1,20],x=[1,21],w=[1,22],_=[1,23],D=[1,25],$=[1,35];var C={trace:(0,r.K2)((function t(){}),"trace"),yy:{},symbols_:{error:2,start:3,gantt:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NL:10,weekday:11,weekday_monday:12,weekday_tuesday:13,weekday_wednesday:14,weekday_thursday:15,weekday_friday:16,weekday_saturday:17,weekday_sunday:18,weekend:19,weekend_friday:20,weekend_saturday:21,dateFormat:22,inclusiveEndDates:23,topAxis:24,axisFormat:25,tickInterval:26,excludes:27,includes:28,todayMarker:29,title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,section:36,clickStatement:37,taskTxt:38,taskData:39,click:40,callbackname:41,callbackargs:42,href:43,clickStatementDebug:44,$accept:0,$end:1},terminals_:{2:"error",4:"gantt",6:"EOF",8:"SPACE",10:"NL",12:"weekday_monday",13:"weekday_tuesday",14:"weekday_wednesday",15:"weekday_thursday",16:"weekday_friday",17:"weekday_saturday",18:"weekday_sunday",20:"weekend_friday",21:"weekend_saturday",22:"dateFormat",23:"inclusiveEndDates",24:"topAxis",25:"axisFormat",26:"tickInterval",27:"excludes",28:"includes",29:"todayMarker",30:"title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"section",38:"taskTxt",39:"taskData",40:"click",41:"callbackname",42:"callbackargs",43:"href"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[19,1],[19,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,2],[37,2],[37,3],[37,3],[37,4],[37,3],[37,4],[37,2],[44,2],[44,3],[44,3],[44,4],[44,3],[44,4],[44,2]],performAction:(0,r.K2)((function t(e,n,i,r,s,a,o){var c=a.length-1;switch(s){case 1:return a[c-1];break;case 2:this.$=[];break;case 3:a[c-1].push(a[c]);this.$=a[c-1];break;case 4:case 5:this.$=a[c];break;case 6:case 7:this.$=[];break;case 8:r.setWeekday("monday");break;case 9:r.setWeekday("tuesday");break;case 10:r.setWeekday("wednesday");break;case 11:r.setWeekday("thursday");break;case 12:r.setWeekday("friday");break;case 13:r.setWeekday("saturday");break;case 14:r.setWeekday("sunday");break;case 15:r.setWeekend("friday");break;case 16:r.setWeekend("saturday");break;case 17:r.setDateFormat(a[c].substr(11));this.$=a[c].substr(11);break;case 18:r.enableInclusiveEndDates();this.$=a[c].substr(18);break;case 19:r.TopAxis();this.$=a[c].substr(8);break;case 20:r.setAxisFormat(a[c].substr(11));this.$=a[c].substr(11);break;case 21:r.setTickInterval(a[c].substr(13));this.$=a[c].substr(13);break;case 22:r.setExcludes(a[c].substr(9));this.$=a[c].substr(9);break;case 23:r.setIncludes(a[c].substr(9));this.$=a[c].substr(9);break;case 24:r.setTodayMarker(a[c].substr(12));this.$=a[c].substr(12);break;case 27:r.setDiagramTitle(a[c].substr(6));this.$=a[c].substr(6);break;case 28:this.$=a[c].trim();r.setAccTitle(this.$);break;case 29:case 30:this.$=a[c].trim();r.setAccDescription(this.$);break;case 31:r.addSection(a[c].substr(8));this.$=a[c].substr(8);break;case 33:r.addTask(a[c-1],a[c]);this.$="task";break;case 34:this.$=a[c-1];r.setClickEvent(a[c-1],a[c],null);break;case 35:this.$=a[c-2];r.setClickEvent(a[c-2],a[c-1],a[c]);break;case 36:this.$=a[c-2];r.setClickEvent(a[c-2],a[c-1],null);r.setLink(a[c-2],a[c]);break;case 37:this.$=a[c-3];r.setClickEvent(a[c-3],a[c-2],a[c-1]);r.setLink(a[c-3],a[c]);break;case 38:this.$=a[c-2];r.setClickEvent(a[c-2],a[c],null);r.setLink(a[c-2],a[c-1]);break;case 39:this.$=a[c-3];r.setClickEvent(a[c-3],a[c-1],a[c]);r.setLink(a[c-3],a[c-2]);break;case 40:this.$=a[c-1];r.setLink(a[c-1],a[c]);break;case 41:case 47:this.$=a[c-1]+" "+a[c];break;case 42:case 43:case 45:this.$=a[c-2]+" "+a[c-1]+" "+a[c];break;case 44:case 46:this.$=a[c-3]+" "+a[c-2]+" "+a[c-1]+" "+a[c];break}}),"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},t(e,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:17,12:n,13:i,14:s,15:a,16:o,17:c,18:l,19:18,20:u,21:d,22:f,23:h,24:k,25:y,26:m,27:p,28:g,29:v,30:b,31:T,33:x,35:w,36:_,37:24,38:D,40:$},t(e,[2,7],{1:[2,1]}),t(e,[2,3]),{9:36,11:17,12:n,13:i,14:s,15:a,16:o,17:c,18:l,19:18,20:u,21:d,22:f,23:h,24:k,25:y,26:m,27:p,28:g,29:v,30:b,31:T,33:x,35:w,36:_,37:24,38:D,40:$},t(e,[2,5]),t(e,[2,6]),t(e,[2,17]),t(e,[2,18]),t(e,[2,19]),t(e,[2,20]),t(e,[2,21]),t(e,[2,22]),t(e,[2,23]),t(e,[2,24]),t(e,[2,25]),t(e,[2,26]),t(e,[2,27]),{32:[1,37]},{34:[1,38]},t(e,[2,30]),t(e,[2,31]),t(e,[2,32]),{39:[1,39]},t(e,[2,8]),t(e,[2,9]),t(e,[2,10]),t(e,[2,11]),t(e,[2,12]),t(e,[2,13]),t(e,[2,14]),t(e,[2,15]),t(e,[2,16]),{41:[1,40],43:[1,41]},t(e,[2,4]),t(e,[2,28]),t(e,[2,29]),t(e,[2,33]),t(e,[2,34],{42:[1,42],43:[1,43]}),t(e,[2,40],{41:[1,44]}),t(e,[2,35],{43:[1,45]}),t(e,[2,36]),t(e,[2,38],{42:[1,46]}),t(e,[2,37]),t(e,[2,39])],defaultActions:{},parseError:(0,r.K2)((function t(e,n){if(n.recoverable){this.trace(e)}else{var i=new Error(e);i.hash=n;throw i}}),"parseError"),parse:(0,r.K2)((function t(e){var n=this,i=[0],s=[],a=[null],o=[],c=this.table,l="",u=0,d=0,f=0,h=2,k=1;var y=o.slice.call(arguments,1);var m=Object.create(this.lexer);var p={yy:{}};for(var g in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,g)){p.yy[g]=this.yy[g]}}m.setInput(e,p.yy);p.yy.lexer=m;p.yy.parser=this;if(typeof m.yylloc=="undefined"){m.yylloc={}}var v=m.yylloc;o.push(v);var b=m.options&&m.options.ranges;if(typeof p.yy.parseError==="function"){this.parseError=p.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function T(t){i.length=i.length-2*t;a.length=a.length-t;o.length=o.length-t}(0,r.K2)(T,"popStack");function x(){var t;t=s.pop()||m.lex()||k;if(typeof t!=="number"){if(t instanceof Array){s=t;t=s.pop()}t=n.symbols_[t]||t}return t}(0,r.K2)(x,"lex");var w,_,D,$,C,S,K={},E,M,A,L;while(true){D=i[i.length-1];if(this.defaultActions[D]){$=this.defaultActions[D]}else{if(w===null||typeof w=="undefined"){w=x()}$=c[D]&&c[D][w]}if(typeof $==="undefined"||!$.length||!$[0]){var Y="";L=[];for(E in c[D]){if(this.terminals_[E]&&E>h){L.push("'"+this.terminals_[E]+"'")}}if(m.showPosition){Y="Parse error on line "+(u+1)+":\n"+m.showPosition()+"\nExpecting "+L.join(", ")+", got '"+(this.terminals_[w]||w)+"'"}else{Y="Parse error on line "+(u+1)+": Unexpected "+(w==k?"end of input":"'"+(this.terminals_[w]||w)+"'")}this.parseError(Y,{text:m.match,token:this.terminals_[w]||w,line:m.yylineno,loc:v,expected:L})}if($[0]instanceof Array&&$.length>1){throw new Error("Parse Error: multiple actions possible at state: "+D+", token: "+w)}switch($[0]){case 1:i.push(w);a.push(m.yytext);o.push(m.yylloc);i.push($[1]);w=null;if(!_){d=m.yyleng;l=m.yytext;u=m.yylineno;v=m.yylloc;if(f>0){f--}}else{w=_;_=null}break;case 2:M=this.productions_[$[1]][1];K.$=a[a.length-M];K._$={first_line:o[o.length-(M||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(M||1)].first_column,last_column:o[o.length-1].last_column};if(b){K._$.range=[o[o.length-(M||1)].range[0],o[o.length-1].range[1]]}S=this.performAction.apply(K,[l,d,u,p.yy,$[1],a,o].concat(y));if(typeof S!=="undefined"){return S}if(M){i=i.slice(0,-1*M*2);a=a.slice(0,-1*M);o=o.slice(0,-1*M)}i.push(this.productions_[$[1]][0]);a.push(K.$);o.push(K._$);A=c[i[i.length-2]][i[i.length-1]];i.push(A);break;case 3:return true}}return true}),"parse")};var S=function(){var t={EOF:1,parseError:(0,r.K2)((function t(e,n){if(this.yy.parser){this.yy.parser.parseError(e,n)}else{throw new Error(e)}}),"parseError"),setInput:(0,r.K2)((function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,r.K2)((function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t}),"input"),unput:(0,r.K2)((function(t){var e=t.length;var n=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(n.length-1){this.yylineno-=n.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:n?(n.length===i.length?this.yylloc.first_column:0)+i[i.length-n.length].length-n[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,r.K2)((function(){this._more=true;return this}),"more"),reject:(0,r.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,r.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,r.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,r.K2)((function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,r.K2)((function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,r.K2)((function(t,e){var n,i,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}i=t[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];n=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(n){return n}else if(this._backtrack){for(var s in r){this[s]=r[s]}return false}return false}),"test_match"),next:(0,r.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,n,i;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var s=0;s<r.length;s++){n=this._input.match(this.rules[r[s]]);if(n&&(!e||n[0].length>e[0].length)){e=n;i=s;if(this.options.backtrack_lexer){t=this.test_match(n,r[s]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,r[i]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,r.K2)((function t(){var e=this.next();if(e){return e}else{return this.lex()}}),"lex"),begin:(0,r.K2)((function t(e){this.conditionStack.push(e)}),"begin"),popState:(0,r.K2)((function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,r.K2)((function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,r.K2)((function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}}),"topState"),pushState:(0,r.K2)((function t(e){this.begin(e)}),"pushState"),stateStackSize:(0,r.K2)((function t(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,r.K2)((function t(e,n,i,r){var s=r;switch(i){case 0:this.begin("open_directive");return"open_directive";break;case 1:this.begin("acc_title");return 31;break;case 2:this.popState();return"acc_title_value";break;case 3:this.begin("acc_descr");return 33;break;case 4:this.popState();return"acc_descr_value";break;case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";break;case 8:break;case 9:break;case 10:break;case 11:return 10;break;case 12:break;case 13:break;case 14:this.begin("href");break;case 15:this.popState();break;case 16:return 43;break;case 17:this.begin("callbackname");break;case 18:this.popState();break;case 19:this.popState();this.begin("callbackargs");break;case 20:return 41;break;case 21:this.popState();break;case 22:return 42;break;case 23:this.begin("click");break;case 24:this.popState();break;case 25:return 40;break;case 26:return 4;break;case 27:return 22;break;case 28:return 23;break;case 29:return 24;break;case 30:return 25;break;case 31:return 26;break;case 32:return 28;break;case 33:return 27;break;case 34:return 29;break;case 35:return 12;break;case 36:return 13;break;case 37:return 14;break;case 38:return 15;break;case 39:return 16;break;case 40:return 17;break;case 41:return 18;break;case 42:return 20;break;case 43:return 21;break;case 44:return"date";break;case 45:return 30;break;case 46:return"accDescription";break;case 47:return 36;break;case 48:return 38;break;case 49:return 39;break;case 50:return":";break;case 51:return 6;break;case 52:return"INVALID";break}}),"anonymous"),rules:[/^(?:%%\{)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:%%(?!\{)*[^\n]*)/i,/^(?:[^\}]%%*[^\n]*)/i,/^(?:%%*[^\n]*[\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:%[^\n]*)/i,/^(?:href[\s]+["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:call[\s]+)/i,/^(?:\([\s]*\))/i,/^(?:\()/i,/^(?:[^(]*)/i,/^(?:\))/i,/^(?:[^)]*)/i,/^(?:click[\s]+)/i,/^(?:[\s\n])/i,/^(?:[^\s\n]*)/i,/^(?:gantt\b)/i,/^(?:dateFormat\s[^#\n;]+)/i,/^(?:inclusiveEndDates\b)/i,/^(?:topAxis\b)/i,/^(?:axisFormat\s[^#\n;]+)/i,/^(?:tickInterval\s[^#\n;]+)/i,/^(?:includes\s[^#\n;]+)/i,/^(?:excludes\s[^#\n;]+)/i,/^(?:todayMarker\s[^\n;]+)/i,/^(?:weekday\s+monday\b)/i,/^(?:weekday\s+tuesday\b)/i,/^(?:weekday\s+wednesday\b)/i,/^(?:weekday\s+thursday\b)/i,/^(?:weekday\s+friday\b)/i,/^(?:weekday\s+saturday\b)/i,/^(?:weekday\s+sunday\b)/i,/^(?:weekend\s+friday\b)/i,/^(?:weekend\s+saturday\b)/i,/^(?:\d\d\d\d-\d\d-\d\d\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accDescription\s[^#\n;]+)/i,/^(?:section\s[^\n]+)/i,/^(?:[^:\n]+)/i,/^(?::[^#\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:false},acc_descr:{rules:[4],inclusive:false},acc_title:{rules:[2],inclusive:false},callbackargs:{rules:[21,22],inclusive:false},callbackname:{rules:[18,19,20],inclusive:false},href:{rules:[15,16],inclusive:false},click:{rules:[24,25],inclusive:false},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,17,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],inclusive:true}}};return t}();C.lexer=S;function K(){this.yy={}}(0,r.K2)(K,"Parser");K.prototype=C;C.Parser=K;return new K}();y.parser=y;var m=y;o().extend(l());o().extend(d());o().extend(h());var p={friday:5,saturday:6};var g="";var v="";var b=void 0;var T="";var x=[];var w=[];var _=new Map;var D=[];var $=[];var C="";var S="";var K=["active","done","crit","milestone"];var E=[];var M=false;var A=false;var L="sunday";var Y="saturday";var I=0;var F=(0,r.K2)((function(){D=[];$=[];C="";E=[];kt=0;gt=void 0;vt=void 0;bt=[];g="";v="";S="";b=void 0;T="";x=[];w=[];M=false;A=false;I=0;_=new Map;(0,r.IU)();L="sunday";Y="saturday"}),"clear");var W=(0,r.K2)((function(t){v=t}),"setAxisFormat");var O=(0,r.K2)((function(){return v}),"getAxisFormat");var P=(0,r.K2)((function(t){b=t}),"setTickInterval");var B=(0,r.K2)((function(){return b}),"getTickInterval");var z=(0,r.K2)((function(t){T=t}),"setTodayMarker");var N=(0,r.K2)((function(){return T}),"getTodayMarker");var G=(0,r.K2)((function(t){g=t}),"setDateFormat");var H=(0,r.K2)((function(){M=true}),"enableInclusiveEndDates");var j=(0,r.K2)((function(){return M}),"endDatesAreInclusive");var R=(0,r.K2)((function(){A=true}),"enableTopAxis");var U=(0,r.K2)((function(){return A}),"topAxisEnabled");var V=(0,r.K2)((function(t){S=t}),"setDisplayMode");var Z=(0,r.K2)((function(){return S}),"getDisplayMode");var X=(0,r.K2)((function(){return g}),"getDateFormat");var q=(0,r.K2)((function(t){x=t.toLowerCase().split(/[\s,]+/)}),"setIncludes");var Q=(0,r.K2)((function(){return x}),"getIncludes");var J=(0,r.K2)((function(t){w=t.toLowerCase().split(/[\s,]+/)}),"setExcludes");var tt=(0,r.K2)((function(){return w}),"getExcludes");var et=(0,r.K2)((function(){return _}),"getLinks");var nt=(0,r.K2)((function(t){C=t;D.push(t)}),"addSection");var it=(0,r.K2)((function(){return D}),"getSections");var rt=(0,r.K2)((function(){let t=Dt();const e=10;let n=0;while(!t&&n<e){t=Dt();n++}$=bt;return $}),"getTasks");var st=(0,r.K2)((function(t,e,n,i){if(i.includes(t.format(e.trim()))){return false}if(n.includes("weekends")&&(t.isoWeekday()===p[Y]||t.isoWeekday()===p[Y]+1)){return true}if(n.includes(t.format("dddd").toLowerCase())){return true}return n.includes(t.format(e.trim()))}),"isInvalidDate");var at=(0,r.K2)((function(t){L=t}),"setWeekday");var ot=(0,r.K2)((function(){return L}),"getWeekday");var ct=(0,r.K2)((function(t){Y=t}),"setWeekend");var lt=(0,r.K2)((function(t,e,n,i){if(!n.length||t.manualEndTime){return}let r;if(t.startTime instanceof Date){r=o()(t.startTime)}else{r=o()(t.startTime,e,true)}r=r.add(1,"d");let s;if(t.endTime instanceof Date){s=o()(t.endTime)}else{s=o()(t.endTime,e,true)}const[a,c]=ut(r,s,e,n,i);t.endTime=a.toDate();t.renderEndTime=c}),"checkTaskDates");var ut=(0,r.K2)((function(t,e,n,i,r){let s=false;let a=null;while(t<=e){if(!s){a=e.toDate()}s=st(t,n,i,r);if(s){e=e.add(1,"d")}t=t.add(1,"d")}return[e,a]}),"fixTaskDates");var dt=(0,r.K2)((function(t,e,n){n=n.trim();const i=/^after\s+(?<ids>[\d\w- ]+)/;const s=i.exec(n);if(s!==null){let t=null;for(const n of s.groups.ids.split(" ")){let e=wt(n);if(e!==void 0&&(!t||e.endTime>t.endTime)){t=e}}if(t){return t.endTime}const e=new Date;e.setHours(0,0,0,0);return e}let a=o()(n,e.trim(),true);if(a.isValid()){return a.toDate()}else{r.Rm.debug("Invalid date:"+n);r.Rm.debug("With date format:"+e.trim());const t=new Date(n);if(t===void 0||isNaN(t.getTime())||t.getFullYear()<-1e4||t.getFullYear()>1e4){throw new Error("Invalid date:"+n)}return t}}),"getStartDate");var ft=(0,r.K2)((function(t){const e=/^(\d+(?:\.\d+)?)([Mdhmswy]|ms)$/.exec(t.trim());if(e!==null){return[Number.parseFloat(e[1]),e[2]]}return[NaN,"ms"]}),"parseDuration");var ht=(0,r.K2)((function(t,e,n,i=false){n=n.trim();const r=/^until\s+(?<ids>[\d\w- ]+)/;const s=r.exec(n);if(s!==null){let t=null;for(const n of s.groups.ids.split(" ")){let e=wt(n);if(e!==void 0&&(!t||e.startTime<t.startTime)){t=e}}if(t){return t.startTime}const e=new Date;e.setHours(0,0,0,0);return e}let a=o()(n,e.trim(),true);if(a.isValid()){if(i){a=a.add(1,"d")}return a.toDate()}let c=o()(t);const[l,u]=ft(n);if(!Number.isNaN(l)){const t=c.add(l,u);if(t.isValid()){c=t}}return c.toDate()}),"getEndDate");var kt=0;var yt=(0,r.K2)((function(t){if(t===void 0){kt=kt+1;return"task"+kt}return t}),"parseId");var mt=(0,r.K2)((function(t,e){let n;if(e.substr(0,1)===":"){n=e.substr(1,e.length)}else{n=e}const i=n.split(",");const r={};Lt(i,r,K);for(let a=0;a<i.length;a++){i[a]=i[a].trim()}let s="";switch(i.length){case 1:r.id=yt();r.startTime=t.endTime;s=i[0];break;case 2:r.id=yt();r.startTime=dt(void 0,g,i[0]);s=i[1];break;case 3:r.id=yt(i[0]);r.startTime=dt(void 0,g,i[1]);s=i[2];break;default:}if(s){r.endTime=ht(r.startTime,g,s,M);r.manualEndTime=o()(s,"YYYY-MM-DD",true).isValid();lt(r,g,w,x)}return r}),"compileData");var pt=(0,r.K2)((function(t,e){let n;if(e.substr(0,1)===":"){n=e.substr(1,e.length)}else{n=e}const i=n.split(",");const r={};Lt(i,r,K);for(let s=0;s<i.length;s++){i[s]=i[s].trim()}switch(i.length){case 1:r.id=yt();r.startTime={type:"prevTaskEnd",id:t};r.endTime={data:i[0]};break;case 2:r.id=yt();r.startTime={type:"getStartDate",startData:i[0]};r.endTime={data:i[1]};break;case 3:r.id=yt(i[0]);r.startTime={type:"getStartDate",startData:i[1]};r.endTime={data:i[2]};break;default:}return r}),"parseData");var gt;var vt;var bt=[];var Tt={};var xt=(0,r.K2)((function(t,e){const n={section:C,type:C,processed:false,manualEndTime:false,renderEndTime:null,raw:{data:e},task:t,classes:[]};const i=pt(vt,e);n.raw.startTime=i.startTime;n.raw.endTime=i.endTime;n.id=i.id;n.prevTaskId=vt;n.active=i.active;n.done=i.done;n.crit=i.crit;n.milestone=i.milestone;n.order=I;I++;const r=bt.push(n);vt=n.id;Tt[n.id]=r-1}),"addTask");var wt=(0,r.K2)((function(t){const e=Tt[t];return bt[e]}),"findTaskById");var _t=(0,r.K2)((function(t,e){const n={section:C,type:C,description:t,task:t,classes:[]};const i=mt(gt,e);n.startTime=i.startTime;n.endTime=i.endTime;n.id=i.id;n.active=i.active;n.done=i.done;n.crit=i.crit;n.milestone=i.milestone;gt=n;$.push(n)}),"addTaskOrg");var Dt=(0,r.K2)((function(){const t=(0,r.K2)((function(t){const e=bt[t];let n="";switch(bt[t].raw.startTime.type){case"prevTaskEnd":{const t=wt(e.prevTaskId);e.startTime=t.endTime;break}case"getStartDate":n=dt(void 0,g,bt[t].raw.startTime.startData);if(n){bt[t].startTime=n}break}if(bt[t].startTime){bt[t].endTime=ht(bt[t].startTime,g,bt[t].raw.endTime.data,M);if(bt[t].endTime){bt[t].processed=true;bt[t].manualEndTime=o()(bt[t].raw.endTime.data,"YYYY-MM-DD",true).isValid();lt(bt[t],g,w,x)}}return bt[t].processed}),"compileTask");let e=true;for(const[n,i]of bt.entries()){t(n);e=e&&i.processed}return e}),"compileTasks");var $t=(0,r.K2)((function(t,e){let n=e;if((0,r.D7)().securityLevel!=="loose"){n=(0,s.J)(e)}t.split(",").forEach((function(t){let e=wt(t);if(e!==void 0){Kt(t,(()=>{window.open(n,"_self")}));_.set(t,n)}}));Ct(t,"clickable")}),"setLink");var Ct=(0,r.K2)((function(t,e){t.split(",").forEach((function(t){let n=wt(t);if(n!==void 0){n.classes.push(e)}}))}),"setClass");var St=(0,r.K2)((function(t,e,n){if((0,r.D7)().securityLevel!=="loose"){return}if(e===void 0){return}let s=[];if(typeof n==="string"){s=n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let t=0;t<s.length;t++){let e=s[t].trim();if(e.startsWith('"')&&e.endsWith('"')){e=e.substr(1,e.length-2)}s[t]=e}}if(s.length===0){s.push(t)}let a=wt(t);if(a!==void 0){Kt(t,(()=>{i._K.runFunc(e,...s)}))}}),"setClickFun");var Kt=(0,r.K2)((function(t,e){E.push((function(){const n=document.querySelector(`[id="${t}"]`);if(n!==null){n.addEventListener("click",(function(){e()}))}}),(function(){const n=document.querySelector(`[id="${t}-text"]`);if(n!==null){n.addEventListener("click",(function(){e()}))}}))}),"pushFun");var Et=(0,r.K2)((function(t,e,n){t.split(",").forEach((function(t){St(t,e,n)}));Ct(t,"clickable")}),"setClickEvent");var Mt=(0,r.K2)((function(t){E.forEach((function(e){e(t)}))}),"bindFunctions");var At={getConfig:(0,r.K2)((()=>(0,r.D7)().gantt),"getConfig"),clear:F,setDateFormat:G,getDateFormat:X,enableInclusiveEndDates:H,endDatesAreInclusive:j,enableTopAxis:R,topAxisEnabled:U,setAxisFormat:W,getAxisFormat:O,setTickInterval:P,getTickInterval:B,setTodayMarker:z,getTodayMarker:N,setAccTitle:r.SV,getAccTitle:r.iN,setDiagramTitle:r.ke,getDiagramTitle:r.ab,setDisplayMode:V,getDisplayMode:Z,setAccDescription:r.EI,getAccDescription:r.m7,addSection:nt,getSections:it,getTasks:rt,addTask:xt,findTaskById:wt,addTaskOrg:_t,setIncludes:q,getIncludes:Q,setExcludes:J,getExcludes:tt,setClickEvent:Et,setLink:$t,getLinks:et,bindFunctions:Mt,parseDuration:ft,isInvalidDate:st,setWeekday:at,getWeekday:ot,setWeekend:ct};function Lt(t,e,n){let i=true;while(i){i=false;n.forEach((function(n){const r="^\\s*"+n+"\\s*$";const s=new RegExp(r);if(t[0].match(s)){e[n]=true;t.shift(1);i=true}}))}}(0,r.K2)(Lt,"getTaskTags");var Yt=(0,r.K2)((function(){r.Rm.debug("Something is calling, setConf, remove the call")}),"setConf");var It={monday:k.ABi,tuesday:k.PGu,wednesday:k.GuW,thursday:k.Mol,friday:k.TUC,saturday:k.rGn,sunday:k.YPH};var Ft=(0,r.K2)(((t,e)=>{let n=[...t].map((()=>-Infinity));let i=[...t].sort(((t,e)=>t.startTime-e.startTime||t.order-e.order));let r=0;for(const s of i){for(let t=0;t<n.length;t++){if(s.startTime>=n[t]){n[t]=s.endTime;s.order=t+e;if(t>r){r=t}break}}}return r}),"getMaxIntersections");var Wt;var Ot=(0,r.K2)((function(t,e,n,i){const s=(0,r.D7)().gantt;const a=(0,r.D7)().securityLevel;let c;if(a==="sandbox"){c=(0,k.Ltv)("#i"+e)}const l=a==="sandbox"?(0,k.Ltv)(c.nodes()[0].contentDocument.body):(0,k.Ltv)("body");const u=a==="sandbox"?c.nodes()[0].contentDocument:document;const d=u.getElementById(e);Wt=d.parentElement.offsetWidth;if(Wt===void 0){Wt=1200}if(s.useWidth!==void 0){Wt=s.useWidth}const f=i.db.getTasks();let h=[];for(const r of f){h.push(r.type)}h=$(h);const y={};let m=2*s.topPadding;if(i.db.getDisplayMode()==="compact"||s.displayMode==="compact"){const t={};for(const n of f){if(t[n.section]===void 0){t[n.section]=[n]}else{t[n.section].push(n)}}let e=0;for(const n of Object.keys(t)){const i=Ft(t[n],e)+1;e+=i;m+=i*(s.barHeight+s.barGap);y[n]=i}}else{m+=f.length*(s.barHeight+s.barGap);for(const t of h){y[t]=f.filter((e=>e.type===t)).length}}d.setAttribute("viewBox","0 0 "+Wt+" "+m);const p=l.select(`[id="${e}"]`);const g=(0,k.w7C)().domain([(0,k.jkA)(f,(function(t){return t.startTime})),(0,k.T9B)(f,(function(t){return t.endTime}))]).rangeRound([0,Wt-s.leftPadding-s.rightPadding]);function v(t,e){const n=t.startTime;const i=e.startTime;let r=0;if(n>i){r=1}else if(n<i){r=-1}return r}(0,r.K2)(v,"taskCompare");f.sort(v);b(f,Wt,m);(0,r.a$)(p,m,Wt,s.useMaxWidth);p.append("text").text(i.db.getDiagramTitle()).attr("x",Wt/2).attr("y",s.titleTopMargin).attr("class","titleText");function b(t,e,n){const r=s.barHeight;const a=r+s.barGap;const o=s.topPadding;const c=s.leftPadding;const l=(0,k.m4Y)().domain([0,h.length]).range(["#00B9FA","#F95002"]).interpolate(k.bEH);x(a,o,c,e,n,t,i.db.getExcludes(),i.db.getIncludes());w(c,o,e,n);T(t,a,o,c,r,l,e,n);_(a,o,c,r,l);D(c,o,e,n)}(0,r.K2)(b,"makeGantt");function T(t,n,a,o,c,l,u){const d=[...new Set(t.map((t=>t.order)))];const f=d.map((e=>t.find((t=>t.order===e))));p.append("g").selectAll("rect").data(f).enter().append("rect").attr("x",0).attr("y",(function(t,e){e=t.order;return e*n+a-2})).attr("width",(function(){return u-s.rightPadding/2})).attr("height",n).attr("class",(function(t){for(const[e,n]of h.entries()){if(t.type===n){return"section section"+e%s.numberSectionStyles}}return"section section0"}));const y=p.append("g").selectAll("rect").data(t).enter();const m=i.db.getLinks();y.append("rect").attr("id",(function(t){return t.id})).attr("rx",3).attr("ry",3).attr("x",(function(t){if(t.milestone){return g(t.startTime)+o+.5*(g(t.endTime)-g(t.startTime))-.5*c}return g(t.startTime)+o})).attr("y",(function(t,e){e=t.order;return e*n+a})).attr("width",(function(t){if(t.milestone){return c}return g(t.renderEndTime||t.endTime)-g(t.startTime)})).attr("height",c).attr("transform-origin",(function(t,e){e=t.order;return(g(t.startTime)+o+.5*(g(t.endTime)-g(t.startTime))).toString()+"px "+(e*n+a+.5*c).toString()+"px"})).attr("class",(function(t){const e="task";let n="";if(t.classes.length>0){n=t.classes.join(" ")}let i=0;for(const[a,o]of h.entries()){if(t.type===o){i=a%s.numberSectionStyles}}let r="";if(t.active){if(t.crit){r+=" activeCrit"}else{r=" active"}}else if(t.done){if(t.crit){r=" doneCrit"}else{r=" done"}}else{if(t.crit){r+=" crit"}}if(r.length===0){r=" task"}if(t.milestone){r=" milestone "+r}r+=i;r+=" "+n;return e+r}));y.append("text").attr("id",(function(t){return t.id+"-text"})).text((function(t){return t.task})).attr("font-size",s.fontSize).attr("x",(function(t){let e=g(t.startTime);let n=g(t.renderEndTime||t.endTime);if(t.milestone){e+=.5*(g(t.endTime)-g(t.startTime))-.5*c}if(t.milestone){n=e+c}const i=this.getBBox().width;if(i>n-e){if(n+i+1.5*s.leftPadding>u){return e+o-5}else{return n+o+5}}else{return(n-e)/2+e+o}})).attr("y",(function(t,e){e=t.order;return e*n+s.barHeight/2+(s.fontSize/2-2)+a})).attr("text-height",c).attr("class",(function(t){const e=g(t.startTime);let n=g(t.endTime);if(t.milestone){n=e+c}const i=this.getBBox().width;let r="";if(t.classes.length>0){r=t.classes.join(" ")}let a=0;for(const[c,l]of h.entries()){if(t.type===l){a=c%s.numberSectionStyles}}let o="";if(t.active){if(t.crit){o="activeCritText"+a}else{o="activeText"+a}}if(t.done){if(t.crit){o=o+" doneCritText"+a}else{o=o+" doneText"+a}}else{if(t.crit){o=o+" critText"+a}}if(t.milestone){o+=" milestoneText"}if(i>n-e){if(n+i+1.5*s.leftPadding>u){return r+" taskTextOutsideLeft taskTextOutside"+a+" "+o}else{return r+" taskTextOutsideRight taskTextOutside"+a+" "+o+" width-"+i}}else{return r+" taskText taskText"+a+" "+o+" width-"+i}}));const v=(0,r.D7)().securityLevel;if(v==="sandbox"){let t;t=(0,k.Ltv)("#i"+e);const n=t.nodes()[0].contentDocument;y.filter((function(t){return m.has(t.id)})).each((function(t){var e=n.querySelector("#"+t.id);var i=n.querySelector("#"+t.id+"-text");const r=e.parentNode;var s=n.createElement("a");s.setAttribute("xlink:href",m.get(t.id));s.setAttribute("target","_top");r.appendChild(s);s.appendChild(e);s.appendChild(i)}))}}(0,r.K2)(T,"drawRects");function x(t,e,n,a,c,l,u,d){if(u.length===0&&d.length===0){return}let f;let h;for(const{startTime:i,endTime:r}of l){if(f===void 0||i<f){f=i}if(h===void 0||r>h){h=r}}if(!f||!h){return}if(o()(h).diff(o()(f),"year")>5){r.Rm.warn("The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.");return}const k=i.db.getDateFormat();const y=[];let m=null;let v=o()(f);while(v.valueOf()<=h){if(i.db.isInvalidDate(v,k,u,d)){if(!m){m={start:v,end:v}}else{m.end=v}}else{if(m){y.push(m);m=null}}v=v.add(1,"d")}const b=p.append("g").selectAll("rect").data(y).enter();b.append("rect").attr("id",(function(t){return"exclude-"+t.start.format("YYYY-MM-DD")})).attr("x",(function(t){return g(t.start)+n})).attr("y",s.gridLineStartPadding).attr("width",(function(t){const e=t.end.add(1,"day");return g(e)-g(t.start)})).attr("height",c-e-s.gridLineStartPadding).attr("transform-origin",(function(e,i){return(g(e.start)+n+.5*(g(e.end)-g(e.start))).toString()+"px "+(i*t+.5*c).toString()+"px"})).attr("class","exclude-range")}(0,r.K2)(x,"drawExcludeDays");function w(t,e,n,r){let a=(0,k.l78)(g).tickSize(-r+e+s.gridLineStartPadding).tickFormat((0,k.DCK)(i.db.getAxisFormat()||s.axisFormat||"%Y-%m-%d"));const o=/^([1-9]\d*)(millisecond|second|minute|hour|day|week|month)$/;const c=o.exec(i.db.getTickInterval()||s.tickInterval);if(c!==null){const t=c[1];const e=c[2];const n=i.db.getWeekday()||s.weekday;switch(e){case"millisecond":a.ticks(k.t6C.every(t));break;case"second":a.ticks(k.ucG.every(t));break;case"minute":a.ticks(k.wXd.every(t));break;case"hour":a.ticks(k.Agd.every(t));break;case"day":a.ticks(k.UAC.every(t));break;case"week":a.ticks(It[n].every(t));break;case"month":a.ticks(k.Ui6.every(t));break}}p.append("g").attr("class","grid").attr("transform","translate("+t+", "+(r-50)+")").call(a).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10).attr("dy","1em");if(i.db.topAxisEnabled()||s.topAxis){let n=(0,k.tlR)(g).tickSize(-r+e+s.gridLineStartPadding).tickFormat((0,k.DCK)(i.db.getAxisFormat()||s.axisFormat||"%Y-%m-%d"));if(c!==null){const t=c[1];const e=c[2];const r=i.db.getWeekday()||s.weekday;switch(e){case"millisecond":n.ticks(k.t6C.every(t));break;case"second":n.ticks(k.ucG.every(t));break;case"minute":n.ticks(k.wXd.every(t));break;case"hour":n.ticks(k.Agd.every(t));break;case"day":n.ticks(k.UAC.every(t));break;case"week":n.ticks(It[r].every(t));break;case"month":n.ticks(k.Ui6.every(t));break}}p.append("g").attr("class","grid").attr("transform","translate("+t+", "+e+")").call(n).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10)}}(0,r.K2)(w,"makeGrid");function _(t,e){let n=0;const i=Object.keys(y).map((t=>[t,y[t]]));p.append("g").selectAll("text").data(i).enter().append((function(t){const e=t[0].split(r.Y2.lineBreakRegex);const n=-(e.length-1)/2;const i=u.createElementNS("http://www.w3.org/2000/svg","text");i.setAttribute("dy",n+"em");for(const[r,s]of e.entries()){const t=u.createElementNS("http://www.w3.org/2000/svg","tspan");t.setAttribute("alignment-baseline","central");t.setAttribute("x","10");if(r>0){t.setAttribute("dy","1em")}t.textContent=s;i.appendChild(t)}return i})).attr("x",10).attr("y",(function(r,s){if(s>0){for(let a=0;a<s;a++){n+=i[s-1][1];return r[1]*t/2+n*t+e}}else{return r[1]*t/2+e}})).attr("font-size",s.sectionFontSize).attr("class",(function(t){for(const[e,n]of h.entries()){if(t[0]===n){return"sectionTitle sectionTitle"+e%s.numberSectionStyles}}return"sectionTitle"}))}(0,r.K2)(_,"vertLabels");function D(t,e,n,r){const a=i.db.getTodayMarker();if(a==="off"){return}const o=p.append("g").attr("class","today");const c=new Date;const l=o.append("line");l.attr("x1",g(c)+t).attr("x2",g(c)+t).attr("y1",s.titleTopMargin).attr("y2",r-s.titleTopMargin).attr("class","today");if(a!==""){l.attr("style",a.replace(/,/g,";"))}}(0,r.K2)(D,"drawToday");function $(t){const e={};const n=[];for(let i=0,r=t.length;i<r;++i){if(!Object.prototype.hasOwnProperty.call(e,t[i])){e[t[i]]=true;n.push(t[i])}}return n}(0,r.K2)($,"checkUnique")}),"draw");var Pt={setConf:Yt,draw:Ot};var Bt=(0,r.K2)((t=>`\n  .mermaid-main-font {\n        font-family: ${t.fontFamily};\n  }\n\n  .exclude-range {\n    fill: ${t.excludeBkgColor};\n  }\n\n  .section {\n    stroke: none;\n    opacity: 0.2;\n  }\n\n  .section0 {\n    fill: ${t.sectionBkgColor};\n  }\n\n  .section2 {\n    fill: ${t.sectionBkgColor2};\n  }\n\n  .section1,\n  .section3 {\n    fill: ${t.altSectionBkgColor};\n    opacity: 0.2;\n  }\n\n  .sectionTitle0 {\n    fill: ${t.titleColor};\n  }\n\n  .sectionTitle1 {\n    fill: ${t.titleColor};\n  }\n\n  .sectionTitle2 {\n    fill: ${t.titleColor};\n  }\n\n  .sectionTitle3 {\n    fill: ${t.titleColor};\n  }\n\n  .sectionTitle {\n    text-anchor: start;\n    font-family: ${t.fontFamily};\n  }\n\n\n  /* Grid and axis */\n\n  .grid .tick {\n    stroke: ${t.gridColor};\n    opacity: 0.8;\n    shape-rendering: crispEdges;\n  }\n\n  .grid .tick text {\n    font-family: ${t.fontFamily};\n    fill: ${t.textColor};\n  }\n\n  .grid path {\n    stroke-width: 0;\n  }\n\n\n  /* Today line */\n\n  .today {\n    fill: none;\n    stroke: ${t.todayLineColor};\n    stroke-width: 2px;\n  }\n\n\n  /* Task styling */\n\n  /* Default task */\n\n  .task {\n    stroke-width: 2;\n  }\n\n  .taskText {\n    text-anchor: middle;\n    font-family: ${t.fontFamily};\n  }\n\n  .taskTextOutsideRight {\n    fill: ${t.taskTextDarkColor};\n    text-anchor: start;\n    font-family: ${t.fontFamily};\n  }\n\n  .taskTextOutsideLeft {\n    fill: ${t.taskTextDarkColor};\n    text-anchor: end;\n  }\n\n\n  /* Special case clickable */\n\n  .task.clickable {\n    cursor: pointer;\n  }\n\n  .taskText.clickable {\n    cursor: pointer;\n    fill: ${t.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideLeft.clickable {\n    cursor: pointer;\n    fill: ${t.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideRight.clickable {\n    cursor: pointer;\n    fill: ${t.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n\n  /* Specific task settings for the sections*/\n\n  .taskText0,\n  .taskText1,\n  .taskText2,\n  .taskText3 {\n    fill: ${t.taskTextColor};\n  }\n\n  .task0,\n  .task1,\n  .task2,\n  .task3 {\n    fill: ${t.taskBkgColor};\n    stroke: ${t.taskBorderColor};\n  }\n\n  .taskTextOutside0,\n  .taskTextOutside2\n  {\n    fill: ${t.taskTextOutsideColor};\n  }\n\n  .taskTextOutside1,\n  .taskTextOutside3 {\n    fill: ${t.taskTextOutsideColor};\n  }\n\n\n  /* Active task */\n\n  .active0,\n  .active1,\n  .active2,\n  .active3 {\n    fill: ${t.activeTaskBkgColor};\n    stroke: ${t.activeTaskBorderColor};\n  }\n\n  .activeText0,\n  .activeText1,\n  .activeText2,\n  .activeText3 {\n    fill: ${t.taskTextDarkColor} !important;\n  }\n\n\n  /* Completed task */\n\n  .done0,\n  .done1,\n  .done2,\n  .done3 {\n    stroke: ${t.doneTaskBorderColor};\n    fill: ${t.doneTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneText0,\n  .doneText1,\n  .doneText2,\n  .doneText3 {\n    fill: ${t.taskTextDarkColor} !important;\n  }\n\n\n  /* Tasks on the critical line */\n\n  .crit0,\n  .crit1,\n  .crit2,\n  .crit3 {\n    stroke: ${t.critBorderColor};\n    fill: ${t.critBkgColor};\n    stroke-width: 2;\n  }\n\n  .activeCrit0,\n  .activeCrit1,\n  .activeCrit2,\n  .activeCrit3 {\n    stroke: ${t.critBorderColor};\n    fill: ${t.activeTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneCrit0,\n  .doneCrit1,\n  .doneCrit2,\n  .doneCrit3 {\n    stroke: ${t.critBorderColor};\n    fill: ${t.doneTaskBkgColor};\n    stroke-width: 2;\n    cursor: pointer;\n    shape-rendering: crispEdges;\n  }\n\n  .milestone {\n    transform: rotate(45deg) scale(0.8,0.8);\n  }\n\n  .milestoneText {\n    font-style: italic;\n  }\n  .doneCritText0,\n  .doneCritText1,\n  .doneCritText2,\n  .doneCritText3 {\n    fill: ${t.taskTextDarkColor} !important;\n  }\n\n  .activeCritText0,\n  .activeCritText1,\n  .activeCritText2,\n  .activeCritText3 {\n    fill: ${t.taskTextDarkColor} !important;\n  }\n\n  .titleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${t.titleColor||t.textColor};\n    font-family: ${t.fontFamily};\n  }\n`),"getStyles");var zt=Bt;var Nt={parser:m,db:At,renderer:Pt,styles:zt}}}]);