/*************************************************************
 *
 *  MathJax/localization/kn/kn.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("kn",null,{
  menuTitle: "\u0C95\u0CA8\u0CCD\u0CA8\u0CA1",
  version: "2.7.5",
  isLoaded: true,
  domains: {
    "_": {
        version: "2.7.5",
        isLoaded: true,
        strings: {
          CookieConfig: "\u0CAE\u0CA5\u0CCD\u0C9C\u0C95\u0CCD\u0CB7\u0CCD \u0C87\u0C97\u0CC6  \u0C92\u0C82\u0CA6\u0CC1 \u0CB8\u0CA6\u0CB8\u0CCD\u0CAF \u0C9A\u0CCA\u0CA8\u0CCD\u0CAB\u0CBC\u0CBF\u0C97\u0CC1\u0CB0\u0CA4\u0CBF\u0C92\u0CA8\u0CCD \u0C95\u0CC1\u0C95\u0CC0 \u0CB8\u0CBF\u0C95\u0CCD\u0C95\u0CBF\u0CA6\u0CC6. \u0C85\u0CA6\u0CCD\u0CA6\u0CA8\u0CCD\u0CA8 \u0CB0\u0CA8\u0CCD \u0CAE\u0CBE\u0CA1\u0CAC\u0CC7\u0C95?   \n\n(\u0CA8\u0CC0\u0CB5\u0CC7 \u0C85\u0CA6\u0CA8\u0CCD\u0CA8 \u0CB8\u0CC6\u0C9F\u0CCD \u0C85\u0CAA\u0CCD \u0CAE\u0CBE\u0CA1\u0CBF\u0CA6\u0CC1 \u0C85\u0CB2\u0CCD\u0CB2 \u0C86\u0CA6\u0CB0\u0CC6 \"Cancel\" \u0C85\u0CA5\u0CB5\u0CBE \"\u0CB0\u0CA6\u0CCD\u0CA6\u0CC1\" \u0CB5\u0CA4\u0CCD\u0CA4\u0CBF.)",
          MathProcessingError: "\u0C97\u0CA3\u0CBF\u0CA4 \u0CAA\u0CCD\u0CB0\u0C95\u0CCD\u0CB0\u0CBF\u0CAF\u0CC6\u0CAF\u0CB2\u0CCD\u0CB2\u0CBF \u0CA6\u0CCB\u0CB7",
          MathError: "\u0C97\u0CA3\u0CBF\u0CA4 \u0CA6\u0CCB\u0CB7",
          LoadFile: "%1 \u0CB2\u0CCB\u0CA1\u0CCD \u0C86\u0C97\u0CC1\u0CA4\u0CCD\u0CA4\u0CBF\u0CA6\u0CC6",
          Loading: "\u0CB2\u0CCB\u0CA1\u0CCD \u0C86\u0C97\u0CC1\u0CA4\u0CCD\u0CA4\u0CBF\u0CA6\u0CC6",
          LoadFailed: "%1 \u0CAB\u0CC8\u0CB2\u0CCD \u0CB2\u0CCB\u0CA1\u0CCD \u0C86\u0C97\u0CB2\u0CBF\u0CB2\u0CCD\u0CB2",
          ProcessMath: "\u0C97\u0CA3\u0CBF\u0CA4 \u0CAA\u0CCD\u0CB0\u0C95\u0CCD\u0CB0\u0CBF\u0CAF\u0CC6 \u0C86\u0C97\u0CC1\u0CA4\u0CCD\u0CA4\u0CBF\u0CA6\u0CC6: %1%%",
          Processing: "\u0CAA\u0CCD\u0CB0\u0C95\u0CCD\u0CB0\u0CBF\u0CAF\u0CC6 \u0C86\u0C97\u0CC1\u0CA4\u0CCD\u0CA4\u0CBF\u0CA6\u0CC6",
          TypesetMath: "\u0C97\u0CA3\u0CBF\u0CA4 \u0C9F\u0CC8\u0CAA\u0CCD\u200D\u0CB8\u0CC6\u0C9F\u0CCD\u0C9F\u0CBF\u0C82\u0C97\u0CCD: %1%%",
          Typesetting: "\u0C9F\u0CC8\u0CAA\u0CCD\u200D\u0CB8\u0CC6\u0C9F\u0CCD\u0C9F\u0CBF\u0C82\u0C97\u0CCD",
          MathJaxNotSupported: "\u0CA8\u0CBF\u0CAE\u0CCD\u0CAE \u0CAC\u0CCD\u0CB0\u0CCC\u0CB8\u0CB0\u0CCD MathJax\u0C85\u0CA8\u0CCD\u0CA8\u0CC1 \u0CAC\u0CC6\u0C82\u0CAC\u0CB2\u0CBF\u0CB8\u0CC1\u0CB5\u0CC1\u0CA6\u0CBF\u0CB2\u0CCD\u0CB2"
        }
    },
    "FontWarnings": {},
    "HTML-CSS": {},
    "HelpDialog": {},
    "MathML": {},
    "MathMenu": {},
    "TeX": {}
  },
  plural: function (n) {
      return 1; // other
    },
  number: function (n) {
      return n;
    }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/kn/kn.js");
