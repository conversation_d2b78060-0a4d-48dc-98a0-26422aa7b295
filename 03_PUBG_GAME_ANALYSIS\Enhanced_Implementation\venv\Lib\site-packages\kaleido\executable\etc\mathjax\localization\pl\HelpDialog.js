/*************************************************************
 *
 *  MathJax/localization/pl/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("pl","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "Pomoc MathJax",
          MathJax: "*MathJax* to biblioteka Javascript umo\u017Cliwiaj\u0105ca autorom stron Web na zapisywanie wzor\u00F3w matematycznych. Jako u\u017Cytkownik, nie musisz robi\u0107 nic dodatkowo, aby wzory by\u0142y poprawnie wy\u015Bwietlane.",
          Browsers: "*Przegl\u0105darki*: MathJax dzia\u0142a z nowymi przegl\u0105darkami, m.in. IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ i wi\u0119kszo\u015Bci\u0105 przegl\u0105darek mobilnych.",
          Menu: "*Menu*: MathJax dodaje menu kontekstowe do wzor\u00F3w matematycznych. Kliknij prawym klawiszem myszki (lub lewy klawisz i Ctrl), aby je otworzy\u0107.",
          ShowMath: "Opcja *Poka\u017C wzory jako* pozwala zobaczy\u0107 \u017Ar\u00F3d\u0142ow\u0105 posta\u0107 wzor\u00F3w, aby mo\u017Cna by\u0142o je skopiowa\u0107 do schowka.",
          Settings: "Opcja *Ustawienia* pozwala kontrolowa\u0107 MathJax, m.in. ustawia\u0107 wielko\u015B\u0107 wzor\u00F3w i zmienia\u0107 mechanizm ich wy\u015Bwietlania.",
          Language: "*J\u0119zyk* pozwala wybra\u0107 j\u0119zyk, w jakim wy\u015Bwietlane jest menu oraz komunikaty o b\u0142\u0119dach.",
          Zoom: "*Powi\u0119kszanie*: Je\u017Celi masz problem z odczytaniem wzoru, MathJax mo\u017Ce zwi\u0119kszy\u0107 wielko\u015B\u0107 liter, aby \u0142atwiej by\u0142o go odczyta\u0107.",
          Accessibilty: "*Dost\u0119pno\u015B\u0107*: MathJax dzia\u0142a automatycznie z czytnikami ekranowymi, aby wzory matematyczne by\u0142y dost\u0119pne dla niedowidz\u0105cych.",
          Fonts: "*Czcionki*: MathJax u\u017Cyje czcionek matematycznych zainstalowanych w Twoim systemie. Je\u017Celi ich nie masz, to u\u017Cyje czcionek Web. Nie jest to wymagane, ale lokalnie dost\u0119pne czcionki przyspiesz\u0105 dzia\u0142anie MathJax. Sugerujemy zainstalowanie czcionek [STIX](%1).",
          CloseDialog: "Zamknij okno pomocy"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/pl/HelpDialog.js");
