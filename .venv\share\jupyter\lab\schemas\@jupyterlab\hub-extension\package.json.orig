{"name": "@jupyterlab/hub-extension", "version": "4.4.3", "description": "JupyterLab integration for JupyterHub", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "schema/*.json", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc", "build:test": "tsc --build tsconfig.test.json", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "test": "jest", "test:cov": "jest --collect-coverage", "test:debug": "node --inspect-brk ../../node_modules/.bin/jest --runInBand", "test:debug:watch": "node --inspect-brk ../../node_modules/.bin/jest --runInBand --watch", "watch": "tsc -w --listEmittedFiles"}, "dependencies": {"@jupyterlab/application": "^4.4.3", "@jupyterlab/apputils": "^4.5.3", "@jupyterlab/coreutils": "^6.4.3", "@jupyterlab/services": "^7.4.3", "@jupyterlab/translation": "^4.4.3"}, "devDependencies": {"@types/jest": "^29.2.0", "jest": "^29.2.0", "rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}