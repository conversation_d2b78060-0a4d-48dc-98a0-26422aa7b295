../../Scripts/dask.exe,sha256=WotEHNg--BUgw1O_KswkoVK-bP288Y6-Q6VRrIQdMl4,106443
dask-2025.5.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
dask-2025.5.1.dist-info/METADATA,sha256=ZSXctoWBSeBEY6lgF3ob9Y2kLwNdYEkk7K62O1OuveE,3796
dask-2025.5.1.dist-info/RECORD,,
dask-2025.5.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask-2025.5.1.dist-info/WHEEL,sha256=zaaOINJESkSfm_4HQVc5ssNzHCPXhJm0kEUakpsEHaU,91
dask-2025.5.1.dist-info/entry_points.txt,sha256=DqIpjJmPgioJQpDSCDN2w_ZwaOvs2zlhHoZ2yahFCRI,124
dask-2025.5.1.dist-info/licenses/LICENSE.txt,sha256=Eht58q-4tqaPMAZ_mKA1qwg3gL00MgWgctdpJDg7Y88,1531
dask-2025.5.1.dist-info/licenses/dask/array/NUMPY_LICENSE.txt,sha256=VGrgTUHkF-M_FQ1sMIt_4tu2im7Ej0nHkDcsTTeZ4Cc,1543
dask-2025.5.1.dist-info/top_level.txt,sha256=iT6x3D1cKsV7ordRW_UwlPRBdavTonNRtWVPB-Kti7U,5
dask/__init__.py,sha256=24HKIANhFOj13ZAqtXQ9aJmUfC-YFdowPIXm6eTLIBM,546
dask/__main__.py,sha256=XUa3ye8KAp0GH0H7SKSUpNq27X7f9StwEWkG_VGVHjY,133
dask/__pycache__/__init__.cpython-310.pyc,,
dask/__pycache__/__main__.cpython-310.pyc,,
dask/__pycache__/_collections.cpython-310.pyc,,
dask/__pycache__/_compatibility.cpython-310.pyc,,
dask/__pycache__/_dispatch.cpython-310.pyc,,
dask/__pycache__/_expr.cpython-310.pyc,,
dask/__pycache__/_task_spec.cpython-310.pyc,,
dask/__pycache__/_version.cpython-310.pyc,,
dask/__pycache__/backends.cpython-310.pyc,,
dask/__pycache__/base.cpython-310.pyc,,
dask/__pycache__/blockwise.cpython-310.pyc,,
dask/__pycache__/cache.cpython-310.pyc,,
dask/__pycache__/callbacks.cpython-310.pyc,,
dask/__pycache__/cli.cpython-310.pyc,,
dask/__pycache__/config.cpython-310.pyc,,
dask/__pycache__/conftest.cpython-310.pyc,,
dask/__pycache__/context.cpython-310.pyc,,
dask/__pycache__/core.cpython-310.pyc,,
dask/__pycache__/datasets.cpython-310.pyc,,
dask/__pycache__/delayed.cpython-310.pyc,,
dask/__pycache__/distributed.cpython-310.pyc,,
dask/__pycache__/dot.cpython-310.pyc,,
dask/__pycache__/graph_manipulation.cpython-310.pyc,,
dask/__pycache__/hashing.cpython-310.pyc,,
dask/__pycache__/highlevelgraph.cpython-310.pyc,,
dask/__pycache__/layers.cpython-310.pyc,,
dask/__pycache__/local.cpython-310.pyc,,
dask/__pycache__/ml.cpython-310.pyc,,
dask/__pycache__/multiprocessing.cpython-310.pyc,,
dask/__pycache__/optimization.cpython-310.pyc,,
dask/__pycache__/order.cpython-310.pyc,,
dask/__pycache__/rewrite.cpython-310.pyc,,
dask/__pycache__/sizeof.cpython-310.pyc,,
dask/__pycache__/system.cpython-310.pyc,,
dask/__pycache__/task_spec.cpython-310.pyc,,
dask/__pycache__/threaded.cpython-310.pyc,,
dask/__pycache__/tokenize.cpython-310.pyc,,
dask/__pycache__/typing.cpython-310.pyc,,
dask/__pycache__/utils.cpython-310.pyc,,
dask/__pycache__/utils_test.cpython-310.pyc,,
dask/_collections.py,sha256=5mydYXcnbyKlVoIcK4Roh9tJj8bYW7P88zMX-rUtJYE,266
dask/_compatibility.py,sha256=qmYELVfss0z-lTaAzr-X9MK9PkIrftMlx0Gh4gagj74,4530
dask/_dispatch.py,sha256=b_jNKmAlScLnh7rpW15tNk09Y0QOsju2ebTAeTaTw14,123
dask/_expr.py,sha256=cOlEXYYfS1fDGQvssw3_hSs3VSyut7Impv0nHuZjoMA,43658
dask/_task_spec.py,sha256=OyJD2D1BvHp2XdjH6UBsXLh7KZGQ4dPpjN1KZ7j4Bm8,36391
dask/_version.py,sha256=vcLFEi5ZkV4BQ5A4c1Iw4umxt4gLzyqLifsrQkPCUg0,500
dask/array/NUMPY_LICENSE.txt,sha256=VGrgTUHkF-M_FQ1sMIt_4tu2im7Ej0nHkDcsTTeZ4Cc,1543
dask/array/__init__.py,sha256=aEjie6BTaDLc5G2WW1AhDz5sqkzCu5FkBmPk03IyrSM,22152
dask/array/__pycache__/__init__.cpython-310.pyc,,
dask/array/__pycache__/_reductions_generic.cpython-310.pyc,,
dask/array/__pycache__/_shuffle.cpython-310.pyc,,
dask/array/__pycache__/api.cpython-310.pyc,,
dask/array/__pycache__/backends.cpython-310.pyc,,
dask/array/__pycache__/blockwise.cpython-310.pyc,,
dask/array/__pycache__/chunk.cpython-310.pyc,,
dask/array/__pycache__/chunk_types.cpython-310.pyc,,
dask/array/__pycache__/core.cpython-310.pyc,,
dask/array/__pycache__/creation.cpython-310.pyc,,
dask/array/__pycache__/cupy_entry_point.cpython-310.pyc,,
dask/array/__pycache__/dispatch.cpython-310.pyc,,
dask/array/__pycache__/einsumfuncs.cpython-310.pyc,,
dask/array/__pycache__/fft.cpython-310.pyc,,
dask/array/__pycache__/gufunc.cpython-310.pyc,,
dask/array/__pycache__/image.cpython-310.pyc,,
dask/array/__pycache__/linalg.cpython-310.pyc,,
dask/array/__pycache__/ma.cpython-310.pyc,,
dask/array/__pycache__/numpy_compat.cpython-310.pyc,,
dask/array/__pycache__/optimization.cpython-310.pyc,,
dask/array/__pycache__/overlap.cpython-310.pyc,,
dask/array/__pycache__/percentile.cpython-310.pyc,,
dask/array/__pycache__/random.cpython-310.pyc,,
dask/array/__pycache__/rechunk.cpython-310.pyc,,
dask/array/__pycache__/reductions.cpython-310.pyc,,
dask/array/__pycache__/reshape.cpython-310.pyc,,
dask/array/__pycache__/routines.cpython-310.pyc,,
dask/array/__pycache__/slicing.cpython-310.pyc,,
dask/array/__pycache__/stats.cpython-310.pyc,,
dask/array/__pycache__/svg.cpython-310.pyc,,
dask/array/__pycache__/tiledb_io.cpython-310.pyc,,
dask/array/__pycache__/ufunc.cpython-310.pyc,,
dask/array/__pycache__/utils.cpython-310.pyc,,
dask/array/__pycache__/wrap.cpython-310.pyc,,
dask/array/_array_expr/__init__.py,sha256=42JZ1LyhwahkGADTmOh7ydJ6rweqSY6TEW_rdv6DcN8,794
dask/array/_array_expr/__pycache__/__init__.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_backends.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_blockwise.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_collection.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_creation.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_expr.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_gufunc.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_io.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_map_blocks.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_overlap.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_rechunk.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_reductions.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_shuffle.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_slicing.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_ufunc.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/_utils.cpython-310.pyc,,
dask/array/_array_expr/__pycache__/random.cpython-310.pyc,,
dask/array/_array_expr/_backends.py,sha256=mGVDQzZvsytAmVJp1XPuy43feLbnHQnASA2nJ6ePzxQ,3540
dask/array/_array_expr/_blockwise.py,sha256=oYhew5CgmLCkgeNOFyKnWGIZtt9CsE5kCAljNdDxa04,11627
dask/array/_array_expr/_collection.py,sha256=nOXm5EIZZ2ErecWAewbNPl8v7mueNoXHwhjITimnUiY,51523
dask/array/_array_expr/_creation.py,sha256=jSa5EbHD67cW-Op8v3GXew2Yfjw9Vk3lwSwv5QCVcLA,18389
dask/array/_array_expr/_expr.py,sha256=rnsbmeZJcyCBRRsfh_6HYS68z7OaWQDW72YFziQrMDg,8310
dask/array/_array_expr/_gufunc.py,sha256=IDIdnLnTIih-SwskKNl0uUtu3QncwAXesymRqRtceAs,34435
dask/array/_array_expr/_io.py,sha256=Fw1yC60ihJS8ESoXdxrc8ocgD00JvC-yu-DrkYXpjks,4067
dask/array/_array_expr/_map_blocks.py,sha256=bCGdzML0QcR-wxB6K0geVe8--mr6JaXsab6jYmUm5Qo,18849
dask/array/_array_expr/_overlap.py,sha256=lHrmSFSySy30yW45pJHG_Vq8DzrN59k3ioA2ILT4ux0,31226
dask/array/_array_expr/_rechunk.py,sha256=AgIxoMDQi1ZgHtcaPt8y_uwYefqVmxeOmzGgz5kwtMU,6156
dask/array/_array_expr/_reductions.py,sha256=2zeof0XMQS36ce5d5UnJdHMO6zp-262ptmdkLpg2hpw,13664
dask/array/_array_expr/_shuffle.py,sha256=bZOdB-Y86BTy2nQSBXvxLdzPf4OYkSVoEcBaS_bH9TM,7576
dask/array/_array_expr/_slicing.py,sha256=y8it0ffwr3ItgJUOZRAHcMUthwUjjaTtl0ASBEeUH_k,15813
dask/array/_array_expr/_ufunc.py,sha256=QimIkKCPh3gyza2SvfAyuXLyx6p_enf1Q1hQfQLRvOk,10044
dask/array/_array_expr/_utils.py,sha256=9eYCGKFWNKwaPdtawx1njKZd53gl1oxnDGe3Gol3q3U,2482
dask/array/_array_expr/random.py,sha256=AhxLdCVlO48XjK2JrEPFnlmVqU12-cBwmDvQbcsEFec,39950
dask/array/_array_expr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/array/_array_expr/tests/__pycache__/__init__.cpython-310.pyc,,
dask/array/_array_expr/tests/__pycache__/test_collection.cpython-310.pyc,,
dask/array/_array_expr/tests/test_collection.py,sha256=7iwX44tTIzqWjhf5yIZqsx1f0lWDjiTsWXh1rf1j_nY,3394
dask/array/_reductions_generic.py,sha256=oBqW1rFWYxc85uN7a27KH4ojITVqNSZom1VpoYo5hi8,13153
dask/array/_shuffle.py,sha256=H7zCW8w44l-JhFORzKtjR8sutzjkLmUrHHViEYWwSMk,12064
dask/array/api.py,sha256=dwPMjz0bncUcu6A6JWnUpV0lsyWUMYmAhnwmlYAoTxI,120
dask/array/backends.py,sha256=5hBvhrnTQaxsjr1CN2FcHNwIEQKnZJUgCSBnXJWxiR0,13594
dask/array/blockwise.py,sha256=iInduqvWKuMhM2yIzhulaKuzRpWMU9nDbWBj-ygj1Rk,10489
dask/array/chunk.py,sha256=QygROb_k3FbaY4ZGlWmTa4XJ3rLs754AhBP-wSy8Jw0,12303
dask/array/chunk_types.py,sha256=VY9BBcjPcuai9JkawtKGNO-4wL0Ze5kZGwkW8sUMMuw,5172
dask/array/core.py,sha256=iu7fihYPee3Rs_XlGnRZ8gxmY3MK1ErG4CLW9CZ64Io,200182
dask/array/creation.py,sha256=5AWzjCXNX8iiee_7tgUkB46Uh8ZJMKGFBisLpyj0U2U,41423
dask/array/cupy_entry_point.py,sha256=1xMgToPbbgCMiay1x5kTvCbnV-LF5C31XDtgI9NBZX4,2114
dask/array/dispatch.py,sha256=PbidHbP_-hNC5aT0LOJrge2_I-j-6Rg2iTOgo2-ifio,557
dask/array/einsumfuncs.py,sha256=TbmvNJWciQjgYwY5BqKAKUn__Zkm7YnEdEyXLUbtKds,10699
dask/array/fft.py,sha256=p2wl8PQg64W3cYYtD0GNAqNgC3iP6knZ81B9eRG_O0U,8175
dask/array/gufunc.py,sha256=IFKduKgw6mpALi5KsdREy_7d6GM9NjJRFGJVgrVq4qk,33726
dask/array/image.py,sha256=y6shaJH7lcSaS0A5DoYdDJZDBy5XVQG6RG6ibqWwOQY,1996
dask/array/lib/__init__.py,sha256=xpnjNzwKzSijzNSqhafnevJRTmPTBHjeQLJwQQIXnJc,77
dask/array/lib/__pycache__/__init__.cpython-310.pyc,,
dask/array/lib/__pycache__/stride_tricks.cpython-310.pyc,,
dask/array/lib/stride_tricks.py,sha256=vMxI24UwhZfPZVlnx2uV_uo9UHL6ZFvRgAg8uc0sCto,101
dask/array/linalg.py,sha256=c7B4Vs7nmyzmsivjNsR5VmggeEMtNGycpjhyqzGMUPk,53218
dask/array/ma.py,sha256=cDI4TDWn2rB_-r-RoaOMQLjc86qXTAz-VN4gcFyeXJs,6367
dask/array/numpy_compat.py,sha256=z7Wg8ALxs8ZTNERXZEhMo9aM_Aq24u4_25Z147cvg3k,5886
dask/array/optimization.py,sha256=GeF269gcwuiVYjNz_cg-2xtX7vGhASvNpM6mkrUqh-U,10795
dask/array/overlap.py,sha256=_LGjDjMmZx95yDbnZe1wz69Q3U0IPW25TxtNlPmMR7E,32300
dask/array/percentile.py,sha256=TBIkxhWGurdrkzL56knlgZuZjm3-HrHWGhZCl8KytFI,11029
dask/array/random.py,sha256=mhuwUbtzxNaa1fgTjfGs1rNLet0K9VsZLuAQULzFYsE,40998
dask/array/rechunk.py,sha256=0RPO7pfGmLrUIIp--YlCI3Whqk08YEQkRr2hs7ZL66g,29557
dask/array/reductions.py,sha256=UdfGR5L0gfysblqt-8SiAI9h67oRDhHu6jhi0q-4gn0,53554
dask/array/reshape.py,sha256=ALb9qBBiGI_rTC0S8pVt7jlV4DXHlhXLtIh5MfPL8Jk,21869
dask/array/routines.py,sha256=jkCDuE8-3kVzwCwhIJB_ahPX-xZESxmOPmU_qwYYz2Y,82244
dask/array/slicing.py,sha256=BvPudmJWwWHziRApB8VsStUwRwYvigw_NKOquFpYgCU,70793
dask/array/stats.py,sha256=V0LiCiUzbCfgiI72BaDH9j-_WGMSoM7SacAWCsrinmU,16185
dask/array/svg.py,sha256=huq-mDm356PIyEqOpXi_FJ0sfgDddUfiHzPfiUY4MhU,8168
dask/array/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/array/tests/__pycache__/__init__.cpython-310.pyc,,
dask/array/tests/__pycache__/test_api.cpython-310.pyc,,
dask/array/tests/__pycache__/test_array_core.cpython-310.pyc,,
dask/array/tests/__pycache__/test_array_expr_incompatible.cpython-310.pyc,,
dask/array/tests/__pycache__/test_array_function.cpython-310.pyc,,
dask/array/tests/__pycache__/test_array_utils.cpython-310.pyc,,
dask/array/tests/__pycache__/test_atop.cpython-310.pyc,,
dask/array/tests/__pycache__/test_chunk.cpython-310.pyc,,
dask/array/tests/__pycache__/test_creation.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_core.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_creation.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_gufunc.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_linalg.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_overlap.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_percentile.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_random.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_reductions.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_routines.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_slicing.cpython-310.pyc,,
dask/array/tests/__pycache__/test_cupy_sparse.cpython-310.pyc,,
dask/array/tests/__pycache__/test_dispatch.cpython-310.pyc,,
dask/array/tests/__pycache__/test_fft.cpython-310.pyc,,
dask/array/tests/__pycache__/test_gufunc.cpython-310.pyc,,
dask/array/tests/__pycache__/test_image.cpython-310.pyc,,
dask/array/tests/__pycache__/test_linalg.cpython-310.pyc,,
dask/array/tests/__pycache__/test_map_blocks.cpython-310.pyc,,
dask/array/tests/__pycache__/test_masked.cpython-310.pyc,,
dask/array/tests/__pycache__/test_numpy_compat.cpython-310.pyc,,
dask/array/tests/__pycache__/test_optimization.cpython-310.pyc,,
dask/array/tests/__pycache__/test_overlap.cpython-310.pyc,,
dask/array/tests/__pycache__/test_percentiles.cpython-310.pyc,,
dask/array/tests/__pycache__/test_random.cpython-310.pyc,,
dask/array/tests/__pycache__/test_rechunk.cpython-310.pyc,,
dask/array/tests/__pycache__/test_reductions.cpython-310.pyc,,
dask/array/tests/__pycache__/test_reshape.cpython-310.pyc,,
dask/array/tests/__pycache__/test_routines.cpython-310.pyc,,
dask/array/tests/__pycache__/test_shuffle.cpython-310.pyc,,
dask/array/tests/__pycache__/test_slicing.cpython-310.pyc,,
dask/array/tests/__pycache__/test_sparse.cpython-310.pyc,,
dask/array/tests/__pycache__/test_stats.cpython-310.pyc,,
dask/array/tests/__pycache__/test_svg.cpython-310.pyc,,
dask/array/tests/__pycache__/test_testing.cpython-310.pyc,,
dask/array/tests/__pycache__/test_ufunc.cpython-310.pyc,,
dask/array/tests/__pycache__/test_wrap.cpython-310.pyc,,
dask/array/tests/__pycache__/test_xarray.cpython-310.pyc,,
dask/array/tests/test_api.py,sha256=gWKLik9VkmxAqEuxC8VXXkorZv_18lN4D3B5VLaOnXA,874
dask/array/tests/test_array_core.py,sha256=HQnK6lrfSRNrjPuj0B-n8t1Ut3iYyVvLlQERuwc5VQk,179771
dask/array/tests/test_array_expr_incompatible.py,sha256=6OOthS-waqOtZq6vDtuzGf6iVP3Ke-JZq03_j6TQ5U8,1157
dask/array/tests/test_array_function.py,sha256=betmXcD5nVOCYw85D53Ck1ZP3e01PIZXpcB9Y5poXTA,7867
dask/array/tests/test_array_utils.py,sha256=dtgziTIv2ZC7Lxa6tLAOY_BUNtHnSSZp0ACI1ZPc9So,3772
dask/array/tests/test_atop.py,sha256=H3xzd-s4_r1l38njVmv2CaTx7u_YW6BFLyVa9CsF3UQ,25786
dask/array/tests/test_chunk.py,sha256=T4tLNlFAXTJU9IhwBBn0BlsXw0kAz6Ld-Wn9c6IarTk,3304
dask/array/tests/test_creation.py,sha256=nGZigoluiCodLFgrYC9PKbxYqTIZhmBQ6jyVTR7BSgk,35103
dask/array/tests/test_cupy_core.py,sha256=_mlFsjOP7qY4xVJTBg9I5R95DVPN4o5BqpfOpguKsxs,20052
dask/array/tests/test_cupy_creation.py,sha256=8SMTq3alTUahoYY-UXmXwKP-ZV-iBT-_QgoaqqjmMYw,6496
dask/array/tests/test_cupy_gufunc.py,sha256=On3mH6loPfe4aN2tsdEOI07ig6iddPKZfEITiSSrRSc,594
dask/array/tests/test_cupy_linalg.py,sha256=yn1YMdHdUspOj7rKMyB34aioVgrHTH3qNJjY6h8Jk0c,13385
dask/array/tests/test_cupy_overlap.py,sha256=59kcAP0O8ANbk1MO8R3csbZ8YB2OE4RRYoarLRkI1LA,4270
dask/array/tests/test_cupy_percentile.py,sha256=6xXUJ4ws8WDydWu5NOr40zkNYaWB2i8PDyJa4pPT4mw,2730
dask/array/tests/test_cupy_random.py,sha256=wt6u27AuGKCMcxBKoXM2LbbBAxQ0u9i1M95zNBijUok,9219
dask/array/tests/test_cupy_reductions.py,sha256=Phzsw7-ycE9QM7LeihcGBF4T0RrkrhChLGVKVEgQEG4,2224
dask/array/tests/test_cupy_routines.py,sha256=4GCVOi_n4ueVJtRU7siWMZjZgpNT6OFQO1ILn-JKH3A,7397
dask/array/tests/test_cupy_slicing.py,sha256=5VpkcvNQoh4LuaRu8h_Yn6E-aJVG_Gms-MXYKjir1HU,5317
dask/array/tests/test_cupy_sparse.py,sha256=tpB9oJ8iQS19v3zkRk0koQ3bWeWfFOP0E2bseIwncnY,2982
dask/array/tests/test_dispatch.py,sha256=h9i6nQwLEy68s56q1PVaPqbaRz24hTebZ6-ZvNHBLGo,8980
dask/array/tests/test_fft.py,sha256=gJ0iuwqs_HpaKO4AFU7tDl4jbeA4vYqFxwf2L67aFiA,9743
dask/array/tests/test_gufunc.py,sha256=ExKigKxgsXRsil6pseawcCF1jkiK8vCspIAVVhZVXXY,21939
dask/array/tests/test_image.py,sha256=qYg8LCsuLyHk-YVnjpYnFvIXVZVauAFal-n66uGaVfM,1482
dask/array/tests/test_linalg.py,sha256=kXUoTkPMeCtIXKhM2x26pE2lTFldO5cnuMnaVVqBaoU,37381
dask/array/tests/test_map_blocks.py,sha256=wzSe_5lbJwQP0z6p7rcjl5en4qKQdPMphBihgsMI39U,386
dask/array/tests/test_masked.py,sha256=W17cHAZJ8aXpIo5Vpm2CbMZbsaIfDNTRsAYWXhkddQ0,15582
dask/array/tests/test_numpy_compat.py,sha256=Kn-OcSZWDvbH5rilRLBOaEkg37xVJftwVBwuCygpIoQ,1087
dask/array/tests/test_optimization.py,sha256=vh2PI1D2QOY-jvxzZgBNlLG2tman2mNgu_-gi8dlIWg,7647
dask/array/tests/test_overlap.py,sha256=wFMDx4fNgMRhBxAdhHlvDlgdACxRaGuNfWbEvKK_MCk,31825
dask/array/tests/test_percentiles.py,sha256=O7Bwjcx_6WuOczMQ1RXWCDb5oOHfJCCRnU_rPPbXZyA,3443
dask/array/tests/test_random.py,sha256=Z-xDP1qaqq3ZNR-6t3rd9QQSj9bksiibgMaCSuTqDtc,15347
dask/array/tests/test_rechunk.py,sha256=reSgFeutyUwa-HWCHYXUEcVC1xy-z6ixJaGD53yiB_Q,39103
dask/array/tests/test_reductions.py,sha256=368ajqMuOeLrv8jIs23VIdGe9Ufoy2JeIgFrvCrL2tU,36699
dask/array/tests/test_reshape.py,sha256=PzYE828P2Bs7bAnOx_CrZZ7eOJMpj9qrW-N1tltUfTE,14789
dask/array/tests/test_routines.py,sha256=8boQcsTcv9DEVKybO116SfWM-mFH8EggcORr7zHmHL4,86759
dask/array/tests/test_shuffle.py,sha256=rBYcvPBY0X3mhDe8G-RpkchY1Nr1GAmp_kGV6WfMEkI,5067
dask/array/tests/test_slicing.py,sha256=SZ3z9yBHTO412rZTlKxvecESA8BcXrznieEq1iB-A-E,34507
dask/array/tests/test_sparse.py,sha256=gb3yjuSN_tWulI88s7MecBt8JKN5m0bkuRMu4YNtypY,5853
dask/array/tests/test_stats.py,sha256=T-DZQZWXijRr45f3S9EbfQZKr86gLXPNeIqeJTGad0k,5434
dask/array/tests/test_svg.py,sha256=gx1KhQj0w40aTU7VrgltLB21tHmH3uosKIPR64QDYxI,2740
dask/array/tests/test_testing.py,sha256=vY-XDEYxoZ5yD3BWUZfuEtmEJYtvl_dMp5bkZ7-83O4,525
dask/array/tests/test_ufunc.py,sha256=RinArYiB52dtRD6lH286xZ2ZnjMOOoz0tV3TXg6Ct_k,17390
dask/array/tests/test_wrap.py,sha256=FsjBQXAcydxWWmzAp6aAkxa3FzehpYRUhBicBrbYNhc,2706
dask/array/tests/test_xarray.py,sha256=PQJcLqCCKBngwfMFtiTMiIsHbwNo6hob99UEZXoJrjo,5073
dask/array/tiledb_io.py,sha256=qKYY84AQIwRTjA2BUEUH8aNB7FgQkAnoayayHAtM3Xs,4737
dask/array/ufunc.py,sha256=ID5UPbUpoQzHZzirtkTLlLA18OgrjbRarkank5U6J3o,10055
dask/array/utils.py,sha256=M-EwRWcYDogVe7D8fRj5t-JTfJRO3-w2VfxWL9mFPgI,19714
dask/array/wrap.py,sha256=gfCaugXBzF5jJQM9biwWxcr9-y2VyzwsFbBZa5Duh_c,6906
dask/backends.py,sha256=0F5yUViPebJFW5Lwy39qwZrx1QHJPF2JZotODRqtdyo,5298
dask/bag/__init__.py,sha256=XdcK7NWSiCQeP7Igc7Y3SQJyCRnGiDkLV1LcXylwdHM,1180
dask/bag/__pycache__/__init__.cpython-310.pyc,,
dask/bag/__pycache__/avro.cpython-310.pyc,,
dask/bag/__pycache__/chunk.cpython-310.pyc,,
dask/bag/__pycache__/core.cpython-310.pyc,,
dask/bag/__pycache__/random.cpython-310.pyc,,
dask/bag/__pycache__/text.cpython-310.pyc,,
dask/bag/__pycache__/utils.cpython-310.pyc,,
dask/bag/avro.py,sha256=thwr-V5CiFoetZgyZxTdnQy0cRw8fpNYCrlm80ejb7M,9111
dask/bag/chunk.py,sha256=dplBOA9q6ngfNmDw2DdWrJlUZxq5xAjGgkz4YfWl82M,763
dask/bag/core.py,sha256=B5rkYGzjVqU2ZsBlcrZFyeo1Z9vCnjOt6-2e32uG-Uw,86236
dask/bag/random.py,sha256=A-E81C_U1UzJwYmu2vdgKpc1FBPfjXO2f-F4-eCBU-U,5464
dask/bag/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/bag/tests/__pycache__/__init__.cpython-310.pyc,,
dask/bag/tests/__pycache__/test_avro.cpython-310.pyc,,
dask/bag/tests/__pycache__/test_bag.cpython-310.pyc,,
dask/bag/tests/__pycache__/test_random.cpython-310.pyc,,
dask/bag/tests/__pycache__/test_text.cpython-310.pyc,,
dask/bag/tests/test_avro.py,sha256=TwjuNkqyzBKpAGXNrvaVPAzq3_HNiLAjMVI7vtEJj_E,3808
dask/bag/tests/test_bag.py,sha256=Vl1nGRl2MAQZ5sIJ4RHz9MIORgzfs4yDcH-4N7w2q1I,52797
dask/bag/tests/test_random.py,sha256=fWSI32hEzbuJbptD2mYsOw_ppb4vdCqD9UlybOYyU2c,6528
dask/bag/tests/test_text.py,sha256=OwAxX0bwFGjdsvJAeGCicRj-1ZnPiR9fzYTHMA2Jfa0,5049
dask/bag/text.py,sha256=bFeXyiZUM_sxJ3kV9LEmPxOQDNyqaNjTdA5JEf0kGls,6765
dask/bag/utils.py,sha256=Nc9v3RVrrgSuBDqBTqqabn4g89036SfHQh8EU1bfvCk,241
dask/base.py,sha256=dmhBNFurggWft2h4d-ZNgknsnL_GtHQL9W29vIPG7Qw,45859
dask/blockwise.py,sha256=KG6NMRYmpxA3YCzXuQ6HQpVKCb4l7a9EFbk9WJZ0OE4,53785
dask/bytes/__init__.py,sha256=Gcf_lkJnhBm24a-NHNOYfymIUqbeQqpu0rh4Xkt3aHU,75
dask/bytes/__pycache__/__init__.cpython-310.pyc,,
dask/bytes/__pycache__/core.cpython-310.pyc,,
dask/bytes/__pycache__/utils.cpython-310.pyc,,
dask/bytes/core.py,sha256=hJdDBxnCKUz10cBBusFL7Y_Kn4l5EqihLjcU9hLcTmE,6933
dask/bytes/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/bytes/tests/__pycache__/__init__.cpython-310.pyc,,
dask/bytes/tests/__pycache__/test_bytes_utils.cpython-310.pyc,,
dask/bytes/tests/__pycache__/test_compression.cpython-310.pyc,,
dask/bytes/tests/__pycache__/test_http.cpython-310.pyc,,
dask/bytes/tests/__pycache__/test_local.cpython-310.pyc,,
dask/bytes/tests/__pycache__/test_s3.cpython-310.pyc,,
dask/bytes/tests/test_bytes_utils.py,sha256=LdQNfRkL45HTW2-W5KCnCeYmZEIk21sHMRjA1MmhJqU,5158
dask/bytes/tests/test_compression.py,sha256=MBIHyiQLpbtKl0h7-udXpMYvIp_5VqPRj2t37ZbjRbY,530
dask/bytes/tests/test_http.py,sha256=zMvEYxIRLpxH2_9kuTHFmuZcqmMekPOgT4nYjG5uVvI,6519
dask/bytes/tests/test_local.py,sha256=95y-So3mFKaJS3189HGBoV8s9EAYMHcudIdU3ylqDCI,11464
dask/bytes/tests/test_s3.py,sha256=eqqdAVPGOMR_F43qKFfTtA6kdNJP_YzTg2ZCkSRqqgc,19254
dask/bytes/utils.py,sha256=JZT0fNf313HTTmOPuKd6n0J6cTcAeoL44AOgmVTTzxQ,500
dask/cache.py,sha256=iF8yHYDZim-IlU-XkJGYhc_o0SBY_iLsFOVPV5LART4,1985
dask/callbacks.py,sha256=EoLqePCGyytX2ZO7yrvC-EPQR7o3v6zj5TYUfOeoLhM,4067
dask/cli.py,sha256=j8mnuXgWbnTHl60mHBxzqyt3o_c8r1R-Z3krnPPeMyY,5773
dask/config.py,sha256=-JHFLWKFEeVErzGEB2MyfmqhkWuR-82WAuj3J6HfuR0,24796
dask/conftest.py,sha256=4ZkyYndw6CYi24GmlZS3N9fgkPDFJyvlupGjYI_j0Pw,959
dask/context.py,sha256=iOeupMqLkIUzIRH5eTFqhYFrD9ZhHQJjB4njKvLf1z8,1757
dask/core.py,sha256=DC84a_7_KZPzBlU--wofP4IEvxBo6gvUX4jVRBgWpZk,14944
dask/dask-schema.yaml,sha256=R5pWKnETLys8V7HX_I0fX8fW1FXS8qdav0z4nbku9Go,8763
dask/dask.yaml,sha256=yU3W2DbPag668i8XBDfE-egYfUCYyYoYEtSkLp-kX3g,2144
dask/dataframe/__init__.py,sha256=8fqDqYtSymuZBpCSBZy7Cx9I1SZU4p1F7iOKfk-D_5A,2288
dask/dataframe/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/__pycache__/_compat.cpython-310.pyc,,
dask/dataframe/__pycache__/_dtypes.cpython-310.pyc,,
dask/dataframe/__pycache__/_pyarrow.cpython-310.pyc,,
dask/dataframe/__pycache__/_pyarrow_compat.cpython-310.pyc,,
dask/dataframe/__pycache__/accessor.cpython-310.pyc,,
dask/dataframe/__pycache__/api.cpython-310.pyc,,
dask/dataframe/__pycache__/backends.cpython-310.pyc,,
dask/dataframe/__pycache__/categorical.cpython-310.pyc,,
dask/dataframe/__pycache__/core.cpython-310.pyc,,
dask/dataframe/__pycache__/dispatch.cpython-310.pyc,,
dask/dataframe/__pycache__/extensions.cpython-310.pyc,,
dask/dataframe/__pycache__/groupby.cpython-310.pyc,,
dask/dataframe/__pycache__/hyperloglog.cpython-310.pyc,,
dask/dataframe/__pycache__/indexing.cpython-310.pyc,,
dask/dataframe/__pycache__/methods.cpython-310.pyc,,
dask/dataframe/__pycache__/multi.cpython-310.pyc,,
dask/dataframe/__pycache__/partitionquantiles.cpython-310.pyc,,
dask/dataframe/__pycache__/rolling.cpython-310.pyc,,
dask/dataframe/__pycache__/shuffle.cpython-310.pyc,,
dask/dataframe/__pycache__/utils.cpython-310.pyc,,
dask/dataframe/_compat.py,sha256=-ERveUyGFO7x2dvWuDuLrXECcQ3CQpiANsmRuwtohC0,4772
dask/dataframe/_dtypes.py,sha256=1Erl8C-9euLQQQAmKZQVPLCiaeTCmeXimM-TWKYT7ek,1983
dask/dataframe/_pyarrow.py,sha256=E9_Orskn6V-sp7bVhcR5yDt7pZrOzbJu0L9jyQeklMw,3259
dask/dataframe/_pyarrow_compat.py,sha256=IXsu44efXSvTF1M17d3RfnMHnxFcKO93Z4DEymL4UC8,1130
dask/dataframe/accessor.py,sha256=a1UJXc8rxEcf0WZUoph41DGe-Y2Fm-Pyc5StpBFWJuE,4035
dask/dataframe/api.py,sha256=tVpJU8j6T_FLxU5vIpMJ7QWOi3C6ZfnackedWTOUH8c,645
dask/dataframe/backends.py,sha256=Iq9dRE_CtJsdoh5gsWz78u6hrqA7rWwiU1YYdn0mieQ,26282
dask/dataframe/categorical.py,sha256=KzmsF26aqxVMA8Ms7CHADSVuwQHpaTqZ3ziwF6qGcgU,1977
dask/dataframe/core.py,sha256=-EqY6LnKEDA82oQXG6J3x5QQ3k5ILT3tTGrYx96mXzI,15678
dask/dataframe/dask_expr/__init__.py,sha256=z2FPbYIXSPfO9qeAzEXktFg5X6YJULM-OuUB6xZizHQ,480
dask/dataframe/dask_expr/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_accessor.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_backends.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_categorical.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_collection.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_concat.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_cumulative.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_datetime.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_describe.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_dummies.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_expr.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_groupby.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_indexing.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_interchange.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_merge.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_merge_asof.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_quantile.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_quantiles.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_reductions.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_repartition.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_rolling.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_shuffle.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_str_accessor.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_util.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/_version.cpython-310.pyc,,
dask/dataframe/dask_expr/__pycache__/datasets.cpython-310.pyc,,
dask/dataframe/dask_expr/_accessor.py,sha256=zxM0s02jS6gEbZMRiB8gswqrYZzkEDgbI0rZM3mUlhI,3908
dask/dataframe/dask_expr/_backends.py,sha256=0O2Y_ttMgPlRQ_0dn3OldT2Lrfz56kZdRkbgP5dXQV0,2055
dask/dataframe/dask_expr/_categorical.py,sha256=2dfZ847louwa9-ac4ggfuJwm2IhhJv7zOo75sUeeFVQ,6426
dask/dataframe/dask_expr/_collection.py,sha256=-rYM5tPIzev-GUL8vbgpBDrHJzmdUr7WNEhNIwB2RF0,236947
dask/dataframe/dask_expr/_concat.py,sha256=GlEekrSjhZ1ZpfDxQMHL-B6K2h6Ps5gKN7MS8EQu_Qw,12620
dask/dataframe/dask_expr/_cumulative.py,sha256=FlIph4fg4iJFIj9CKEy7W1OAjR_203vk4P32YAAQUQE,4316
dask/dataframe/dask_expr/_datetime.py,sha256=8hS-MtA8WBmDSiB3bEGPeM9v0JyqVAyaS3uxx0rrvgs,1474
dask/dataframe/dask_expr/_describe.py,sha256=YMCZyqXA4lHKvlLfpGKskbc9E8Ckyvycg_PB8w-QgsA,3579
dask/dataframe/dask_expr/_dummies.py,sha256=0FHnbZcCLO2kKVaTP9CQBiHyE3V9YJVOrPpJ5UcvJeo,5725
dask/dataframe/dask_expr/_expr.py,sha256=t_GbNFS8stZbgiB7SXWKXq2l4GI0wLJNq30QgUb4710,130413
dask/dataframe/dask_expr/_groupby.py,sha256=qHA9vzbHToD0ci6KVHsdyj7XPElJV8JN3Iuwka3vL04,69116
dask/dataframe/dask_expr/_indexing.py,sha256=9Tg-jIAoFtce_c9h41J37NGkOgq99zjTkDb0Lrx15b0,14962
dask/dataframe/dask_expr/_interchange.py,sha256=4Wssh5ChIi7ekzYdPM7em9s6J4vuYPU_Mx0O0UzrJ20,1691
dask/dataframe/dask_expr/_merge.py,sha256=cgOals4qqf-_I4JOu69hj4fKdtcGPn_EuotVP6wTPGg,32366
dask/dataframe/dask_expr/_merge_asof.py,sha256=4vStxLJiX9BXGHcaZJqn7QpioFo7Oi_9pm33Vfg7B3s,12575
dask/dataframe/dask_expr/_quantile.py,sha256=i7PVUBJEi8VcUQTQGCCt5_b5rhfwG6wJ2qxe5996UJE,3954
dask/dataframe/dask_expr/_quantiles.py,sha256=RiYhZ18F9BqcVoD0rJ8BvzHcP_edXbjln2DxE9DLPCo,2546
dask/dataframe/dask_expr/_reductions.py,sha256=_H_GRjwUKyA9Jz5EqtaJE6aq5fFG71Ru_Kz_61ji38A,45509
dask/dataframe/dask_expr/_repartition.py,sha256=Ac1y4n5w25oKS9AaGDSeAK1AhKpLOeh12HlV7Dmt_4Y,18062
dask/dataframe/dask_expr/_rolling.py,sha256=zlSzCLzCfo6I8o5eJYuO5Dndt9s3lpJ7Urni6uXRfuA,9462
dask/dataframe/dask_expr/_shuffle.py,sha256=tPC6EGS4IZEluNsT2co-oAIkdlCKPWeXM8eBl3Iy_a8,45517
dask/dataframe/dask_expr/_str_accessor.py,sha256=bIw4whcWxQ0UqdWqe0D9nH39vIMcvMRF6GKlLIbtyaU,5132
dask/dataframe/dask_expr/_util.py,sha256=JPNoRR7knbIHpZc0NR0QWo2kcLCJjGfVTDnXz7OdkuA,6380
dask/dataframe/dask_expr/_version.py,sha256=zF0taXeB0VyYuU_r8LOL4DQgs7Vjqe9--p12m5kwt1c,24618
dask/dataframe/dask_expr/datasets.py,sha256=3uXO_ZShNM4uPIUDXMP6b1GzQkhxm5B5Ams8UE4tjew,6847
dask/dataframe/dask_expr/diagnostics/__init__.py,sha256=r88AnyLYGQh6xIIfhFLRjQ20v-2TvbrnPPXO_tIzPr0,202
dask/dataframe/dask_expr/diagnostics/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/dask_expr/diagnostics/__pycache__/_analyze.cpython-310.pyc,,
dask/dataframe/dask_expr/diagnostics/__pycache__/_analyze_plugin.cpython-310.pyc,,
dask/dataframe/dask_expr/diagnostics/__pycache__/_explain.cpython-310.pyc,,
dask/dataframe/dask_expr/diagnostics/_analyze.py,sha256=JBVNXGcOdHWtjXL5dYERcmv-AuF_PhZMDSWPZ8e5_oA,5860
dask/dataframe/dask_expr/diagnostics/_analyze_plugin.py,sha256=aHx00bRP65M4JBOVChByB3dULvOQsv-Ab7wQuC0mpyI,4046
dask/dataframe/dask_expr/diagnostics/_explain.py,sha256=39MogeOdPBrEeAFsWGN0nsg4Scw_2BmcxXpKcZtrUyg,3133
dask/dataframe/dask_expr/io/__init__.py,sha256=O1XndZYHkpRXBCg-Esmpi_ysn74lXtsGGurFwhnlmtE,131
dask/dataframe/dask_expr/io/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/dask_expr/io/__pycache__/_delayed.cpython-310.pyc,,
dask/dataframe/dask_expr/io/__pycache__/bag.cpython-310.pyc,,
dask/dataframe/dask_expr/io/__pycache__/io.cpython-310.pyc,,
dask/dataframe/dask_expr/io/__pycache__/parquet.cpython-310.pyc,,
dask/dataframe/dask_expr/io/__pycache__/records.cpython-310.pyc,,
dask/dataframe/dask_expr/io/_delayed.py,sha256=8L-hsaucFqJFaVdqdKUPU2mTzXucNW9VElXOcJROMto,5229
dask/dataframe/dask_expr/io/bag.py,sha256=UPaimKbB2VPHVRu-eLUltYNV9nMS2N2KdsZn9s7XvVA,1308
dask/dataframe/dask_expr/io/io.py,sha256=_Qjc6VvWstWSiQ4yeoCBZl5MsTmSaWFQNJkJNrBo50Q,22180
dask/dataframe/dask_expr/io/parquet.py,sha256=wPekoAmKuqNFCnIN58e2ZUwYR3Fw9EgXWaW4oc9ElYQ,71177
dask/dataframe/dask_expr/io/records.py,sha256=kzgi_wLI-JxLiyGr1h3U5HdSdD7RD3ONXZN_KlwtqCU,518
dask/dataframe/dask_expr/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/dataframe/dask_expr/io/tests/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/dask_expr/io/tests/__pycache__/test_delayed.cpython-310.pyc,,
dask/dataframe/dask_expr/io/tests/__pycache__/test_distributed.cpython-310.pyc,,
dask/dataframe/dask_expr/io/tests/__pycache__/test_from_pandas.cpython-310.pyc,,
dask/dataframe/dask_expr/io/tests/__pycache__/test_io.cpython-310.pyc,,
dask/dataframe/dask_expr/io/tests/__pycache__/test_parquet.cpython-310.pyc,,
dask/dataframe/dask_expr/io/tests/__pycache__/test_sql.cpython-310.pyc,,
dask/dataframe/dask_expr/io/tests/test_delayed.py,sha256=Ry_X--PK9SH5CiQJOAkfhUNhgRIFtu7jxcokOG_OdII,2799
dask/dataframe/dask_expr/io/tests/test_distributed.py,sha256=JeHa-MGlgFcpM5xJK5YXkacaiyTYMGtx9oL-aConKHc,2248
dask/dataframe/dask_expr/io/tests/test_from_pandas.py,sha256=29Oj2IL4p2BZ4h6pVFf8dGMB0BzPycvFe9qWSrevbXQ,4740
dask/dataframe/dask_expr/io/tests/test_io.py,sha256=nL7MsFeuUdqcxvkNcoTFo2H5ypUGzR80QkDarwQxcFs,16936
dask/dataframe/dask_expr/io/tests/test_parquet.py,sha256=d2yW0-9ZLcTzULlC16iatKQIgt1mqWYoQUuywtwlBYY,21551
dask/dataframe/dask_expr/io/tests/test_sql.py,sha256=hv9PSDJwrimkZf_uebRtTVg3tKSxyF8EioEOyxJw8CQ,1162
dask/dataframe/dask_expr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/dataframe/dask_expr/tests/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/_util.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_align_partitions.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_categorical.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_collection.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_concat.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_core.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_cumulative.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_datasets.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_datetime.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_describe.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_diagnostics.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_distributed.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_dummies.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_format.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_fusion.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_groupby.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_indexing.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_interchange.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_map_partitions_overlap.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_merge.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_merge_asof.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_partitioning_knowledge.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_predicate_pushdown.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_quantiles.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_reductions.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_repartition.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_reshape.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_rolling.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_shuffle.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_string_accessor.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/__pycache__/test_ufunc.cpython-310.pyc,,
dask/dataframe/dask_expr/tests/_util.py,sha256=bt6ddQI9MiB1sSjwsJHpoP8CdRwCeicHs96jDv39Ngw,1237
dask/dataframe/dask_expr/tests/test_align_partitions.py,sha256=6-ynbB5hMDfoZ6adx5mu7ZRZGLFNNXA0XQwaG2XNjRw,3534
dask/dataframe/dask_expr/tests/test_categorical.py,sha256=S59J-hchmNdfGhZXm0iJLrCED09D-YW3RR5G3qczrXQ,2173
dask/dataframe/dask_expr/tests/test_collection.py,sha256=Z7kDXpcDjToUC5dk8gLvhaJyR5J5hCbc0hgO_VqjqD8,86311
dask/dataframe/dask_expr/tests/test_concat.py,sha256=SbFNEhBbWUXN64fw24oFOezSc7r2DnB4NT6Atk9U48Y,12012
dask/dataframe/dask_expr/tests/test_core.py,sha256=bUtOy547s5ABD7lrPQd-jXzzakJiu_qlr1aapqqmCfc,1342
dask/dataframe/dask_expr/tests/test_cumulative.py,sha256=WIiCLkVKkBJntPmb_3KWUKc5ldQzPYse9E_pLLr8COY,706
dask/dataframe/dask_expr/tests/test_datasets.py,sha256=URC3gktSU5Oy-f2HxMrYtPTc6_X-nc7iqxqAdOYHOZE,4218
dask/dataframe/dask_expr/tests/test_datetime.py,sha256=1Qn-XBz0pix-q9Y1y2qB7eB8-EWfxXL4gcYxAwacuu4,2544
dask/dataframe/dask_expr/tests/test_describe.py,sha256=rUXGwHjBsObGgg9JSRRwRkiNECb8vVEILzFCitCfKLg,1865
dask/dataframe/dask_expr/tests/test_diagnostics.py,sha256=3SSY-qOjCdgnCZ1j01aHoi4mNk_madWptdzEKcFK7ak,1060
dask/dataframe/dask_expr/tests/test_distributed.py,sha256=NxQGwZj4uX9AstFoXz1ZLI3l7CADULK161exz3l8bcY,16624
dask/dataframe/dask_expr/tests/test_dummies.py,sha256=2LZix8wVN8O6UA8uBA8aINv9Oz9FSJOMdq2zmK1VsTg,789
dask/dataframe/dask_expr/tests/test_format.py,sha256=B9n2dh0pBkUAZhCTtgZ_snM0zcIOpFz7_O2HHQRSKc0,5340
dask/dataframe/dask_expr/tests/test_fusion.py,sha256=zcNxjz-j1lg8hyYnHgQ04L1Ftw4TP6vtikcGgYaW4fg,4043
dask/dataframe/dask_expr/tests/test_groupby.py,sha256=woe-4ABtj_5xdItACjEU91p7v55pjBI784wEd3B7JMg,35241
dask/dataframe/dask_expr/tests/test_indexing.py,sha256=OrVGIoIqEGlC_e5UBqy042GsXYpbmOfaERnxwpDvwyE,6194
dask/dataframe/dask_expr/tests/test_interchange.py,sha256=i-VKCizCK7XIQNwulVplz1g1Cc6_wiCeAKP2stRzVp8,663
dask/dataframe/dask_expr/tests/test_map_partitions_overlap.py,sha256=fGfajkJipnOEf5ZhTyVCAlq1RxfP2UpifPCaJ_vawzE,10598
dask/dataframe/dask_expr/tests/test_merge.py,sha256=R9pdjMMoryakm585LIScowWHYQ--iKnan8tvi6LvLW8,37545
dask/dataframe/dask_expr/tests/test_merge_asof.py,sha256=CaC6PMV0RL40n6ESQgYF5-DAdnGJoYcHbn2ysjjqsIA,2426
dask/dataframe/dask_expr/tests/test_partitioning_knowledge.py,sha256=zv5HS0TG_dIMcwov2EDjnk6rlfPv6dVQIBhV2Asq6-I,8015
dask/dataframe/dask_expr/tests/test_predicate_pushdown.py,sha256=BfRU6jaGTSKM8eHngqeCB8BXLbQ40gYXTMKJSnYW1cc,2019
dask/dataframe/dask_expr/tests/test_quantiles.py,sha256=TU7A548mQdu4ak4ef-k2LE7yG9P7xCP_5taaNNexWy8,783
dask/dataframe/dask_expr/tests/test_reductions.py,sha256=lPH_8F-7GAVJIQP2dfAY4vdWbIqCmBURpinJYaB7rng,17696
dask/dataframe/dask_expr/tests/test_repartition.py,sha256=FIeHhE7eFoNITG8PlRBnni_22sE2_aeyvVFehh6Nc4I,4666
dask/dataframe/dask_expr/tests/test_reshape.py,sha256=9qgStfjuawtMDH7VP7JzUA3mqEanAbmPAgfIAMT_k5Q,2826
dask/dataframe/dask_expr/tests/test_rolling.py,sha256=OQZsple3TR9TWJpdxQAEAjPUHwrz4cknX7yAWjcXkvY,6693
dask/dataframe/dask_expr/tests/test_shuffle.py,sha256=hHJC3i3Os9TVS7X4fnNzAsHIYMPXf-seyh_5yBrHb-c,29008
dask/dataframe/dask_expr/tests/test_string_accessor.py,sha256=4oiOiQsSbCbKVbT5dA-UtGJEqUG6UbRhrA4NCgm3oQs,4586
dask/dataframe/dask_expr/tests/test_ufunc.py,sha256=Huu-nQaMqF4A5J8hiLDiuUAw97dDvd-cp0rIqrWlmMU,1801
dask/dataframe/dispatch.py,sha256=OplZWmQ_BgpfitUYJtrjZWXqv3LTmmM02oAHzX0PAtY,4577
dask/dataframe/extensions.py,sha256=EBwe_Mal5b-QKn4u9LrwEYaiYrNpgGUeQhOe5aXgrBQ,553
dask/dataframe/groupby.py,sha256=6G39AbQmIIDJeGBg3C7HBfkYpD4bUNaJlgX9CpkoZjo,41663
dask/dataframe/hyperloglog.py,sha256=_79_nkwF3sxLBDZPWrHG870KLwtKe0iuweDDouyjImo,2464
dask/dataframe/indexing.py,sha256=fQ5HX8JJllFXwv6B6TOffss6npVr2_Yhph0C1Urkh6g,3741
dask/dataframe/io/__init__.py,sha256=KjMp2qVX-52MILFiJinGjpwbNeN-eZKgabxoDXVyPwM,471
dask/dataframe/io/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/io/__pycache__/csv.cpython-310.pyc,,
dask/dataframe/io/__pycache__/demo.cpython-310.pyc,,
dask/dataframe/io/__pycache__/hdf.cpython-310.pyc,,
dask/dataframe/io/__pycache__/io.cpython-310.pyc,,
dask/dataframe/io/__pycache__/json.cpython-310.pyc,,
dask/dataframe/io/__pycache__/sql.cpython-310.pyc,,
dask/dataframe/io/__pycache__/utils.cpython-310.pyc,,
dask/dataframe/io/csv.py,sha256=Qd45iyKidlQzDljAm0h8hDRvj2rKZiNxruh2MnAekHM,32620
dask/dataframe/io/demo.py,sha256=Xor4jioSG3387FAufq-c2uCyZ44DV-cUa6BU4jpRgUU,17515
dask/dataframe/io/hdf.py,sha256=SpkNjzQmI6yWFiNbzS5TRmKP2wiYKVT0mewg-JgOVM4,17510
dask/dataframe/io/io.py,sha256=fnK7IbzyjfbjGFzAOvlEQf3oyTrEN_iFqXCfkMyqKIs,13216
dask/dataframe/io/json.py,sha256=QpngiLHd_Yr8c3u3iYsRdtyLE5-NaNewX670z03ceTk,10752
dask/dataframe/io/orc/__init__.py,sha256=pnaL1N6L2b_OzESf5llwH8rLu9qLR7KyV1zEe3AEFyY,92
dask/dataframe/io/orc/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/io/orc/__pycache__/arrow.cpython-310.pyc,,
dask/dataframe/io/orc/__pycache__/core.cpython-310.pyc,,
dask/dataframe/io/orc/__pycache__/utils.cpython-310.pyc,,
dask/dataframe/io/orc/arrow.py,sha256=u6-KyBU4uipl8lu91EwpoCcaYOpvL9mwuhfkgLvt-pk,4487
dask/dataframe/io/orc/core.py,sha256=vx1AtR9NEFAKccS_ZxVvTdEfPadwL0hf3GJBqS18n5g,6560
dask/dataframe/io/orc/utils.py,sha256=agAUr-3OBt6O6weUQA9ZvS6cZZ1vh-WT6vNN501JRjI,510
dask/dataframe/io/parquet/__init__.py,sha256=Rb9-P3EvzyROA75MRpVEcBlaFJAIhltdAe4vKRbrkJ0,119
dask/dataframe/io/parquet/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/io/parquet/__pycache__/arrow.cpython-310.pyc,,
dask/dataframe/io/parquet/__pycache__/core.cpython-310.pyc,,
dask/dataframe/io/parquet/__pycache__/utils.cpython-310.pyc,,
dask/dataframe/io/parquet/arrow.py,sha256=AMwqTZobKJdAkyEMGxANLQ4uwIXh0ZTwGn3N1x2-Eo0,70182
dask/dataframe/io/parquet/core.py,sha256=l2yDtHROsJWUFeO374Sg6zKu5sjmm_LY7-74II1oG-s,25426
dask/dataframe/io/parquet/utils.py,sha256=Zjx87DIMRkoMIpCOXt1enaaB7fEtomtpwxxIpNQJdzs,26788
dask/dataframe/io/sql.py,sha256=-5RCTtGI8POlYBfgTVFo0T-iUpPBRyG2VyMjDmS48YU,21109
dask/dataframe/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/dataframe/io/tests/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/io/tests/__pycache__/test_csv.cpython-310.pyc,,
dask/dataframe/io/tests/__pycache__/test_demo.cpython-310.pyc,,
dask/dataframe/io/tests/__pycache__/test_hdf.cpython-310.pyc,,
dask/dataframe/io/tests/__pycache__/test_io.cpython-310.pyc,,
dask/dataframe/io/tests/__pycache__/test_json.cpython-310.pyc,,
dask/dataframe/io/tests/__pycache__/test_orc.cpython-310.pyc,,
dask/dataframe/io/tests/__pycache__/test_parquet.cpython-310.pyc,,
dask/dataframe/io/tests/__pycache__/test_sql.cpython-310.pyc,,
dask/dataframe/io/tests/test_csv.py,sha256=ViSQPjXv2XgUpHeq4O2-eCExWvZ0i2bThLttPlFy9dk,61162
dask/dataframe/io/tests/test_demo.py,sha256=E5A_wGy2cKy_zivh-2_IVAaCYgMlAYq210TlTGdrKog,10267
dask/dataframe/io/tests/test_hdf.py,sha256=iXIZ7T86HLm9LcIvvb6aiv31WOT9jSO7mThCrvYWmdY,25105
dask/dataframe/io/tests/test_io.py,sha256=LMsbYdzKjVfcqVTl949IyA5zba75BCHSI_AwRw07yno,30919
dask/dataframe/io/tests/test_json.py,sha256=Opk1QkOv8-azStdvj8TxXt7zJ2UtG8R-vlIYMpVIBmM,8546
dask/dataframe/io/tests/test_orc.py,sha256=0sa6LRZ9h9KCD2q-NFObc0HAkR1yTs-VO-xoEZxXbvg,4947
dask/dataframe/io/tests/test_parquet.py,sha256=evZl61ij9B_1L8s8gCMTtE3X2rwbFg1eJCSDStOqhog,133800
dask/dataframe/io/tests/test_sql.py,sha256=6IKMLFJ1SUMuLqMzRWHXchuTdmu-woQLhZviBvkOd4E,17828
dask/dataframe/io/utils.py,sha256=fq8_trYEgnNf0Pw3aS0jpASBgWnA_CQT7iO_D6zvgWI,7998
dask/dataframe/methods.py,sha256=4X7q4qIc5Gym9l7D4WVkECbaMHJ_xuaJ2HEQ0YvjA6s,14201
dask/dataframe/multi.py,sha256=F9GFoC-xcOGStk-YiKVHRSXtXx4zAsNYmJEx0qLhmMY,12303
dask/dataframe/partitionquantiles.py,sha256=i6AoyEAfBt6tw4wZpNhduBVhtnnlygndnaypWtEUym8,18912
dask/dataframe/rolling.py,sha256=-WVzc7PasCYPt6XeSZY5WDt6Vfb4ysLu9zhWK2uifrs,1312
dask/dataframe/shuffle.py,sha256=UdTlWVOm8KWc6p8qZc_OJ5b9ldOZE7QZoUjeP7EH3tU,7841
dask/dataframe/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/dataframe/tests/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_accessors.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_api.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_arithmetics_reduction.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_boolean.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_categorical.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_dataframe.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_extensions.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_groupby.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_hashing.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_hyperloglog.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_indexing.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_merge_column_and_index.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_methods.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_multi.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_numeric.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_pyarrow.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_pyarrow_compat.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_reshape.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_rolling.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_shuffle.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_ufunc.cpython-310.pyc,,
dask/dataframe/tests/__pycache__/test_utils_dataframe.cpython-310.pyc,,
dask/dataframe/tests/test_accessors.py,sha256=9RxEgCeo9YYsDcwU5WW7c-bAKLVUCasrpUR6XINph5k,10856
dask/dataframe/tests/test_api.py,sha256=S_aGRHe3ZbDdhfO1gJjhzT57Tz-Jc-N2vLAtYC3vOuc,845
dask/dataframe/tests/test_arithmetics_reduction.py,sha256=y_nfrgEteI-srbBurhNAQuekg0XWJJTk-nPBsLyZxzs,53372
dask/dataframe/tests/test_boolean.py,sha256=uRCbR_KY4A2Z7t1xAxCU__rS5-j2ciKrJhUwcNJ37pc,942
dask/dataframe/tests/test_categorical.py,sha256=w0_6pMWTKWUtqArMqUGqY8bDTdxPTrjhPiAsi5o3P_k,17160
dask/dataframe/tests/test_dataframe.py,sha256=5Pt-DsvzTia9a841v5ymr0iqsIVW5HNmi9hVx2SyFB8,173418
dask/dataframe/tests/test_extensions.py,sha256=0Z9tzfJuYcrsjg5o2LW4lsF9vFRsLstN7kc36FBn0Ac,1457
dask/dataframe/tests/test_groupby.py,sha256=DmSeSjf3SkYbPj55EL5pmsOf5P4I6qNC780XPzlHEVY,105788
dask/dataframe/tests/test_hashing.py,sha256=aZ-fY1lZYVju2IHX6Li1RmRst1QU313kLYoe6efuHS8,2702
dask/dataframe/tests/test_hyperloglog.py,sha256=g6x6S03r1cnK9W0poQ7S45ixIkOMco96Tx3D5554ZLY,2897
dask/dataframe/tests/test_indexing.py,sha256=wcG9VzmquNF0jOvGwrXECDowBO212bjSCz81VMlGK3g,19985
dask/dataframe/tests/test_merge_column_and_index.py,sha256=pTiNyADKIFUVCqoRFvsCriRF8kDDIs1eCuExUeyj9sk,7990
dask/dataframe/tests/test_methods.py,sha256=KRVCcYwGBlmxAUVUyuIAw2Xvv3xuXqsTfzjXPUXG-M8,388
dask/dataframe/tests/test_multi.py,sha256=Q4CAXDooo3vQXJ0PLjxYfLvztP8elRVjCHzaBqCMbi8,78406
dask/dataframe/tests/test_numeric.py,sha256=gm0x_rrxhaVmFbJALbNOK9Li0wSUjFLYO7F8LXH3RPY,2624
dask/dataframe/tests/test_pyarrow.py,sha256=MGw8_SyhCB-5S07-IWKktjGEXCOWeCrRZ8Pk83_0kZU,5501
dask/dataframe/tests/test_pyarrow_compat.py,sha256=zuhXab8TlOBCeD2gdQ9qCRYf55RcA9B8K7rMla4YX0U,4706
dask/dataframe/tests/test_reshape.py,sha256=jvnAQGOKzqTcfa38TYJfQ-4bD10Pc7sQvanF8g-TWFI,10613
dask/dataframe/tests/test_rolling.py,sha256=iN70ejBK93AtM7yv0wy48glbqQjImvcZ9vOIsb0vGxk,15170
dask/dataframe/tests/test_shuffle.py,sha256=pr0QZlc7Zznpam1uSE0e_hDVsrozyzDQnNqqRVppDek,43852
dask/dataframe/tests/test_ufunc.py,sha256=fMnWT2I6_kelP21kvj0Uia-ND3OJgmeiO7FLmV7-mT4,17085
dask/dataframe/tests/test_utils_dataframe.py,sha256=vPIej7vBnYB8uno8KUGLk5ak2s90C7omCCj-p0L0y7c,21928
dask/dataframe/tseries/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/dataframe/tseries/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/tseries/__pycache__/resample.cpython-310.pyc,,
dask/dataframe/tseries/resample.py,sha256=QAO6xDgzK3sVWk-yw9F74vsxfK2QWlKOSjsYiVD74Vc,9629
dask/dataframe/tseries/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/dataframe/tseries/tests/__pycache__/__init__.cpython-310.pyc,,
dask/dataframe/tseries/tests/__pycache__/test_resample.cpython-310.pyc,,
dask/dataframe/tseries/tests/__pycache__/test_resample_expr.cpython-310.pyc,,
dask/dataframe/tseries/tests/test_resample.py,sha256=eB5dXUdDAw6k56A44ikpKCWhYd_D6Uy5gFjXlt3bdg4,7568
dask/dataframe/tseries/tests/test_resample_expr.py,sha256=601hw2iUqeKz3DOFpSXAPKKvIkUGZOben2ItVUwjzM4,4500
dask/dataframe/utils.py,sha256=V7gn3CHT1hALHo1hXRHLhBDA0wVr5SwhuJtFUe1OkZc,25896
dask/datasets.py,sha256=FPt5NKIJ-kpWsViLIqW4bLsQ12vWZVdy-4LvZl9RBeI,5332
dask/delayed.py,sha256=FIrI5vhVICnxyKNcoltrNuGG7HsQnnatp74bRJR2fnk,30518
dask/diagnostics/__init__.py,sha256=18qAq00itRU6HAkcYD6UoS8hPj_CkCbtNlROlG-n62I,258
dask/diagnostics/__pycache__/__init__.cpython-310.pyc,,
dask/diagnostics/__pycache__/profile.cpython-310.pyc,,
dask/diagnostics/__pycache__/profile_visualize.cpython-310.pyc,,
dask/diagnostics/__pycache__/progress.cpython-310.pyc,,
dask/diagnostics/profile.py,sha256=ymbWuuzptT-ipnEpGS8wF-5G4BeaTVf4OcuHtGrmVx8,12136
dask/diagnostics/profile_visualize.py,sha256=gur5klAokH5K-KhByvH6ZdlS1pc8PzWd8jbMHXNuQo0,13378
dask/diagnostics/progress.py,sha256=4D1bc_h5-fLuOta3KJmZeK00a02GorgC0X3Ap6DFbuM,5001
dask/diagnostics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/diagnostics/tests/__pycache__/__init__.cpython-310.pyc,,
dask/diagnostics/tests/__pycache__/test_profiler.cpython-310.pyc,,
dask/diagnostics/tests/__pycache__/test_progress.cpython-310.pyc,,
dask/diagnostics/tests/test_profiler.py,sha256=x0bxpfEM6c0GFporG8USsM6pboPwlRmKyVAWKzI8ZNU,10806
dask/diagnostics/tests/test_progress.py,sha256=-HimxrEnY-Ypuin5IXma-3azpuaiYsTfdkIlYoBxVNw,3421
dask/distributed.py,sha256=NCj3IP2dSBkecgmOU2TMCnL-ZTsuRFjEQ5p6hH4c360,715
dask/dot.py,sha256=zrVcLEJf_zZX7CO0f5rbMKq4QZG8uhRlBfFe0IfgYkA,16788
dask/graph_manipulation.py,sha256=Kccb8-sj3mQ1o4APl2MeUQJpvA_xmeTkDawuk0bY26A,19729
dask/hashing.py,sha256=9EPRgsXGXJAbf3d2EjJjW1h2Oorcw2xhPq3F9C0OxFw,2492
dask/highlevelgraph.py,sha256=JgT67F3kQxrDqcrai9hQthS3Q2b3_bRlOuHdIpHwdic,36055
dask/layers.py,sha256=8DptUmsV-KszXGHYLH9bwFiVftw1MyOOONr0rhf792k,14823
dask/local.py,sha256=f5YDMt4Dj2LYe7l06c6UUR-Km-diJZUX0VbeuIX9pq0,20098
dask/ml.py,sha256=qPEZ9NdCbcmixIGHFnSNerx4kUZ15SpFqEnTYwlDfdY,487
dask/multiprocessing.py,sha256=w5P_z5rKnOWqdY-1tSu2Yc9Sxstv85Lvl3uEGVHOy6U,8525
dask/optimization.py,sha256=YplEilTKQABQ2rKkc9D5xXIPYwvDjolSNe1Rl1KbEzk,32408
dask/order.py,sha256=8OTToWNm8ws9ighi7HujWuif_Yx3gBZAccfGodFdIIA,33673
dask/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/rewrite.py,sha256=h2YXuRTmNshgPLs-7SCsyegDfyXv82WRV5ABJvzMvpc,12597
dask/sizeof.py,sha256=JulXuHUoB4_J0M64__jVfp2aN4I7NFvQJBAYxOV7Uck,8844
dask/system.py,sha256=5v_JzIuW4YMtlvuMgWCPrJBszswtgFTAbsnAmJB3V0Y,2089
dask/task_spec.py,sha256=e1jNKjLYmdIOPKphNIpZwYzP69fp2dYSPVvtYvuM4b4,212
dask/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dask/tests/__pycache__/__init__.cpython-310.pyc,,
dask/tests/__pycache__/test_backends.cpython-310.pyc,,
dask/tests/__pycache__/test_base.cpython-310.pyc,,
dask/tests/__pycache__/test_cache.cpython-310.pyc,,
dask/tests/__pycache__/test_callbacks.cpython-310.pyc,,
dask/tests/__pycache__/test_ci.cpython-310.pyc,,
dask/tests/__pycache__/test_cli.cpython-310.pyc,,
dask/tests/__pycache__/test_compatibility.cpython-310.pyc,,
dask/tests/__pycache__/test_config.cpython-310.pyc,,
dask/tests/__pycache__/test_context.cpython-310.pyc,,
dask/tests/__pycache__/test_core.cpython-310.pyc,,
dask/tests/__pycache__/test_datasets.cpython-310.pyc,,
dask/tests/__pycache__/test_delayed.cpython-310.pyc,,
dask/tests/__pycache__/test_distributed.cpython-310.pyc,,
dask/tests/__pycache__/test_docs.cpython-310.pyc,,
dask/tests/__pycache__/test_dot.cpython-310.pyc,,
dask/tests/__pycache__/test_expr.cpython-310.pyc,,
dask/tests/__pycache__/test_graph_manipulation.cpython-310.pyc,,
dask/tests/__pycache__/test_hashing.cpython-310.pyc,,
dask/tests/__pycache__/test_highgraph.cpython-310.pyc,,
dask/tests/__pycache__/test_hlgexpr.cpython-310.pyc,,
dask/tests/__pycache__/test_imports.cpython-310.pyc,,
dask/tests/__pycache__/test_layers.cpython-310.pyc,,
dask/tests/__pycache__/test_local.cpython-310.pyc,,
dask/tests/__pycache__/test_ml.cpython-310.pyc,,
dask/tests/__pycache__/test_multiprocessing.cpython-310.pyc,,
dask/tests/__pycache__/test_optimization.cpython-310.pyc,,
dask/tests/__pycache__/test_order.cpython-310.pyc,,
dask/tests/__pycache__/test_rewrite.cpython-310.pyc,,
dask/tests/__pycache__/test_sizeof.cpython-310.pyc,,
dask/tests/__pycache__/test_spark_compat.cpython-310.pyc,,
dask/tests/__pycache__/test_system.cpython-310.pyc,,
dask/tests/__pycache__/test_task_spec.cpython-310.pyc,,
dask/tests/__pycache__/test_threaded.cpython-310.pyc,,
dask/tests/__pycache__/test_tokenize.cpython-310.pyc,,
dask/tests/__pycache__/test_traceback.cpython-310.pyc,,
dask/tests/__pycache__/test_typing.cpython-310.pyc,,
dask/tests/__pycache__/test_utils.cpython-310.pyc,,
dask/tests/__pycache__/test_utils_test.cpython-310.pyc,,
dask/tests/__pycache__/warning_aliases.cpython-310.pyc,,
dask/tests/test_backends.py,sha256=H-b0zzMMpX8EnNN1BQEGYrmErFidXAE6-vb4yp_4n6A,742
dask/tests/test_base.py,sha256=MupkuJLkXlwOvMvm-yFxPK7HPrnrMQVu9-tT6nLTliI,30335
dask/tests/test_cache.py,sha256=hAhZlT3ZyhVbBSNDBTlW_2Jg8YEfKBwaYuYEVlSFPqQ,1646
dask/tests/test_callbacks.py,sha256=IbDMN8si_o9BgHLXObBQtMNTS5HBKm9ohF784JvgM5M,2570
dask/tests/test_ci.py,sha256=cuz93WpARxCe4R5aRWjLPkVjh8SLYYJNNBIafFg84GM,1199
dask/tests/test_cli.py,sha256=INLDu3B7Axp6HQEO6K1YGrteoooLOqn-njG2rfzNYlY,7488
dask/tests/test_compatibility.py,sha256=BowGlq9C0NZQdlAHIuvTX3BSuGfMak4osrHuAxr65Yc,251
dask/tests/test_config.py,sha256=2XpuzmqzX5zOCGVY2yICiwVOm5muZc3anxqmj8UKN_k,20268
dask/tests/test_context.py,sha256=u6-fH9Wog8ChvX7BYAPYfNgGvmSE9k9D5gXrwaNJLaA,1170
dask/tests/test_core.py,sha256=XpnCyWzDw7GrQhMczRMb8NfUbcBSvSFuLs8IXGcdL8M,7771
dask/tests/test_datasets.py,sha256=QCtpWnkBVVKm9jyxicNbiAqydspkwA66U5KwUt2OM00,1112
dask/tests/test_delayed.py,sha256=1XNE0azk5KIbe6lqgww2lQfdJ9MIGb69gaBEaHbE6uI,25832
dask/tests/test_distributed.py,sha256=VVVBWAgtj_ZAqPFXHOI267WpkyBsUo6YPYcE7ddEY4I,34745
dask/tests/test_docs.py,sha256=3zR6tnGn05xV5KN-azKxWGW8b-qT79Cff_s8TIqJgJw,719
dask/tests/test_dot.py,sha256=Q5C405GS4N63tROHVW5Lyy8qq29-0vvOlTqgRmorraE,12271
dask/tests/test_expr.py,sha256=c3z_lvK7D7myMNm4fhETWJvyb37E4UoOeVeJb_7re3k,4524
dask/tests/test_graph_manipulation.py,sha256=5cSDa4v446qMZ3Gt4vDX2GOMdYEiKAHBnNzPtRZlZoQ,11533
dask/tests/test_hashing.py,sha256=BKYmyQUYSjWlC54ZCFC6JW_9LXg9kYgrNSmbvBYsvxA,1095
dask/tests/test_highgraph.py,sha256=gQLeE6aF9t3M1susS7H9kZYr4VMiKTW2ktu35xxnrVU,11319
dask/tests/test_hlgexpr.py,sha256=DHzIlgdI4qhcZr_MfngBi1xwwJc8qjM5djvk98jCvJc,8919
dask/tests/test_imports.py,sha256=87DgwIhtLl1XMftlkLMXDaxmWH01VZdEf5JJY5nHLaI,920
dask/tests/test_layers.py,sha256=MAy_nFdkSsQgYbmvyKISUSy2cTvwG0PT9R0_5ehKEYw,6471
dask/tests/test_local.py,sha256=5r_LcITi45Kjw5Qpn5VooC6TiM1kG2PjC1-t8HFVK08,5829
dask/tests/test_ml.py,sha256=uWXNr-BJtlaQqrq_zFK8ClBcuQGdY7AzOZck4Ijsz1A,419
dask/tests/test_multiprocessing.py,sha256=pWwU5kgxMjMm315kD--CM5VchqvuAqQHlz7CmKcwiIQ,8457
dask/tests/test_optimization.py,sha256=ZJREJW25qxi95V-PiCl0DiWc3i05NYK-6Lqis2cPnB4,43602
dask/tests/test_order.py,sha256=OO0UIQVlbBlDoWWtgE2h0sRC9ST2XDVICeg2MvpcO5w,89383
dask/tests/test_rewrite.py,sha256=QEB9fyVY2GMJXzCILQwCzlOm80LoiXr2QyNDcbcQJFE,4684
dask/tests/test_sizeof.py,sha256=Y0PAu3OSg0KOmgoiZySgQjQDvMD_3R_0WZDESFJ8hIc,9369
dask/tests/test_spark_compat.py,sha256=N_qlKmXrZ_9KfkE7bjlOebSBrx6HiadfuxmxLZfoRVE,6763
dask/tests/test_system.py,sha256=f6j6IduYS2TJR7lbqc0OF1jDZ74DaJ1DkN2_efL7gLE,2571
dask/tests/test_task_spec.py,sha256=N_LS-YqiNEzRb2vAgJg0g5wEiC9I5TNhJX9kT2D6krE,32353
dask/tests/test_threaded.py,sha256=_MDEo_VeiEYRmZPg2u0v5P6PPqiGg9LHf027ebgB_FE,4749
dask/tests/test_tokenize.py,sha256=dWl-yC96QKpWZcyYb6XBrSSf1PKQzHhtXLIqYqA1u6I,43412
dask/tests/test_traceback.py,sha256=NZEZfEj3UJHCSk5g4hTD4bleUZkwQ2WAc7xVQDtTKP0,4592
dask/tests/test_typing.py,sha256=FY1NVzwImirrj7v3Kmj-KHhGokZ-XFdqRkGRQ5T9LQo,5665
dask/tests/test_utils.py,sha256=n_beCZeCo7VHqlJjjq-5dtGaQ8RHjKHfUMEqMGT0Yg4,22984
dask/tests/test_utils_test.py,sha256=VD64A0D49cohFB6pfu8rhhzmIwjQpeg1Udkbe-z90o4,1598
dask/tests/warning_aliases.py,sha256=wvgM9CiTzpyCxIK3CxHs1EhWBCr8Vx5cAll2VH31BB0,211
dask/threaded.py,sha256=hmmVwHxBCP2GGqIa7TR1m4L9wPwBo6lvj4YN-QjS_1Y,3002
dask/tokenize.py,sha256=i8WpFzPBOWUhExw_IKispuAeinF7J-xozIpxaFq4mtU,13567
dask/typing.py,sha256=QYzzvmqh3bNM7KcSmxYfiuPgOxND4X1iwKV8L9xNNhk,15696
dask/utils.py,sha256=9ubPfZNitZ56qewgbfSot-JHhJT2xyReVK_auoHC7e0,63329
dask/utils_test.py,sha256=scsBJ25CPjB1mjvNgK5_Q4M5OzYFk6qbB6xQNOa1oQ4,4825
dask/widgets/__init__.py,sha256=7ZB-WfPlyl4nniwRJTkoSCW_bCeIiww1n17aJ8QysnY,792
dask/widgets/__pycache__/__init__.cpython-310.pyc,,
dask/widgets/__pycache__/widgets.cpython-310.pyc,,
dask/widgets/templates/array.html.j2,sha256=dmatjTOa1GQFjehtgPf57Bpy7p-TERAlVbgv5wN2WYc,1295
dask/widgets/templates/dataframe.html.j2,sha256=JdoiOJ5p3pTirI1iW7bl5s0QO7XWWSKvGyuQmFFINiw,124
dask/widgets/templates/highlevelgraph.html.j2,sha256=pYk0nNtTNsN1dfdVUD-xXMoP_b-CJPsS0YyjHRufyvo,2579
dask/widgets/templates/highlevelgraph_layer.html.j2,sha256=-WHBsG79OutWZDXCule_9fSOCO_iS3C7TGfs_M7e9pY,1981
dask/widgets/tests/__pycache__/test_widgets.cpython-310.pyc,,
dask/widgets/tests/templates/bytes.html.j2,sha256=jfLERUOl0h_F58xlSnhiTjsmSDSMP1tZTXjEgHhfSAg,38
dask/widgets/tests/templates/custom_filter.html.j2,sha256=JLuvLerswhNWyJpoFlflHgNnnEbDcoIROrQ2n4r3sCE,39
dask/widgets/tests/templates/example.html.j2,sha256=EXQ82ink7Nr8qsvXOzQznGO37mFHMoe9TPkv2t6vxeA,30
dask/widgets/tests/test_widgets.py,sha256=Jf8bcZ7jedOaa6qVuBqt7gPEBRZiJX_G0cU96Ix6UcU,1401
dask/widgets/widgets.py,sha256=2E9K2nE5wdgBA5ITbDXdGLtpM7QivTBFFhQsD4gvzNk,1120
