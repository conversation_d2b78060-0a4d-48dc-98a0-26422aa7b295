import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._zoom import <PERSON>m<PERSON><PERSON>da<PERSON>
    from ._uirevision import UirevisionValidator
    from ._style import <PERSON>Validator
    from ._pitch import PitchValidator
    from ._layerdefaults import LayerdefaultsValidator
    from ._layers import LayersValidator
    from ._domain import DomainValidator
    from ._center import CenterValidator
    from ._bounds import BoundsValidator
    from ._bearing import BearingValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._zoom.ZoomValidator",
            "._uirevision.UirevisionValidator",
            "._style.StyleValidator",
            "._pitch.PitchValidator",
            "._layerdefaults.LayerdefaultsValidator",
            "._layers.LayersValidator",
            "._domain.DomainValidator",
            "._center.CenterValidator",
            "._bounds.BoundsValidator",
            "._bearing.BearingValidator",
        ],
    )
