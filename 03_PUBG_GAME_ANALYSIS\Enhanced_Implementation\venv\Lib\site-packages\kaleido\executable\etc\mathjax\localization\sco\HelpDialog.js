/*************************************************************
 *
 *  MathJax/localization/sco/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("sco","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "MathJax Heelp",
          MathJax: "*MathJax* is ae JavaScreept librairie that allous page authers tae incluid mathematics wiin thair wab pages. Aes ae reader, ye dinna need tae dae oniething tae mak that happen.",
          Browsers: "*Brousers*: MathJax warks wi aw modern brousers incluidin IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ n maist mobile brousers.",
          Menu: "*Maths menu*: MathJax adds ae contextual menu til equations. Richt-clap or Ctrl-clap oan onie mathematics tae access the menu.",
          ShowMath: "*Shaw maths aes* Permits ye tae view the formula's soorce maurkup fer copie \u0026 paste (aes MathML or in its oreeginal format).",
          Settings: "*Settins* gies ye control ower features o MathJax, lik the size o the mathematics, n the mechanism uised tae displey equations.",
          Language: "*Leid* lets ye select the leid uised bi MathJax fer its menus n warnishment messages.",
          Zoom: "*Maths zuim*: Gif ye'r haein difficultie readin aen equation, MathJax can mak it mair muckle tae heelp ye see it better.",
          Accessibilty: "*Accessibeelitie*: MathJax will aut\u00E6maticlie wark wi screen readers tae mak mathematics accessible til the veesuallie impaired.",
          Fonts: "*Fonts*: MathJax will uise certain maths fonts gif thay ar installed oan yer computer; itherwise, it will uise wab-based fonts. Awthough no needit, locallie installed fonts will speed up typesettin. We suggest installin the [STIX fonts](%1)."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/sco/HelpDialog.js");
