from .base import RedirectView as RedirectView, TemplateView as Template<PERSON>ie<PERSON>, View as View
from .dates import (
    ArchiveIndexView as ArchiveIndexView,
    DateDetailView as DateDetailView,
    DayArchiveView as DayArchiveView,
    MonthArchiveView as MonthArchive<PERSON>ie<PERSON>,
    TodayArchiveView as TodayArchive<PERSON>iew,
    WeekArchiveView as WeekArchiveVie<PERSON>,
    YearArchiveView as YearArchiveView,
)
from .detail import DetailView as DetailView
from .edit import CreateView as CreateView, DeleteView as DeleteView, FormView as FormView, UpdateView as UpdateView
from .list import ListView as ListView

class GenericViewError(Exception): ...
