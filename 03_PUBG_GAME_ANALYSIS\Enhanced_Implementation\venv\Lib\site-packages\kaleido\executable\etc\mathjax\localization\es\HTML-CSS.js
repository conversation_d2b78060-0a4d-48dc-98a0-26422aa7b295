/*************************************************************
 *
 *  MathJax/localization/es/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("es","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "Cargando el tipo de letra web %1",
          CantLoadWebFont: "No se pudo cargar la fuente web %1",
          FirefoxCantLoadWebFont: "Firefox no puede cargar tipos de letra web desde un equipo remoto",
          CantFindFontUsing: "No se puede encontrar un tipo de letra v\u00E1lido mediante %1",
          WebFontsNotAvailable: "No hay fuentes web disponibles. Usando fuentes de imagen en su lugar"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/es/HTML-CSS.js");
