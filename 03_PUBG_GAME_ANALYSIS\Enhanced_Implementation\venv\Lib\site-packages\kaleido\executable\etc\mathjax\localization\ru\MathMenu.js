/*************************************************************
 *
 *  MathJax/localization/ru/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ru","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u043A\u043E\u0434 \u0444\u043E\u0440\u043C\u0443\u043B\u044B \u0432\u00A0\u0432\u0438\u0434\u0435:",
          MathMLcode: "\u041A\u043E\u0434 MathML",
          OriginalMathML: "\u041E\u0440\u0438\u0433\u0438\u043D\u0430\u043B\u044C\u043D\u044B\u0439 MathML",
          TeXCommands: "\u041A\u043E\u043C\u0430\u043D\u0434\u044B TeX",
          AsciiMathInput: "\u0412\u0432\u043E\u0434 AsciiMathML",
          Original: "\u0418\u0441\u0445\u043E\u0434\u043D\u044B\u0439 \u0432\u0438\u0434",
          ErrorMessage: "\u0421\u043E\u043E\u0431\u0449\u0435\u043D\u0438\u0435 \u043E\u0431 \u043E\u0448\u0438\u0431\u043A\u0435",
          Annotation: "\u0410\u043D\u043D\u043E\u0442\u0430\u0446\u0438\u044F",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "\u0421\u043C\u044B\u0441\u043B\u043E\u0432\u0430\u044F \u0440\u0430\u0437\u043C\u0435\u0442\u043A\u0430",
          OpenMath: "OpenMath",
          texHints: "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u043A\u043E\u043C\u043C\u0435\u043D\u0442\u0430\u0440\u0438\u0438 \u0438\u0437 TeX \u0432 MathML",
          Settings: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 Math",
          ZoomTrigger: "\u0412\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435 \u0443\u0432\u0435\u043B\u0438\u0447\u0435\u043D\u0438\u044F",
          Hover: "\u041F\u0440\u0438\u00A0\u043D\u0430\u0432\u0435\u0434\u0435\u043D\u0438\u0438 \u0443\u043A\u0430\u0437\u0430\u0442\u0435\u043B\u044F \u043C\u044B\u0448\u0438",
          Click: "\u041F\u0440\u0438 \u0449\u0435\u043B\u0447\u043A\u0435 \u043C\u044B\u0448\u044C\u044E",
          DoubleClick: "\u041F\u0440\u0438\u00A0\u0434\u0432\u043E\u0439\u043D\u043E\u043C \u0449\u0435\u043B\u0447\u043A\u0435 \u043C\u044B\u0448\u044C\u044E",
          NoZoom: "\u0411\u0435\u0437 \u043C\u0430\u0441\u0448\u0442\u0430\u0431\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F",
          TriggerRequires: "\u041F\u0440\u0438\u00A0\u043D\u0430\u0436\u0430\u0442\u043E\u0439 \u043A\u043B\u0430\u0432\u0438\u0448\u0435:",
          Option: "Option",
          Alt: "Alt",
          Command: "Command",
          Control: "Control",
          Shift: "Shift",
          ZoomFactor: "\u0423\u0432\u0435\u043B\u0438\u0447\u0435\u043D\u0438\u0435",
          Renderer: "\u041E\u0442\u0440\u0438\u0441\u043E\u0432\u0449\u0438\u043A \u0444\u043E\u0440\u043C\u0443\u043B:",
          MPHandles: "\u041F\u0440\u0435\u0434\u043E\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u043E\u0431\u0440\u0430\u0431\u043E\u0442\u043A\u0443 \u0449\u0435\u043B\u0447\u043A\u043E\u0432 \u043C\u044B\u0448\u044C\u044E MathPlayer",
          MenuEvents: "\u041A\u043E\u043C\u0430\u043D\u0434\u044B \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u0433\u043E \u043C\u0435\u043D\u044E",
          MouseEvents: "\u0421\u043E\u0431\u044B\u0442\u0438\u044F \u043C\u044B\u0448\u0438",
          MenuAndMouse: "\u041A\u043E\u043C\u0430\u043D\u0434\u044B \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u0433\u043E \u043C\u0435\u043D\u044E \u0438 \u0441\u043E\u0431\u044B\u0442\u0438\u044F \u043C\u044B\u0448\u0438",
          FontPrefs: "\u041F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u044B \u0448\u0440\u0438\u0444\u0442\u043E\u0432",
          ForHTMLCSS: "\u0414\u043B\u044F HTML-CSS:",
          Auto: "\u0410\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u0438",
          TeXLocal: "TeX (\u043B\u043E\u043A\u0430\u043B\u044C\u043D\u044B\u0435)",
          TeXWeb: "TeX (\u0438\u043D\u0442\u0435\u0440\u043D\u0435\u0442)",
          TeXImage: "TeX (\u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435)",
          STIXLocal: "STIX (\u043B\u043E\u043A\u0430\u043B\u044C\u043D\u044B\u0435)",
          STIXWeb: "STIX (\u0432\u0435\u0431-\u0448\u0440\u0438\u0444\u0442)",
          AsanaMathWeb: "Asana Math (\u0432\u0435\u0431-\u0448\u0440\u0438\u0444\u0442)",
          GyrePagellaWeb: "Gyre Pagella (\u0432\u0435\u0431-\u0448\u0440\u0438\u0444\u0442)",
          GyreTermesWeb: "Gyre Termes (\u0432\u0435\u0431-\u0448\u0440\u0438\u0444\u0442)",
          LatinModernWeb: "Latin Modern (\u0432\u0435\u0431-\u0448\u0440\u0438\u0444\u0442)",
          NeoEulerWeb: "Neo Euler (\u0432\u0435\u0431-\u0448\u0440\u0438\u0444\u0442)",
          ContextMenu: "\u041A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u0435 \u043C\u0435\u043D\u044E",
          Browser: "\u0411\u0440\u0430\u0443\u0437\u0435\u0440",
          Scale: "\u0423\u0432\u0435\u043B\u0438\u0447\u0438\u0442\u044C \u0432\u0441\u0435 \u0444\u043E\u0440\u043C\u0443\u043B\u044B\u2026",
          Discoverable: "\u041F\u043E\u0434\u0441\u0432\u0435\u0442\u0438\u0442\u044C \u043F\u043E\u0434\u00A0\u043C\u044B\u0448\u044C\u044E",
          Locale: "\u042F\u0437\u044B\u043A",
          LoadLocale: "\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0441 URL-\u0430\u0434\u0440\u0435\u0441\u0430...",
          About: "\u041E MathJax",
          Help: "\u041F\u043E\u043C\u043E\u0449\u044C \u043F\u043E MathJax",
          localTeXfonts: "\u0441 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435\u043C \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u044B\u0445 \u0448\u0440\u0438\u0444\u0442\u043E\u0432 TeX",
          webTeXfonts: "\u0441 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435\u043C \u0432\u0435\u0431-\u0448\u0440\u0438\u0444\u0442\u043E\u0432 TeX",
          imagefonts: "\u0441 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435\u043C \u0433\u0440\u0430\u0444\u0438\u0447\u0435\u0441\u043A\u0438\u0445 \u0448\u0440\u0438\u0444\u0442\u043E\u0432",
          localSTIXfonts: "\u0441\u00A0\u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435\u043C \u0448\u0440\u0438\u0444\u0442\u043E\u0432 STIX \u043D\u0430\u00A0\u044D\u0442\u043E\u0439 \u043C\u0430\u0448\u0438\u043D\u0435",
          webSVGfonts: "\u0441\u00A0\u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435\u043C \u0441\u0435\u0442\u0435\u0432\u044B\u0445 SVG-\u0448\u0440\u0438\u0444\u0442\u043E\u0432",
          genericfonts: "\u0441\u00A0\u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435\u043C Unicod-\u0448\u0440\u0438\u0444\u0442\u043E\u0432 \u043F\u043E\u00A0\u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E \u043D\u0430\u00A0\u044D\u0442\u043E\u0439 \u043C\u0430\u0448\u0438\u043D\u0435",
          wofforotffonts: "\u0428\u0440\u0438\u0444\u0442\u044B WOFF \u0438\u043B\u0438\u00A0OTF",
          eotffonts: "EOT-\u0448\u0440\u0438\u0444\u0442\u044B",
          svgfonts: "SVG-\u0448\u0440\u0438\u0444\u0442\u044B",
          WebkitNativeMMLWarning: "\u041F\u043E\u0445\u043E\u0436\u0435, \u0412\u0430\u0448 \u0431\u0440\u0430\u0443\u0437\u0435\u0440 \u043D\u0435 \u0441\u043F\u043E\u0441\u043E\u0431\u0435\u043D \u0441\u0430\u043C \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0442\u044C MathML, \u043F\u043E\u044D\u0442\u043E\u043C\u0443\u00A0\u043F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435 \u0432\u00A0\u0440\u0435\u0436\u0438\u043C MathML \u043C\u043E\u0436\u0435\u0442 \u0441\u0434\u0435\u043B\u0430\u0442\u044C \u0444\u043E\u0440\u043C\u0443\u043B\u044B \u043D\u0435\u0447\u0438\u0442\u0430\u0435\u043C\u044B\u043C\u0438",
          MSIENativeMMLWarning: "Internet Explorer \u043C\u043E\u0436\u0435\u0442 \u043E\u0442\u043E\u0431\u0440\u0430\u0437\u0438\u0442\u044C MathML \u0442\u043E\u043B\u044C\u043A\u043E \u0441\u00A0\u043F\u043E\u043C\u043E\u0449\u044C\u044E \u043F\u043B\u0430\u0433\u0438\u043D\u0430 MathPlayer.",
          OperaNativeMMLWarning: "Opera \u0442\u043E\u043B\u044C\u043A\u043E \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0435\u043D\u043D\u043E \u043F\u043E\u0434\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0435\u0442 MathML, \u043F\u043E\u044D\u0442\u043E\u043C\u0443 \u0432\u00A0\u0440\u0435\u0436\u0438\u043C\u0435 MathML \u0447\u0430\u0441\u0442\u044C \u0444\u043E\u0440\u043C\u0443\u043B \u0431\u0443\u0434\u0435\u0442 \u0432\u044B\u0432\u043E\u0434\u0438\u0442\u044C\u0441\u044F \u043F\u043B\u043E\u0445\u043E.",
          SafariNativeMMLWarning: "\u0412\u0441\u0442\u0440\u043E\u0435\u043D\u043D\u044B\u0439 \u043C\u0435\u0445\u0430\u043D\u0438\u0437\u043C MathML \u0412\u0430\u0448\u0435\u0433\u043E \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430 \u043D\u0435\u00A0\u043F\u043E\u0434\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0435\u0442 \u0432\u0441\u0435\u0445 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u043C\u044B\u0445 MathJax \u0432\u043E\u0437\u043C\u043E\u0436\u043D\u043E\u0441\u0442\u0435\u0439, \u043F\u043E\u044D\u0442\u043E\u043C\u0443\u00A0\u0447\u0430\u0441\u0442\u044C \u0444\u043E\u0440\u043C\u0443\u043B \u043C\u043E\u0436\u0435\u0442 \u0431\u044B\u0442\u044C\u00A0\u0432\u044B\u0432\u0435\u0434\u0435\u043D\u0430 \u043D\u0435\u043F\u0440\u0430\u0432\u0438\u043B\u044C\u043D\u043E.",
          FirefoxNativeMMLWarning: "\u0412\u0441\u0442\u0440\u043E\u0435\u043D\u043D\u044B\u0439 \u043C\u0435\u0445\u0430\u043D\u0438\u0437\u043C MathML \u0412\u0430\u0448\u0435\u0433\u043E \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430 \u043F\u043E\u0434\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0435\u0442 \u043D\u0435\u00A0\u0432\u0441\u0435 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u043C\u044B\u0435 MathJax \u0432\u043E\u0437\u043C\u043E\u0436\u043D\u043E\u0441\u0442\u0438, \u043F\u043E\u044D\u0442\u043E\u043C\u0443 \u0447\u0430\u0441\u0442\u044C \u0444\u043E\u0440\u043C\u0443\u043B \u043C\u043E\u0436\u0435\u0442 \u043F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C\u0441\u044F \u043D\u0435\u043F\u0440\u0430\u0432\u0438\u043B\u044C\u043D\u043E.",
          MSIESVGWarning: "SVG \u043D\u0435\u00A0\u043F\u043E\u0434\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0435\u0442\u0441\u044F Internet Explorer'\u043E\u043C \u0434\u043E\u00A0IE9 \u0438\u00A0\u0432\u00A0\u0440\u0435\u0436\u0438\u043C\u0435 \u044D\u043C\u0443\u043B\u044F\u0446\u0438\u0438 \u0432\u0435\u0440\u0441\u0438\u0438 IE8 \u0438\u043B\u0438\u00A0\u043D\u0438\u0436\u0435. \u041F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435 \u0432\u00A0\u0440\u0435\u0436\u0438\u043C SVG \u043C\u043E\u0436\u0435\u0442 \u0432\u044B\u0437\u0432\u0430\u0442\u044C \u043D\u0435\u043F\u0440\u0430\u0432\u0438\u043B\u044C\u043D\u043E\u0435 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u043D\u0435\u043A\u043E\u0442\u043E\u0440\u044B\u0445 \u0444\u043E\u0440\u043C\u0443\u043B.",
          LoadURL: "\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u043F\u0435\u0440\u0435\u0432\u0435\u0434\u0451\u043D\u043D\u044B\u0435 \u0441\u043E\u043E\u0431\u0449\u0435\u043D\u0438\u044F \u043F\u043E\u00A0\u0430\u0434\u0440\u0435\u0441\u0443:",
          BadURL: "URL \u0434\u043E\u043B\u0436\u0435\u043D \u0443\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u043D\u0430\u00A0\u0444\u0430\u0439\u043B JavaScript, \u043E\u043F\u0440\u0435\u0434\u0435\u043B\u044F\u044E\u0449\u0438\u0439 \u043F\u0435\u0440\u0435\u0432\u0435\u0434\u0451\u043D\u043D\u044B\u0435 \u0441\u043E\u043E\u0431\u0449\u0435\u043D\u0438\u044F. \u0424\u0430\u0439\u043B\u044B JavaScript \u043E\u043A\u0430\u043D\u0447\u0438\u0432\u0430\u044E\u0442\u0441\u044F \u0440\u0430\u0441\u0448\u0438\u0440\u0435\u043D\u0438\u0435\u043C \u00AB.js\u00BB",
          BadData: "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u043F\u0435\u0440\u0435\u0432\u0435\u0434\u0451\u043D\u043D\u044B\u0445 \u0441\u043E\u043E\u0431\u0449\u0435\u043D\u0438\u0439 \u043F\u043E\u00A0\u0430\u0434\u0440\u0435\u0441\u0443 %1 \u043D\u0435\u00A0\u0443\u0434\u0430\u043B\u0430\u0441\u044C",
          SwitchAnyway: "\u0412\u0441\u0451 \u0440\u0430\u0432\u043D\u043E \u0441\u043C\u0435\u043D\u0438\u0442\u044C \u043C\u0435\u0445\u0430\u043D\u0438\u0437\u043C \u043E\u0442\u0440\u0438\u0441\u043E\u0432\u043A\u0438 \u0444\u043E\u0440\u043C\u0443\u043B?\n\n(\u041D\u0430\u0436\u043C\u0438\u0442\u0435 OK \u0434\u043B\u044F\u00A0\u0441\u043C\u0435\u043D\u044B, \u041E\u0442\u043C\u0435\u043D\u0430, \u0447\u0442\u043E\u0431\u044B\u00A0\u043E\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u043F\u0440\u0435\u0436\u043D\u0438\u0439)",
          ScaleMath: "\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u043C\u0430\u0441\u0448\u0442\u0430\u0431 \u0432\u0441\u0435\u0445 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u0438\u0445 \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u0439 (\u043F\u043E \u0441\u0440\u0430\u0432\u043D\u0435\u043D\u0438\u044E \u0441 \u043E\u043A\u0440\u0443\u0436\u0430\u044E\u0449\u0438\u043C \u0442\u0435\u043A\u0441\u0442\u043E\u043C) \u043D\u0430",
          NonZeroScale: "\u041C\u0430\u0441\u0448\u0442\u0430\u0431 \u043D\u0435 \u0434\u043E\u043B\u0436\u0435\u043D \u0431\u044B\u0442\u044C \u0440\u0430\u0432\u0435\u043D \u043D\u0443\u043B\u044E",
          PercentScale: "\u041C\u0430\u0441\u0448\u0442\u0430\u0431 \u0434\u043E\u043B\u0436\u0435\u043D \u0431\u044B\u0442\u044C \u0432\u044B\u0440\u0430\u0436\u0435\u043D \u0432 \u043F\u0440\u043E\u0446\u0435\u043D\u0442\u0430\u0445 (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440, 120%%)",
          IE8warning: "\u042D\u0442\u043E \u043E\u0442\u043A\u043B\u044E\u0447\u0438\u0442 \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u0435 \u043C\u0435\u043D\u044E MathJax \u0438\u00A0\u0441\u0440\u0435\u0434\u0441\u0442\u0432\u0430 \u0443\u0432\u0435\u043B\u0438\u0447\u0435\u043D\u0438\u044F, \u043D\u043E\u00A0\u043C\u0435\u043D\u044E \u043F\u043E-\u043F\u0440\u0435\u0436\u043D\u0435\u043C\u0443 \u0431\u0443\u0434\u0435\u0442 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u043E \u043F\u043E\u00A0\u0449\u0435\u043B\u0447\u043A\u0443 \u043C\u044B\u0448\u044C\u044E \u0441\u00A0\u043D\u0430\u0436\u0430\u0442\u043E\u0439 Alt.\n\n\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0438\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 MathPlayer?",
          IE9warning: "\u041A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u0435 \u043C\u0435\u043D\u044E MathJax \u0431\u0443\u0434\u0435\u0442 \u043E\u0442\u043A\u043B\u044E\u0447\u0435\u043D\u043E, \u043D\u043E\u00A0\u043F\u043E \u0449\u0435\u043B\u0447\u043A\u0443 \u043C\u044B\u0448\u044C\u044E \u0441\u00A0\u043D\u0430\u0436\u0430\u0442\u043E\u0439 Alt \u043E\u043D\u043E \u043F\u043E-\u043F\u0440\u0435\u0436\u043D\u0435\u043C\u0443 \u0431\u0443\u0434\u0435\u0442 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u043E.",
          NoOriginalForm: "\u041D\u0435\u0442 \u0438\u0441\u0445\u043E\u0434\u043D\u043E\u0433\u043E \u043A\u043E\u0434\u0430",
          Close: "\u0417\u0430\u043A\u0440\u044B\u0442\u044C",
          EqSource: "\u0418\u0441\u0445\u043E\u0434\u043D\u044B\u0439 \u043A\u043E\u0434 \u0444\u043E\u0440\u043C\u0443\u043B\u044B",
          CloseAboutDialog: "\u0417\u0430\u043A\u0440\u044B\u0442\u044C \u0434\u0438\u0430\u043B\u043E\u0433 \"\u041E \u043F\u0440\u043E\u0433\u0440\u0430\u043C\u043C\u0435\"",
          FastPreview: "\u0411\u044B\u0441\u0442\u0440\u044B\u0439 \u041F\u0440\u043E\u0441\u043C\u043E\u0442\u0440",
          AssistiveMML: "\u0412\u0441\u043F\u043E\u043C\u043E\u0433\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 MathML",
          InTabOrder: "\u0412\u043A\u043B\u044E\u0447\u0430\u0442\u044C \u0432 \u043F\u043E\u0440\u044F\u0434\u043A\u0435 \u0442\u0430\u0431\u0443\u043B\u044F\u0446\u0438\u0438"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ru/MathMenu.js");
