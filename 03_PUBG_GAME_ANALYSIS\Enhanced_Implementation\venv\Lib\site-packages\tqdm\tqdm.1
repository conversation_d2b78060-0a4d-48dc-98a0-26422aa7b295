.\" Automatically generated by Pandoc 1.19.2
.\"
.TH "TQDM" "1" "2015\-2021" "tqdm User Manuals" ""
.hy
.SH NAME
.PP
tqdm \- fast, extensible progress bar for Python and CLI
.SH SYNOPSIS
.PP
tqdm [\f[I]options\f[]]
.SH DESCRIPTION
.PP
See <https://github.com/tqdm/tqdm>.
Can be used as a pipe:
.IP
.nf
\f[C]
$\ #\ count\ lines\ of\ code
$\ cat\ *.py\ |\ tqdm\ |\ wc\ \-l
327it\ [00:00,\ 981773.38it/s]
327

$\ #\ find\ all\ files
$\ find\ .\ \-name\ "*.py"\ |\ tqdm\ |\ wc\ \-l
432it\ [00:00,\ 833842.30it/s]
432

#\ ...\ and\ more\ info
$\ find\ .\ \-name\ \[aq]*.py\[aq]\ \-exec\ wc\ \-l\ \\{}\ \\;\ \\
\ \ |\ tqdm\ \-\-total\ 432\ \-\-unit\ files\ \-\-desc\ counting\ \\
\ \ |\ awk\ \[aq]{\ sum\ +=\ $1\ };\ END\ {\ print\ sum\ }\[aq]
counting:\ 100%|█████████|\ 432/432\ [00:00<00:00,\ 794361.83files/s]
131998
\f[]
.fi
.SH OPTIONS
.TP
.B \-h, \-\-help
Print this help and exit.
.RS
.RE
.TP
.B \-v, \-\-version
Print version and exit.
.RS
.RE
.TP
.B \-\-desc=\f[I]desc\f[]
str, optional.
Prefix for the progressbar.
.RS
.RE
.TP
.B \-\-total=\f[I]total\f[]
int or float, optional.
The number of expected iterations.
If unspecified, len(iterable) is used if possible.
If float("inf") or as a last resort, only basic progress statistics are
displayed (no ETA, no progressbar).
If \f[C]gui\f[] is True and this parameter needs subsequent updating,
specify an initial arbitrary large positive number, e.g.
9e9.
.RS
.RE
.TP
.B \-\-leave
bool, optional.
If [default: True], keeps all traces of the progressbar upon termination
of iteration.
If \f[C]None\f[], will leave only if \f[C]position\f[] is \f[C]0\f[].
.RS
.RE
.TP
.B \-\-ncols=\f[I]ncols\f[]
int, optional.
The width of the entire output message.
If specified, dynamically resizes the progressbar to stay within this
bound.
If unspecified, attempts to use environment width.
The fallback is a meter width of 10 and no limit for the counter and
statistics.
If 0, will not print any meter (only stats).
.RS
.RE
.TP
.B \-\-mininterval=\f[I]mininterval\f[]
float, optional.
Minimum progress display update interval [default: 0.1] seconds.
.RS
.RE
.TP
.B \-\-maxinterval=\f[I]maxinterval\f[]
float, optional.
Maximum progress display update interval [default: 10] seconds.
Automatically adjusts \f[C]miniters\f[] to correspond to
\f[C]mininterval\f[] after long display update lag.
Only works if \f[C]dynamic_miniters\f[] or monitor thread is enabled.
.RS
.RE
.TP
.B \-\-miniters=\f[I]miniters\f[]
int or float, optional.
Minimum progress display update interval, in iterations.
If 0 and \f[C]dynamic_miniters\f[], will automatically adjust to equal
\f[C]mininterval\f[] (more CPU efficient, good for tight loops).
If > 0, will skip display of specified number of iterations.
Tweak this and \f[C]mininterval\f[] to get very efficient loops.
If your progress is erratic with both fast and slow iterations (network,
skipping items, etc) you should set miniters=1.
.RS
.RE
.TP
.B \-\-ascii=\f[I]ascii\f[]
bool or str, optional.
If unspecified or False, use unicode (smooth blocks) to fill the meter.
The fallback is to use ASCII characters " 123456789#".
.RS
.RE
.TP
.B \-\-disable
bool, optional.
Whether to disable the entire progressbar wrapper [default: False].
If set to None, disable on non\-TTY.
.RS
.RE
.TP
.B \-\-unit=\f[I]unit\f[]
str, optional.
String that will be used to define the unit of each iteration [default:
it].
.RS
.RE
.TP
.B \-\-unit\-scale=\f[I]unit_scale\f[]
bool or int or float, optional.
If 1 or True, the number of iterations will be reduced/scaled
automatically and a metric prefix following the International System of
Units standard will be added (kilo, mega, etc.) [default: False].
If any other non\-zero number, will scale \f[C]total\f[] and \f[C]n\f[].
.RS
.RE
.TP
.B \-\-dynamic\-ncols
bool, optional.
If set, constantly alters \f[C]ncols\f[] and \f[C]nrows\f[] to the
environment (allowing for window resizes) [default: False].
.RS
.RE
.TP
.B \-\-smoothing=\f[I]smoothing\f[]
float, optional.
Exponential moving average smoothing factor for speed estimates (ignored
in GUI mode).
Ranges from 0 (average speed) to 1 (current/instantaneous speed)
[default: 0.3].
.RS
.RE
.TP
.B \-\-bar\-format=\f[I]bar_format\f[]
str, optional.
Specify a custom bar string formatting.
May impact performance.
[default: \[aq]{l_bar}{bar}{r_bar}\[aq]], where l_bar=\[aq]{desc}:
{percentage:3.0f}%|\[aq] and r_bar=\[aq]| {n_fmt}/{total_fmt}
[{elapsed}<{remaining}, \[aq] \[aq]{rate_fmt}{postfix}]\[aq] Possible
vars: l_bar, bar, r_bar, n, n_fmt, total, total_fmt, percentage,
elapsed, elapsed_s, ncols, nrows, desc, unit, rate, rate_fmt,
rate_noinv, rate_noinv_fmt, rate_inv, rate_inv_fmt, postfix,
unit_divisor, remaining, remaining_s, eta.
Note that a trailing ": " is automatically removed after {desc} if the
latter is empty.
.RS
.RE
.TP
.B \-\-initial=\f[I]initial\f[]
int or float, optional.
The initial counter value.
Useful when restarting a progress bar [default: 0].
If using float, consider specifying \f[C]{n:.3f}\f[] or similar in
\f[C]bar_format\f[], or specifying \f[C]unit_scale\f[].
.RS
.RE
.TP
.B \-\-position=\f[I]position\f[]
int, optional.
Specify the line offset to print this bar (starting from 0) Automatic if
unspecified.
Useful to manage multiple bars at once (eg, from threads).
.RS
.RE
.TP
.B \-\-postfix=\f[I]postfix\f[]
dict or *, optional.
Specify additional stats to display at the end of the bar.
Calls \f[C]set_postfix(**postfix)\f[] if possible (dict).
.RS
.RE
.TP
.B \-\-unit\-divisor=\f[I]unit_divisor\f[]
float, optional.
[default: 1000], ignored unless \f[C]unit_scale\f[] is True.
.RS
.RE
.TP
.B \-\-write\-bytes
bool, optional.
Whether to write bytes.
If (default: False) will write unicode.
.RS
.RE
.TP
.B \-\-lock\-args=\f[I]lock_args\f[]
tuple, optional.
Passed to \f[C]refresh\f[] for intermediate output (initialisation,
iterating, and updating).
.RS
.RE
.TP
.B \-\-nrows=\f[I]nrows\f[]
int, optional.
The screen height.
If specified, hides nested bars outside this bound.
If unspecified, attempts to use environment height.
The fallback is 20.
.RS
.RE
.TP
.B \-\-colour=\f[I]colour\f[]
str, optional.
Bar colour (e.g.
\[aq]green\[aq], \[aq]#00ff00\[aq]).
.RS
.RE
.TP
.B \-\-delay=\f[I]delay\f[]
float, optional.
Don\[aq]t display until [default: 0] seconds have elapsed.
.RS
.RE
.TP
.B \-\-delim=\f[I]delim\f[]
chr, optional.
Delimiting character [default: \[aq]\\n\[aq]].
Use \[aq]\\0\[aq] for null.
N.B.: on Windows systems, Python converts \[aq]\\n\[aq] to
\[aq]\\r\\n\[aq].
.RS
.RE
.TP
.B \-\-buf\-size=\f[I]buf_size\f[]
int, optional.
String buffer size in bytes [default: 256] used when \f[C]delim\f[] is
specified.
.RS
.RE
.TP
.B \-\-bytes
bool, optional.
If true, will count bytes, ignore \f[C]delim\f[], and default
\f[C]unit_scale\f[] to True, \f[C]unit_divisor\f[] to 1024, and
\f[C]unit\f[] to \[aq]B\[aq].
.RS
.RE
.TP
.B \-\-tee
bool, optional.
If true, passes \f[C]stdin\f[] to both \f[C]stderr\f[] and
\f[C]stdout\f[].
.RS
.RE
.TP
.B \-\-update
bool, optional.
If true, will treat input as newly elapsed iterations, i.e.
numbers to pass to \f[C]update()\f[].
Note that this is slow (~2e5 it/s) since every input must be decoded as
a number.
.RS
.RE
.TP
.B \-\-update\-to
bool, optional.
If true, will treat input as total elapsed iterations, i.e.
numbers to assign to \f[C]self.n\f[].
Note that this is slow (~2e5 it/s) since every input must be decoded as
a number.
.RS
.RE
.TP
.B \-\-null
bool, optional.
If true, will discard input (no stdout).
.RS
.RE
.TP
.B \-\-manpath=\f[I]manpath\f[]
str, optional.
Directory in which to install tqdm man pages.
.RS
.RE
.TP
.B \-\-comppath=\f[I]comppath\f[]
str, optional.
Directory in which to place tqdm completion.
.RS
.RE
.TP
.B \-\-log=\f[I]log\f[]
str, optional.
CRITICAL|FATAL|ERROR|WARN(ING)|[default: \[aq]INFO\[aq]]|DEBUG|NOTSET.
.RS
.RE
.SH AUTHORS
tqdm developers <https://github.com/tqdm>.
