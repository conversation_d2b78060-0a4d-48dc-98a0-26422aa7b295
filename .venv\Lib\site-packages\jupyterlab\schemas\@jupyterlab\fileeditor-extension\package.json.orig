{"name": "@jupyterlab/fileeditor-extension", "version": "4.4.3", "description": "<PERSON><PERSON><PERSON><PERSON>ab - Editor Widget Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "schema/*.json", "style/**/*.css", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -b --watch"}, "dependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/search": "^6.5.10", "@jupyterlab/application": "^4.4.3", "@jupyterlab/apputils": "^4.5.3", "@jupyterlab/codeeditor": "^4.4.3", "@jupyterlab/codemirror": "^4.4.3", "@jupyterlab/completer": "^4.4.3", "@jupyterlab/console": "^4.4.3", "@jupyterlab/coreutils": "^6.4.3", "@jupyterlab/docregistry": "^4.4.3", "@jupyterlab/documentsearch": "^4.4.3", "@jupyterlab/filebrowser": "^4.4.3", "@jupyterlab/fileeditor": "^4.4.3", "@jupyterlab/launcher": "^4.4.3", "@jupyterlab/lsp": "^4.4.3", "@jupyterlab/mainmenu": "^4.4.3", "@jupyterlab/observables": "^5.4.3", "@jupyterlab/rendermime-interfaces": "^3.12.3", "@jupyterlab/services": "^7.4.3", "@jupyterlab/settingregistry": "^4.4.3", "@jupyterlab/statusbar": "^4.4.3", "@jupyterlab/toc": "^6.4.3", "@jupyterlab/translation": "^4.4.3", "@jupyterlab/ui-components": "^4.4.3", "@lumino/algorithm": "^2.0.3", "@lumino/commands": "^2.3.2", "@lumino/coreutils": "^2.2.1", "@lumino/disposable": "^2.1.4", "@lumino/widgets": "^2.7.1"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}