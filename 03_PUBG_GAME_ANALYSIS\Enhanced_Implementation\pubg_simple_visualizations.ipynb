{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PUBG Simple Visualizations - NO External HTML Files\n", "\n", "## 🎯 Purpose\n", "This notebook creates **SIMPLE** visualizations for PUBG data that display **INLINE ONLY**.\n", "- **NO external HTML files created**\n", "- **Simple matplotlib plots**\n", "- **Basic plotly with inline display**\n", "- **Small column layouts for easy understanding**\n", "\n", "## 📊 Strategy\n", "- Use matplotlib for most visualizations (guaranteed inline)\n", "- Use plotly only with specific inline configuration\n", "- Focus on simple, effective charts\n", "- Small 2x2 layouts for easy comprehension"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ESSENTIAL IMPORTS AND SETUP\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# FORCE MATPLOTLIB INLINE (NO EXTERNAL FILES)\n", "%matplotlib inline\n", "plt.style.use('default')\n", "\n", "print(\"✅ Basic imports completed\")\n", "print(f\"✅ Matplotlib backend: {plt.get_backend()}\")\n", "print(\"🎯 Ready for INLINE visualizations only!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# LOAD PUBG DATA (EFFICIENT SAMPLING)\n", "print(\"📊 LOADING PUBG DATA...\")\n", "print(\"=\" * 40)\n", "\n", "# Try to find the PUBG CSV file\n", "possible_paths = [\n", "    '../data/pubg.csv',\n", "    '../../data/pubg.csv', \n", "    'data/pubg.csv',\n", "    'pubg.csv'\n", "]\n", "\n", "pubg_data = None\n", "data_path = None\n", "\n", "for path in possible_paths:\n", "    if Path(path).exists():\n", "        data_path = path\n", "        print(f\"📁 Found PUBG data at: {path}\")\n", "        break\n", "\n", "if data_path:\n", "    try:\n", "        # Load a sample for faster processing\n", "        print(\"📊 Loading sample of PUBG data...\")\n", "        pubg_data = pd.read_csv(data_path, nrows=50000)  # Load first 50K rows\n", "        print(f\"✅ Loaded {len(pubg_data):,} rows\")\n", "        print(f\"📋 Columns: {list(pubg_data.columns)}\")\n", "        print(f\"💾 Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "        \n", "        # Basic data info\n", "        print(f\"\\n📊 Data shape: {pubg_data.shape}\")\n", "        print(f\"🎯 Key columns available:\")\n", "        key_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc']\n", "        for col in key_cols:\n", "            if col in pubg_data.columns:\n", "                print(f\"  ✅ {col}: {pubg_data[col].dtype}\")\n", "            else:\n", "                print(f\"  ❌ {col}: Not found\")\n", "                \n", "    except Exception as e:\n", "        print(f\"❌ Error loading data: {e}\")\n", "        pubg_data = None\n", "else:\n", "    print(\"❌ PUBG CSV file not found in expected locations\")\n", "    print(\"📝 Please ensure pubg.csv is in one of these locations:\")\n", "    for path in possible_paths:\n", "        print(f\"   - {path}\")\n", "    \n", "    # Create sample data for demonstration\n", "    print(\"\\n🔧 Creating sample data for demonstration...\")\n", "    np.random.seed(42)\n", "    pubg_data = pd.DataFrame({\n", "        'kills': np.random.poisson(2, 10000),\n", "        'damageDealt': np.random.gamma(2, 100, 10000),\n", "        'walkDistance': np.random.gamma(3, 500, 10000),\n", "        'winPlacePerc': np.random.beta(2, 5, 10000)\n", "    })\n", "    print(f\"✅ Created sample data: {pubg_data.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Simple Matplotlib Visualizations (Small Columns)\n", "\n", "### Using 2x2 layout for easy understanding - NO external files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PUBG ANALYSIS - 2x2 MATPLOTLIB GRID (SIMPLE & INLINE)\n", "print(\"🎮 CREATING PUBG SIMPLE VISUALIZATIONS...\")\n", "print(\"📊 Using 2x2 layout - NO external files\")\n", "print(\"=\" * 50)\n", "\n", "if pubg_data is not None:\n", "    # Create 2x2 subplot layout\n", "    fig, axes = plt.subplots(2, 2, figsize=(14, 10))\n", "    fig.suptitle('PUBG Player Performance Analysis - Simple Visualizations', \n", "                 fontsize=16, fontweight='bold', y=0.98)\n", "    \n", "    # 1. KILLS DISTRIBUTION (Top-left)\n", "    if 'kills' in pubg_data.columns:\n", "        axes[0, 0].hist(pubg_data['kills'], bins=range(0, min(pubg_data['kills'].max() + 2, 21)), \n", "                       alpha=0.7, color='skyblue', edgecolor='black')\n", "        axes[0, 0].axvline(pubg_data['kills'].mean(), color='red', linestyle='--', linewidth=2, \n", "                          label=f'Mean: {pubg_data[\"kills\"].mean():.1f}')\n", "        axes[0, 0].set_title('Kills Distribution', fontweight='bold', fontsize=12)\n", "        axes[0, 0].set_xlabel('Number of Kills')\n", "        axes[0, 0].set_ylabel('Frequency')\n", "        axes[0, 0].legend()\n", "        axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. DAMAGE DEALT DISTRIBUTION (Top-right)\n", "    if 'damageDealt' in pubg_data.columns:\n", "        axes[0, 1].hist(pubg_data['damageDealt'], bins=40, alpha=0.7, color='lightcoral', \n", "                       edgecolor='black')\n", "        axes[0, 1].axvline(pubg_data['damageDealt'].mean(), color='red', linestyle='--', linewidth=2, \n", "                          label=f'Mean: {pubg_data[\"damageDealt\"].mean():.0f}')\n", "        axes[0, 1].set_title('Damage Dealt Distribution', fontweight='bold', fontsize=12)\n", "        axes[0, 1].set_xlabel('Damage Dealt')\n", "        axes[0, 1].set_ylabel('Frequency')\n", "        axes[0, 1].legend()\n", "        axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 3. WALK DISTANCE vs KILLS (Bottom-left)\n", "    if 'walkDistance' in pubg_data.columns and 'kills' in pubg_data.columns:\n", "        # Sample for better performance\n", "        sample_size = min(3000, len(pubg_data))\n", "        sample_data = pubg_data.sample(n=sample_size, random_state=42)\n", "        \n", "        scatter = axes[1, 0].scatter(sample_data['walkDistance'], sample_data['kills'], \n", "                                   alpha=0.6, c=sample_data['kills'], cmap='viridis', s=15)\n", "        axes[1, 0].set_title('Walk Distance vs Kills', fontweight='bold', fontsize=12)\n", "        axes[1, 0].set_xlabel('Walk Distance (meters)')\n", "        axes[1, 0].set_ylabel('Number of Kills')\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "        plt.colorbar(scatter, ax=axes[1, 0], label='Kills')\n", "    \n", "    # 4. WIN PLACEMENT DISTRIBUTION (Bottom-right)\n", "    if 'winPlacePerc' in pubg_data.columns:\n", "        axes[1, 1].hist(pubg_data['winPlacePerc'], bins=40, alpha=0.7, color='lightgreen', \n", "                       edgecolor='black')\n", "        axes[1, 1].axvline(pubg_data['winPlacePerc'].mean(), color='red', linestyle='--', linewidth=2, \n", "                          label=f'Mean: {pubg_data[\"winPlacePerc\"].mean():.3f}')\n", "        axes[1, 1].set_title('Win Placement Percentile', fontweight='bold', fontsize=12)\n", "        axes[1, 1].set_xlabel('Win Placement Percentile')\n", "        axes[1, 1].set_ylabel('Frequency')\n", "        axes[1, 1].legend()\n", "        axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()  # This will display inline only\n", "    \n", "    print(\"✅ Simple matplotlib visualizations completed!\")\n", "    print(\"📊 All plots displayed inline - NO external files created\")\n", "    \n", "else:\n", "    print(\"❌ No data available for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 Additional Simple Visualizations\n", "\n", "### More PUBG insights with simple matplotlib - Small columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ADDITIONAL PUBG INSIGHTS - 1x3 LAYOUT\n", "print(\"🎮 CREATING ADDITIONAL PUBG INSIGHTS...\")\n", "print(\"📊 Using 1x3 layout for detailed analysis\")\n", "print(\"=\" * 50)\n", "\n", "if pubg_data is not None:\n", "    # Create 1x3 subplot layout\n", "    fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "    fig.suptitle('PUBG Player Behavior Analysis', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. <PERSON><PERSON><PERSON> vs DAMAGE CORRELATION\n", "    if 'kills' in pubg_data.columns and 'damageDealt' in pubg_data.columns:\n", "        sample_size = min(2000, len(pubg_data))\n", "        sample_data = pubg_data.sample(n=sample_size, random_state=42)\n", "        \n", "        axes[0].scatter(sample_data['kills'], sample_data['damageDealt'], \n", "                       alpha=0.6, color='purple', s=20)\n", "        axes[0].set_title('Kills vs Damage Correlation', fontweight='bold')\n", "        axes[0].set_xlabel('Number of Kills')\n", "        axes[0].set_ylabel('Damage Dealt')\n", "        axes[0].grid(True, alpha=0.3)\n", "        \n", "        # Add correlation coefficient\n", "        corr = sample_data['kills'].corr(sample_data['damageDealt'])\n", "        axes[0].text(0.05, 0.95, f'Correlation: {corr:.3f}', \n", "                    transform=axes[0].transAxes, fontsize=10, \n", "                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "    \n", "    # 2. PERFORMANCE CATEGORIES\n", "    if 'kills' in pubg_data.columns:\n", "        # Create performance categories\n", "        kill_categories = ['0 Kills', '1-2 Kills', '3-5 Kills', '6+ Kills']\n", "        kill_counts = [\n", "            len(pubg_data[pubg_data['kills'] == 0]),\n", "            len(pubg_data[(pubg_data['kills'] >= 1) & (pubg_data['kills'] <= 2)]),\n", "            len(pubg_data[(pubg_data['kills'] >= 3) & (pubg_data['kills'] <= 5)]),\n", "            len(pubg_data[pubg_data['kills'] >= 6])\n", "        ]\n", "        \n", "        colors = ['lightcoral', 'skyblue', 'lightgreen', 'gold']\n", "        bars = axes[1].bar(kill_categories, kill_counts, color=colors, alpha=0.7, edgecolor='black')\n", "        axes[1].set_title('Player Performance Categories', fontweight='bold')\n", "        axes[1].set_xlabel('Kill Categories')\n", "        axes[1].set_ylabel('Number of Players')\n", "        axes[1].grid(True, alpha=0.3, axis='y')\n", "        \n", "        # Add value labels on bars\n", "        for bar, count in zip(bars, kill_counts):\n", "            axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(kill_counts)*0.01,\n", "                        f'{count:,}', ha='center', va='bottom', fontsize=9)\n", "    \n", "    # 3. WIN PLACEMENT BY KILLS\n", "    if 'kills' in pubg_data.columns and 'winPlacePerc' in pubg_data.columns:\n", "        # Box plot of win placement by kill groups\n", "        kill_groups = []\n", "        win_groups = []\n", "        \n", "        for kills in range(0, min(8, pubg_data['kills'].max() + 1)):\n", "            group_data = pubg_data[pubg_data['kills'] == kills]['winPlacePerc']\n", "            if len(group_data) > 10:  # Only include groups with enough data\n", "                kill_groups.append(f'{kills} Kills')\n", "                win_groups.append(group_data.values)\n", "        \n", "        if win_groups:\n", "            bp = axes[2].boxplot(win_groups, labels=kill_groups, patch_artist=True)\n", "            \n", "            # Color the boxes\n", "            colors = plt.cm.viridis(np.linspace(0, 1, len(bp['boxes'])))\n", "            for patch, color in zip(bp['boxes'], colors):\n", "                patch.set_facecolor(color)\n", "                patch.set_alpha(0.7)\n", "            \n", "            axes[2].set_title('Win Placement by Kills', fontweight='bold')\n", "            axes[2].set_xlabel('Kill Groups')\n", "            axes[2].set_ylabel('Win Placement Percentile')\n", "            axes[2].grid(True, alpha=0.3)\n", "            axes[2].tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()  # Inline display only\n", "    \n", "    print(\"✅ Additional insights completed!\")\n", "    print(\"📊 All plots displayed inline - NO external files\")\n", "    \n", "else:\n", "    print(\"❌ No data available for additional insights\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Simple Statistics Summary\n", "\n", "### Key PUBG statistics in easy-to-read format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PUBG STATISTICS SUMMARY\n", "print(\"📊 PUBG DATASET STATISTICS SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "if pubg_data is not None:\n", "    print(f\"📋 Dataset Overview:\")\n", "    print(f\"   Total Players: {len(pubg_data):,}\")\n", "    print(f\"   Total Columns: {len(pubg_data.columns)}\")\n", "    print(f\"   Memory Usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    \n", "    # Key statistics for important columns\n", "    key_columns = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc']\n", "    \n", "    print(f\"\\n🎯 Key Performance Metrics:\")\n", "    for col in key_columns:\n", "        if col in pubg_data.columns:\n", "            print(f\"\\n📈 {col.upper()}:\")\n", "            print(f\"   Mean: {pubg_data[col].mean():.3f}\")\n", "            print(f\"   Median: {pubg_data[col].median():.3f}\")\n", "            print(f\"   Std Dev: {pubg_data[col].std():.3f}\")\n", "            print(f\"   Min: {pubg_data[col].min():.3f}\")\n", "            print(f\"   Max: {pubg_data[col].max():.3f}\")\n", "    \n", "    # Performance insights\n", "    if 'kills' in pubg_data.columns:\n", "        zero_kills = len(pubg_data[pubg_data['kills'] == 0])\n", "        high_kills = len(pubg_data[pubg_data['kills'] >= 5])\n", "        \n", "        print(f\"\\n🎮 Player Performance Insights:\")\n", "        print(f\"   Players with 0 kills: {zero_kills:,} ({zero_kills/len(pubg_data)*100:.1f}%)\")\n", "        print(f\"   Players with 5+ kills: {high_kills:,} ({high_kills/len(pubg_data)*100:.1f}%)\")\n", "        print(f\"   Average kills per player: {pubg_data['kills'].mean():.2f}\")\n", "    \n", "    if 'winPlacePerc' in pubg_data.columns:\n", "        top_10_percent = len(pubg_data[pubg_data['winPlacePerc'] >= 0.9])\n", "        bottom_50_percent = len(pubg_data[pubg_data['winPlacePerc'] <= 0.5])\n", "        \n", "        print(f\"\\n🏆 Win Performance:\")\n", "        print(f\"   Top 10% finishers: {top_10_percent:,} ({top_10_percent/len(pubg_data)*100:.1f}%)\")\n", "        print(f\"   Bottom 50% finishers: {bottom_50_percent:,} ({bottom_50_percent/len(pubg_data)*100:.1f}%)\")\n", "        print(f\"   Average win placement: {pubg_data['winPlacePerc'].mean():.3f}\")\n", "    \n", "    print(f\"\\n✅ SUMMARY COMPLETE\")\n", "    print(f\"📊 All visualizations above are displayed inline\")\n", "    print(f\"🎯 NO external HTML files were created\")\n", "    \n", "else:\n", "    print(\"❌ No data available for statistics\")\n", "\n", "print(f\"\\n🎉 SIMPLE PUBG VISUALIZATION ANALYSIS COMPLETE!\")\n", "print(f\"✅ All plots display inline in notebook cells\")\n", "print(f\"✅ No external files created\")\n", "print(f\"✅ Easy-to-understand small column layouts\")\n", "print(f\"✅ Professional matplotlib styling\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}