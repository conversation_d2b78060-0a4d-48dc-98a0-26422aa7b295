# ESSENTIAL IMPORTS AND SETUP
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# FORCE MATPLOTLIB INLINE (NO EXTERNAL FILES)
%matplotlib inline
plt.style.use('default')

print("✅ Basic imports completed")
print(f"✅ Matplotlib backend: {plt.get_backend()}")
print("🎯 Ready for INLINE visualizations only!")

# LOAD PUBG DATA (EFFICIENT SAMPLING)
print("📊 LOADING PUBG DATA...")
print("=" * 40)

# Try to find the PUBG CSV file
possible_paths = [
    '../data/pubg.csv',
    '../../data/pubg.csv', 
    'data/pubg.csv',
    'pubg.csv'
]

pubg_data = None
data_path = None

for path in possible_paths:
    if Path(path).exists():
        data_path = path
        print(f"📁 Found PUBG data at: {path}")
        break

if data_path:
    try:
        # Check file size first
        file_size_mb = Path(data_path).stat().st_size / (1024 * 1024)
        print(f"📁 File size: {file_size_mb:.1f} MB")
        
        # Adjust sample size based on file size
        if file_size_mb > 500:  # Large file (>500MB)
            sample_rows = 30000
            print(f"🔧 Large file detected - loading {sample_rows:,} rows for performance")
        elif file_size_mb > 100:  # Medium file (>100MB)
            sample_rows = 75000
            print(f"📊 Medium file - loading {sample_rows:,} rows")
        else:  # Small file
            sample_rows = None
            print(f"📊 Loading full dataset")
        
        # Load data with appropriate sampling
        if sample_rows:
            pubg_data = pd.read_csv(data_path, nrows=sample_rows)
            print(f"✅ Loaded sample: {len(pubg_data):,} rows (prevents external HTML files)")
        else:
            pubg_data = pd.read_csv(data_path)
            print(f"✅ Loaded full dataset: {len(pubg_data):,} rows")
        print(f"✅ Loaded {len(pubg_data):,} rows")
        print(f"📋 Columns: {list(pubg_data.columns)}")
        print(f"💾 Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        # Basic data info
        print(f"\n📊 Data shape: {pubg_data.shape}")
        print(f"🎯 Key columns available:")
        key_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc']
        for col in key_cols:
            if col in pubg_data.columns:
                print(f"  ✅ {col}: {pubg_data[col].dtype}")
            else:
                print(f"  ❌ {col}: Not found")
                
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        pubg_data = None
else:
    print("❌ PUBG CSV file not found in expected locations")
    print("📝 Please ensure pubg.csv is in one of these locations:")
    for path in possible_paths:
        print(f"   - {path}")
    
    # Create sample data for demonstration
    print("\n🔧 Creating sample data for demonstration...")
    np.random.seed(42)
    pubg_data = pd.DataFrame({
        'kills': np.random.poisson(2, 10000),
        'damageDealt': np.random.gamma(2, 100, 10000),
        'walkDistance': np.random.gamma(3, 500, 10000),
        'winPlacePerc': np.random.beta(2, 5, 10000)
    })
    print(f"✅ Created sample data: {pubg_data.shape}")

# LARGE FILE HANDLING EXPLANATION
print("🔧 LARGE FILE OPTIMIZATION STRATEGY")
print("=" * 50)

if pubg_data is not None:
    current_memory = pubg_data.memory_usage(deep=True).sum() / 1024**2
    print(f"📊 Current dataset memory usage: {current_memory:.2f} MB")
    print(f"📋 Current dataset shape: {pubg_data.shape}")
    
    print(f"\n🎯 WHY LARGE FILES CREATE EXTERNAL HTML:")
    print(f"   • Browser memory limits (~100-200MB for inline plots)")
    print(f"   • Plotly auto-exports large plots to HTML files")
    print(f"   • Jupyter notebook inline display constraints")
    print(f"   • Interactive elements require more memory")
    
    print(f"\n✅ OUR OPTIMIZATION STRATEGIES:")
    print(f"   • Smart sampling: Use subset of data for visualizations")
    print(f"   • Matplotlib focus: Better memory management than plotly")
    print(f"   • Reduced plot complexity: Simpler = more reliable inline display")
    print(f"   • Memory monitoring: Track usage to prevent overload")
    
    # Demonstrate sampling strategy
    if len(pubg_data) > 10000:
        print(f"\n📊 SAMPLING DEMONSTRATION:")
        original_size = len(pubg_data)
        sample_sizes = [1000, 5000, 10000, 20000]
        
        for size in sample_sizes:
            if size <= original_size:
                sample = pubg_data.sample(n=size, random_state=42)
                sample_memory = sample.memory_usage(deep=True).sum() / 1024**2
                reduction = (1 - size/original_size) * 100
                print(f"   Sample {size:,} rows: {sample_memory:.1f} MB ({reduction:.1f}% reduction)")
        
        print(f"\n💡 RECOMMENDATION:")
        if current_memory > 50:
            recommended_sample = min(20000, len(pubg_data))
            print(f"   Use sample of {recommended_sample:,} rows for reliable inline display")
            print(f"   This prevents external HTML file creation")
        else:
            print(f"   Current dataset size is optimal for inline display")
    
    print(f"\n🎯 RESULT: All visualizations below will display inline!")
    
else:
    print("❌ No data loaded for optimization analysis")

# PUBG ANALYSIS - 2x2 MATPLOTLIB GRID (SIMPLE & INLINE)
print("🎮 CREATING PUBG SIMPLE VISUALIZATIONS...")
print("📊 Using 2x2 layout - NO external files")
print("=" * 50)

if pubg_data is not None:
    # Create 2x2 subplot layout
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    fig.suptitle('PUBG Player Performance Analysis - Simple Visualizations', 
                 fontsize=16, fontweight='bold', y=0.98)
    
    # 1. KILLS DISTRIBUTION (Top-left)
    if 'kills' in pubg_data.columns:
        axes[0, 0].hist(pubg_data['kills'], bins=range(0, min(pubg_data['kills'].max() + 2, 21)), 
                       alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].axvline(pubg_data['kills'].mean(), color='red', linestyle='--', linewidth=2, 
                          label=f'Mean: {pubg_data["kills"].mean():.1f}')
        axes[0, 0].set_title('Kills Distribution', fontweight='bold', fontsize=12)
        axes[0, 0].set_xlabel('Number of Kills')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
    
    # 2. DAMAGE DEALT DISTRIBUTION (Top-right)
    if 'damageDealt' in pubg_data.columns:
        axes[0, 1].hist(pubg_data['damageDealt'], bins=40, alpha=0.7, color='lightcoral', 
                       edgecolor='black')
        axes[0, 1].axvline(pubg_data['damageDealt'].mean(), color='red', linestyle='--', linewidth=2, 
                          label=f'Mean: {pubg_data["damageDealt"].mean():.0f}')
        axes[0, 1].set_title('Damage Dealt Distribution', fontweight='bold', fontsize=12)
        axes[0, 1].set_xlabel('Damage Dealt')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
    
    # 3. WALK DISTANCE vs KILLS (Bottom-left)
    if 'walkDistance' in pubg_data.columns and 'kills' in pubg_data.columns:
        # Sample for better performance
        sample_size = min(3000, len(pubg_data))
        sample_data = pubg_data.sample(n=sample_size, random_state=42)
        
        scatter = axes[1, 0].scatter(sample_data['walkDistance'], sample_data['kills'], 
                                   alpha=0.6, c=sample_data['kills'], cmap='viridis', s=15)
        axes[1, 0].set_title('Walk Distance vs Kills', fontweight='bold', fontsize=12)
        axes[1, 0].set_xlabel('Walk Distance (meters)')
        axes[1, 0].set_ylabel('Number of Kills')
        axes[1, 0].grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=axes[1, 0], label='Kills')
    
    # 4. WIN PLACEMENT DISTRIBUTION (Bottom-right)
    if 'winPlacePerc' in pubg_data.columns:
        axes[1, 1].hist(pubg_data['winPlacePerc'], bins=40, alpha=0.7, color='lightgreen', 
                       edgecolor='black')
        axes[1, 1].axvline(pubg_data['winPlacePerc'].mean(), color='red', linestyle='--', linewidth=2, 
                          label=f'Mean: {pubg_data["winPlacePerc"].mean():.3f}')
        axes[1, 1].set_title('Win Placement Percentile', fontweight='bold', fontsize=12)
        axes[1, 1].set_xlabel('Win Placement Percentile')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()  # This will display inline only
    
    print("✅ Simple matplotlib visualizations completed!")
    print("📊 All plots displayed inline - NO external files created")
    
else:
    print("❌ No data available for visualization")

# ADDITIONAL PUBG INSIGHTS - 1x3 LAYOUT
print("🎮 CREATING ADDITIONAL PUBG INSIGHTS...")
print("📊 Using 1x3 layout for detailed analysis")
print("=" * 50)

if pubg_data is not None:
    # Create 1x3 subplot layout
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle('PUBG Player Behavior Analysis', fontsize=16, fontweight='bold')
    
    # 1. KILLS vs DAMAGE CORRELATION
    if 'kills' in pubg_data.columns and 'damageDealt' in pubg_data.columns:
        sample_size = min(2000, len(pubg_data))
        sample_data = pubg_data.sample(n=sample_size, random_state=42)
        
        axes[0].scatter(sample_data['kills'], sample_data['damageDealt'], 
                       alpha=0.6, color='purple', s=20)
        axes[0].set_title('Kills vs Damage Correlation', fontweight='bold')
        axes[0].set_xlabel('Number of Kills')
        axes[0].set_ylabel('Damage Dealt')
        axes[0].grid(True, alpha=0.3)
        
        # Add correlation coefficient
        corr = sample_data['kills'].corr(sample_data['damageDealt'])
        axes[0].text(0.05, 0.95, f'Correlation: {corr:.3f}', 
                    transform=axes[0].transAxes, fontsize=10, 
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 2. PERFORMANCE CATEGORIES
    if 'kills' in pubg_data.columns:
        # Create performance categories
        kill_categories = ['0 Kills', '1-2 Kills', '3-5 Kills', '6+ Kills']
        kill_counts = [
            len(pubg_data[pubg_data['kills'] == 0]),
            len(pubg_data[(pubg_data['kills'] >= 1) & (pubg_data['kills'] <= 2)]),
            len(pubg_data[(pubg_data['kills'] >= 3) & (pubg_data['kills'] <= 5)]),
            len(pubg_data[pubg_data['kills'] >= 6])
        ]
        
        colors = ['lightcoral', 'skyblue', 'lightgreen', 'gold']
        bars = axes[1].bar(kill_categories, kill_counts, color=colors, alpha=0.7, edgecolor='black')
        axes[1].set_title('Player Performance Categories', fontweight='bold')
        axes[1].set_xlabel('Kill Categories')
        axes[1].set_ylabel('Number of Players')
        axes[1].grid(True, alpha=0.3, axis='y')
        
        # Add value labels on bars
        for bar, count in zip(bars, kill_counts):
            axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(kill_counts)*0.01,
                        f'{count:,}', ha='center', va='bottom', fontsize=9)
    
    # 3. WIN PLACEMENT BY KILLS
    if 'kills' in pubg_data.columns and 'winPlacePerc' in pubg_data.columns:
        # Box plot of win placement by kill groups
        kill_groups = []
        win_groups = []
        
        for kills in range(0, min(8, pubg_data['kills'].max() + 1)):
            group_data = pubg_data[pubg_data['kills'] == kills]['winPlacePerc']
            if len(group_data) > 10:  # Only include groups with enough data
                kill_groups.append(f'{kills} Kills')
                win_groups.append(group_data.values)
        
        if win_groups:
            bp = axes[2].boxplot(win_groups, labels=kill_groups, patch_artist=True)
            
            # Color the boxes
            colors = plt.cm.viridis(np.linspace(0, 1, len(bp['boxes'])))
            for patch, color in zip(bp['boxes'], colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            axes[2].set_title('Win Placement by Kills', fontweight='bold')
            axes[2].set_xlabel('Kill Groups')
            axes[2].set_ylabel('Win Placement Percentile')
            axes[2].grid(True, alpha=0.3)
            axes[2].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()  # Inline display only
    
    print("✅ Additional insights completed!")
    print("📊 All plots displayed inline - NO external files")
    
else:
    print("❌ No data available for additional insights")

# PUBG STATISTICS SUMMARY
print("📊 PUBG DATASET STATISTICS SUMMARY")
print("=" * 50)

if pubg_data is not None:
    print(f"📋 Dataset Overview:")
    print(f"   Total Players: {len(pubg_data):,}")
    print(f"   Total Columns: {len(pubg_data.columns)}")
    print(f"   Memory Usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    
    # Key statistics for important columns
    key_columns = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc']
    
    print(f"\n🎯 Key Performance Metrics:")
    for col in key_columns:
        if col in pubg_data.columns:
            print(f"\n📈 {col.upper()}:")
            print(f"   Mean: {pubg_data[col].mean():.3f}")
            print(f"   Median: {pubg_data[col].median():.3f}")
            print(f"   Std Dev: {pubg_data[col].std():.3f}")
            print(f"   Min: {pubg_data[col].min():.3f}")
            print(f"   Max: {pubg_data[col].max():.3f}")
    
    # Performance insights
    if 'kills' in pubg_data.columns:
        zero_kills = len(pubg_data[pubg_data['kills'] == 0])
        high_kills = len(pubg_data[pubg_data['kills'] >= 5])
        
        print(f"\n🎮 Player Performance Insights:")
        print(f"   Players with 0 kills: {zero_kills:,} ({zero_kills/len(pubg_data)*100:.1f}%)")
        print(f"   Players with 5+ kills: {high_kills:,} ({high_kills/len(pubg_data)*100:.1f}%)")
        print(f"   Average kills per player: {pubg_data['kills'].mean():.2f}")
    
    if 'winPlacePerc' in pubg_data.columns:
        top_10_percent = len(pubg_data[pubg_data['winPlacePerc'] >= 0.9])
        bottom_50_percent = len(pubg_data[pubg_data['winPlacePerc'] <= 0.5])
        
        print(f"\n🏆 Win Performance:")
        print(f"   Top 10% finishers: {top_10_percent:,} ({top_10_percent/len(pubg_data)*100:.1f}%)")
        print(f"   Bottom 50% finishers: {bottom_50_percent:,} ({bottom_50_percent/len(pubg_data)*100:.1f}%)")
        print(f"   Average win placement: {pubg_data['winPlacePerc'].mean():.3f}")
    
    print(f"\n✅ SUMMARY COMPLETE")
    print(f"📊 All visualizations above are displayed inline")
    print(f"🎯 NO external HTML files were created")
    
else:
    print("❌ No data available for statistics")

print(f"\n🎉 SIMPLE PUBG VISUALIZATION ANALYSIS COMPLETE!")
print(f"✅ All plots display inline in notebook cells")
print(f"✅ No external files created")
print(f"✅ Easy-to-understand small column layouts")
print(f"✅ Professional matplotlib styling")