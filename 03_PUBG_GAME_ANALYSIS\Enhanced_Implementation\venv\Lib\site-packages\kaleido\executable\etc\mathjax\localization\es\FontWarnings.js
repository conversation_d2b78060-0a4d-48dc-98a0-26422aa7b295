/*************************************************************
 *
 *  MathJax/localization/es/FontWarnings.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("es","FontWarnings",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          webFont: "MathJax utiliza tipos de letra web para mostrar la notaci\u00F3n matem\u00E1tica de esta p\u00E1gina. Estos tardan un poco en descargarse, por lo que instalarlos en el equipo acelerar\u00E1 la renderizaci\u00F3n de la p\u00E1gina.",
          imageFonts: "MathJax est\u00E1 utilizando sus fuentes de imagen en lugar de las fuentes locales o basadas en web. Esto renderizar\u00E1 m\u00E1s lento que de costumbre, y las matem\u00E1ticas no podr\u00E1n imprimir a la resoluci\u00F3n completa de la impresora.",
          noFonts: "MathJax es incapaz de encontrar una fuente para mostrar sus matem\u00E1ticas y las fuentes de imagen no est\u00E1n disponibles, as\u00ED que usar\u00E1 caracteres Unicode gen\u00E9ricos con la esperanza de que el navegador ser\u00E1 capaz de mostrarlas. Es posible que algunos caracteres no se muestren correctamente, o no en absoluto.",
          webFonts: "La mayor\u00EDa de los navegadores modernos permiten fuentes para ser descargadas a trav\u00E9s de la web. Actualizar a una versi\u00F3n m\u00E1s reciente de tu navegador (o cambiar de navegador) podr\u00EDa mejorar la calidad de las matem\u00E1ticas en esta p\u00E1gina.",
          fonts: "MathJax puede utilizar las [fuentes STIX](%1) o las [fuentes TeX MathJax](%2). Descarga e instala una de esas fuentes para mejorar tu experiencia en MathJax.",
          STIXPage: "Esta p\u00E1gina est\u00E1 dise\u00F1ada para usar las [fuentes STIX](%1). Descarga e instala esas fuentes para mejorar tu experiencia en MathJax.",
          TeXPage: "Esta p\u00E1gina est\u00E1 dise\u00F1ada para usar las [fuentes MathJax TeX](%1). Descarga e instala esas fuentes para mejorar tu experiencia en MathJax."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/es/FontWarnings.js");
