/*************************************************************
 *
 *  MathJax/localization/zh-hans/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("zh-hans","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "\u6570\u5F0F\u663E\u793A\u5F62\u5F0F",
          MathMLcode: "MathML\u4EE3\u7801",
          OriginalMathML: "\u539F\u59CB\u7684MathML",
          TeXCommands: "TeX\u547D\u4EE4",
          AsciiMathInput: "AsciiMathML\u8F93\u5165",
          Original: "\u539F\u59CB\u683C\u5F0F",
          ErrorMessage: "\u9519\u8BEF\u4FE1\u606F",
          Annotation: "\u6CE8\u91CA",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "\u67AB\u53F6",
          ContentMathML: "MathML\u5185\u5BB9",
          OpenMath: "OpenMath",
          texHints: "\u5728MathML\u4E2D\u663E\u793ATeX\u63D0\u793A",
          Settings: "\u6570\u5B66\u8BBE\u7F6E",
          ZoomTrigger: "\u7F29\u653E\u89E6\u53D1",
          Hover: "\u6307\u9488\u60AC\u505C",
          Click: "\u70B9\u51FB",
          DoubleClick: "\u53CC\u51FB",
          NoZoom: "\u5E76\u65E0\u7F29\u653E",
          TriggerRequires: "\u89E6\u53D1\u9700\u8981\uFF1A",
          Option: "\u9009\u9879",
          Alt: "Alt\u952E",
          Command: "Command\u952E",
          Control: "Ctrl\u952E",
          Shift: "Shift\u952E",
          ZoomFactor: "\u53D8\u7126\u500D\u7387",
          Renderer: "\u6570\u5B66\u6E32\u67D3",
          MPHandles: "\u5141\u8BB8MathPlayer\u5904\u7406\uFF1A",
          MenuEvents: "\u83DC\u5355\u4E8B\u4EF6",
          MouseEvents: "\u9F20\u6807\u4E8B\u4EF6",
          MenuAndMouse: "\u9F20\u6807\u548C\u83DC\u5355\u4E8B\u4EF6",
          FontPrefs: "\u5B57\u4F53\u8BBE\u7F6E",
          ForHTMLCSS: "\u7528\u4E8EHTML-CSS\uFF1A",
          Auto: "\u81EA\u52A8",
          TeXLocal: "TeX\uFF08\u672C\u5730\uFF09",
          TeXWeb: "TeX\uFF08\u7F51\u9875\uFF09",
          TeXImage: "TeX\uFF08\u56FE\u7247\uFF09",
          STIXLocal: "STIX\uFF08\u672C\u5730\uFF09",
          STIXWeb: "STIX\uFF08web\uFF09",
          LatinModernWeb: "\u62C9\u4E01\u6469\u767B\uFF08web\uFF09",
          ContextMenu: "\u4E0A\u4E0B\u6587\u83DC\u5355",
          Browser: "\u6D4F\u89C8\u5668",
          Scale: "\u7F29\u653E\u6240\u6709\u6570\u5B66...",
          Discoverable: "\u60AC\u505C\u4EAE\u70B9",
          Locale: "\u8BED\u8A00",
          LoadLocale: "\u4ECEURL\u8F7D\u5165...",
          About: "\u5173\u4E8EMathJax",
          Help: "MathJax\u5E2E\u52A9",
          localTeXfonts: "\u4F7F\u7528\u672C\u5730TeX\u5B57\u4F53",
          webTeXfonts: "\u4F7F\u7528\u7F51\u9875TeX\u5B57\u4F53",
          imagefonts: "\u56FE\u50CF\u5B57\u4F53\u4F7F\u7528\u4E2D",
          localSTIXfonts: "\u4F7F\u7528\u672C\u5730STIX\u5B57\u4F53",
          webSVGfonts: "\u4F7F\u7528\u7F51\u9875SVG\u5B57\u4F53",
          genericfonts: "\u4F7F\u7528\u901A\u7528Unicode\u5B57\u4F53",
          wofforotffonts: "woff\u6216otf\u5B57\u4F53",
          eotffonts: "eot\u5B57\u4F53",
          svgfonts: "svg\u5B57\u4F53",
          WebkitNativeMMLWarning: "\u60A8\u7684\u6D4F\u89C8\u5668\u636E\u4FE1\u65E0\u6CD5\u652F\u6301MathML\u672C\u8EAB\uFF0C\u56E0\u6B64\u5207\u6362\u5230MathML\u8F93\u51FA\u5C06\u53EF\u80FD\u5BFC\u81F4\u9875\u9762\u4E0A\u7684\u6570\u5B66\u516C\u5F0F\u4E0D\u53EF\u8BFB\u3002",
          MSIENativeMMLWarning: "Internet Explorer\u9700\u8981MathPlayer\u63D2\u4EF6\u6765\u5904\u7406MathML\u8F93\u51FA\u3002",
          OperaNativeMMLWarning: "Opera\u5BF9MathML\u7684\u652F\u6301\u5341\u5206\u6709\u9650\uFF0C\u56E0\u6B64\u8F6C\u6362\u81F3MathML\u8F93\u51FA\u53EF\u80FD\u4F1A\u5BFC\u81F4\u4E00\u4E9B\u8868\u8FBE\u5F0F\u65E0\u6CD5\u6B63\u5E38\u6E32\u67D3\u3002",
          SafariNativeMMLWarning: "\u60A8\u7684\u6D4F\u89C8\u5668\u7684MathML\u4E0D\u652F\u6301\u6240\u6709MathJax\u7684\u529F\u80FD\uFF0C\u6709\u4E9B\u8868\u8FBE\u5F0F\u53EF\u80FD\u65E0\u6CD5\u6B63\u5E38\u5448\u73B0\u3002",
          FirefoxNativeMMLWarning: "\u60A8\u7684\u6D4F\u89C8\u5668\u7684MathML\u4E0D\u652F\u6301\u6240\u6709MathJax\u7684\u529F\u80FD\uFF0C\u6709\u4E9B\u8868\u8FBE\u5F0F\u53EF\u80FD\u65E0\u6CD5\u6B63\u5E38\u5448\u73B0\u3002",
          LoadURL: "\u4ECE\u6B64URL\u52A0\u8F7D\u7FFB\u8BD1\u6570\u636E\uFF1A",
          BadURL: "URL\u5FC5\u987B\u94FE\u81F3\u5B9A\u4E49MathJax\u7FFB\u8BD1\u6570\u636E\u7684JavaScript\u6587\u4EF6\u3002JavaScript\u7684\u6587\u4EF6\u540D\u5E94\u4EE5\u201C.js\u201D\u7ED3\u5C3E",
          BadData: "\u4ECE%1\u52A0\u8F7D\u7FFB\u8BD1\u6570\u636E\u5931\u8D25",
          SwitchAnyway: "\u4E00\u5B9A\u8981\u66F4\u6539\u6E32\u67D3\u5668\uFF1F\n\n\uFF08\u70B9OK\u66F4\u6539\uFF0C\u70B9\u53D6\u6D88\u5373\u4EE5\u5F53\u524D\u6E32\u67D3\u5668\u7EE7\u7EED\uFF09",
          ScaleMath: "\u6570\u5F0F\u7F29\u653E\u767E\u5206\u6BD4\uFF08\u5BF9\u6BD4\u4E8E\u65C1\u8FB9\u7684\u6587\u5B57\uFF09",
          NonZeroScale: "\u500D\u7387\u4E0D\u5E94\u4E3A\u96F6",
          PercentScale: "\u500D\u7387\u5E94\u8BE5\u662F\u4E00\u4E2A\u767E\u5206\u6BD4 \uFF08\u4F8B\u5982120%%\uFF09",
          IE8warning: "\u8FD9\u5C06\u5173\u95EDMathJax\u83DC\u5355\u4E0E\u7F29\u653E\u529F\u80FD\uFF0C\u4F46\u60A8\u53EF\u4EE5\u70B9\u51FBAlt-Click\u6216\u70B9\u51FB\u4E00\u4E2A\u8868\u8FBE\u5F0F\u6765\u8FDB\u5165MathJax\u83DC\u5355\u3002\n\n\u786E\u5B9E\u8981\u66F4\u6539MathPlayer\u8BBE\u7F6E\u5417\uFF1F",
          IE9warning: "MathJax\u5FEB\u6377\u83DC\u5355\u5C06\u5173\u95ED\uFF0C\u4F46\u60A8\u53EF\u4EE5\u6309Alt\u5E76\u70B9\u51FB\u8868\u8FBE\u5F0F\u6765\u547C\u51FA\u83DC\u5355\u3002",
          NoOriginalForm: "\u6CA1\u6709\u53EF\u7528\u7684\u539F\u59CB\u5F62\u5F0F",
          Close: "\u5173\u95ED",
          EqSource: "MathJax \u65B9\u7A0B\u6E90",
          AsanaMathWeb: "Asana Math (web)",
          GyrePagellaWeb: "Gyre Pagella (web)",
          GyreTermesWeb: "Gyre Termes (web)",
          NeoEulerWeb: "Neo Euler (web)",
          MSIESVGWarning: "SVG\u5728 IE9 \u4E4B\u524D\u7684 Internet Explorer \u6216\u4F7F\u7528 IE8 \u53CA\u4EE5\u4E0B\u67B6\u6784\u7684\u6D4F\u89C8\u5668\u4E2D\u4E0D\u6267\u884C\u3002\u8F6C\u6362SVG\u8F93\u51FA\u5C06\u5BFC\u81F4\u6570\u5B66\u516C\u5F0F\u4E0D\u80FD\u6B63\u5E38\u663E\u793A\u3002",
          CloseAboutDialog: "\u5173\u95ED\u6709\u5173MathJax\u5BF9\u8BDD",
          FastPreview: "\u5FEB\u901F\u9884\u89C8",
          AssistiveMML: "\u8F85\u52A9MathML",
          InTabOrder: "\u5305\u542B\u5728\u6807\u7B7E\u987A\u5E8F\u4E2D"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/zh-hans/MathMenu.js");
