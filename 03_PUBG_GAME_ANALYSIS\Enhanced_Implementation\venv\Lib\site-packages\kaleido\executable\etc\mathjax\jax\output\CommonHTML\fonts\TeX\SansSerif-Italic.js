/*************************************************************
 *
 *  MathJax/jax/output/CommonHTML/fonts/TeX/SansSerif-Italic.js
 *
 *  Copyright (c) 2015-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

(function (CHTML) {

var font = 'MathJax_SansSerif-Italic';

CHTML.FONTDATA.FONTS[font] = {
  className: CHTML.FONTDATA.familyName(font),
  centerline: 250, ascent: 750, descent: 250,
  style: 'italic',
    0x20: [0,0,250,0,0],               // SPACE
    0x21: [694,0,319,110,355],         // EXCLAMATION MARK
    0x22: [694,-471,500,133,472],      // QUOTATION MARK
    0x23: [694,194,833,87,851],        // NUMBER SIGN
    0x24: [750,56,500,56,565],         // DOLLAR SIGN
    0x25: [750,56,833,165,815],        // PERCENT SIGN
    0x26: [716,22,758,71,747],         // AMPERSAND
    0x27: [694,-471,278,190,335],      // APOSTROPHE
    0x28: [750,250,389,104,491],       // LEFT PARENTHESIS
    0x29: [750,250,389,2,390],         // RIGHT PARENTHESIS
    0x2A: [750,-306,500,156,568],      // ASTERISK
    0x2B: [583,83,778,108,775],        // PLUS SIGN
    0x2C: [98,125,278,63,209],         // COMMA
    0x2D: [259,-186,333,51,332],       // HYPHEN-MINUS
    0x2E: [98,0,278,90,209],           // FULL STOP
    0x2F: [750,250,500,6,600],         // SOLIDUS
    0x30: [678,22,500,88,549],         // DIGIT ZERO
    0x31: [678,0,500,88,451],          // DIGIT ONE
    0x32: [678,0,500,50,551],          // DIGIT TWO
    0x33: [678,22,500,56,544],         // DIGIT THREE
    0x34: [656,0,500,62,521],          // DIGIT FOUR
    0x35: [656,22,500,50,555],         // DIGIT FIVE
    0x36: [678,22,500,94,548],         // DIGIT SIX
    0x37: [656,11,500,143,596],        // DIGIT SEVEN
    0x38: [678,22,500,77,554],         // DIGIT EIGHT
    0x39: [677,22,500,77,545],         // DIGIT NINE
    0x3A: [444,0,278,90,282],          // COLON
    0x3B: [444,125,278,63,282],        // SEMICOLON
    0x3D: [370,-130,778,88,796],       // EQUALS SIGN
    0x3F: [704,0,472,173,536],         // QUESTION MARK
    0x40: [705,10,667,120,707],        // COMMERCIAL AT
    0x41: [694,0,667,28,638],          // LATIN CAPITAL LETTER A
    0x42: [694,0,667,90,696],          // LATIN CAPITAL LETTER B
    0x43: [705,10,639,124,719],        // LATIN CAPITAL LETTER C
    0x44: [694,0,722,88,747],          // LATIN CAPITAL LETTER D
    0x45: [691,0,597,86,688],          // LATIN CAPITAL LETTER E
    0x46: [691,0,569,86,673],          // LATIN CAPITAL LETTER F
    0x47: [705,11,667,125,730],        // LATIN CAPITAL LETTER G
    0x48: [694,0,708,86,768],          // LATIN CAPITAL LETTER H
    0x49: [694,0,278,87,338],          // LATIN CAPITAL LETTER I
    0x4A: [694,22,472,46,535],         // LATIN CAPITAL LETTER J
    0x4B: [694,0,694,88,785],          // LATIN CAPITAL LETTER K
    0x4C: [694,0,542,87,516],          // LATIN CAPITAL LETTER L
    0x4D: [694,0,875,92,929],          // LATIN CAPITAL LETTER M
    0x4E: [694,0,708,88,766],          // LATIN CAPITAL LETTER N
    0x4F: [716,22,736,118,763],        // LATIN CAPITAL LETTER O
    0x50: [694,0,639,88,690],          // LATIN CAPITAL LETTER P
    0x51: [716,125,736,118,763],       // LATIN CAPITAL LETTER Q
    0x52: [694,0,646,88,698],          // LATIN CAPITAL LETTER R
    0x53: [716,22,556,54,609],         // LATIN CAPITAL LETTER S
    0x54: [688,0,681,165,790],         // LATIN CAPITAL LETTER T
    0x55: [694,22,688,131,747],        // LATIN CAPITAL LETTER U
    0x56: [694,0,667,161,799],         // LATIN CAPITAL LETTER V
    0x57: [694,0,944,161,1076],        // LATIN CAPITAL LETTER W
    0x58: [694,0,667,14,758],          // LATIN CAPITAL LETTER X
    0x59: [694,0,667,151,810],         // LATIN CAPITAL LETTER Y
    0x5A: [694,0,611,55,702],          // LATIN CAPITAL LETTER Z
    0x5B: [750,250,289,41,425],        // LEFT SQUARE BRACKET
    0x5D: [750,250,289,-31,353],       // RIGHT SQUARE BRACKET
    0x5E: [694,-527,500,190,533],      // CIRCUMFLEX ACCENT
    0x5F: [-38,114,500,50,565],        // LOW LINE
    0x61: [461,10,481,61,473],         // LATIN SMALL LETTER A
    0x62: [694,11,517,75,539],         // LATIN SMALL LETTER B
    0x63: [460,11,444,75,499],         // LATIN SMALL LETTER C
    0x64: [694,10,517,73,588],         // LATIN SMALL LETTER D
    0x65: [460,11,444,71,472],         // LATIN SMALL LETTER E
    0x66: [705,0,306,94,494],          // LATIN SMALL LETTER F
    0x67: [455,206,500,12,568],        // LATIN SMALL LETTER G
    0x68: [694,0,517,73,513],          // LATIN SMALL LETTER H
    0x69: [680,0,239,74,315],          // LATIN SMALL LETTER I
    0x6A: [680,204,267,-96,336],       // LATIN SMALL LETTER J
    0x6B: [694,0,489,76,543],          // LATIN SMALL LETTER K
    0x6C: [694,0,239,74,311],          // LATIN SMALL LETTER L
    0x6D: [455,0,794,73,790],          // LATIN SMALL LETTER M
    0x6E: [454,0,517,73,513],          // LATIN SMALL LETTER N
    0x6F: [461,11,500,69,523],         // LATIN SMALL LETTER O
    0x70: [455,194,517,34,538],        // LATIN SMALL LETTER P
    0x71: [455,194,517,72,538],        // LATIN SMALL LETTER Q
    0x72: [455,0,342,74,424],          // LATIN SMALL LETTER R
    0x73: [461,11,383,35,436],         // LATIN SMALL LETTER S
    0x74: [571,11,361,97,410],         // LATIN SMALL LETTER T
    0x75: [444,10,517,90,537],         // LATIN SMALL LETTER U
    0x76: [444,0,461,108,540],         // LATIN SMALL LETTER V
    0x77: [444,0,683,108,762],         // LATIN SMALL LETTER W
    0x78: [444,0,461,1,537],           // LATIN SMALL LETTER X
    0x79: [444,205,461,1,540],         // LATIN SMALL LETTER Y
    0x7A: [444,0,435,28,494],          // LATIN SMALL LETTER Z
    0x7E: [327,-193,500,199,560],      // TILDE
    0xA0: [0,0,250,0,0],               // NO-BREAK SPACE
    0x131: [444,0,239,74,258],         // LATIN SMALL LETTER DOTLESS I
    0x237: [444,204,267,-96,286],      // LATIN SMALL LETTER DOTLESS J
    0x300: [694,-527,0,-270,-87],      // COMBINING GRAVE ACCENT
    0x301: [694,-527,0,-190,63],       // COMBINING ACUTE ACCENT
    0x302: [694,-527,0,-310,33],       // COMBINING CIRCUMFLEX ACCENT
    0x303: [677,-543,0,-301,60],       // COMBINING TILDE
    0x304: [631,-552,0,-314,64],       // COMBINING MACRON
    0x306: [694,-508,0,-284,73],       // COMBINING BREVE
    0x307: [680,-576,0,-180,-54],      // COMBINING DOT ABOVE
    0x308: [680,-582,0,-273,40],       // COMBINING DIAERESIS
    0x30A: [693,-527,0,-227,-2],       // COMBINING RING ABOVE
    0x30B: [694,-527,0,-287,63],       // COMBINING DOUBLE ACUTE ACCENT
    0x30C: [654,-487,0,-283,60],       // COMBINING CARON
    0x393: [691,0,542,87,646],         // GREEK CAPITAL LETTER GAMMA
    0x394: [694,0,833,42,790],         // GREEK CAPITAL LETTER DELTA
    0x398: [715,22,778,119,804],       // GREEK CAPITAL LETTER THETA
    0x39B: [694,0,611,28,582],         // GREEK CAPITAL LETTER LAMDA
    0x39E: [688,0,667,42,765],         // GREEK CAPITAL LETTER XI
    0x3A0: [691,0,708,86,768],         // GREEK CAPITAL LETTER PI
    0x3A3: [694,0,722,55,813],         // GREEK CAPITAL LETTER SIGMA
    0x3A5: [716,0,778,173,843],        // GREEK CAPITAL LETTER UPSILON
    0x3A6: [694,0,722,124,743],        // GREEK CAPITAL LETTER PHI
    0x3A8: [694,0,778,171,854],        // GREEK CAPITAL LETTER PSI
    0x3A9: [716,0,722,44,769],         // GREEK CAPITAL LETTER OMEGA
    0x2013: [312,-236,500,50,565],     // EN DASH
    0x2014: [312,-236,1000,50,1065],   // EM DASH
    0x2018: [694,-471,278,190,336],    // LEFT SINGLE QUOTATION MARK
    0x2019: [694,-471,278,190,335],    // RIGHT SINGLE QUOTATION MARK
    0x201C: [694,-471,500,274,614],    // LEFT DOUBLE QUOTATION MARK
    0x201D: [694,-471,500,133,472]     // RIGHT DOUBLE QUOTATION MARK
};

CHTML.fontLoaded("TeX/"+font.substr(8));

})(MathJax.OutputJax.CommonHTML);
