/*************************************************************
 *
 *  MathJax/localization/ko/ko.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ko",null,{
  menuTitle: "\uD55C\uAD6D\uC5B4",
  version: "2.7.5",
  isLoaded: true,
  domains: {
    "_": {
        version: "2.7.5",
        isLoaded: true,
        strings: {
          CookieConfig: "MathJax\uAC00 \uC2E4\uD589\uD560 \uCF54\uB4DC\uB97C \uD3EC\uD568\uD558\uB294 \uC0AC\uC6A9\uC790-\uC124\uC815 \uCFE0\uD0A4\uB97C \uCC3E\uC558\uC2B5\uB2C8\uB2E4. \uC2E4\uD589\uD558\uACA0\uC2B5\uB2C8\uAE4C?\n\n(\uCFE0\uD0A4\uB97C \uC2A4\uC2A4\uB85C \uC124\uC815\uD558\uC9C0 \uC54A\uC73C\uBA74 \uCDE8\uC18C\uB97C \uB20C\uB7EC\uC57C \uD569\uB2C8\uB2E4.)",
          MathProcessingError: "\uC218\uC2DD \uCC98\uB9AC \uC624\uB958",
          MathError: "\uC218\uC2DD \uC624\uB958",
          LoadFile: "%1\uC744(\uB97C) \uBD88\uB7EC\uC624\uB294 \uC911",
          Loading: "\uBD88\uB7EC\uC624\uB294 \uC911",
          LoadFailed: "\uD30C\uC77C\uC744 \uBD88\uB7EC\uC62C \uC218 \uC5C6\uC2B5\uB2C8\uB2E4: %1",
          ProcessMath: "\uC218\uC2DD \uCC98\uB9AC \uC911: %1%%",
          Processing: "\uCC98\uB9AC \uC911",
          TypesetMath: "\uC218\uC2DD \uC870\uD310 \uC911: %1%%",
          Typesetting: "\uC870\uD310 \uC911",
          MathJaxNotSupported: "\uC0AC\uC6A9\uD558\uB294 \uBE0C\uB77C\uC6B0\uC800\uB294 MathJax\uB97C \uC9C0\uC6D0\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4"
        }
    },
    "FontWarnings": {},
    "HTML-CSS": {},
    "HelpDialog": {},
    "MathML": {},
    "MathMenu": {},
    "TeX": {}
  },
  plural: function (n) {
      return 1; // other
    },
  number: function (n) {
      return n;
    }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ko/ko.js");
