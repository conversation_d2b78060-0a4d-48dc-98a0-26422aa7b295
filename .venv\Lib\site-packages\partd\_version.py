
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-05-06T14:50:19-0500",
 "dirty": false,
 "error": null,
 "full-revisionid": "829b539a7355f2740124fd3f9a70ca4356f981d2",
 "version": "1.4.2"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
