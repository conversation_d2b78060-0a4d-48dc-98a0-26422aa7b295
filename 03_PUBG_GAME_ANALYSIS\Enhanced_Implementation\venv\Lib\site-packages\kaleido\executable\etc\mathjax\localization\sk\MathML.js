/*************************************************************
 *
 *  MathJax/localization/sk/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("sk","MathML",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          BadMglyph: "Chybn\u00FD mglyph: %1",
          BadMglyphFont: "Nevhodn\u00E9 p\u00EDsmo: %1",
          MathPlayer: "MathJax nedok\u00E1zal spusti\u0165 MathPlayer.\n\nAk nie je MathPlayer nain\u0161talovan\u00FD, budete si ho musie\u0165 najprv nain\u0161talova\u0165.\nInak mo\u017Eno spustenie ovl\u00E1dac\u00EDch prvkov ActiveX br\u00E1ni va\u0161e bezpe\u010Dnostn\u00E9\nnastavenie. Kliknite v ponuke N\u00E1stroje na polo\u017Eku Mo\u017Enosti Internetu,\nvyberte z\u00E1lo\u017Eku Zabezpe\u010Denie a kliknite na tla\u010Didlo Vlastn\u00E1 \u00FArove\u0148.\nSkontrolujte, \u010Di s\u00FA povolen\u00E9 mo\u017Enosti \u201ESp\u00FA\u0161\u0165a\u0165 ovl\u00E1dacie prvky ActiveX\u201C\na \u201EChovanie skriptov a bin\u00E1rnych s\u00FAborov\u201C.\n\nMoment\u00E1lne uvid\u00EDte miesto vys\u00E1dzanej matematiky chybov\u00E9 hl\u00E1senia.",
          CantCreateXMLParser: "MathJax nem\u00F4\u017Ee vytvori\u0165 syntaktick\u00FD analyz\u00E1tor XML pre MathML.\nSkontrolujte, \u010Di m\u00E1te povolen\u00E9 nastavenie \u201ESkriptova\u0165 ovl\u00E1dacie\nprvky ActiveX ozna\u010Den\u00E9 ako bezpe\u010Dn\u00E9\u201C (v ponuke N\u00E1stroje\nkliknite na polo\u017Eku Mo\u017Enosti Internetu, vyberte z\u00E1lo\u017Eku\nZabezpe\u010Denie a kliknite na tla\u010Didlo Vlastn\u00E1 \u00FArove\u0148).\n\nMathJax nebude m\u00F4c\u0165 spracov\u00E1va\u0165 rovnice v MathML",
          UnknownNodeType: "Nezn\u00E1my typ uzla: %1",
          UnexpectedTextNode: "Neo\u010Dak\u00E1van\u00FD textov\u00FD uzol: %1",
          ErrorParsingMathML: "Chyba pri anal\u00FDze MathML",
          ParsingError: "Chyba pri anal\u00FDze MathML: %1",
          MathMLSingleElement: "MathML mus\u00ED by\u0165 tvoren\u00E9 jedin\u00FDm elementom",
          MathMLRootElement: "MathML mus\u00ED by\u0165 tvoren\u00E9 elementom \u003Cmath\u003E, nie %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/sk/MathML.js");
