/*************************************************************
 *
 *  MathJax/jax/output/CommonHTML/fonts/TeX/Caligraphic-Bold.js
 *
 *  Copyright (c) 2015-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

(function (CHTML) {

var font = 'MathJax_Caligraphic-Bold';

CHTML.FONTDATA.FONTS[font] = {
  className: CHTML.FONTDATA.familyName(font),
  centerline: 315, ascent: 840, descent: 211,
  weight: 'bold',
  skew: {
    0x41: 0.224,
    0x42: 0.16,
    0x43: 0.16,
    0x44: 0.0958,
    0x45: 0.128,
    0x46: 0.128,
    0x47: 0.128,
    0x48: 0.128,
    0x49: 0.0319,
    0x4A: 0.192,
    0x4B: 0.0639,
    0x4C: 0.16,
    0x4D: 0.16,
    0x4E: 0.0958,
    0x4F: 0.128,
    0x50: 0.0958,
    0x51: 0.128,
    0x52: 0.0958,
    0x53: 0.16,
    0x54: 0.0319,
    0x55: 0.0958,
    0x56: 0.0319,
    0x57: 0.0958,
    0x58: 0.16,
    0x59: 0.0958,
    0x5A: 0.16
  },
  0x20: [0,0,250,0,0],               // SPACE
  0x30: [460,17,575,46,528],         // DIGIT ZERO
  0x31: [461,0,575,80,494],          // DIGIT ONE
  0x32: [460,0,575,51,517],          // DIGIT TWO
  0x33: [461,211,575,48,525],        // DIGIT THREE
  0x34: [469,194,575,32,542],        // DIGIT FOUR
  0x35: [461,211,575,57,517],        // DIGIT FIVE
  0x36: [660,17,575,48,526],         // DIGIT SIX
  0x37: [476,211,575,64,558],        // DIGIT SEVEN
  0x38: [661,17,575,48,526],         // DIGIT EIGHT
  0x39: [461,210,575,48,526],        // DIGIT NINE
  0x41: [751,49,921,39,989],         // LATIN CAPITAL LETTER A
  0x42: [705,17,748,40,740],         // LATIN CAPITAL LETTER B
  0x43: [703,20,613,20,599],         // LATIN CAPITAL LETTER C
  0x44: [686,0,892,20,885],          // LATIN CAPITAL LETTER D
  0x45: [703,16,607,37,627],         // LATIN CAPITAL LETTER E
  0x46: [686,30,814,17,930],         // LATIN CAPITAL LETTER F
  0x47: [703,113,682,50,671],        // LATIN CAPITAL LETTER G
  0x48: [686,48,987,20,946],         // LATIN CAPITAL LETTER H
  0x49: [686,0,642,-27,746],         // LATIN CAPITAL LETTER I
  0x4A: [686,114,779,53,937],        // LATIN CAPITAL LETTER J
  0x4B: [703,17,871,40,834],         // LATIN CAPITAL LETTER K
  0x4C: [703,17,788,41,751],         // LATIN CAPITAL LETTER L
  0x4D: [703,49,1378,38,1353],       // LATIN CAPITAL LETTER M
  0x4E: [840,49,937,-24,1105],       // LATIN CAPITAL LETTER N
  0x4F: [703,17,906,63,882],         // LATIN CAPITAL LETTER O
  0x50: [686,67,810,20,846],         // LATIN CAPITAL LETTER P
  0x51: [703,146,939,120,905],       // LATIN CAPITAL LETTER Q
  0x52: [686,17,990,20,981],         // LATIN CAPITAL LETTER R
  0x53: [703,16,696,25,721],         // LATIN CAPITAL LETTER S
  0x54: [720,69,644,38,947],         // LATIN CAPITAL LETTER T
  0x55: [686,24,715,-10,771],        // LATIN CAPITAL LETTER U
  0x56: [686,77,737,25,774],         // LATIN CAPITAL LETTER V
  0x57: [686,77,1169,25,1206],       // LATIN CAPITAL LETTER W
  0x58: [686,-1,817,56,906],         // LATIN CAPITAL LETTER X
  0x59: [686,164,759,36,797],        // LATIN CAPITAL LETTER Y
  0x5A: [686,0,818,46,853],          // LATIN CAPITAL LETTER Z
  0xA0: [0,0,250,0,0]                // NO-BREAK SPACE
};

CHTML.fontLoaded("TeX/"+font.substr(8));

})(MathJax.OutputJax.CommonHTML);
