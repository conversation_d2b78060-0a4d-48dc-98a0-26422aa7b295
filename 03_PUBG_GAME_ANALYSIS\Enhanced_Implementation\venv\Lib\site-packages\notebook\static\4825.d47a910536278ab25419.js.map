{"version": 3, "file": "4825.d47a910536278ab25419.js?v=d47a910536278ab25419", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;;AAEtC;AACA;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yCAAyC,SAAS;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA,8BAA8B,aAAa,SAAS;;AAEpD;AACA,6BAA6B,MAAM;;AAEnC;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI,2CAA2C;AAC/C;AACA;AACA;AACA;AACA,MAAM,+BAA+B;AACrC;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI,8BAA8B;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,0DAA0D;AAC9D;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI,0CAA0C;AAC9C;AACA;;AAEA;AACA;AACA,wBAAwB;AACxB,kCAAkC;AAClC;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,sBAAsB,yBAAyB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,iBAAiB;AACvC;AACA;AACA;AACA;AACA;AACA,sBAAsB,oBAAoB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,oBAAoB,QAAQ;AAC5B;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/sas.js"], "sourcesContent": ["var words = {};\nvar isDoubleOperatorSym = {\n  eq: 'operator',\n  lt: 'operator',\n  le: 'operator',\n  gt: 'operator',\n  ge: 'operator',\n  \"in\": 'operator',\n  ne: 'operator',\n  or: 'operator'\n};\nvar isDoubleOperatorChar = /(<=|>=|!=|<>)/;\nvar isSingleOperatorChar = /[=\\(:\\),{}.*<>+\\-\\/^\\[\\]]/;\n\n// Takes a string of words separated by spaces and adds them as\n// keys with the value of the first argument 'style'\nfunction define(style, string, context) {\n  if (context) {\n    var split = string.split(' ');\n    for (var i = 0; i < split.length; i++) {\n      words[split[i]] = {style: style, state: context};\n    }\n  }\n}\n//datastep\ndefine('def', 'stack pgm view source debug nesting nolist', ['inDataStep']);\ndefine('def', 'if while until for do do; end end; then else cancel', ['inDataStep']);\ndefine('def', 'label format _n_ _error_', ['inDataStep']);\ndefine('def', 'ALTER BUFNO BUFSIZE CNTLLEV COMPRESS DLDMGACTION ENCRYPT ENCRYPTKEY EXTENDOBSCOUNTER GENMAX GENNUM INDEX LABEL OBSBUF OUTREP PW PWREQ READ REPEMPTY REPLACE REUSE ROLE SORTEDBY SPILL TOBSNO TYPE WRITE FILECLOSE FIRSTOBS IN OBS POINTOBS WHERE WHEREUP IDXNAME IDXWHERE DROP KEEP RENAME', ['inDataStep']);\ndefine('def', 'filevar finfo finv fipname fipnamel fipstate first firstobs floor', ['inDataStep']);\ndefine('def', 'varfmt varinfmt varlabel varlen varname varnum varray varrayx vartype verify vformat vformatd vformatdx vformatn vformatnx vformatw vformatwx vformatx vinarray vinarrayx vinformat vinformatd vinformatdx vinformatn vinformatnx vinformatw vinformatwx vinformatx vlabel vlabelx vlength vlengthx vname vnamex vnferr vtype vtypex weekday', ['inDataStep']);\ndefine('def', 'zipfips zipname zipnamel zipstate', ['inDataStep']);\ndefine('def', 'put putc putn', ['inDataStep']);\ndefine('builtin', 'data run', ['inDataStep']);\n\n\n//proc\ndefine('def', 'data', ['inProc']);\n\n// flow control for macros\ndefine('def', '%if %end %end; %else %else; %do %do; %then', ['inMacro']);\n\n//everywhere\ndefine('builtin', 'proc run; quit; libname filename %macro %mend option options', ['ALL']);\n\ndefine('def', 'footnote title libname ods', ['ALL']);\ndefine('def', '%let %put %global %sysfunc %eval ', ['ALL']);\n// automatic macro variables http://support.sas.com/documentation/cdl/en/mcrolref/61885/HTML/default/viewer.htm#a003167023.htm\ndefine('variable', '&sysbuffr &syscc &syscharwidth &syscmd &sysdate &sysdate9 &sysday &sysdevic &sysdmg &sysdsn &sysencoding &sysenv &syserr &syserrortext &sysfilrc &syshostname &sysindex &sysinfo &sysjobid &syslast &syslckrc &syslibrc &syslogapplname &sysmacroname &sysmenv &sysmsg &sysncpu &sysodspath &sysparm &syspbuff &sysprocessid &sysprocessname &sysprocname &sysrc &sysscp &sysscpl &sysscpl &syssite &sysstartid &sysstartname &systcpiphostname &systime &sysuserid &sysver &sysvlong &sysvlong4 &syswarningtext', ['ALL']);\n\n//footnote[1-9]? title[1-9]?\n\n//options statement\ndefine('def', 'source2 nosource2 page pageno pagesize', ['ALL']);\n\n//proc and datastep\ndefine('def', '_all_ _character_ _cmd_ _freq_ _i_ _infile_ _last_ _msg_ _null_ _numeric_ _temporary_ _type_ abort abs addr adjrsq airy alpha alter altlog altprint and arcos array arsin as atan attrc attrib attrn authserver autoexec awscontrol awsdef awsmenu awsmenumerge awstitle backward band base betainv between blocksize blshift bnot bor brshift bufno bufsize bxor by byerr byline byte calculated call cards cards4 catcache cbufno cdf ceil center cexist change chisq cinv class cleanup close cnonct cntllev coalesce codegen col collate collin column comamid comaux1 comaux2 comdef compbl compound compress config continue convert cos cosh cpuid create cross crosstab css curobs cv daccdb daccdbsl daccsl daccsyd dacctab dairy datalines datalines4 datejul datepart datetime day dbcslang dbcstype dclose ddfm ddm delete delimiter depdb depdbsl depsl depsyd deptab dequote descending descript design= device dflang dhms dif digamma dim dinfo display distinct dkricond dkrocond dlm dnum do dopen doptname doptnum dread drop dropnote dsname dsnferr echo else emaildlg emailid emailpw emailserver emailsys encrypt end endsas engine eof eov erf erfc error errorcheck errors exist exp fappend fclose fcol fdelete feedback fetch fetchobs fexist fget file fileclose fileexist filefmt filename fileref  fmterr fmtsearch fnonct fnote font fontalias  fopen foptname foptnum force formatted formchar formdelim formdlim forward fpoint fpos fput fread frewind frlen from fsep fuzz fwrite gaminv gamma getoption getvarc getvarn go goto group gwindow hbar hbound helpenv helploc hms honorappearance hosthelp hostprint hour hpct html hvar ibessel ibr id if index indexc indexw initcmd initstmt inner input inputc inputn inr insert int intck intnx into intrr invaliddata irr is jbessel join juldate keep kentb kurtosis label lag last lbound leave left length levels lgamma lib  library libref line linesize link list log log10 log2 logpdf logpmf logsdf lostcard lowcase lrecl ls macro macrogen maps mautosource max maxdec maxr mdy mean measures median memtype merge merror min minute missing missover mlogic mod mode model modify month mopen mort mprint mrecall msglevel msymtabmax mvarsize myy n nest netpv new news nmiss no nobatch nobs nocaps nocardimage nocenter nocharcode nocmdmac nocol nocum nodate nodbcs nodetails nodmr nodms nodmsbatch nodup nodupkey noduplicates noechoauto noequals noerrorabend noexitwindows nofullstimer noicon noimplmac noint nolist noloadlist nomiss nomlogic nomprint nomrecall nomsgcase nomstored nomultenvappl nonotes nonumber noobs noovp nopad nopercent noprint noprintinit normal norow norsasuser nosetinit  nosplash nosymbolgen note notes notitle notitles notsorted noverbose noxsync noxwait npv null number numkeys nummousekeys nway obs  on open     order ordinal otherwise out outer outp= output over ovp p(1 5 10 25 50 75 90 95 99) pad pad2  paired parm parmcards path pathdll pathname pdf peek peekc pfkey pmf point poisson poke position printer probbeta probbnml probchi probf probgam probhypr probit probnegb probnorm probsig probt procleave prt ps  pw pwreq qtr quote r ranbin rancau random ranexp rangam range ranks rannor ranpoi rantbl rantri ranuni rcorr read recfm register regr remote remove rename repeat repeated replace resolve retain return reuse reverse rewind right round rsquare rtf rtrace rtraceloc s s2 samploc sasautos sascontrol sasfrscr sasmsg sasmstore sasscript sasuser saving scan sdf second select selection separated seq serror set setcomm setot sign simple sin sinh siteinfo skewness skip sle sls sortedby sortpgm sortseq sortsize soundex  spedis splashlocation split spool sqrt start std stderr stdin stfips stimer stname stnamel stop stopover sub subgroup subpopn substr sum sumwgt symbol symbolgen symget symput sysget sysin sysleave sysmsg sysparm sysprint sysprintfont sysprod sysrc system t table tables tan tanh tapeclose tbufsize terminal test then timepart tinv  tnonct to today tol tooldef totper transformout translate trantab tranwrd trigamma trim trimn trunc truncover type unformatted uniform union until upcase update user usericon uss validate value var  weight when where while wincharset window work workinit workterm write wsum xsync xwait yearcutoff yes yyq  min max', ['inDataStep', 'inProc']);\ndefine('operator', 'and not ', ['inDataStep', 'inProc']);\n\n// Main function\nfunction tokenize(stream, state) {\n  // Finally advance the stream\n  var ch = stream.next();\n\n  // BLOCKCOMMENT\n  if (ch === '/' && stream.eat('*')) {\n    state.continueComment = true;\n    return \"comment\";\n  } else if (state.continueComment === true) { // in comment block\n    //comment ends at the beginning of the line\n    if (ch === '*' && stream.peek() === '/') {\n      stream.next();\n      state.continueComment = false;\n    } else if (stream.skipTo('*')) { //comment is potentially later in line\n      stream.skipTo('*');\n      stream.next();\n      if (stream.eat('/'))\n        state.continueComment = false;\n    } else {\n      stream.skipToEnd();\n    }\n    return \"comment\";\n  }\n\n  if (ch == \"*\" && stream.column() == stream.indentation()) {\n    stream.skipToEnd()\n    return \"comment\"\n  }\n\n  // DoubleOperator match\n  var doubleOperator = ch + stream.peek();\n\n  if ((ch === '\"' || ch === \"'\") && !state.continueString) {\n    state.continueString = ch\n    return \"string\"\n  } else if (state.continueString) {\n    if (state.continueString == ch) {\n      state.continueString = null;\n    } else if (stream.skipTo(state.continueString)) {\n      // quote found on this line\n      stream.next();\n      state.continueString = null;\n    } else {\n      stream.skipToEnd();\n    }\n    return \"string\";\n  } else if (state.continueString !== null && stream.eol()) {\n    stream.skipTo(state.continueString) || stream.skipToEnd();\n    return \"string\";\n  } else if (/[\\d\\.]/.test(ch)) { //find numbers\n    if (ch === \".\")\n      stream.match(/^[0-9]+([eE][\\-+]?[0-9]+)?/);\n    else if (ch === \"0\")\n      stream.match(/^[xX][0-9a-fA-F]+/) || stream.match(/^0[0-7]+/);\n    else\n      stream.match(/^[0-9]*\\.?[0-9]*([eE][\\-+]?[0-9]+)?/);\n    return \"number\";\n  } else if (isDoubleOperatorChar.test(ch + stream.peek())) { // TWO SYMBOL TOKENS\n    stream.next();\n    return \"operator\";\n  } else if (isDoubleOperatorSym.hasOwnProperty(doubleOperator)) {\n    stream.next();\n    if (stream.peek() === ' ')\n      return isDoubleOperatorSym[doubleOperator.toLowerCase()];\n  } else if (isSingleOperatorChar.test(ch)) { // SINGLE SYMBOL TOKENS\n    return \"operator\";\n  }\n\n  // Matches one whole word -- even if the word is a character\n  var word;\n  if (stream.match(/[%&;\\w]+/, false) != null) {\n    word = ch + stream.match(/[%&;\\w]+/, true);\n    if (/&/.test(word)) return 'variable'\n  } else {\n    word = ch;\n  }\n  // the word after DATA PROC or MACRO\n  if (state.nextword) {\n    stream.match(/[\\w]+/);\n    // match memname.libname\n    if (stream.peek() === '.') stream.skipTo(' ');\n    state.nextword = false;\n    return 'variableName.special';\n  }\n\n  word = word.toLowerCase()\n  // Are we in a DATA Step?\n  if (state.inDataStep) {\n    if (word === 'run;' || stream.match(/run\\s;/)) {\n      state.inDataStep = false;\n      return 'builtin';\n    }\n    // variable formats\n    if ((word) && stream.next() === '.') {\n      //either a format or libname.memname\n      if (/\\w/.test(stream.peek())) return 'variableName.special';\n      else return 'variable';\n    }\n    // do we have a DATA Step keyword\n    if (word && words.hasOwnProperty(word) &&\n        (words[word].state.indexOf(\"inDataStep\") !== -1 ||\n         words[word].state.indexOf(\"ALL\") !== -1)) {\n      //backup to the start of the word\n      if (stream.start < stream.pos)\n        stream.backUp(stream.pos - stream.start);\n      //advance the length of the word and return\n      for (var i = 0; i < word.length; ++i) stream.next();\n      return words[word].style;\n    }\n  }\n  // Are we in an Proc statement?\n  if (state.inProc) {\n    if (word === 'run;' || word === 'quit;') {\n      state.inProc = false;\n      return 'builtin';\n    }\n    // do we have a proc keyword\n    if (word && words.hasOwnProperty(word) &&\n        (words[word].state.indexOf(\"inProc\") !== -1 ||\n         words[word].state.indexOf(\"ALL\") !== -1)) {\n      stream.match(/[\\w]+/);\n      return words[word].style;\n    }\n  }\n  // Are we in a Macro statement?\n  if (state.inMacro) {\n    if (word === '%mend') {\n      if (stream.peek() === ';') stream.next();\n      state.inMacro = false;\n      return 'builtin';\n    }\n    if (word && words.hasOwnProperty(word) &&\n        (words[word].state.indexOf(\"inMacro\") !== -1 ||\n         words[word].state.indexOf(\"ALL\") !== -1)) {\n      stream.match(/[\\w]+/);\n      return words[word].style;\n    }\n\n    return 'atom';\n  }\n  // Do we have Keywords specific words?\n  if (word && words.hasOwnProperty(word)) {\n    // Negates the initial next()\n    stream.backUp(1);\n    // Actually move the stream\n    stream.match(/[\\w]+/);\n    if (word === 'data' && /=/.test(stream.peek()) === false) {\n      state.inDataStep = true;\n      state.nextword = true;\n      return 'builtin';\n    }\n    if (word === 'proc') {\n      state.inProc = true;\n      state.nextword = true;\n      return 'builtin';\n    }\n    if (word === '%macro') {\n      state.inMacro = true;\n      state.nextword = true;\n      return 'builtin';\n    }\n    if (/title[1-9]/.test(word)) return 'def';\n\n    if (word === 'footnote') {\n      stream.eat(/[1-9]/);\n      return 'def';\n    }\n\n    // Returns their value as state in the prior define methods\n    if (state.inDataStep === true && words[word].state.indexOf(\"inDataStep\") !== -1)\n      return words[word].style;\n    if (state.inProc === true && words[word].state.indexOf(\"inProc\") !== -1)\n      return words[word].style;\n    if (state.inMacro === true && words[word].state.indexOf(\"inMacro\") !== -1)\n      return words[word].style;\n    if (words[word].state.indexOf(\"ALL\") !== -1)\n      return words[word].style;\n    return null;\n  }\n  // Unrecognized syntax\n  return null;\n}\n\nexport const sas = {\n  name: \"sas\",\n  startState: function () {\n    return {\n      inDataStep: false,\n      inProc: false,\n      inMacro: false,\n      nextword: false,\n      continueString: null,\n      continueComment: false\n    };\n  },\n  token: function (stream, state) {\n    // Strip the spaces, but regex will account for them either way\n    if (stream.eatSpace()) return null;\n    // Go through the main process\n    return tokenize(stream, state);\n  },\n\n  languageData: {\n    commentTokens: {block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n"], "names": [], "sourceRoot": ""}