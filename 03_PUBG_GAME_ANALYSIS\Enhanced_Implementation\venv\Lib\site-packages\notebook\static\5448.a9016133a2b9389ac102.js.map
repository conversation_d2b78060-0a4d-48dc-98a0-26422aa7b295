{"version": 3, "file": "5448.a9016133a2b9389ac102.js?v=a9016133a2b9389ac102", "mappings": ";;;;;;AAAa;;AAEb;;;;AAIA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,uBAAuB,UAAU;AACjC;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,UAAU;AAC/B;;AAEA,qBAAqB,UAAU;AAC/B;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;;;;;AC7CY;;AAEZ,QAAQ,kGAAkG,EAAE,mBAAO,CAAC,KAAa;AACjI,gBAAgB,mBAAO,CAAC,KAAe;;AAEvC;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA,4CAA4C,gBAAgB;AAC5D;AACA,+BAA+B,wCAAwC;AACvE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;AACA,YAAY;AACZ;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,+EAA+E,8BAA8B;AAC7G,IAAI;AACJ,+DAA+D,8BAA8B;AAC7F;;AAEA;AACA;AACA,+EAA+E,8BAA8B;AAC7G,IAAI;AACJ,+DAA+D,8BAA8B;AAC7F;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,+BAA+B,aAAa,8BAA8B,QAAQ;;AAElF;AACA;AACA,sCAAsC,SAAS;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,yBAAsB;AACtB,sBAAsB;;;;;;;;AC9SV;;AAEZ,2BAA2B,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,GAAG;AAC1E,mCAAmC,KAAK,sBAAsB,aAAa,EAAE;;AAE7E;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,OAAO,GAAG,iCAAiC;AACpE;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,OAAO,GAAG,mBAAmB;AACpD;;AAEA;AACA;AACA;;AAEA;AACA;AACA,0BAA0B,mBAAmB,GAAG,IAAI;;AAEpD;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;AC3LY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;AC7BY;;AAEZ,QAAQ,MAAM,EAAE,mBAAO,CAAC,KAAe;;AAEvC,8CAA8C,EAAE,gBAAgB,EAAE,wBAAwB,EAAE;;AAE5F;AACA,kCAAkC,SAAS;AAC3C;AACA;AACA;AACA,aAAa;AACb,IAAI;AACJ,aAAa;AACb;AACA;;AAEA;AACA,WAAW,UAAU;AACrB,WAAW,SAAS;AACpB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,kBAAkB;AACpC;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,wBAAwB;AACxB;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,kCAAkC,SAAS;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,IAAI;AACJ,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB,OAAO;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,gBAAgB;AAClC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA,mBAAmB,oBAAoB;AACvC,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACnPa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,cAAc;AACtC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/fast-deep-equal/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/fast-uri/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/fast-uri/lib/schemes.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/fast-uri/lib/scopedChars.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/fast-uri/lib/utils.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/json-schema-traverse/index.js"], "sourcesContent": ["'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "'use strict'\n\nconst { normalizeIPv6, normalizeIPv4, removeDotSegments, recomposeAuthority, normalizeComponentEncoding } = require('./lib/utils')\nconst SCHEMES = require('./lib/schemes')\n\nfunction normalize (uri, options) {\n  if (typeof uri === 'string') {\n    uri = serialize(parse(uri, options), options)\n  } else if (typeof uri === 'object') {\n    uri = parse(serialize(uri, options), options)\n  }\n  return uri\n}\n\nfunction resolve (baseURI, relativeURI, options) {\n  const schemelessOptions = Object.assign({ scheme: 'null' }, options)\n  const resolved = resolveComponents(parse(baseURI, schemelessOptions), parse(relativeURI, schemelessOptions), schemelessOptions, true)\n  return serialize(resolved, { ...schemelessOptions, skipEscape: true })\n}\n\nfunction resolveComponents (base, relative, options, skipNormalization) {\n  const target = {}\n  if (!skipNormalization) {\n    base = parse(serialize(base, options), options) // normalize base components\n    relative = parse(serialize(relative, options), options) // normalize relative components\n  }\n  options = options || {}\n\n  if (!options.tolerant && relative.scheme) {\n    target.scheme = relative.scheme\n    // target.authority = relative.authority;\n    target.userinfo = relative.userinfo\n    target.host = relative.host\n    target.port = relative.port\n    target.path = removeDotSegments(relative.path || '')\n    target.query = relative.query\n  } else {\n    if (relative.userinfo !== undefined || relative.host !== undefined || relative.port !== undefined) {\n      // target.authority = relative.authority;\n      target.userinfo = relative.userinfo\n      target.host = relative.host\n      target.port = relative.port\n      target.path = removeDotSegments(relative.path || '')\n      target.query = relative.query\n    } else {\n      if (!relative.path) {\n        target.path = base.path\n        if (relative.query !== undefined) {\n          target.query = relative.query\n        } else {\n          target.query = base.query\n        }\n      } else {\n        if (relative.path.charAt(0) === '/') {\n          target.path = removeDotSegments(relative.path)\n        } else {\n          if ((base.userinfo !== undefined || base.host !== undefined || base.port !== undefined) && !base.path) {\n            target.path = '/' + relative.path\n          } else if (!base.path) {\n            target.path = relative.path\n          } else {\n            target.path = base.path.slice(0, base.path.lastIndexOf('/') + 1) + relative.path\n          }\n          target.path = removeDotSegments(target.path)\n        }\n        target.query = relative.query\n      }\n      // target.authority = base.authority;\n      target.userinfo = base.userinfo\n      target.host = base.host\n      target.port = base.port\n    }\n    target.scheme = base.scheme\n  }\n\n  target.fragment = relative.fragment\n\n  return target\n}\n\nfunction equal (uriA, uriB, options) {\n  if (typeof uriA === 'string') {\n    uriA = unescape(uriA)\n    uriA = serialize(normalizeComponentEncoding(parse(uriA, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriA === 'object') {\n    uriA = serialize(normalizeComponentEncoding(uriA, true), { ...options, skipEscape: true })\n  }\n\n  if (typeof uriB === 'string') {\n    uriB = unescape(uriB)\n    uriB = serialize(normalizeComponentEncoding(parse(uriB, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriB === 'object') {\n    uriB = serialize(normalizeComponentEncoding(uriB, true), { ...options, skipEscape: true })\n  }\n\n  return uriA.toLowerCase() === uriB.toLowerCase()\n}\n\nfunction serialize (cmpts, opts) {\n  const components = {\n    host: cmpts.host,\n    scheme: cmpts.scheme,\n    userinfo: cmpts.userinfo,\n    port: cmpts.port,\n    path: cmpts.path,\n    query: cmpts.query,\n    nid: cmpts.nid,\n    nss: cmpts.nss,\n    uuid: cmpts.uuid,\n    fragment: cmpts.fragment,\n    reference: cmpts.reference,\n    resourceName: cmpts.resourceName,\n    secure: cmpts.secure,\n    error: ''\n  }\n  const options = Object.assign({}, opts)\n  const uriTokens = []\n\n  // find scheme handler\n  const schemeHandler = SCHEMES[(options.scheme || components.scheme || '').toLowerCase()]\n\n  // perform scheme specific serialization\n  if (schemeHandler && schemeHandler.serialize) schemeHandler.serialize(components, options)\n\n  if (components.path !== undefined) {\n    if (!options.skipEscape) {\n      components.path = escape(components.path)\n\n      if (components.scheme !== undefined) {\n        components.path = components.path.split('%3A').join(':')\n      }\n    } else {\n      components.path = unescape(components.path)\n    }\n  }\n\n  if (options.reference !== 'suffix' && components.scheme) {\n    uriTokens.push(components.scheme, ':')\n  }\n\n  const authority = recomposeAuthority(components)\n  if (authority !== undefined) {\n    if (options.reference !== 'suffix') {\n      uriTokens.push('//')\n    }\n\n    uriTokens.push(authority)\n\n    if (components.path && components.path.charAt(0) !== '/') {\n      uriTokens.push('/')\n    }\n  }\n  if (components.path !== undefined) {\n    let s = components.path\n\n    if (!options.absolutePath && (!schemeHandler || !schemeHandler.absolutePath)) {\n      s = removeDotSegments(s)\n    }\n\n    if (authority === undefined) {\n      s = s.replace(/^\\/\\//u, '/%2F') // don't allow the path to start with \"//\"\n    }\n\n    uriTokens.push(s)\n  }\n\n  if (components.query !== undefined) {\n    uriTokens.push('?', components.query)\n  }\n\n  if (components.fragment !== undefined) {\n    uriTokens.push('#', components.fragment)\n  }\n  return uriTokens.join('')\n}\n\nconst hexLookUp = Array.from({ length: 127 }, (_v, k) => /[^!\"$&'()*+,\\-.;=_`a-z{}~]/u.test(String.fromCharCode(k)))\n\nfunction nonSimpleDomain (value) {\n  let code = 0\n  for (let i = 0, len = value.length; i < len; ++i) {\n    code = value.charCodeAt(i)\n    if (code > 126 || hexLookUp[code]) {\n      return true\n    }\n  }\n  return false\n}\n\nconst URI_PARSE = /^(?:([^#/:?]+):)?(?:\\/\\/((?:([^#/?@]*)@)?(\\[[^#/?\\]]+\\]|[^#/:?]*)(?::(\\d*))?))?([^#?]*)(?:\\?([^#]*))?(?:#((?:.|[\\n\\r])*))?/u\n\nfunction parse (uri, opts) {\n  const options = Object.assign({}, opts)\n  const parsed = {\n    scheme: undefined,\n    userinfo: undefined,\n    host: '',\n    port: undefined,\n    path: '',\n    query: undefined,\n    fragment: undefined\n  }\n  const gotEncoding = uri.indexOf('%') !== -1\n  let isIP = false\n  if (options.reference === 'suffix') uri = (options.scheme ? options.scheme + ':' : '') + '//' + uri\n\n  const matches = uri.match(URI_PARSE)\n\n  if (matches) {\n    // store each component\n    parsed.scheme = matches[1]\n    parsed.userinfo = matches[3]\n    parsed.host = matches[4]\n    parsed.port = parseInt(matches[5], 10)\n    parsed.path = matches[6] || ''\n    parsed.query = matches[7]\n    parsed.fragment = matches[8]\n\n    // fix port number\n    if (isNaN(parsed.port)) {\n      parsed.port = matches[5]\n    }\n    if (parsed.host) {\n      const ipv4result = normalizeIPv4(parsed.host)\n      if (ipv4result.isIPV4 === false) {\n        const ipv6result = normalizeIPv6(ipv4result.host)\n        parsed.host = ipv6result.host.toLowerCase()\n        isIP = ipv6result.isIPV6\n      } else {\n        parsed.host = ipv4result.host\n        isIP = true\n      }\n    }\n    if (parsed.scheme === undefined && parsed.userinfo === undefined && parsed.host === undefined && parsed.port === undefined && parsed.query === undefined && !parsed.path) {\n      parsed.reference = 'same-document'\n    } else if (parsed.scheme === undefined) {\n      parsed.reference = 'relative'\n    } else if (parsed.fragment === undefined) {\n      parsed.reference = 'absolute'\n    } else {\n      parsed.reference = 'uri'\n    }\n\n    // check for reference errors\n    if (options.reference && options.reference !== 'suffix' && options.reference !== parsed.reference) {\n      parsed.error = parsed.error || 'URI is not a ' + options.reference + ' reference.'\n    }\n\n    // find scheme handler\n    const schemeHandler = SCHEMES[(options.scheme || parsed.scheme || '').toLowerCase()]\n\n    // check if scheme can't handle IRIs\n    if (!options.unicodeSupport && (!schemeHandler || !schemeHandler.unicodeSupport)) {\n      // if host component is a domain name\n      if (parsed.host && (options.domainHost || (schemeHandler && schemeHandler.domainHost)) && isIP === false && nonSimpleDomain(parsed.host)) {\n        // convert Unicode IDN -> ASCII IDN\n        try {\n          parsed.host = URL.domainToASCII(parsed.host.toLowerCase())\n        } catch (e) {\n          parsed.error = parsed.error || \"Host's domain name can not be converted to ASCII: \" + e\n        }\n      }\n      // convert IRI -> URI\n    }\n\n    if (!schemeHandler || (schemeHandler && !schemeHandler.skipNormalize)) {\n      if (gotEncoding && parsed.scheme !== undefined) {\n        parsed.scheme = unescape(parsed.scheme)\n      }\n      if (gotEncoding && parsed.host !== undefined) {\n        parsed.host = unescape(parsed.host)\n      }\n      if (parsed.path) {\n        parsed.path = escape(unescape(parsed.path))\n      }\n      if (parsed.fragment) {\n        parsed.fragment = encodeURI(decodeURIComponent(parsed.fragment))\n      }\n    }\n\n    // perform scheme specific parsing\n    if (schemeHandler && schemeHandler.parse) {\n      schemeHandler.parse(parsed, options)\n    }\n  } else {\n    parsed.error = parsed.error || 'URI can not be parsed.'\n  }\n  return parsed\n}\n\nconst fastUri = {\n  SCHEMES,\n  normalize,\n  resolve,\n  resolveComponents,\n  equal,\n  serialize,\n  parse\n}\n\nmodule.exports = fastUri\nmodule.exports.default = fastUri\nmodule.exports.fastUri = fastUri\n", "'use strict'\n\nconst UUID_REG = /^[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}$/iu\nconst URN_REG = /([\\da-z][\\d\\-a-z]{0,31}):((?:[\\w!$'()*+,\\-.:;=@]|%[\\da-f]{2})+)/iu\n\nfunction isSecure (wsComponents) {\n  return typeof wsComponents.secure === 'boolean' ? wsComponents.secure : String(wsComponents.scheme).toLowerCase() === 'wss'\n}\n\nfunction httpParse (components) {\n  if (!components.host) {\n    components.error = components.error || 'HTTP URIs must have a host.'\n  }\n\n  return components\n}\n\nfunction httpSerialize (components) {\n  const secure = String(components.scheme).toLowerCase() === 'https'\n\n  // normalize the default port\n  if (components.port === (secure ? 443 : 80) || components.port === '') {\n    components.port = undefined\n  }\n\n  // normalize the empty path\n  if (!components.path) {\n    components.path = '/'\n  }\n\n  // NOTE: We do not parse query strings for HTTP URIs\n  // as WWW Form Url Encoded query strings are part of the HTML4+ spec,\n  // and not the HTTP spec.\n\n  return components\n}\n\nfunction wsParse (wsComponents) {\n// indicate if the secure flag is set\n  wsComponents.secure = isSecure(wsComponents)\n\n  // construct resouce name\n  wsComponents.resourceName = (wsComponents.path || '/') + (wsComponents.query ? '?' + wsComponents.query : '')\n  wsComponents.path = undefined\n  wsComponents.query = undefined\n\n  return wsComponents\n}\n\nfunction wsSerialize (wsComponents) {\n// normalize the default port\n  if (wsComponents.port === (isSecure(wsComponents) ? 443 : 80) || wsComponents.port === '') {\n    wsComponents.port = undefined\n  }\n\n  // ensure scheme matches secure flag\n  if (typeof wsComponents.secure === 'boolean') {\n    wsComponents.scheme = (wsComponents.secure ? 'wss' : 'ws')\n    wsComponents.secure = undefined\n  }\n\n  // reconstruct path from resource name\n  if (wsComponents.resourceName) {\n    const [path, query] = wsComponents.resourceName.split('?')\n    wsComponents.path = (path && path !== '/' ? path : undefined)\n    wsComponents.query = query\n    wsComponents.resourceName = undefined\n  }\n\n  // forbid fragment component\n  wsComponents.fragment = undefined\n\n  return wsComponents\n}\n\nfunction urnParse (urnComponents, options) {\n  if (!urnComponents.path) {\n    urnComponents.error = 'URN can not be parsed'\n    return urnComponents\n  }\n  const matches = urnComponents.path.match(URN_REG)\n  if (matches) {\n    const scheme = options.scheme || urnComponents.scheme || 'urn'\n    urnComponents.nid = matches[1].toLowerCase()\n    urnComponents.nss = matches[2]\n    const urnScheme = `${scheme}:${options.nid || urnComponents.nid}`\n    const schemeHandler = SCHEMES[urnScheme]\n    urnComponents.path = undefined\n\n    if (schemeHandler) {\n      urnComponents = schemeHandler.parse(urnComponents, options)\n    }\n  } else {\n    urnComponents.error = urnComponents.error || 'URN can not be parsed.'\n  }\n\n  return urnComponents\n}\n\nfunction urnSerialize (urnComponents, options) {\n  const scheme = options.scheme || urnComponents.scheme || 'urn'\n  const nid = urnComponents.nid.toLowerCase()\n  const urnScheme = `${scheme}:${options.nid || nid}`\n  const schemeHandler = SCHEMES[urnScheme]\n\n  if (schemeHandler) {\n    urnComponents = schemeHandler.serialize(urnComponents, options)\n  }\n\n  const uriComponents = urnComponents\n  const nss = urnComponents.nss\n  uriComponents.path = `${nid || options.nid}:${nss}`\n\n  options.skipEscape = true\n  return uriComponents\n}\n\nfunction urnuuidParse (urnComponents, options) {\n  const uuidComponents = urnComponents\n  uuidComponents.uuid = uuidComponents.nss\n  uuidComponents.nss = undefined\n\n  if (!options.tolerant && (!uuidComponents.uuid || !UUID_REG.test(uuidComponents.uuid))) {\n    uuidComponents.error = uuidComponents.error || 'UUID is not valid.'\n  }\n\n  return uuidComponents\n}\n\nfunction urnuuidSerialize (uuidComponents) {\n  const urnComponents = uuidComponents\n  // normalize UUID\n  urnComponents.nss = (uuidComponents.uuid || '').toLowerCase()\n  return urnComponents\n}\n\nconst http = {\n  scheme: 'http',\n  domainHost: true,\n  parse: httpParse,\n  serialize: httpSerialize\n}\n\nconst https = {\n  scheme: 'https',\n  domainHost: http.domainHost,\n  parse: httpParse,\n  serialize: httpSerialize\n}\n\nconst ws = {\n  scheme: 'ws',\n  domainHost: true,\n  parse: wsParse,\n  serialize: wsSerialize\n}\n\nconst wss = {\n  scheme: 'wss',\n  domainHost: ws.domainHost,\n  parse: ws.parse,\n  serialize: ws.serialize\n}\n\nconst urn = {\n  scheme: 'urn',\n  parse: urnParse,\n  serialize: urnSerialize,\n  skipNormalize: true\n}\n\nconst urnuuid = {\n  scheme: 'urn:uuid',\n  parse: urnuuidParse,\n  serialize: urnuuidSerialize,\n  skipNormalize: true\n}\n\nconst SCHEMES = {\n  http,\n  https,\n  ws,\n  wss,\n  urn,\n  'urn:uuid': urnuuid\n}\n\nmodule.exports = SCHEMES\n", "'use strict'\n\nconst HEX = {\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3,\n  4: 4,\n  5: 5,\n  6: 6,\n  7: 7,\n  8: 8,\n  9: 9,\n  a: 10,\n  A: 10,\n  b: 11,\n  B: 11,\n  c: 12,\n  C: 12,\n  d: 13,\n  D: 13,\n  e: 14,\n  E: 14,\n  f: 15,\n  F: 15\n}\n\nmodule.exports = {\n  HEX\n}\n", "'use strict'\n\nconst { HEX } = require('./scopedChars')\n\nconst IPV4_REG = /^(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)$/u\n\nfunction normalizeIPv4 (host) {\n  if (findToken(host, '.') < 3) { return { host, isIPV4: false } }\n  const matches = host.match(IPV4_REG) || []\n  const [address] = matches\n  if (address) {\n    return { host: stripLeadingZeros(address, '.'), isIPV4: true }\n  } else {\n    return { host, isIPV4: false }\n  }\n}\n\n/**\n * @param {string[]} input\n * @param {boolean} [keepZero=false]\n * @returns {string|undefined}\n */\nfunction stringArrayToHexStripped (input, keepZero = false) {\n  let acc = ''\n  let strip = true\n  for (const c of input) {\n    if (HEX[c] === undefined) return undefined\n    if (c !== '0' && strip === true) strip = false\n    if (!strip) acc += c\n  }\n  if (keepZero && acc.length === 0) acc = '0'\n  return acc\n}\n\nfunction getIPV6 (input) {\n  let tokenCount = 0\n  const output = { error: false, address: '', zone: '' }\n  const address = []\n  const buffer = []\n  let isZone = false\n  let endipv6Encountered = false\n  let endIpv6 = false\n\n  function consume () {\n    if (buffer.length) {\n      if (isZone === false) {\n        const hex = stringArrayToHexStripped(buffer)\n        if (hex !== undefined) {\n          address.push(hex)\n        } else {\n          output.error = true\n          return false\n        }\n      }\n      buffer.length = 0\n    }\n    return true\n  }\n\n  for (let i = 0; i < input.length; i++) {\n    const cursor = input[i]\n    if (cursor === '[' || cursor === ']') { continue }\n    if (cursor === ':') {\n      if (endipv6Encountered === true) {\n        endIpv6 = true\n      }\n      if (!consume()) { break }\n      tokenCount++\n      address.push(':')\n      if (tokenCount > 7) {\n        // not valid\n        output.error = true\n        break\n      }\n      if (i - 1 >= 0 && input[i - 1] === ':') {\n        endipv6Encountered = true\n      }\n      continue\n    } else if (cursor === '%') {\n      if (!consume()) { break }\n      // switch to zone detection\n      isZone = true\n    } else {\n      buffer.push(cursor)\n      continue\n    }\n  }\n  if (buffer.length) {\n    if (isZone) {\n      output.zone = buffer.join('')\n    } else if (endIpv6) {\n      address.push(buffer.join(''))\n    } else {\n      address.push(stringArrayToHexStripped(buffer))\n    }\n  }\n  output.address = address.join('')\n  return output\n}\n\nfunction normalizeIPv6 (host) {\n  if (findToken(host, ':') < 2) { return { host, isIPV6: false } }\n  const ipv6 = getIPV6(host)\n\n  if (!ipv6.error) {\n    let newHost = ipv6.address\n    let escapedHost = ipv6.address\n    if (ipv6.zone) {\n      newHost += '%' + ipv6.zone\n      escapedHost += '%25' + ipv6.zone\n    }\n    return { host: newHost, escapedHost, isIPV6: true }\n  } else {\n    return { host, isIPV6: false }\n  }\n}\n\nfunction stripLeadingZeros (str, token) {\n  let out = ''\n  let skip = true\n  const l = str.length\n  for (let i = 0; i < l; i++) {\n    const c = str[i]\n    if (c === '0' && skip) {\n      if ((i + 1 <= l && str[i + 1] === token) || i + 1 === l) {\n        out += c\n        skip = false\n      }\n    } else {\n      if (c === token) {\n        skip = true\n      } else {\n        skip = false\n      }\n      out += c\n    }\n  }\n  return out\n}\n\nfunction findToken (str, token) {\n  let ind = 0\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === token) ind++\n  }\n  return ind\n}\n\nconst RDS1 = /^\\.\\.?\\//u\nconst RDS2 = /^\\/\\.(?:\\/|$)/u\nconst RDS3 = /^\\/\\.\\.(?:\\/|$)/u\nconst RDS5 = /^\\/?(?:.|\\n)*?(?=\\/|$)/u\n\nfunction removeDotSegments (input) {\n  const output = []\n\n  while (input.length) {\n    if (input.match(RDS1)) {\n      input = input.replace(RDS1, '')\n    } else if (input.match(RDS2)) {\n      input = input.replace(RDS2, '/')\n    } else if (input.match(RDS3)) {\n      input = input.replace(RDS3, '/')\n      output.pop()\n    } else if (input === '.' || input === '..') {\n      input = ''\n    } else {\n      const im = input.match(RDS5)\n      if (im) {\n        const s = im[0]\n        input = input.slice(s.length)\n        output.push(s)\n      } else {\n        throw new Error('Unexpected dot segment condition')\n      }\n    }\n  }\n  return output.join('')\n}\n\nfunction normalizeComponentEncoding (components, esc) {\n  const func = esc !== true ? escape : unescape\n  if (components.scheme !== undefined) {\n    components.scheme = func(components.scheme)\n  }\n  if (components.userinfo !== undefined) {\n    components.userinfo = func(components.userinfo)\n  }\n  if (components.host !== undefined) {\n    components.host = func(components.host)\n  }\n  if (components.path !== undefined) {\n    components.path = func(components.path)\n  }\n  if (components.query !== undefined) {\n    components.query = func(components.query)\n  }\n  if (components.fragment !== undefined) {\n    components.fragment = func(components.fragment)\n  }\n  return components\n}\n\nfunction recomposeAuthority (components) {\n  const uriTokens = []\n\n  if (components.userinfo !== undefined) {\n    uriTokens.push(components.userinfo)\n    uriTokens.push('@')\n  }\n\n  if (components.host !== undefined) {\n    let host = unescape(components.host)\n    const ipV4res = normalizeIPv4(host)\n\n    if (ipV4res.isIPV4) {\n      host = ipV4res.host\n    } else {\n      const ipV6res = normalizeIPv6(ipV4res.host)\n      if (ipV6res.isIPV6 === true) {\n        host = `[${ipV6res.escapedHost}]`\n      } else {\n        host = components.host\n      }\n    }\n    uriTokens.push(host)\n  }\n\n  if (typeof components.port === 'number' || typeof components.port === 'string') {\n    uriTokens.push(':')\n    uriTokens.push(String(components.port))\n  }\n\n  return uriTokens.length ? uriTokens.join('') : undefined\n};\n\nmodule.exports = {\n  recomposeAuthority,\n  normalizeComponentEncoding,\n  removeDotSegments,\n  normalizeIPv4,\n  normalizeIPv6,\n  stringArrayToHexStripped\n}\n", "'use strict';\n\nvar traverse = module.exports = function (schema, opts, cb) {\n  // Legacy support for v0.3.1 and earlier.\n  if (typeof opts == 'function') {\n    cb = opts;\n    opts = {};\n  }\n\n  cb = opts.cb || cb;\n  var pre = (typeof cb == 'function') ? cb : cb.pre || function() {};\n  var post = cb.post || function() {};\n\n  _traverse(opts, pre, post, schema, '', schema);\n};\n\n\ntraverse.keywords = {\n  additionalItems: true,\n  items: true,\n  contains: true,\n  additionalProperties: true,\n  propertyNames: true,\n  not: true,\n  if: true,\n  then: true,\n  else: true\n};\n\ntraverse.arrayKeywords = {\n  items: true,\n  allOf: true,\n  anyOf: true,\n  oneOf: true\n};\n\ntraverse.propsKeywords = {\n  $defs: true,\n  definitions: true,\n  properties: true,\n  patternProperties: true,\n  dependencies: true\n};\n\ntraverse.skipKeywords = {\n  default: true,\n  enum: true,\n  const: true,\n  required: true,\n  maximum: true,\n  minimum: true,\n  exclusiveMaximum: true,\n  exclusiveMinimum: true,\n  multipleOf: true,\n  maxLength: true,\n  minLength: true,\n  pattern: true,\n  format: true,\n  maxItems: true,\n  minItems: true,\n  uniqueItems: true,\n  maxProperties: true,\n  minProperties: true\n};\n\n\nfunction _traverse(opts, pre, post, schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex) {\n  if (schema && typeof schema == 'object' && !Array.isArray(schema)) {\n    pre(schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex);\n    for (var key in schema) {\n      var sch = schema[key];\n      if (Array.isArray(sch)) {\n        if (key in traverse.arrayKeywords) {\n          for (var i=0; i<sch.length; i++)\n            _traverse(opts, pre, post, sch[i], jsonPtr + '/' + key + '/' + i, rootSchema, jsonPtr, key, schema, i);\n        }\n      } else if (key in traverse.propsKeywords) {\n        if (sch && typeof sch == 'object') {\n          for (var prop in sch)\n            _traverse(opts, pre, post, sch[prop], jsonPtr + '/' + key + '/' + escapeJsonPtr(prop), rootSchema, jsonPtr, key, schema, prop);\n        }\n      } else if (key in traverse.keywords || (opts.allKeys && !(key in traverse.skipKeywords))) {\n        _traverse(opts, pre, post, sch, jsonPtr + '/' + key, rootSchema, jsonPtr, key, schema);\n      }\n    }\n    post(schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex);\n  }\n}\n\n\nfunction escapeJsonPtr(str) {\n  return str.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n"], "names": [], "sourceRoot": ""}