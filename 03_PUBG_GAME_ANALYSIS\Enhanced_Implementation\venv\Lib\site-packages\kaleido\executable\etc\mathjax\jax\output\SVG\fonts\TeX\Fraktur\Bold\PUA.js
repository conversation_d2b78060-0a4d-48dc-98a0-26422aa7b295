/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Fraktur/Bold/PUA.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Fraktur-bold'],
  {
    // stix-MATHEMATICAL BOLD CAPITAL GAMMA SLASHED
    0xE301: [630,27,587,64,512,'388 427Q320 485 242 524T128 563H116Q95 563 87 561L77 559Q72 563 69 566T65 570T65 572L75 576Q106 592 154 611T212 630Q230 630 262 622T358 581T492 498L508 486Q512 463 512 396Q512 246 469 112L465 102Q453 94 341 25Q252 -27 247 -27Q243 -27 174 24T97 84Q90 100 90 214Q90 285 98 345Q100 360 102 363T118 377Q175 422 262 465Q264 463 270 460L277 456Q277 455 267 447T244 428T228 414Q206 382 206 269Q206 187 214 164T259 110Q286 89 342 58Q391 131 391 313Q391 355 388 412V427'],

    // stix-capital Delta, Greek slashed
    0xE302: [693,212,394,37,408,'39 362L37 366L38 368L82 405H133V474Q135 563 143 589T198 658Q210 669 224 676T247 687L255 690H253Q241 690 253 692Q254 692 256 692T260 693Q263 693 262 691L261 690Q300 690 361 662L373 656L388 666Q404 675 405 675L406 674Q406 672 406 670T406 664L408 655L301 555Q300 555 287 564T254 584T221 597Q190 597 176 583T161 550Q161 525 184 495T232 440T261 405H387V399Q377 389 364 379L340 359H258V315Q258 52 228 -18L172 -120L121 -211H109Q102 -212 96 -212L109 -174Q131 -108 135 -80T139 53V76V157V362H39'],

    // stix-MATHEMATICAL BOLD CAPITAL DELTA SLASHED
    0xE303: [681,219,387,36,384,'41 352Q40 354 39 355T37 358L36 360H37Q48 370 61 380L84 400H108Q131 400 131 402Q121 424 104 501L100 519Q109 560 134 602T196 664Q230 681 271 681Q291 681 316 669T358 644L373 631Q373 630 304 553Q299 548 294 547Q292 547 290 546H287Q286 546 274 562T243 593T205 609Q180 609 165 596T150 562Q150 526 191 488L217 462Q248 431 253 405V400H381L384 394L349 352H251V332Q249 271 231 17L227 -37L120 -217L109 -218Q103 -219 97 -219Q97 -218 101 -206T110 -177T118 -151Q126 -129 128 -120T136 -46T141 127Q141 250 136 340V352H41'],

    // stix-capital Epsilon, Greek slashed
    0xE304: [474,212,593,67,531,'107 370Q127 384 172 409T255 454T294 473L306 468Q356 446 425 431L435 429L524 468Q528 465 531 461Q499 395 499 271V263Q499 146 509 71T519 -8Q519 -28 512 -45Q510 -50 435 -123T355 -197Q296 -212 257 -212Q209 -212 164 -196T98 -167T67 -143L133 -44H144Q167 -88 216 -111T320 -134Q371 -134 390 -118T410 -69Q410 -52 404 -12T392 60T385 92L193 -29L158 5Q124 39 110 51L96 63V71Q94 79 94 121Q94 130 94 148T93 174Q93 230 96 275T103 344T107 370ZM221 397Q200 334 200 254Q200 170 210 140Q216 126 234 109T268 81L283 71L383 119V127Q384 132 384 241L385 347L368 349Q325 357 290 369T240 389T221 397'],

    // stix-MATHEMATICAL BOLD CAPITAL EPSILON SLASHED
    0xE305: [684,27,393,33,387,'103 453Q103 631 95 661Q95 663 102 667T110 672L114 664Q117 655 123 641T131 621L140 597L154 606Q208 641 275 673L297 684Q300 683 302 682T307 679T310 678L314 676Q283 658 256 625Q238 601 231 579T223 515L224 512L282 548Q339 583 341 583T365 548T386 509Q326 443 318 443L316 446Q314 448 311 452T304 460T294 470T283 480T272 488T260 494T248 497Q231 497 223 474Q220 468 218 440T215 407V401H345L309 360H218V314Q218 181 221 139V129L253 108Q306 73 310 73Q315 73 343 83L373 92L374 87Q375 82 375 79T375 74T360 65T308 36T229 -13L208 -27L192 -13Q149 24 90 61Q89 61 89 62L90 68Q91 73 93 87T97 125T100 191T103 291V360H33V366L34 371L85 405H94L103 404V453'],

    // stix-capital Eta, Greek slashed
    0xE308: [679,220,981,31,875,'602 575Q505 508 505 489Q505 488 505 482T506 463T507 432Q507 314 456 237L449 226L434 216Q420 208 325 143L316 137Q453 82 488 82Q527 82 585 127L596 136Q597 136 599 126L602 115Q578 85 511 27T428 -31Q400 -31 308 10T170 51Q143 51 123 43T92 24T54 -15L34 6L41 14Q65 41 170 129L188 144L204 145Q254 147 293 164T350 208Q378 249 378 344Q378 422 362 478T320 563T268 605T213 618Q177 618 156 600T134 561Q134 539 162 508T217 446T245 394Q245 368 213 337T85 250L62 262Q73 269 86 279T116 308T133 338T108 378T57 439T32 499Q32 556 117 617T291 679Q350 679 393 658Q415 647 433 631T462 600T480 572T490 550T494 541T499 544T516 556T547 578T603 613T689 662L720 679L730 670Q742 659 756 649T785 629T810 615T836 601T855 590Q855 587 860 536T870 419T875 312Q875 114 800 -25Q794 -35 781 -47Q584 -220 398 -220Q322 -220 278 -190Q253 -173 239 -155L244 -150Q248 -145 255 -138T271 -120T290 -100T310 -80T328 -63T341 -51T349 -46Q350 -46 351 -46T354 -47Q357 -47 357 -52Q359 -68 364 -83T383 -118T424 -151T491 -166Q559 -166 613 -129Q629 -118 641 -108T674 -68T710 1T735 107T746 260Q746 433 727 507Q727 512 685 535T615 570L602 575'],

    // stix-MATHEMATICAL BOLD CAPITAL ETA SLASHED
    0xE309: [717,137,727,17,633,'351 571Q317 571 247 563T171 555Q153 555 133 563T107 584Q94 605 98 609Q101 615 138 658T190 717H207Q204 710 204 699Q204 673 231 666Q235 665 264 665Q296 665 345 667T426 669Q474 669 501 660T545 626Q553 612 553 594Q553 531 498 474T379 384Q371 379 371 378Q371 376 390 376H411H434Q520 376 602 318Q621 303 627 288T633 234Q633 59 540 -34Q465 -109 348 -130Q308 -137 235 -137Q159 -136 143 -129Q132 -125 132 -118V-53Q118 -24 90 -24Q69 -24 37 -39L27 -44L25 -42Q23 -39 21 -35T17 -30Q17 -28 40 -14T103 19T177 44Q183 45 205 45Q219 45 227 44T245 37T259 20T264 -12Q264 -33 262 -48T259 -80Q259 -93 260 -95Q271 -110 305 -110Q343 -110 383 -86T443 -33Q491 34 491 154Q491 223 467 249Q428 288 334 288H322Q288 288 237 276L222 273L206 286L262 367Q279 369 303 377T358 403T410 452T431 524Q431 531 431 533T427 545T416 558T392 566T351 571']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Fraktur/Bold/PUA.js");
