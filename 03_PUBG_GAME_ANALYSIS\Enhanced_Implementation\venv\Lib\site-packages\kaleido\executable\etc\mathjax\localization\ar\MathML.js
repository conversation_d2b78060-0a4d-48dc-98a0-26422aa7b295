/*************************************************************
 *
 *  MathJax/localization/ar/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ar","MathML",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          BadMglyph: "mglyph \u0633\u064A\u0626\u0629: %1",
          BadMglyphFont: "\u062E\u0637 \u0633\u064A\u0621: %1",
          UnknownNodeType: "\u0646\u0648\u0639 \u0639\u0642\u062F\u0647 \u063A\u064A\u0631 \u0645\u0639\u0631\u0648\u0641: %1",
          UnexpectedTextNode: "\u0639\u0642\u062F\u0647 \u063A\u064A\u0631 \u0645\u062A\u0648\u0642\u0639\u0629: %1",
          ErrorParsingMathML: "\u062D\u062F\u062B \u062E\u0637\u0623 \u0623\u062B\u0646\u0627\u0621 \u062A\u062D\u0644\u064A\u0644 MathML",
          ParsingError: "\u062D\u062F\u062B \u062E\u0637\u0623 \u0623\u062B\u0646\u0627\u0621 \u062A\u062D\u0644\u064A\u0644 MathML: %1",
          MathMLSingleElement: "MathML \u064A\u062C\u0628 \u0623\u0646 \u062A\u062A\u0643\u0648\u0646 \u0645\u0646 \u0639\u0646\u0635\u0631 \u0648\u0627\u062D\u062F",
          MathMLRootElement: "MathML \u064A\u062C\u0628 \u0623\u0646 \u062A\u062A\u0643\u0648\u0646 \u0645\u0646 \u003Cmath\u003E\u0639\u0646\u0635\u0631\u060C \u0644\u0627 %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ar/MathML.js");
