/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Main/Bold/SupplementalArrowsA.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Main-bold'],
  {
    // LONG LEFTWARDS ARROW
    0x27F5: [518,17,1805,64,1741,'1063 221L649 220H235Q340 133 364 17Q368 1 368 -2Q368 -16 343 -17Q340 -17 338 -17H332Q317 -17 314 -14T305 6Q298 34 285 62T247 126T179 189T78 233Q64 237 64 251Q64 261 74 265T108 277T154 297Q212 328 251 379T305 495Q309 511 313 514T333 518H338Q363 517 367 510Q368 507 368 503Q368 500 364 484Q345 401 287 331Q254 295 235 282L649 281H1063L1065 280Q1079 281 1392 281H1719Q1720 280 1723 278T1729 274T1735 269T1739 262T1741 251Q1741 230 1719 221H1063'],

    // LONG RIGHTWARDS ARROW
    0x27F6: [518,17,1833,96,1773,'119 221Q96 230 96 251T116 279Q121 281 448 281H1188L1602 282Q1569 304 1530 357T1473 482Q1469 500 1469 501Q1469 512 1480 517Q1482 518 1500 518H1505Q1520 518 1523 515T1532 495Q1552 412 1611 351T1753 270Q1773 263 1773 251Q1773 240 1765 236T1732 225T1683 204Q1625 173 1586 122T1532 6Q1528 -10 1524 -13T1504 -17H1499Q1474 -16 1470 -9Q1469 -6 1469 -2Q1469 1 1473 17Q1501 138 1602 220H1188L775 221H119'],

    // LONG LEFT RIGHT ARROW
    0x27F7: [518,17,2126,64,2061,'1063 221L649 220H235Q340 133 364 17Q368 1 368 -2Q368 -16 343 -17Q340 -17 338 -17H332Q317 -17 314 -14T305 6Q298 34 285 62T247 126T179 189T78 233Q64 237 64 251Q64 261 74 265T108 277T154 297Q212 328 251 379T305 495Q309 511 313 514T333 518H338Q363 517 367 510Q368 507 368 503Q368 500 364 484Q345 401 287 331Q254 295 235 282L649 281H1476L1890 282Q1857 304 1818 357T1761 482Q1757 500 1757 501Q1757 512 1768 517Q1770 518 1788 518H1793Q1808 518 1811 515T1820 495Q1840 412 1899 351T2041 270Q2061 263 2061 251Q2061 240 2053 236T2020 225T1971 204Q1913 173 1874 122T1820 6Q1816 -10 1812 -13T1792 -17H1787Q1762 -16 1758 -9Q1757 -6 1757 -2Q1757 1 1761 17Q1789 138 1890 220H1476L1063 221'],

    // LONG LEFTWARDS DOUBLE ARROW
    0x27F8: [547,46,1868,64,1804,'1063 333L700 332H338L324 321Q283 290 230 264L205 250Q266 224 323 180L338 170L700 169H1063L1064 168Q1080 169 1423 169H1782L1786 166Q1791 163 1793 162T1798 157T1802 149T1804 139Q1804 118 1782 109H1063L733 108H404L412 99Q455 50 488 -10Q498 -27 493 -37Q487 -46 465 -46H460Q446 -46 439 -39T426 -18T399 25T344 89Q239 194 99 229Q96 230 92 231T85 232T79 234T73 235T69 237T66 240T65 244T64 250Q64 267 90 271Q197 295 286 361T430 525Q439 542 442 544T460 547H465Q487 547 492 539Q496 531 496 530Q496 521 471 482T414 405L404 394L733 393H1063Q1064 392 1065 392Q1081 393 1423 393H1782Q1783 392 1786 390T1792 386T1798 381T1802 374T1804 363Q1804 345 1782 333H1063'],

    // LONG RIGHTWARDS DOUBLE ARROW
    0x27F9: [547,46,1870,64,1804,'87 109Q64 118 64 139Q64 159 86 168Q89 169 448 169H1169L1532 170L1546 180Q1562 193 1580 204T1612 223T1638 237T1657 246L1664 250L1639 264Q1586 290 1545 321L1531 332H1169L807 333H87Q64 343 64 362Q64 383 84 391Q89 393 448 393H1136L1465 394L1455 405Q1428 436 1401 477T1374 531Q1374 547 1399 547H1404H1409Q1423 547 1430 540T1443 519T1470 475T1526 411Q1634 303 1779 271Q1780 271 1783 270T1788 269T1792 268T1796 266T1799 264T1802 260T1803 256T1804 250Q1804 242 1800 238T1783 231T1755 225T1712 211T1654 185Q1517 112 1439 -24Q1430 -40 1426 -43T1409 -46H1404Q1373 -46 1373 -31Q1373 -24 1381 -10Q1414 50 1457 99L1465 108H1136L807 109H87'],

    // LONG LEFT RIGHT DOUBLE ARROW
    0x27FA: [547,46,2126,64,2060,'1063 333L700 332H338L324 321Q283 290 230 264L205 250Q266 224 323 180L338 170L700 169H1425L1788 170L1802 180Q1818 193 1836 204T1868 223T1894 237T1913 246L1920 250L1895 264Q1842 290 1801 321L1787 332H1425L1063 333ZM733 393H1392L1721 394L1711 405Q1684 436 1657 477T1630 531Q1630 547 1655 547H1660H1665Q1679 547 1686 540T1699 519T1726 475T1782 411Q1890 303 2035 271Q2036 271 2039 270T2044 269T2048 268T2052 266T2055 264T2058 260T2059 256T2060 250Q2060 242 2056 238T2039 231T2011 225T1968 211T1910 185Q1773 112 1695 -24Q1686 -40 1682 -43T1665 -46H1660Q1629 -46 1629 -31Q1629 -24 1637 -10Q1670 50 1713 99L1721 108H1392L1063 109L733 108H404L412 99Q455 50 488 -10Q498 -27 493 -37Q487 -46 465 -46H460Q446 -46 439 -39T426 -18T399 25T344 89Q239 194 99 229Q96 230 92 231T85 232T79 234T73 235T69 237T66 240T65 244T64 250Q64 267 90 271Q197 295 286 361T430 525Q439 542 442 544T460 547H465Q487 547 492 539Q496 531 496 530Q496 521 471 482T414 405L404 394L733 393'],

    // LONG RIGHTWARDS ARROW FROM BAR
    0x27FC: [518,17,1833,65,1773,'65 426Q74 448 95 448Q112 448 125 426V281H130L132 280H134Q162 281 448 281H1188L1602 282Q1569 304 1530 357T1473 482Q1469 500 1469 501Q1469 512 1480 517Q1482 518 1500 518H1505Q1520 518 1523 515T1532 495Q1552 412 1611 351T1753 270Q1773 263 1773 251Q1773 240 1765 236T1732 225T1683 204Q1625 173 1586 122T1532 6Q1528 -10 1524 -13T1504 -17H1499Q1474 -16 1470 -9Q1469 -6 1469 -2Q1469 1 1473 17Q1501 138 1602 220H1188L775 221H135Q133 220 130 220H125V76Q115 54 95 54Q73 54 65 76V426']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/SupplementalArrowsA.js");
