
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-05-20T14:52:29-0500",
 "dirty": false,
 "error": null,
 "full-revisionid": "87a177d866521a573f7a147cbee4dec7235f88f4",
 "version": "2025.5.1"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
