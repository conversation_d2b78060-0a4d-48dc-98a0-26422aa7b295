/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Main/Italic/GreekAndCoptic.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Main-italic'],
  {
    // GREEK CAPITAL LETTER GAMMA
    0x393: [680,0,627,54,705,'337 46Q339 46 342 46Q360 46 363 45T367 34Q367 13 359 5Q356 0 343 0Q339 0 320 0T268 1T196 2Q98 2 72 0H61Q54 7 54 11Q54 27 61 41Q65 46 95 46Q131 47 142 50T159 62L194 200Q229 337 264 477T299 623Q299 630 292 631T248 634Q216 634 214 638Q213 641 213 646Q213 674 224 678Q226 680 462 680H698Q705 676 705 669Q705 665 691 557T675 447Q673 440 652 440H637Q630 445 630 451Q630 452 632 467T636 504T638 543Q638 569 632 586T615 613T586 627T549 633T500 634Q491 634 487 634Q426 634 414 633T399 625Q397 621 327 342T257 59Q257 54 261 52T283 48T337 46'],

    // GREEK CAPITAL LETTER DELTA
    0x394: [716,0,818,70,751,'409 0H145Q117 0 103 0T81 1T72 3T70 6Q70 9 310 354T557 707Q559 711 565 713Q570 716 587 716Q608 716 613 710Q616 704 683 358Q752 9 750 6Q750 2 747 2Q745 0 409 0ZM581 342L531 597Q530 597 352 342T174 85T403 84T632 86Q632 87 581 342'],

    // GREEK CAPITAL LETTER THETA
    0x398: [704,22,767,149,788,'149 237Q149 326 186 413T282 563T412 665T552 704Q619 704 667 678T741 611T777 528T788 444Q788 328 728 219T572 44T377 -22Q275 -22 212 50T149 237ZM691 480Q691 569 652 618T551 668Q458 668 367 562Q307 485 277 382T246 202Q246 147 260 108T298 51T343 24T387 16Q408 16 433 22T494 51T562 109T626 211T677 363Q691 423 691 480ZM302 275Q302 281 317 346T338 415L341 418H356Q380 418 380 408Q380 405 378 398T375 385Q375 384 480 384H585L588 398Q592 412 598 418H613Q636 418 636 409Q636 406 621 340Q617 326 612 306Q603 272 598 267L597 266H582Q558 266 558 276Q558 279 560 286T563 299Q563 300 458 300H353L350 286Q346 272 340 266H325Q302 266 302 275'],

    // GREEK CAPITAL LETTER LAMDA
    0x39B: [716,0,692,58,646,'71 0Q58 0 58 11Q58 33 68 43Q71 46 77 46Q106 46 128 59T156 79T173 104Q174 106 255 256T416 556T497 707Q502 716 519 716H525Q543 716 547 711Q550 707 561 398T572 65Q573 57 574 54T587 49T623 46Q646 46 646 36Q646 35 643 23Q642 21 641 17T640 12T639 8T638 5T636 3T633 1T629 1T624 0Q622 0 612 0T578 1T514 2Q466 2 434 2T398 1Q381 1 381 11Q381 13 383 23Q387 40 390 43T406 46Q460 46 472 63L464 298Q457 533 455 536V537Q452 537 335 317T214 90Q211 80 211 75Q211 53 244 47Q246 47 251 47T258 46Q270 46 270 34Q270 33 268 19Q264 6 258 0H247Q185 2 143 2Q77 2 71 0'],

    // GREEK CAPITAL LETTER XI
    0x39E: [678,0,664,74,755,'243 668Q243 670 250 677H640Q661 677 687 677T719 678Q741 678 747 676T754 667T740 595T726 522Q725 521 724 520T723 517T720 516T714 515T704 514Q690 514 685 515T680 525Q680 531 683 543Q690 578 690 590V597H686Q670 600 468 600Q282 599 275 596Q267 591 251 539Q246 521 243 518T228 514H224Q200 511 200 525Q200 526 220 594T243 668ZM535 277Q535 282 538 296T543 312Q543 314 411 314H278L277 310Q277 309 272 291T266 272Q264 267 245 267Q237 267 233 268T228 269T225 272T222 277L241 354Q245 368 250 389Q261 432 266 437L267 438H299Q306 431 306 428Q306 426 306 424T304 417T302 409L297 391H430L562 392L567 412Q572 431 579 438H611Q615 434 616 432L618 430Q618 423 599 352Q581 275 577 270Q574 267 568 267H558Q535 267 535 277ZM81 0Q74 4 74 11Q74 14 89 89T106 168Q112 174 127 174Q138 174 142 174T148 171T151 164Q151 163 147 143Q140 101 139 92Q139 81 140 81Q143 78 265 78H349H484Q518 78 532 78T555 80T566 83T570 91Q575 103 589 145Q595 166 598 170T611 174H617H622Q641 174 641 163Q641 162 619 86T595 7Q593 2 584 1T530 0H334H81'],

    // GREEK CAPITAL LETTER PI
    0x3A0: [680,0,743,54,859,'248 634Q216 634 214 638Q213 641 213 646Q213 674 224 678Q226 680 539 680H852Q859 676 859 669Q859 653 852 639Q848 634 818 634Q782 633 771 630T754 618L719 480Q684 343 649 203T614 57Q614 50 621 49T666 46Q697 46 699 40Q701 37 698 21Q693 3 689 1Q686 0 677 0Q673 0 657 0T611 1T546 2Q453 2 428 0H418Q411 7 411 11Q411 27 418 41Q422 46 452 46Q488 47 499 50T516 62Q518 67 553 209T624 491T660 632Q660 634 530 634H400L399 630Q399 629 390 593T363 484T328 344Q257 60 257 57Q257 50 264 49T309 46Q340 46 342 40Q344 37 341 21Q336 3 332 1Q329 0 320 0Q316 0 300 0T254 1T189 2Q96 2 71 0H61Q54 7 54 11Q54 27 61 41Q65 46 95 46Q131 47 142 50T159 62L194 200Q229 337 264 477T299 623Q299 630 292 631T248 634'],

    // GREEK CAPITAL LETTER SIGMA
    0x3A3: [683,1,716,80,782,'87 0Q80 7 80 11Q80 14 81 15Q81 16 231 161Q381 304 381 305L310 475Q239 646 239 648T242 662T247 677Q247 681 251 681Q253 683 514 683H775Q780 678 782 674Q782 668 767 559T751 448Q747 443 729 443Q715 443 711 444T707 453Q707 454 710 479T713 529Q713 555 709 573T691 604T663 622T619 632T564 636T492 637H457Q356 637 356 635Q358 634 417 489T478 343Q478 340 474 335T436 297T330 196L185 57L294 56H339Q450 56 499 65T581 109Q603 131 620 164T646 221T657 248Q660 250 675 250Q699 253 699 239L681 188Q663 138 643 81T617 9Q614 2 605 1T552 -1Q541 -1 475 -1T348 0H87'],

    // GREEK CAPITAL LETTER UPSILON
    0x3A5: [706,0,767,213,832,'213 543Q213 576 262 640T379 705Q507 705 507 531Q507 514 505 492L504 482L514 505Q524 528 543 561T587 626T653 682T738 705Q783 705 807 675T832 594Q832 535 808 535H802Q780 535 780 542Q780 543 781 550T783 563Q783 589 765 606T716 623Q588 623 521 354Q521 353 485 208T448 59Q448 46 529 46Q559 46 559 36Q559 31 556 22Q552 4 547 1Q546 1 544 1T538 0Q534 0 514 0T458 1T380 2Q271 2 241 0H228Q222 6 222 9T224 27Q228 40 234 46H257Q322 46 336 52Q346 54 349 62Q351 64 372 145T416 324T445 461Q446 471 446 506Q446 528 445 541T436 577T410 610T361 622Q319 622 295 600T261 552Q257 539 249 536Q245 535 233 535T218 536L214 540V541Q213 542 213 543'],

    // GREEK CAPITAL LETTER PHI
    0x3A6: [683,0,716,159,729,'467 624Q467 631 454 633T400 637Q361 637 359 643Q358 644 358 649Q358 673 369 682Q371 683 387 683Q430 681 532 681Q569 681 600 681T650 682T672 683Q689 683 689 672Q689 670 686 658T681 643Q679 637 640 637Q595 636 579 633T558 617Q556 613 547 575T538 533Q538 532 541 532Q555 532 582 524T643 500T703 450T728 374Q728 311 673 256Q621 203 550 177T445 151Q443 151 441 149Q440 146 430 104T419 59Q419 46 500 46Q530 46 530 36Q530 31 527 22Q523 4 518 1Q517 1 515 1T510 0Q505 0 486 0T431 1T355 2Q248 2 218 0H205Q199 6 199 9T201 27Q205 40 211 46H234Q300 46 313 52Q323 54 326 62Q328 64 334 86T344 129L349 150Q349 151 346 151Q333 151 307 158T245 182T185 231T159 309V314Q159 325 162 338T174 374T207 421T264 468Q334 518 435 531L446 533L457 577Q467 620 467 624ZM436 494Q429 494 417 492T373 477T319 442Q288 408 274 362T260 284Q260 257 269 239T298 210T328 196T359 188L436 494ZM626 398Q626 438 605 460T539 493L528 495Q527 495 489 342T451 188Q455 188 462 189T490 197T528 212T566 241T598 285Q626 341 626 398'],

    // GREEK CAPITAL LETTER PSI
    0x3A8: [683,0,767,207,824,'494 626Q493 627 493 628T491 629T489 631T484 632T477 634T465 635T449 636T426 637Q387 637 385 643Q384 644 384 649Q384 673 395 682Q397 683 413 683Q456 681 558 681Q595 681 626 681T676 682T698 683Q715 683 715 672Q715 670 712 658T707 643Q705 637 666 637Q621 636 605 633T584 617L478 193Q486 195 498 199T542 229T597 291Q626 335 646 415Q669 500 694 523T759 546H777H801Q824 546 824 536Q822 509 809 509Q762 509 735 406Q707 300 642 234T481 153L467 151L456 106Q445 62 445 59Q445 46 526 46Q556 46 556 36Q556 31 553 22Q549 4 544 1Q543 1 541 1T536 0Q531 0 512 0T457 1T381 2Q274 2 244 0H231Q225 6 225 9T227 27Q231 40 237 46H260Q326 46 339 52Q349 54 352 62Q354 64 365 106T376 151Q374 152 371 152Q360 153 347 156T310 172T270 201T239 250T225 323Q225 359 235 405T245 470T239 498T226 507T213 510T207 520Q207 528 209 534T215 544L218 546H257Q305 546 314 540Q338 530 338 485Q338 455 326 402T313 312Q313 218 379 193Q386 192 387 192Q387 196 441 408Q494 621 494 626'],

    // GREEK CAPITAL LETTER OMEGA
    0x3A9: [705,0,716,100,759,'183 393Q183 451 206 502T267 590T348 652T435 691T510 704Q513 705 525 705Q631 705 695 650T759 505Q759 454 732 397T672 299T593 203T527 117Q503 81 503 76Q503 75 521 75Q576 75 588 77Q589 77 592 78T595 79T598 80T602 82T605 86T609 92T614 101T620 112T627 127T636 147Q646 169 649 170Q651 172 667 172H682Q689 167 689 162Q689 158 654 81T617 2Q614 0 530 0H447Q441 5 441 9T444 28Q461 85 498 158T569 285T628 408T654 534Q654 592 621 630T527 668Q488 668 448 649T371 593T310 487T286 330Q286 302 290 247T294 137Q294 34 280 6Q278 1 268 1T190 0H107Q100 5 100 12Q100 24 103 94T108 165Q110 172 131 172H146Q150 169 153 165L152 141V116Q152 84 153 82Q156 75 217 75H252V84Q252 126 218 231T183 393']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Italic/GreekAndCoptic.js");
