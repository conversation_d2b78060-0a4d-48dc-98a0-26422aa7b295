/*************************************************************
 *
 *  MathJax/localization/ar/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ar","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "\u0645\u0633\u0627\u0639\u062F\u0629 \u0645\u0627\u062B \u062C\u0627\u0643\u0633",
          Browsers: "*\u0645\u062A\u0635\u0641\u062D\u0627\u062A*: \u064A\u0639\u0645\u0644 \u0645\u0627\u062B \u062C\u0627\u0643\u0633 \u0645\u0639 \u062C\u0645\u064A\u0639 \u0627\u0644\u0645\u062A\u0635\u0641\u062D\u0627\u062A \u0627\u0644\u062D\u062F\u064A\u062B\u0629 \u0628\u0645\u0627 \u0641\u064A \u0630\u0644\u0643 IE6+\u060C \u0648\u0641\u0627\u064A\u0631\u0641\u0648\u0643\u0633 3+\u060C \u0648\u0643\u0631\u0648\u0645 0.2+\u060C \u0648\u0633\u0641\u0627\u0631\u064A2+\u060C \u0648\u0623\u0648\u0628\u0631\u0627 9.6+ \u0648\u0645\u0639\u0638\u0645 \u0645\u062A\u0635\u0641\u062D\u0627\u062A \u0627\u0644\u062C\u0648\u0627\u0644.",
          ShowMath: "* \u0645\u0634\u0627\u0647\u062F\u0629 \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A \u0643 * \u064A\u0633\u0645\u062D \u0644\u0643 \u0628\u0639\u0631\u0636 \u0645\u0635\u062F\u0631 \u0627\u0644\u0635\u064A\u063A \u0644\u0644\u0646\u0633\u062E \u0648\u0627\u0644\u0644\u0635\u0642 (\u0643 MathML \u0623\u0648 \u0628\u0634\u0643\u0644\u0647 \u0627\u0644\u0623\u0635\u0644\u064A).",
          Settings: "* \u0636\u0628\u0637 * \u064A\u0645\u0646\u062D\u0643 \u0627\u0644\u0633\u064A\u0637\u0631\u0629 \u0639\u0644\u0649 \u0645\u064A\u0632\u0627\u062A \u0645\u0627\u062B \u062C\u0627\u0643\u0633\u060C \u0645\u062B\u0644 \u062D\u062C\u0645 \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A\u060C \u0648\u0622\u0644\u064A\u0629 \u0627\u0633\u062A\u062E\u062F\u0627\u0645\u0647\u0627 \u0644\u0639\u0631\u0636 \u0627\u0644\u0645\u0639\u0627\u062F\u0644\u0627\u062A.",
          Language: "* \u0627\u0644\u0644\u063A\u0629 * \u064A\u062A\u064A\u062D \u0644\u0643 \u0627\u062E\u062A\u064A\u0627\u0631 \u0627\u0644\u0644\u063A\u0629 \u0627\u0644\u062A\u064A \u064A\u0633\u062A\u062E\u062F\u0645\u0647\u0627 \u0645\u0627\u062B \u062C\u0627\u0643\u0633 \u0644\u0644\u0642\u0648\u0627\u0626\u0645 \u0648\u0631\u0633\u0627\u0626\u0644 \u0627\u0644\u062A\u062D\u0630\u064A\u0631.",
          Zoom: "* \u062A\u0643\u0628\u064A\u0631 \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A *: \u0625\u0630\u0627 \u0643\u0646\u062A \u062A\u0648\u0627\u062C\u0647 \u0635\u0639\u0648\u0628\u0629 \u0641\u064A \u0642\u0631\u0627\u0621\u0629 \u0627\u0644\u0645\u0639\u0627\u062F\u0644\u0629\u060C \u064A\u0645\u0643\u0650\u0651\u0646 \u0645\u0627\u062B \u062C\u0627\u0643\u0633 \u062A\u0643\u0628\u064A\u0631\u0647\u0627 \u0644\u0645\u0633\u0627\u0639\u062F\u062A\u0643 \u0639\u0644\u0649 \u0645\u0639\u0631\u0641\u0629 \u0623\u0641\u0636\u0644.",
          Accessibilty: "* \u0627\u0644\u0648\u0635\u0648\u0644 *: \u0645\u0627\u062B \u062C\u0627\u0643\u0633 \u064A\u0639\u0645\u0644 \u062A\u0644\u0642\u0627\u0626\u064A\u0627 \u0645\u0639 \u0642\u0627\u0631\u0626\u0627\u062A \u0627\u0644\u0634\u0627\u0634\u0629 \u0644\u062C\u0639\u0644 \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A \u0641\u064A \u0645\u062A\u0646\u0627\u0648\u0644 \u0636\u0639\u0627\u0641 \u0627\u0644\u0628\u0635\u0631.",
          Fonts: "* \u0627\u0644\u062E\u0637\u0648\u0637 *: \u0645\u0627\u062B \u062C\u0627\u0643\u0633 \u0633\u064A\u0633\u062A\u062E\u062F\u0645 \u062E\u0637\u0648\u0637 \u0631\u064A\u0627\u0636\u064A\u0627\u062A \u0645\u0639\u064A\u0646\u0629 \u0625\u0630\u0627 \u0643\u0627\u0646\u062A \u0645\u062B\u0628\u062A\u0629 \u0639\u0644\u0649 \u062C\u0647\u0627\u0632 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631 \u0627\u0644\u062E\u0627\u0635 \u0628\u0643\u060C \u062E\u0644\u0627\u0641 \u0630\u0644\u0643\u060C \u0641\u0625\u0646\u0647 \u0633\u064A\u062A\u0645 \u0627\u0633\u062A\u062E\u062F\u0627\u0645 \u0627\u0644\u062E\u0637\u0648\u0637 \u0639\u0644\u0649 \u0634\u0628\u0643\u0629 \u0627\u0644\u0625\u0646\u062A\u0631\u0646\u062A. \u0639\u0644\u0649 \u0627\u0644\u0631\u063A\u0645 \u0645\u0646 \u0623\u0646 \u0630\u0644\u0643 \u0644\u064A\u0633 \u0645\u0637\u0644\u0648\u0628\u0627\u060C \u0648\u0627\u0644\u062E\u0637\u0648\u0637 \u0627\u0644\u0645\u062B\u0628\u062A\u0629 \u0645\u062D\u0644\u064A\u0627 \u062A\u0633\u0631\u0639 \u0635\u0641 \u0627\u0644\u062D\u0631\u0648\u0641. \u0646\u0642\u062A\u0631\u062D \u062A\u062B\u0628\u064A\u062A [\u062E\u0637\u0648\u0637 STIX] (%1)."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ar/HelpDialog.js");
