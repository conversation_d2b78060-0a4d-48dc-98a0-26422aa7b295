/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Fraktur/Regular/Other.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Fraktur'],
  {
    // LEFT SINGLE QUOTATION MARK
    0x2018: [708,-410,215,45,158,'117 410Q97 410 71 455T45 539Q45 588 129 694L140 708Q142 708 153 694L147 682Q106 609 106 582V577V571Q106 548 132 511T158 455Q158 434 143 422T117 410'],

    // RIGHT SINGLE QUOTATION MARK
    0x2019: [692,-395,215,49,163,'105 529Q105 546 77 588T49 651Q49 658 51 666Q53 672 67 682T92 692Q111 692 137 644T163 563Q163 534 143 497T99 428T74 395Q72 395 65 400T58 407Q105 476 105 523V529']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Fraktur/Regular/Other.js");
