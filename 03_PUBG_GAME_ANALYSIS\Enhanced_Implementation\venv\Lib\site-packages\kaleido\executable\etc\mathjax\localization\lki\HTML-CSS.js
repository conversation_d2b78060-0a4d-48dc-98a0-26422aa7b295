/*************************************************************
 *
 *  MathJax/localization/lki/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("lki","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "\u0628\u0627\u0631\u06AF\u06CC\u0631\u06CC \u0642\u0644\u0645 \u0648\u0628\u06CC %1",
          CantLoadWebFont: "\u0646\u0645\u06CC\u200C\u062A\u0648\u0627\u0646 \u0642\u0644\u0645 \u0648\u0628\u06CC %1 \u0631\u0627 \u0628\u0627\u0631\u06AF\u06CC\u0631\u06CC \u06A9\u0631\u062F",
          FirefoxCantLoadWebFont: "\u0641\u0627\u06CC\u0631\u0641\u0627\u06A9\u0633 \u0646\u0645\u06CC\u200C\u062A\u0648\u0627\u0646\u062F \u0642\u0644\u0645\u200C\u0647\u0627\u06CC \u0648\u0628\u06CC \u0631\u0627 \u0627\u0632 \u06CC\u06A9 \u0645\u06CC\u0632\u0627\u0646 \u0627\u0632 \u0631\u0627\u0647 \u062F\u0648\u0631 \u0628\u0627\u0631\u06AF\u06CC\u0631\u06CC \u06A9\u0646\u062F",
          CantFindFontUsing: "\u0646\u0645\u06CC\u200C\u062A\u0648\u0627\u0646 \u06CC\u06A9 \u0642\u0644\u0645 \u0645\u0639\u062A\u0628\u0631 \u0628\u0627\u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0627\u0632 %1 \u06CC\u0627\u0641\u062A",
          WebFontsNotAvailable: "\u0642\u0644\u0645\u200C\u0647\u0627\u06CC \u0648\u0628\u06CC \u0646\u0627\u0645\u0648\u062C\u0648\u062F\u0646\u062F -- \u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0627\u0632 \u0642\u0644\u0645\u200C\u0647\u0627\u06CC \u062A\u0635\u0648\u06CC\u0631\u06CC \u0628\u0647 \u062C\u0627\u06CC \u0622\u0646"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/lki/HTML-CSS.js");
