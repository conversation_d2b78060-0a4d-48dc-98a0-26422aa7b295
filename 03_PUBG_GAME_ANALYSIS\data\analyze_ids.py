#!/usr/bin/env python3
"""
Analyze ID fields in PUBG CSV file
"""

import pandas as pd
import numpy as np

def analyze_id_fields():
    """Analyze the structure of Id, groupId, and matchId fields"""
    
    print("🔍 PUBG ID FIELD ANALYSIS")
    print("=" * 60)
    
    # Load sample data
    print("📊 Loading sample data...")
    df = pd.read_csv('pubg.csv', nrows=1000)
    
    print(f"✅ Loaded {len(df):,} records for analysis")
    
    # Analyze each ID field
    id_fields = ['Id', 'groupId', 'matchId']
    
    for field in id_fields:
        print(f"\n🎯 ANALYZING {field.upper()}")
        print("-" * 40)
        
        # Sample values
        print(f"📋 Sample {field} values:")
        samples = df[field].head(5).tolist()
        for i, sample in enumerate(samples, 1):
            print(f"  {i}. {sample}")
        
        # Length analysis
        lengths = df[field].str.len()
        print(f"\n📏 {field} Length Statistics:")
        print(f"  Min length: {lengths.min()}")
        print(f"  Max length: {lengths.max()}")
        print(f"  Mean length: {lengths.mean():.1f}")
        print(f"  Most common length: {lengths.mode().iloc[0]}")
        
        # Character analysis
        print(f"\n🔤 {field} Character Analysis:")
        all_chars = ''.join(df[field].astype(str))
        unique_chars = set(all_chars)
        print(f"  Unique characters: {sorted(unique_chars)}")
        print(f"  Character count: {len(unique_chars)}")
        
        # Check if hexadecimal
        is_hex = all(c in '0123456789abcdefABCDEF' for c in all_chars)
        print(f"  Is hexadecimal: {is_hex}")
        
        # Uniqueness
        unique_count = df[field].nunique()
        total_count = len(df)
        print(f"\n🎲 {field} Uniqueness:")
        print(f"  Unique values: {unique_count:,}")
        print(f"  Total values: {total_count:,}")
        print(f"  Uniqueness ratio: {unique_count/total_count:.3f}")
        
        # Pattern analysis
        print(f"\n🔍 {field} Pattern Analysis:")
        if field == 'Id':
            print("  Pattern: Appears to be unique player identifier")
            print("  Format: 14-character hexadecimal string")
            print("  Purpose: Individual player identification")
        elif field == 'groupId':
            print("  Pattern: Appears to be team/group identifier")
            print("  Format: 14-character hexadecimal string")
            print("  Purpose: Team/squad identification")
        elif field == 'matchId':
            print("  Pattern: Appears to be match identifier")
            print("  Format: 14-character hexadecimal string")
            print("  Purpose: Game match identification")
    
    # Relationship analysis
    print(f"\n🔗 ID RELATIONSHIP ANALYSIS")
    print("-" * 40)
    
    # Players per group
    players_per_group = df.groupby('groupId')['Id'].count()
    print(f"📊 Players per Group:")
    print(f"  Min players per group: {players_per_group.min()}")
    print(f"  Max players per group: {players_per_group.max()}")
    print(f"  Mean players per group: {players_per_group.mean():.1f}")
    print(f"  Most common group size: {players_per_group.mode().iloc[0]}")
    
    # Groups per match
    groups_per_match = df.groupby('matchId')['groupId'].nunique()
    print(f"\n📊 Groups per Match:")
    print(f"  Min groups per match: {groups_per_match.min()}")
    print(f"  Max groups per match: {groups_per_match.max()}")
    print(f"  Mean groups per match: {groups_per_match.mean():.1f}")
    
    # Players per match
    players_per_match = df.groupby('matchId')['Id'].count()
    print(f"\n📊 Players per Match:")
    print(f"  Min players per match: {players_per_match.min()}")
    print(f"  Max players per match: {players_per_match.max()}")
    print(f"  Mean players per match: {players_per_match.mean():.1f}")
    
    print(f"\n✅ ID field analysis completed!")

if __name__ == "__main__":
    analyze_id_fields()
