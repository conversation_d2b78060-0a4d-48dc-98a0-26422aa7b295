[{"locale": "en"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital alpha", "alternative": "bold capital alpha", "short": "bold cap alpha"}, "mathspeak": {"default": "bold upper Alpha"}}, "key": "1D6A8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital beta", "alternative": "bold capital beta", "short": "bold cap beta"}, "mathspeak": {"default": "bold upper Beta"}}, "key": "1D6A9"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital gamma", "alternative": "bold capital gamma", "short": "bold cap gamma"}, "mathspeak": {"default": "bold upper Gamma"}}, "key": "1D6AA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital delta", "alternative": "bold capital delta", "short": "bold cap delta"}, "mathspeak": {"default": "bold upper Delta"}}, "key": "1D6AB"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital epsilon", "alternative": "bold capital epsilon", "short": "bold cap epsilon"}, "mathspeak": {"default": "bold upper Epsilon"}}, "key": "1D6AC"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital zeta", "alternative": "bold capital zeta", "short": "bold cap zeta"}, "mathspeak": {"default": "bold upper Zeta"}}, "key": "1D6AD"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital eta", "alternative": "bold capital eta", "short": "bold cap eta"}, "mathspeak": {"default": "bold upper Eta"}}, "key": "1D6AE"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital theta", "alternative": "bold capital theta", "short": "bold cap theta"}, "mathspeak": {"default": "bold upper Theta"}}, "key": "1D6AF"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital iota", "alternative": "bold capital iota", "short": "bold cap iota"}, "mathspeak": {"default": "bold upper Iota"}}, "key": "1D6B0"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital kappa", "alternative": "bold capital kappa", "short": "bold cap kappa"}, "mathspeak": {"default": "bold upper Kappa"}}, "key": "1D6B1"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital lamda", "alternative": "bold capital lamda", "short": "bold cap lamda"}, "mathspeak": {"default": "bold upper Lamda"}}, "key": "1D6B2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital mu", "alternative": "bold capital mu", "short": "bold cap mu"}, "mathspeak": {"default": "bold upper Mu"}}, "key": "1D6B3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital nu", "alternative": "bold capital nu", "short": "bold cap nu"}, "mathspeak": {"default": "bold upper Nu"}}, "key": "1D6B4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital xi", "alternative": "bold capital xi", "short": "bold cap xi"}, "mathspeak": {"default": "bold upper Xi"}}, "key": "1D6B5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital omicron", "alternative": "bold capital omicron", "short": "bold cap omicron"}, "mathspeak": {"default": "bold upper Omicron"}}, "key": "1D6B6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital pi", "alternative": "bold capital pi", "short": "bold cap pi"}, "mathspeak": {"default": "bold upper Pi"}}, "key": "1D6B7"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital rho", "alternative": "bold capital rho", "short": "bold cap rho"}, "mathspeak": {"default": "bold upper Rho"}}, "key": "1D6B8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital sigma", "alternative": "bold capital sigma", "short": "bold cap sigma"}, "mathspeak": {"default": "bold upper Sigma"}}, "key": "1D6BA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital tau", "alternative": "bold capital tau", "short": "bold cap tau"}, "mathspeak": {"default": "bold upper Tau"}}, "key": "1D6BB"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital upsilon", "alternative": "bold capital upsilon", "short": "bold cap upsilon"}, "mathspeak": {"default": "bold upper Upsilon"}}, "key": "1D6BC"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital phi", "alternative": "bold capital phi", "short": "bold cap phi"}, "mathspeak": {"default": "bold upper Phi"}}, "key": "1D6BD"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital chi", "alternative": "bold capital chi", "short": "bold cap chi"}, "mathspeak": {"default": "bold upper Chi"}}, "key": "1D6BE"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital psi", "alternative": "bold capital psi", "short": "bold cap psi"}, "mathspeak": {"default": "bold upper Psi"}}, "key": "1D6BF"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital omega", "alternative": "bold capital omega", "short": "bold cap omega"}, "mathspeak": {"default": "bold upper Omega"}}, "key": "1D6C0"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small alpha", "alternative": "bold small alpha", "short": "bold alpha"}}, "key": "1D6C2"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small beta", "alternative": "bold small beta", "short": "bold beta"}}, "key": "1D6C3"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small gamma", "alternative": "bold small gamma", "short": "bold gamma"}}, "key": "1D6C4"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small delta", "alternative": "bold small delta", "short": "bold delta"}}, "key": "1D6C5"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small epsilon", "alternative": "bold small epsilon", "short": "bold epsilon"}}, "key": "1D6C6"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small zeta", "alternative": "bold small zeta", "short": "bold zeta"}}, "key": "1D6C7"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small eta", "alternative": "bold small eta", "short": "bold eta"}}, "key": "1D6C8"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small theta", "alternative": "bold small theta", "short": "bold theta"}}, "key": "1D6C9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small iota", "alternative": "bold small iota", "short": "bold iota"}}, "key": "1D6CA"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small kappa", "alternative": "bold small kappa", "short": "bold kappa"}}, "key": "1D6CB"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small lamda", "alternative": "bold small lamda", "short": "bold lamda"}}, "key": "1D6CC"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small mu", "alternative": "bold small mu", "short": "bold mu"}}, "key": "1D6CD"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small nu", "alternative": "bold small nu", "short": "bold nu"}}, "key": "1D6CE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small xi", "alternative": "bold small xi", "short": "bold xi"}}, "key": "1D6CF"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small omicron", "alternative": "bold small omicron", "short": "bold omicron"}}, "key": "1D6D0"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small pi", "alternative": "bold small pi", "short": "bold pi"}}, "key": "1D6D1"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small rho", "alternative": "bold small rho", "short": "bold rho"}}, "key": "1D6D2"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small final sigma", "alternative": "bold small final sigma", "short": "bold final sigma"}}, "key": "1D6D3"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small sigma", "alternative": "bold small sigma", "short": "bold sigma"}}, "key": "1D6D4"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small tau", "alternative": "bold small tau", "short": "bold tau"}}, "key": "1D6D5"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small upsilon", "alternative": "bold small upsilon", "short": "bold upsilon"}}, "key": "1D6D6"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small phi", "alternative": "bold small phi", "short": "bold phi"}}, "key": "1D6D7"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small chi", "alternative": "bold small chi", "short": "bold chi"}}, "key": "1D6D8"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small psi", "alternative": "bold small psi", "short": "bold psi"}}, "key": "1D6D9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small omega", "alternative": "bold small omega", "short": "bold omega"}}, "key": "1D6DA"}]