/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Main/Bold/MiscMathSymbolsA.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Main-bold'],
  {
    // MATHEMATICAL LEFT ANGLE BRACKET
    0x27E8: [750,249,447,127,382,'127 243V259L223 491Q251 557 286 642Q318 719 324 732T340 748H341Q347 750 351 750Q365 750 373 740T382 723Q382 713 286 482L190 251Q190 249 286 20T382 -219Q382 -232 373 -240T352 -249Q332 -249 323 -229Q320 -220 223 10L127 243'],

    // MATHEMATICAL RIGHT ANGLE BRACKET
    0x27E9: [750,249,447,64,319,'64 720Q64 732 72 741T94 750Q106 750 113 743Q118 741 122 732L319 259V243L122 -231Q112 -249 95 -249Q83 -249 74 -240T64 -218Q64 -210 160 20L256 251L160 482Q64 715 64 720']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/MiscMathSymbolsA.js");
