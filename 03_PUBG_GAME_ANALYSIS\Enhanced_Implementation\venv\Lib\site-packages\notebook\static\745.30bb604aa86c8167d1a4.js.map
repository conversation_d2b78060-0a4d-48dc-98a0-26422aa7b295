{"version": 3, "file": "745.30bb604aa86c8167d1a4.js?v=30bb604aa86c8167d1a4", "mappings": ";;;;;;;;;;AAAA;AACA,cAAc;AACd,kBAAkB,kBAAkB;AACpC;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;;AAEA,eAAe;AACf,yBAAyB;AACzB;AACA;;AAEA;AACA;AACA;AACA;;AAEA,eAAe;AACf;AACA;AACA;;AAEA;AACA;;AAEA,cAAc;AACd;;;AAGA,UAAU,MAAM;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,YAAY;AACZ;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA,0CAA0C;AAC1C;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA,0BAA0B;AAC1B;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/sieve.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = words(\"if elsif else stop require\");\nvar atoms = words(\"true false not\");\n\nfunction tokenBase(stream, state) {\n\n  var ch = stream.next();\n  if (ch == \"/\" && stream.eat(\"*\")) {\n    state.tokenize = tokenCComment;\n    return tokenCComment(stream, state);\n  }\n\n  if (ch === '#') {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  if (ch == \"\\\"\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n\n  if (ch == \"(\") {\n    state._indent.push(\"(\");\n    // add virtual angel wings so that editor behaves...\n    // ...more sane incase of broken brackets\n    state._indent.push(\"{\");\n    return null;\n  }\n\n  if (ch === \"{\") {\n    state._indent.push(\"{\");\n    return null;\n  }\n\n  if (ch == \")\")  {\n    state._indent.pop();\n    state._indent.pop();\n  }\n\n  if (ch === \"}\") {\n    state._indent.pop();\n    return null;\n  }\n\n  if (ch == \",\")\n    return null;\n\n  if (ch == \";\")\n    return null;\n\n\n  if (/[{}\\(\\),;]/.test(ch))\n    return null;\n\n  // 1*DIGIT \"K\" / \"M\" / \"G\"\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\d]/);\n    stream.eat(/[KkMmGg]/);\n    return \"number\";\n  }\n\n  // \":\" (ALPHA / \"_\") *(ALPHA / DIGIT / \"_\")\n  if (ch == \":\") {\n    stream.eatWhile(/[a-zA-Z_]/);\n    stream.eatWhile(/[a-zA-Z0-9_]/);\n\n    return \"operator\";\n  }\n\n  stream.eatWhile(/\\w/);\n  var cur = stream.current();\n\n  // \"text:\" *(SP / HTAB) (hash-comment / CRLF)\n  // *(multiline-literal / multiline-dotstart)\n  // \".\" CRLF\n  if ((cur == \"text\") && stream.eat(\":\"))\n  {\n    state.tokenize = tokenMultiLineString;\n    return \"string\";\n  }\n\n  if (keywords.propertyIsEnumerable(cur))\n    return \"keyword\";\n\n  if (atoms.propertyIsEnumerable(cur))\n    return \"atom\";\n\n  return null;\n}\n\nfunction tokenMultiLineString(stream, state)\n{\n  state._multiLineString = true;\n  // the first line is special it may contain a comment\n  if (!stream.sol()) {\n    stream.eatSpace();\n\n    if (stream.peek() == \"#\") {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n\n    stream.skipToEnd();\n    return \"string\";\n  }\n\n  if ((stream.next() == \".\")  && (stream.eol()))\n  {\n    state._multiLineString = false;\n    state.tokenize = tokenBase;\n  }\n\n  return \"string\";\n}\n\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped)\n        break;\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    if (!escaped) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nexport const sieve = {\n  name: \"sieve\",\n  startState: function(base) {\n    return {tokenize: tokenBase,\n            baseIndent: base || 0,\n            _indent: []};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace())\n      return null;\n\n    return (state.tokenize || tokenBase)(stream, state);\n  },\n\n  indent: function(state, _textAfter, cx) {\n    var length = state._indent.length;\n    if (_textAfter && (_textAfter[0] == \"}\"))\n      length--;\n\n    if (length <0)\n      length = 0;\n\n    return length * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*\\}$/\n  }\n};\n"], "names": [], "sourceRoot": ""}