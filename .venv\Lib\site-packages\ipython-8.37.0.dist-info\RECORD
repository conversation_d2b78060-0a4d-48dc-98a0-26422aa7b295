../../Scripts/ipython.exe,sha256=RErwIpZbbMxNzuxV0KveE28vNO7blIMVF5UIR2zkx-U,108456
../../Scripts/ipython3.exe,sha256=RErwIpZbbMxNzuxV0KveE28vNO7blIMVF5UIR2zkx-U,108456
../../share/man/man1/ipython.1,sha256=PVdQP2hHmHyUEwzLOPcgavnCe9jTDVrM1jKZt4cnF_Q,2058
IPython/__init__.py,sha256=Ge18gaIVEwKqIZgaPArzHS1lkEYVI9FvACjQAWxxU8s,6567
IPython/__main__.py,sha256=VLYnhs1K5_mH0zWpOko7hxVBfRxAS2oKRCjO25_nkgE,490
IPython/__pycache__/__init__.cpython-310.pyc,,
IPython/__pycache__/__main__.cpython-310.pyc,,
IPython/__pycache__/conftest.cpython-310.pyc,,
IPython/__pycache__/consoleapp.cpython-310.pyc,,
IPython/__pycache__/display.cpython-310.pyc,,
IPython/__pycache__/paths.cpython-310.pyc,,
IPython/conftest.py,sha256=SKDzfbcU9GKnTt6rGDUPIYSDl9h4lFsPg69V-jGM9mg,2618
IPython/consoleapp.py,sha256=JykFsZuW_qOIARJHknArAzc9wyC1HGUD2HvmVhzPc_0,415
IPython/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/core/__pycache__/__init__.cpython-310.pyc,,
IPython/core/__pycache__/alias.cpython-310.pyc,,
IPython/core/__pycache__/application.cpython-310.pyc,,
IPython/core/__pycache__/async_helpers.cpython-310.pyc,,
IPython/core/__pycache__/autocall.cpython-310.pyc,,
IPython/core/__pycache__/builtin_trap.cpython-310.pyc,,
IPython/core/__pycache__/compilerop.cpython-310.pyc,,
IPython/core/__pycache__/completer.cpython-310.pyc,,
IPython/core/__pycache__/completerlib.cpython-310.pyc,,
IPython/core/__pycache__/crashhandler.cpython-310.pyc,,
IPython/core/__pycache__/debugger.cpython-310.pyc,,
IPython/core/__pycache__/display.cpython-310.pyc,,
IPython/core/__pycache__/display_functions.cpython-310.pyc,,
IPython/core/__pycache__/display_trap.cpython-310.pyc,,
IPython/core/__pycache__/displayhook.cpython-310.pyc,,
IPython/core/__pycache__/displaypub.cpython-310.pyc,,
IPython/core/__pycache__/error.cpython-310.pyc,,
IPython/core/__pycache__/events.cpython-310.pyc,,
IPython/core/__pycache__/excolors.cpython-310.pyc,,
IPython/core/__pycache__/extensions.cpython-310.pyc,,
IPython/core/__pycache__/formatters.cpython-310.pyc,,
IPython/core/__pycache__/getipython.cpython-310.pyc,,
IPython/core/__pycache__/guarded_eval.cpython-310.pyc,,
IPython/core/__pycache__/history.cpython-310.pyc,,
IPython/core/__pycache__/historyapp.cpython-310.pyc,,
IPython/core/__pycache__/hooks.cpython-310.pyc,,
IPython/core/__pycache__/inputsplitter.cpython-310.pyc,,
IPython/core/__pycache__/inputtransformer.cpython-310.pyc,,
IPython/core/__pycache__/inputtransformer2.cpython-310.pyc,,
IPython/core/__pycache__/interactiveshell.cpython-310.pyc,,
IPython/core/__pycache__/latex_symbols.cpython-310.pyc,,
IPython/core/__pycache__/logger.cpython-310.pyc,,
IPython/core/__pycache__/macro.cpython-310.pyc,,
IPython/core/__pycache__/magic.cpython-310.pyc,,
IPython/core/__pycache__/magic_arguments.cpython-310.pyc,,
IPython/core/__pycache__/oinspect.cpython-310.pyc,,
IPython/core/__pycache__/page.cpython-310.pyc,,
IPython/core/__pycache__/payload.cpython-310.pyc,,
IPython/core/__pycache__/payloadpage.cpython-310.pyc,,
IPython/core/__pycache__/prefilter.cpython-310.pyc,,
IPython/core/__pycache__/profileapp.cpython-310.pyc,,
IPython/core/__pycache__/profiledir.cpython-310.pyc,,
IPython/core/__pycache__/prompts.cpython-310.pyc,,
IPython/core/__pycache__/pylabtools.cpython-310.pyc,,
IPython/core/__pycache__/release.cpython-310.pyc,,
IPython/core/__pycache__/shellapp.cpython-310.pyc,,
IPython/core/__pycache__/splitinput.cpython-310.pyc,,
IPython/core/__pycache__/ultratb.cpython-310.pyc,,
IPython/core/__pycache__/usage.cpython-310.pyc,,
IPython/core/alias.py,sha256=8972YX00FVyLO-dzD7URjpwmPYXDdotwU-COgBqMB1I,10241
IPython/core/application.py,sha256=YLsn73PkVM6XNq023pigz2fTZNUQ-R6740URr-d76lc,18941
IPython/core/async_helpers.py,sha256=4x_ZSrPImXi0oXzwImaLc3eXlkdLi-4RXh2HcX8YDQg,4296
IPython/core/autocall.py,sha256=109_9e0zV5jcK1ax8SZxp9vsIAu0m_KHbZINEnDSyv8,1991
IPython/core/builtin_trap.py,sha256=2kYS2hoQQ0eDFQAJE9XTp_sQrBVyadDFO2rdPXZbv2I,3009
IPython/core/compilerop.py,sha256=BPie6q8NtBWd08bZ4hiHUfPi3wyhakhCkO1wpFRl1r0,7730
IPython/core/completer.py,sha256=vHexCgedZaAFi-nvV0BFdZsAFnmmXx-AFRgr3ODfCKk,128504
IPython/core/completerlib.py,sha256=C_1uFwR4eiqIsemMRbluMQV1WJ3qSfnGxO01PsGSpr8,12641
IPython/core/crashhandler.py,sha256=vTq5t4lCq3dKRp4ViU8wBLZDCbkIlDf4SaB4U1vP9-w,8921
IPython/core/debugger.py,sha256=l3Sskvg8Cn72sw03POolElV-HnsyvQ9lKkYx1hmSyt8,39002
IPython/core/display.py,sha256=Yclw6mE5fkXtk2RsviW5PLCaY0v66Mesl65yYxcPahw,44372
IPython/core/display_functions.py,sha256=z5EtLZQD6lkr8dDEP880BaGGk-2IAN4aKpA_MnA3Ssg,12919
IPython/core/display_trap.py,sha256=31jM26B3PhNSLJhpyRHuTWscObwVMOlH9PDC2v8HmsU,2098
IPython/core/displayhook.py,sha256=hNatKE_7sMairKUuXWrPF8YpivFyS-faru2e5H8__m8,12962
IPython/core/displaypub.py,sha256=L8z-FXMdv-rd-e7ollFycwMvOpVzWSzKgKnT9PJEP08,5070
IPython/core/error.py,sha256=lL5WP3E0sGNWkBTQL0u795wLQJCSrDCf328ft8TfCjk,1734
IPython/core/events.py,sha256=McEy6Dtt_YjTIionQv-d3wVRfAAcQFdJt5weP6ie_z8,5251
IPython/core/excolors.py,sha256=ELtyBLXN_3h0h2Wyn6HZIktHeWkFIVXzrU8s8e7Bvrc,6527
IPython/core/extensions.py,sha256=KgohNiowl71W-V0WYXWKw7g-q85QN9c_FxtraJoEOvY,5020
IPython/core/formatters.py,sha256=9hyrvbkIxbO26VQvOsKVI3xbnLgJOAmJiQUo8U95V6k,36500
IPython/core/getipython.py,sha256=znwXeOUdFNiyfnJuN6BGmHEk94KjV9mQasXc60QPGF0,912
IPython/core/guarded_eval.py,sha256=D4BibUvA5RuQqFMeVtKxVOO1vPbOCoPKEDuZJvKz5po,29431
IPython/core/history.py,sha256=Ei9mXxM5y8KCmGUz5SqLQY8Tvo6jKFU9SVoHIPSScdo,34821
IPython/core/historyapp.py,sha256=xVVF2UmWH7UUYg6L4bmpMnSgC1C7QXwNEDbQ1Waedkc,5871
IPython/core/hooks.py,sha256=LqgJ_ja_kjouZW97TSl6cRsqFD0SFUnNnz-k_9YqxUY,5668
IPython/core/inputsplitter.py,sha256=Bh_E3H5OMVbinBdXMZpWdBaiBN-dnjAkIa5wZcBnYw0,29162
IPython/core/inputtransformer.py,sha256=WNKm0t45t83OIoVuLgk5ZPIHL4KdovJAZIt1midCN9Y,19331
IPython/core/inputtransformer2.py,sha256=XxfBwAxTSG9-OIJKE1k0txILIUen0UPSk6fiOkN4Nig,29493
IPython/core/interactiveshell.py,sha256=VEtiGl1MurGSVatIXekm6LWlotGDkPVwgzQ1-n08Sy0,155097
IPython/core/latex_symbols.py,sha256=YiIWrYezmqutOkwydHg7VVWyNVjPaL6uqfF_iyGEhbk,31288
IPython/core/logger.py,sha256=0kOafCi77Sfwxu_J7Ir9PYbJo6nldV6bSVGtFbWLRdw,8444
IPython/core/macro.py,sha256=83KmGTzviEMXNLAzfdL-Mwi47zAGmbpFjDSnYPNYpfM,1734
IPython/core/magic.py,sha256=HBjky_9c4Vu-xM2huVP_FIDWqF9uSAVHR2GJujhkBwU,28927
IPython/core/magic_arguments.py,sha256=JWgPRHXdckllfkHq8mk30xXLJKmoB9mABadd8z4_uyk,9734
IPython/core/magics/__init__.py,sha256=pkd-UfzjDGp5UHuFKjw192vZnigpTP9ftXzG3oLdiS8,1619
IPython/core/magics/__pycache__/__init__.cpython-310.pyc,,
IPython/core/magics/__pycache__/ast_mod.cpython-310.pyc,,
IPython/core/magics/__pycache__/auto.cpython-310.pyc,,
IPython/core/magics/__pycache__/basic.cpython-310.pyc,,
IPython/core/magics/__pycache__/code.cpython-310.pyc,,
IPython/core/magics/__pycache__/config.cpython-310.pyc,,
IPython/core/magics/__pycache__/display.cpython-310.pyc,,
IPython/core/magics/__pycache__/execution.cpython-310.pyc,,
IPython/core/magics/__pycache__/extension.cpython-310.pyc,,
IPython/core/magics/__pycache__/history.cpython-310.pyc,,
IPython/core/magics/__pycache__/logging.cpython-310.pyc,,
IPython/core/magics/__pycache__/namespace.cpython-310.pyc,,
IPython/core/magics/__pycache__/osm.cpython-310.pyc,,
IPython/core/magics/__pycache__/packaging.cpython-310.pyc,,
IPython/core/magics/__pycache__/pylab.cpython-310.pyc,,
IPython/core/magics/__pycache__/script.cpython-310.pyc,,
IPython/core/magics/ast_mod.py,sha256=06OoRO7Z7Jzfc-cflf8Z3wyqF17fkYv6fJ_Nw4d7eQE,10295
IPython/core/magics/auto.py,sha256=ZQnw51feGjsxK1KOjRAyY_WQPMwRkrqBa4vzV4X-j-E,4816
IPython/core/magics/basic.py,sha256=oA1Lxo39JqQZHVEyBd5dmpwD9jzXOJjUKlQP-oe0QEs,23207
IPython/core/magics/code.py,sha256=iPjF0eW3A2VkQj2bOLA6qgptDpYsGpnnQ14U-jxPGrU,28144
IPython/core/magics/config.py,sha256=QBL5uY7m-Q7C46mO3q1Yio9s73w1TnI9y__j5E-j44Y,4881
IPython/core/magics/display.py,sha256=STRq66GlZwcvFyBxbkqslclpP_s9LnqD0ew9Z3S4-Jo,3130
IPython/core/magics/execution.py,sha256=Y43Fc8R3a7YhXdO72eQK05HElygQuQYl70LveZNQUBs,61969
IPython/core/magics/extension.py,sha256=Jj6OlkM71PS0j1HfEMDc-jU2Exwo9Ff_K0nD7e_W4N0,2477
IPython/core/magics/history.py,sha256=Aw9gBzK4AJbe-gvRdMW7n-_zxxHuMyHvHJtRDuCwwug,12629
IPython/core/magics/logging.py,sha256=VuDiF5QZrgzTT7Lr1NkkMCtUM1EHoGCw2pYlKsSQc4Q,6867
IPython/core/magics/namespace.py,sha256=2jAK2L7_NH58Ibk_YnHoQI1IYIrto9SZTMfmps0e1mo,24825
IPython/core/magics/osm.py,sha256=qfqUa1qr3BEnQ1KEFGizAYWI15HNxC5sESi-kR1woUw,30696
IPython/core/magics/packaging.py,sha256=5m2pt1bkosMdiQxB46hDdD4KisfPJpNnebCqMnygodE,6030
IPython/core/magics/pylab.py,sha256=BBRczZ0khoZB5NPipgvVtOWYpdkLatB9mQFYzyg-vpg,6624
IPython/core/magics/script.py,sha256=o0kBs9SpbR0MLOmVMh5e3r7gTFuH5U4yKQ37yBadtaI,13286
IPython/core/oinspect.py,sha256=JnbzF-fSet9ZxO5wb6fEmAenkdW3W4EdVts683uFphE,42747
IPython/core/page.py,sha256=P8Dmo0MjUq71DEnIPM_kaSGZoKTaPkrk4sUuM4I6HDA,11788
IPython/core/payload.py,sha256=uHcwG5Ahm3fnz2dsIKbzYK_lHOilqfen0IhQffOUQbE,1763
IPython/core/payloadpage.py,sha256=SFhobOhkr9w_69c8oWVcfZGexDciU4i9Lltztqg6fco,1431
IPython/core/prefilter.py,sha256=JHQ3feaD4bhoBDqZcEgmlDjQ2sfRXC1DNjgJhpaMU7E,25766
IPython/core/profile/README_STARTUP,sha256=Y47GtYQkWy6pdzkqylUNL6eBSZMUIRGwTxXPUF_BBBc,371
IPython/core/profileapp.py,sha256=bFMFIyehxeF9pDUtxw_6D3b0nxeqsupKTe7XhH7GMkc,10711
IPython/core/profiledir.py,sha256=-vjOa1I_UajMZJblJRYXh16Y0RaAUn5a2swQBsw2qEU,8459
IPython/core/prompts.py,sha256=AvNOaoD-saTOGomEBhpuLlVt4nRI6g837hYlrlg9FM4,605
IPython/core/pylabtools.py,sha256=FPQEe_tqivo1vowR4jpfwD46xILOq9TLEra9kD5X9Mw,18110
IPython/core/release.py,sha256=LTyOzM31XdrRLZ9DSdG1lwjMibBG3kPkSUksAa8rKgo,1507
IPython/core/shellapp.py,sha256=y0Li_yDXuRB-nRNg4gMTMAm9JTLbTqVL5VCcgC1_p5M,19270
IPython/core/splitinput.py,sha256=z5ZUddOGD6CeUvKRwsTVCv-xgH3ub8XAzC2bB_4_yas,5014
IPython/core/tests/2x2.jpg,sha256=YF88a1SklCHZeEbuKL9H13TJM8t8myvSqgffxh7LgkY,331
IPython/core/tests/2x2.png,sha256=ZB6RfzGIikj1RojC3sbJNIeiMAjbcfur9IvYVcfi2-I,71
IPython/core/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/core/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/core/tests/__pycache__/bad_all.cpython-310.pyc,,
IPython/core/tests/__pycache__/nonascii.cpython-310.pyc,,
IPython/core/tests/__pycache__/nonascii2.cpython-310.pyc,,
IPython/core/tests/__pycache__/print_argv.cpython-310.pyc,,
IPython/core/tests/__pycache__/refbug.cpython-310.pyc,,
IPython/core/tests/__pycache__/simpleerr.cpython-310.pyc,,
IPython/core/tests/__pycache__/tclass.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_alias.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_application.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_async_helpers.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_autocall.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_compilerop.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_completer.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_completerlib.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_debugger.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_display.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_displayhook.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_events.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_exceptiongroup_tb.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_extension.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_formatters.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_guarded_eval.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_handlers.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_history.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_hooks.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_imports.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_inputsplitter.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_inputtransformer.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_inputtransformer2.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_inputtransformer2_line.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_interactiveshell.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_iplib.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_logger.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_magic.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_magic_arguments.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_magic_terminal.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_oinspect.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_page.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_paths.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_prefilter.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_profile.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_prompts.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_pylabtools.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_run.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_shellapp.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_splitinput.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_ultratb.cpython-310.pyc,,
IPython/core/tests/bad_all.py,sha256=GARnHj1buvbHLjoKLiEy7XTqvo91Xb7ckOrNqh8YsnY,206
IPython/core/tests/daft_extension/__pycache__/daft_extension.cpython-310.pyc,,
IPython/core/tests/daft_extension/daft_extension.py,sha256=P1-QHT4a71h0vbRRPHItoFSTLySzH03tjXvBuEHdJFc,285
IPython/core/tests/nonascii.py,sha256=dLpEo2Q49dEMmo13f49ac4Hy1zixiDsVXehazBJfi54,135
IPython/core/tests/nonascii2.py,sha256=quzWGtfsocuGj_9sg-dfExNn-tPN6DayDiCPru4sDj4,131
IPython/core/tests/print_argv.py,sha256=hZW-UX2m6WLotOLClNc3Bt8Anik3UjSbli5bp6OnT2A,32
IPython/core/tests/refbug.py,sha256=A9Usb4msz_SlT4MMjDoySvoJp-02ucc1mZ12IfmLKqw,1494
IPython/core/tests/simpleerr.py,sha256=WXX2k9CSDEKY7G7zXXYdZcpcavmFi42e-0JTXWeqDEw,583
IPython/core/tests/tclass.py,sha256=cXUhBaPZoIrCxS-EiecpCOeKjzWNzjeH0g1f_e-KEmA,925
IPython/core/tests/test_alias.py,sha256=sfvm14mp0lbzFvr374h1j6bfWk9gk7t6XKArGrWIX8I,2007
IPython/core/tests/test_application.py,sha256=IZZXoWDg4A4QJTqECd7-mdBENzfXnlLcj_r8aKvl8fI,2245
IPython/core/tests/test_async_helpers.py,sha256=M-K19JzUEBXheQdnS9m1xFsMXV4PZol0gW3KBW7uBEI,8860
IPython/core/tests/test_autocall.py,sha256=it9OsUzLyTZ8kyZo924_w08_y4LE9O5glBtoV25yb-k,1426
IPython/core/tests/test_compilerop.py,sha256=6Uz1MtsruRp0GL_k8j6tYFR9c77jxEvTOavwya5sMZI,2174
IPython/core/tests/test_completer.py,sha256=HobpWb5swm93cXJOiCoi4O1DjZsPOiztFW79N3K_OvU,69093
IPython/core/tests/test_completerlib.py,sha256=OVEWgAxfoxGGkyDix2pD1L9Gime1Abk3ljccFCJH__A,6507
IPython/core/tests/test_debugger.py,sha256=UC1w1krmr-fwhJhNEWY8FHkZUXHwj5BluxPV5aHnl7U,15359
IPython/core/tests/test_display.py,sha256=GGpKfWAZxCv0jxUmGkhZhL541HFnGWqCvKTX3uzGATg,15945
IPython/core/tests/test_displayhook.py,sha256=qpnjIMNxvoXKB-Cc4rUK-1epbZJIJiyAvpB5ecB0UKY,3593
IPython/core/tests/test_events.py,sha256=NY79Nc17cVAmsZYoybzxqeOP7s699M1AtAsayxGbz6g,2287
IPython/core/tests/test_exceptiongroup_tb.py,sha256=1Hf-2_yGiLzbc6NTO1bvoh1AuPx4jr-1MRHqX6wIZLQ,3599
IPython/core/tests/test_extension.py,sha256=MsaNMVltHZf_wVfbDR64lz681HDBPa7BHi56KeFh6og,3026
IPython/core/tests/test_formatters.py,sha256=D9dzZxqDVaFg6Iafr4NkPu3fhbGErNFHWmq7rNAkw3Q,14476
IPython/core/tests/test_guarded_eval.py,sha256=PsmR0unvyg_VsHjbA8nkdbwo-XzKIm-i6SEYDgYfcHI,21019
IPython/core/tests/test_handlers.py,sha256=JE2Po5u46qgiwyEj-WFXNktgpkjmdEAy4HadrBkBOng,2931
IPython/core/tests/test_history.py,sha256=-DNmLket-C0T1nhGBY8uiit1Z_xA6JEmmS952JFCk38,11698
IPython/core/tests/test_hooks.py,sha256=TXgjnflaoteTBR79n8P4pBkN9yllGclX42a5ewjmdA4,2316
IPython/core/tests/test_imports.py,sha256=lR-_hRQC-AIe750c4k3AmCbN3spE-KaZevP2Cx8mzNY,1153
IPython/core/tests/test_inputsplitter.py,sha256=_j_G3N7IZuPgyqOg-BGdFDnig-Hx3-7dKm722IC2Cg8,23192
IPython/core/tests/test_inputtransformer.py,sha256=zQRaBtWZg4Thjc9ZJFdO4inJZJ1FyYL7bpHerDuT7-o,15755
IPython/core/tests/test_inputtransformer2.py,sha256=FvX9A2AzMq9YekgMx2ZZhq1R7L1beU39cXK2zv6YYIk,11351
IPython/core/tests/test_inputtransformer2_line.py,sha256=4-Jpebco_LW8Yzpf6OmGryugqYTOV9-MHWl6bp9QFrc,2971
IPython/core/tests/test_interactiveshell.py,sha256=cubAhtqvoej4i-eZykEDkzbr5JwK73WuO5AD6uHwNXo,40025
IPython/core/tests/test_iplib.py,sha256=wDI6uHw5MoRcgh8pZ4wnttm-d7tnYZPe5UNBMdtgchQ,6677
IPython/core/tests/test_logger.py,sha256=ikCJFbax5f2tXG3qstmz4Uem321_1UMyGAzUVGcMrLM,788
IPython/core/tests/test_magic.py,sha256=ylSCW8O-ybSd_Vwhk3aHGXlGRYfbAyVVyRccI0XkKnE,46883
IPython/core/tests/test_magic_arguments.py,sha256=W8twwUwQKwJCRAP-gvrZhOVDJbyx7dovd9oqzNp8vzU,4936
IPython/core/tests/test_magic_terminal.py,sha256=LiHprrkhNSf_3HRsAu7XKUSPir7xAoIVVCIOCkJBNPE,5996
IPython/core/tests/test_oinspect.py,sha256=0RW7d7X5-KgIRCXOuGcu7vJ_2Lri6W8XpSLBnlZOjF0,15979
IPython/core/tests/test_page.py,sha256=dA1r_xWG90NNDvtHpr11JxNUCc_E7QoIEgoUmMLvrdc,772
IPython/core/tests/test_paths.py,sha256=9VN803X9TCbYxGq12PQDVQYdBZeeMn_IY9RE1VwiOj0,7124
IPython/core/tests/test_prefilter.py,sha256=Mu1wWh_m0rGqbY9YuuHsrQOM28Ld61GOJ7ViwZ_G2DE,4864
IPython/core/tests/test_profile.py,sha256=iNC65H5gMrkpdK5tgaTDefIVkDf1_LWvsu_DqvQGbW0,5299
IPython/core/tests/test_prompts.py,sha256=jbga48K0Fg92Fx92k9JuKz5o74yhhoKP2dzrMKZ8Gy8,845
IPython/core/tests/test_pylabtools.py,sha256=-pyXK6MHRPijj01iVhdsdFHjf6KFz4iZ2HUBNYGhw48,11738
IPython/core/tests/test_run.py,sha256=YK8HjFzI3vo-e__loWHPIIvpJu_6uDUUTZHqgrmuu9w,19383
IPython/core/tests/test_shellapp.py,sha256=fVIPW5-E87td88SI-aVGtrO_KHEZ-MCxsR3vsHihXvc,1892
IPython/core/tests/test_splitinput.py,sha256=8_Tu3Z8OkkUiok0-iSGYMgReD_nGQ1tIFAGTA4u7XLI,1251
IPython/core/tests/test_ultratb.py,sha256=SWnIkGCbnsHE5QJL0bbs7C1kJIV-5KUInqAM191XilU,12709
IPython/core/ultratb.py,sha256=4zBXMjYZKkeD6ckuoOPAyRx3IUJwqGSZj_qKmm-Nyfg,56668
IPython/core/usage.py,sha256=agrZE5eZIvJnXoqI8VV9e-oWZx5LbLxUq9MdQpEyts4,13542
IPython/display.py,sha256=mVn5l6vUPceloFQJszpRrqtQ3JFhVzGwy7CHvmn-yPU,1497
IPython/extensions/__init__.py,sha256=V4vSllFd18CVUvDTbXLYqfwTFmPXUiQZGCs5LX2IliE,78
IPython/extensions/__pycache__/__init__.cpython-310.pyc,,
IPython/extensions/__pycache__/autoreload.cpython-310.pyc,,
IPython/extensions/__pycache__/storemagic.cpython-310.pyc,,
IPython/extensions/autoreload.py,sha256=0LGwni8DV6fjWZ0c3pyZhKNy27Jno_T5mpbsHzmaB4E,23469
IPython/extensions/storemagic.py,sha256=ths8PLtGmYZAYaibRuS1QetLm1Xu1soyGwehBjayByg,8168
IPython/extensions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/extensions/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/extensions/tests/__pycache__/test_autoreload.cpython-310.pyc,,
IPython/extensions/tests/__pycache__/test_storemagic.cpython-310.pyc,,
IPython/extensions/tests/test_autoreload.py,sha256=cwV4LQHAF60fG39tLiI3KGbiKQ_k4JFscQzSv8V5TZE,23199
IPython/extensions/tests/test_storemagic.py,sha256=AzoDL1kLVZTtoNWsyFob4MwiuODR20IL1eDtz7j8QNA,2229
IPython/external/__init__.py,sha256=-EQHbuUnBe1RS1_CwaLGzNSZQsCJsrxHW_r15smvVW0,126
IPython/external/__pycache__/__init__.cpython-310.pyc,,
IPython/external/__pycache__/qt_for_kernel.cpython-310.pyc,,
IPython/external/__pycache__/qt_loaders.cpython-310.pyc,,
IPython/external/qt_for_kernel.py,sha256=NHamODujSAq1IrbtFJ5HuEDcuFpjNDtztYjCXlZjyYQ,3443
IPython/external/qt_loaders.py,sha256=3ynYvEE-juM83XRq7-6tf-nuHswKmSn7vR7hEbpk-pY,11863
IPython/external/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/external/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/external/tests/__pycache__/test_qt_loaders.cpython-310.pyc,,
IPython/external/tests/test_qt_loaders.py,sha256=EIsNOsGlfMeOWT1OLKDvAAkxWFs_ltO2V1tGKBa4mz0,367
IPython/lib/__init__.py,sha256=Cj-42grGOsG6Gz976stackKz9-ISoFK1QSYAmConqFQ,411
IPython/lib/__pycache__/__init__.cpython-310.pyc,,
IPython/lib/__pycache__/backgroundjobs.cpython-310.pyc,,
IPython/lib/__pycache__/clipboard.cpython-310.pyc,,
IPython/lib/__pycache__/deepreload.cpython-310.pyc,,
IPython/lib/__pycache__/demo.cpython-310.pyc,,
IPython/lib/__pycache__/display.cpython-310.pyc,,
IPython/lib/__pycache__/editorhooks.cpython-310.pyc,,
IPython/lib/__pycache__/guisupport.cpython-310.pyc,,
IPython/lib/__pycache__/latextools.cpython-310.pyc,,
IPython/lib/__pycache__/lexers.cpython-310.pyc,,
IPython/lib/__pycache__/pretty.cpython-310.pyc,,
IPython/lib/backgroundjobs.py,sha256=9uj1cPtseoXsYYn0gNC1cadPHPz8MvYCa9HpFz3v-_A,17738
IPython/lib/clipboard.py,sha256=Ulrpzp9Wy7XXOes7QNOps6O3ss2_8recXuYF49vIj4s,3051
IPython/lib/deepreload.py,sha256=iymTEcJpP_hpcO1H5GRgb2aODGUtiUFksodoIe9738E,9431
IPython/lib/demo.py,sha256=hKJousFvJQ8wm2a_tlwqnN_e4V8V-gzXS8UPfoo6-O0,24518
IPython/lib/display.py,sha256=igyK1cNwqYiHkmvI7oMNdhm6p1qrHT_2R_rx5gYVOtg,24566
IPython/lib/editorhooks.py,sha256=KmQaAEZawUhHqy9CvRkbmc8BNXx4b1pqqLFhTEBf1CY,3982
IPython/lib/guisupport.py,sha256=e3gQE2-rYF2Mj9EFqSYOKOjMDmys8o-yae7SEjiOeEs,6300
IPython/lib/latextools.py,sha256=YPc7sltgFPnH-QYB87RztOnzWd_p2qde64H9uZ75ZC0,8128
IPython/lib/lexers.py,sha256=8HnMOAvQdpQwIXFtxwbhnyPC_ZIwaEx_PCJAzR-57Qs,20970
IPython/lib/pretty.py,sha256=tZscQfKKFC1f01df4L7BJsKxZa4wFDurOWkOkiBCBo8,31029
IPython/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/lib/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_backgroundjobs.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_clipboard.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_deepreload.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_display.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_editorhooks.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_imports.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_latextools.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_lexers.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_pretty.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_pygments.cpython-310.pyc,,
IPython/lib/tests/test.wav,sha256=y6O86Ch8OfzBfXicO8yG31DyYifGpYMPJgn-NTj1OS4,44144
IPython/lib/tests/test_backgroundjobs.py,sha256=8_rca5-mEeBAiFW_vhgricTkWoZsVbgSgDy4jP7M_YU,2698
IPython/lib/tests/test_clipboard.py,sha256=B4u-RAGkcWd5MhoPeYOlmS7SvGQaQrVi0SwrEcOKFCM,630
IPython/lib/tests/test_deepreload.py,sha256=SQcT4vnTdT2d5-1Fzj4ssCC6h6v-f2xguO01zAnJTCI,1854
IPython/lib/tests/test_display.py,sha256=gYAAjYV_MJeJf_hEZzs_karq7e3eOiF3tFuZ02sAhic,9313
IPython/lib/tests/test_editorhooks.py,sha256=dgl3FrxKrYNtRFO9n3hTho5KnmTUiPlwSSXQAZB14fo,884
IPython/lib/tests/test_imports.py,sha256=59til_pfq_0PFq7-Uhnao05ILUQeqUKNSZe1QCZmWD0,273
IPython/lib/tests/test_latextools.py,sha256=rh2xReu5pt0v7YZXHY1J7y-UQ8bJxQNs8QxgxZNIpOI,5736
IPython/lib/tests/test_lexers.py,sha256=2mo5prbF8fTJAx_Ris_zpOKsWrhVHU2lD-XV5tSgjig,6017
IPython/lib/tests/test_pretty.py,sha256=OPaqf3mav2grBcbGYb3T6aC1gCnDkNiJViFQxqwDkEk,14843
IPython/lib/tests/test_pygments.py,sha256=PBsPc3BIx1IEJRUaxocBcK741MuQJsASA1yscepMDO8,824
IPython/paths.py,sha256=iOW1ArUYyCQRXwVZfvHOFB-AcqVAQqb2eLvGOe98xfc,4335
IPython/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/sphinxext/__pycache__/__init__.cpython-310.pyc,,
IPython/sphinxext/__pycache__/custom_doctests.cpython-310.pyc,,
IPython/sphinxext/__pycache__/ipython_console_highlighting.cpython-310.pyc,,
IPython/sphinxext/__pycache__/ipython_directive.cpython-310.pyc,,
IPython/sphinxext/custom_doctests.py,sha256=e6nLB1dsLrmYEwsKuJab6epdOfZg-rXTqmYIrg8aSBI,4610
IPython/sphinxext/ipython_console_highlighting.py,sha256=D60MJnG-Dsv4B_Byd9FmA3ow9PJBx2Q1B2BWMQB9XYg,970
IPython/sphinxext/ipython_directive.py,sha256=fmclJlBj01azaJFFCOYbOhPalu0UND08ASHDTjC4Bdk,45162
IPython/terminal/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/terminal/__pycache__/__init__.cpython-310.pyc,,
IPython/terminal/__pycache__/console.cpython-310.pyc,,
IPython/terminal/__pycache__/debugger.cpython-310.pyc,,
IPython/terminal/__pycache__/embed.cpython-310.pyc,,
IPython/terminal/__pycache__/interactiveshell.cpython-310.pyc,,
IPython/terminal/__pycache__/ipapp.cpython-310.pyc,,
IPython/terminal/__pycache__/magics.cpython-310.pyc,,
IPython/terminal/__pycache__/prompts.cpython-310.pyc,,
IPython/terminal/__pycache__/ptutils.cpython-310.pyc,,
IPython/terminal/console.py,sha256=LhKpLPNW6KCk2fPssrMNjxb3dP4tfxuVb75NtjENwN0,691
IPython/terminal/debugger.py,sha256=rGZVKZmFDxJbC5P3s4XweaH9H0o9bOQv_H_WpOA-8FY,7106
IPython/terminal/embed.py,sha256=-UsXt7apB_Svg7fBQt-pxbIr1EEmVR2Rnb5lvX9EQyc,16042
IPython/terminal/interactiveshell.py,sha256=TReP2liJ3mTvhp58yFW8UospRNDM1LrLs_nw588tJxA,42073
IPython/terminal/ipapp.py,sha256=-MZJMC67kUM6NhRqdZ5OXQ-a074FTqwUMEE_26fIf3Y,12350
IPython/terminal/magics.py,sha256=49ZVJzbAUkG_EFpebxIBEqm3tEClvqefoeM6QnxGrrk,7705
IPython/terminal/prompts.py,sha256=Lu_rOE4gH8kbBw3POck6gIV0pQ9-5x6UrczL1aGeNwk,4100
IPython/terminal/pt_inputhooks/__init__.py,sha256=jB7MOn9ZtC5qcq9RnCu9kbxjysP5YrN9KbXeYIx79Q4,3606
IPython/terminal/pt_inputhooks/__pycache__/__init__.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/asyncio.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/glut.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/gtk.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/gtk3.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/gtk4.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/osx.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/pyglet.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/qt.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/tk.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/wx.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/asyncio.py,sha256=MPOQheHa33yN1dIDWg0iugt65cOvFExkicoGxM0ZiV0,1845
IPython/terminal/pt_inputhooks/glut.py,sha256=CchyzpkfAqVytLzuKv_XWsU4rmAJXCgxBksACM2bk2s,4999
IPython/terminal/pt_inputhooks/gtk.py,sha256=qx0OTtDdinx39bD9Fbf6m0JZWBXmuoypr56yUITFtfE,2427
IPython/terminal/pt_inputhooks/gtk3.py,sha256=eTzGx-SkT1wlplTtuRSPNzPdnnrJQRQ9FmZ_nvmKmP0,280
IPython/terminal/pt_inputhooks/gtk4.py,sha256=r_MxCT7a0nTHZtqyuZpPgCW2Cl7iuomC0PjgFteSL9c,557
IPython/terminal/pt_inputhooks/osx.py,sha256=TnndyR_SPbmWC_A--0ORB06rhH7IS2_7kjphfPcRqXo,4448
IPython/terminal/pt_inputhooks/pyglet.py,sha256=_4zFB268rMuDcMQHK_uNa1o6u3u_IDeNi64ao-ITwl0,2370
IPython/terminal/pt_inputhooks/qt.py,sha256=ivVQu3UzzfGGeuowh6lNeVtuOJOL7g_VTKcgMG8JHpM,3479
IPython/terminal/pt_inputhooks/tk.py,sha256=1aSYCec97FQpk7HyvIKHFiWBrJrJN7zgXHENQ3DjMEE,3648
IPython/terminal/pt_inputhooks/wx.py,sha256=7mIfO6jzCzPGM5A-2vbRLviq6lRNS2RgBHwbqS-WFBM,7134
IPython/terminal/ptutils.py,sha256=12mXHNFJT-qaI-KiI_8B5m8rQphAMGDXQiy4IRYjvjA,7481
IPython/terminal/shortcuts/__init__.py,sha256=VnRg_x0f3wh70BKCXNSi1ujd5vYyzf1v6grlQFBRpGw,18596
IPython/terminal/shortcuts/__pycache__/__init__.cpython-310.pyc,,
IPython/terminal/shortcuts/__pycache__/auto_match.cpython-310.pyc,,
IPython/terminal/shortcuts/__pycache__/auto_suggest.cpython-310.pyc,,
IPython/terminal/shortcuts/__pycache__/filters.cpython-310.pyc,,
IPython/terminal/shortcuts/auto_match.py,sha256=9uT1fDb-c4Ew7TSIs_zET1jSxDlbfWGluxfW_pj39tk,3066
IPython/terminal/shortcuts/auto_suggest.py,sha256=jlzy5CoSyhYVod_myAMZwq3muz2mnryBKUPV7WYu38Q,24447
IPython/terminal/shortcuts/filters.py,sha256=MgRTQWq8YfIyWvMASuQ9BGKq5RQwiEY5trSyMnMtJAo,10998
IPython/terminal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/terminal/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/fake_llm.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_debug_magic.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_embed.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_help.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_interactivshell.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_pt_inputhooks.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_shortcuts.cpython-310.pyc,,
IPython/terminal/tests/fake_llm.py,sha256=RtCfeGFCrRxnfPCoRkpX_Xgh39BZ0RzgAVyllD3RRzs,2948
IPython/terminal/tests/test_debug_magic.py,sha256=vYFqECY6WyrNNcdgme0k6XGQ4DqNzH8OB73dxifVIXI,2448
IPython/terminal/tests/test_embed.py,sha256=cSElGIEfvrwqK1i6tgEu3ZEhbanqqVrYm5ddeFferds,4804
IPython/terminal/tests/test_help.py,sha256=aPcn8TmY1sey8wl09fmXyPZR3bUO8WYUzd61VPEjr44,722
IPython/terminal/tests/test_interactivshell.py,sha256=OPeC4P0Zdm1faZiFwoK3ZLyA96KL3TCmaxhMEXk5vu0,8276
IPython/terminal/tests/test_pt_inputhooks.py,sha256=Nx-7XeCLr7nSjLRoXnmcRYZO8S4U1boDBOiyCWLCjZ0,1599
IPython/terminal/tests/test_shortcuts.py,sha256=gDwsRzXgN5EkiX8b9ENWzMMhiOG5vUfFgpqiKtjmj-0,16185
IPython/testing/__init__.py,sha256=9t97XO03Ez9GdZA5FWZYmfyHZt2c3AqQe2dj_0AiPJY,784
IPython/testing/__pycache__/__init__.cpython-310.pyc,,
IPython/testing/__pycache__/decorators.cpython-310.pyc,,
IPython/testing/__pycache__/globalipapp.cpython-310.pyc,,
IPython/testing/__pycache__/ipunittest.cpython-310.pyc,,
IPython/testing/__pycache__/skipdoctest.cpython-310.pyc,,
IPython/testing/__pycache__/tools.cpython-310.pyc,,
IPython/testing/decorators.py,sha256=nqTGNtlCbzXCQ6HX4g3ln5fQXBI8657WcTGWkameQew,6834
IPython/testing/globalipapp.py,sha256=F-RD6xLFJ2R1jNPycA9zk116GfH8qTsgsYFECWCeDRY,3948
IPython/testing/ipunittest.py,sha256=2MUggy5Jqf5SzEnFrmtXvJDwKa87n8mMAIRK8bL03D0,6756
IPython/testing/plugin/README.txt,sha256=9Gkx2cWar5iNgkBoZY2HGvEvSEek3tj_MJIDbqNt5Ug,1043
IPython/testing/plugin/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/testing/plugin/__pycache__/__init__.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/dtexample.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/ipdoctest.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/pytest_ipdoctest.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/setup.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/simple.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/simplevars.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/test_ipdoctest.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/test_refs.cpython-310.pyc,,
IPython/testing/plugin/dtexample.py,sha256=yedUX2aIUpFLkeDyshT5fqaODJg0iQzU_zvGOSr4JLc,2921
IPython/testing/plugin/ipdoctest.py,sha256=c2BFZcS25bZkF7K2fbVr5ahFCzex0Jd89PlCzQx57bI,11888
IPython/testing/plugin/pytest_ipdoctest.py,sha256=j1VwY6gDX5XAj6GRmxezeguQ9-sQIR-Z_zEbKyFE9KI,30054
IPython/testing/plugin/setup.py,sha256=945a09Zm2HWWvukd5IVZ4v5p1czQPJfVlr5_Idey2AA,539
IPython/testing/plugin/simple.py,sha256=YjVhrNz_312zpW3C-U7FiyFG0A49FkE33A8kLXmcUf8,727
IPython/testing/plugin/simplevars.py,sha256=YZnDvFqQuFcrgzkgmm-koVRJKDnHCf0278Y5S_tyI3g,24
IPython/testing/plugin/test_combo.txt,sha256=rrXjdOlRh9DltFu3GpuWuD0Hojtj4QQcEBOm52Z3-dE,923
IPython/testing/plugin/test_example.txt,sha256=CGM8aZIYHlePDdAnR1yX3MfDGu0OceZpUiI_Y4tZGaU,730
IPython/testing/plugin/test_exampleip.txt,sha256=5gLcj8iCk-WCOGz0ObpQpuZMhGwS1jUMyH3mouGxQJI,814
IPython/testing/plugin/test_ipdoctest.py,sha256=Lc3qQdZ3amXf9EKA7JlXf30b3BzP8RwdNS9-SMRe2P0,1907
IPython/testing/plugin/test_refs.py,sha256=y-Y2Q8niRIbaanbwpIzvEwwaHkJfAq10HYfb4bAXHBc,715
IPython/testing/skipdoctest.py,sha256=haSEhd8EJr2Y0EbXXxv3pGvK6AQ8Lb7SyqkX5O8EU6s,717
IPython/testing/tests/__init__.py,sha256=GE3JzSy9w35UEay3xg4TS4ZO2b4Y5INUHLwsghv_5JM,560
IPython/testing/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/testing/tests/__pycache__/test_decorators.cpython-310.pyc,,
IPython/testing/tests/__pycache__/test_ipunittest.cpython-310.pyc,,
IPython/testing/tests/__pycache__/test_tools.cpython-310.pyc,,
IPython/testing/tests/test_decorators.py,sha256=YpunMAYirTqL2UCLOazQj6D8SsmKAlKz-5Nb2RpRZig,4006
IPython/testing/tests/test_ipunittest.py,sha256=7-1iCczPW2mH7aqcZA6F_yGpV7EBVYz8s_3rI1mNqsc,3301
IPython/testing/tests/test_tools.py,sha256=13XEr2fwfoto9a9mh8Prbx0ZayPhrzR5o8SpYs3Yna4,4161
IPython/testing/tools.py,sha256=2L6i9I1NmBBc6w3_jmOslX-PuvddX_gv1gw3lRa2Uvk,14013
IPython/utils/PyColorize.py,sha256=VpRFScmCJxcMpk_mpcFTkeTqbfASBuHtfXt1ruFM7Ww,10875
IPython/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/utils/__pycache__/PyColorize.cpython-310.pyc,,
IPython/utils/__pycache__/__init__.cpython-310.pyc,,
IPython/utils/__pycache__/_process_cli.cpython-310.pyc,,
IPython/utils/__pycache__/_process_common.cpython-310.pyc,,
IPython/utils/__pycache__/_process_emscripten.cpython-310.pyc,,
IPython/utils/__pycache__/_process_posix.cpython-310.pyc,,
IPython/utils/__pycache__/_process_win32.cpython-310.pyc,,
IPython/utils/__pycache__/_process_win32_controller.cpython-310.pyc,,
IPython/utils/__pycache__/_sysinfo.cpython-310.pyc,,
IPython/utils/__pycache__/capture.cpython-310.pyc,,
IPython/utils/__pycache__/colorable.cpython-310.pyc,,
IPython/utils/__pycache__/coloransi.cpython-310.pyc,,
IPython/utils/__pycache__/contexts.cpython-310.pyc,,
IPython/utils/__pycache__/daemonize.cpython-310.pyc,,
IPython/utils/__pycache__/data.cpython-310.pyc,,
IPython/utils/__pycache__/decorators.cpython-310.pyc,,
IPython/utils/__pycache__/dir2.cpython-310.pyc,,
IPython/utils/__pycache__/docs.cpython-310.pyc,,
IPython/utils/__pycache__/encoding.cpython-310.pyc,,
IPython/utils/__pycache__/eventful.cpython-310.pyc,,
IPython/utils/__pycache__/frame.cpython-310.pyc,,
IPython/utils/__pycache__/generics.cpython-310.pyc,,
IPython/utils/__pycache__/importstring.cpython-310.pyc,,
IPython/utils/__pycache__/io.cpython-310.pyc,,
IPython/utils/__pycache__/ipstruct.cpython-310.pyc,,
IPython/utils/__pycache__/jsonutil.cpython-310.pyc,,
IPython/utils/__pycache__/localinterfaces.cpython-310.pyc,,
IPython/utils/__pycache__/log.cpython-310.pyc,,
IPython/utils/__pycache__/module_paths.cpython-310.pyc,,
IPython/utils/__pycache__/openpy.cpython-310.pyc,,
IPython/utils/__pycache__/path.cpython-310.pyc,,
IPython/utils/__pycache__/process.cpython-310.pyc,,
IPython/utils/__pycache__/py3compat.cpython-310.pyc,,
IPython/utils/__pycache__/sentinel.cpython-310.pyc,,
IPython/utils/__pycache__/shimmodule.cpython-310.pyc,,
IPython/utils/__pycache__/signatures.cpython-310.pyc,,
IPython/utils/__pycache__/strdispatch.cpython-310.pyc,,
IPython/utils/__pycache__/sysinfo.cpython-310.pyc,,
IPython/utils/__pycache__/syspathcontext.cpython-310.pyc,,
IPython/utils/__pycache__/tempdir.cpython-310.pyc,,
IPython/utils/__pycache__/terminal.cpython-310.pyc,,
IPython/utils/__pycache__/text.cpython-310.pyc,,
IPython/utils/__pycache__/timing.cpython-310.pyc,,
IPython/utils/__pycache__/tokenutil.cpython-310.pyc,,
IPython/utils/__pycache__/traitlets.cpython-310.pyc,,
IPython/utils/__pycache__/tz.cpython-310.pyc,,
IPython/utils/__pycache__/ulinecache.cpython-310.pyc,,
IPython/utils/__pycache__/version.cpython-310.pyc,,
IPython/utils/__pycache__/wildcard.cpython-310.pyc,,
IPython/utils/_process_cli.py,sha256=PEh8SG16vAJLqqKUqoRZd5tawGiKArqwYjlMzVrOyZ8,2021
IPython/utils/_process_common.py,sha256=5T2LhdNL2ba9rN1lsGLfrx1QV_y852xsBKvttb1gJ7g,7003
IPython/utils/_process_emscripten.py,sha256=lGLQb2IgmanNtb502KflfuKIhgOF119Ji3cwo4fnJYg,503
IPython/utils/_process_posix.py,sha256=J3mli0wYipVz3hcTJ6jrXSCiWbKIB-z0bNEs4m5YL98,8747
IPython/utils/_process_win32.py,sha256=N5UYhQ5zQ_LrfsYYsE7ElclNJQnyPkjMd7DZC5gA3gg,6108
IPython/utils/_process_win32_controller.py,sha256=KKqpJW6voLs7kp0A_wRfM03EcAI_iv5BJxglR011qLI,21329
IPython/utils/_sysinfo.py,sha256=1ffEyHVWJEphmGs8MNBn-K9_YJIk4Vw1HH099judENw,45
IPython/utils/capture.py,sha256=seIkDR1LnmlsapRuvon5oiJhUc0pkUA-r_cCDHcH4AI,5161
IPython/utils/colorable.py,sha256=SCaeLJJlW14DZvoyaYPL-I6jLs78GA10xh0jCuSw8IY,786
IPython/utils/coloransi.py,sha256=3w2DJITSqT8OwocHgV1P-STsGmxUWKjlAv_vBKD5jaY,8768
IPython/utils/contexts.py,sha256=9fViIf4GWOp0-5sfUdjYwDSYdRnFTOMTGMTphqOpiO8,1619
IPython/utils/daemonize.py,sha256=91wAJwuFXA1FM8wGlbBJCPPF906jFJ-rdJOUp4b_5ZE,200
IPython/utils/data.py,sha256=36VVGY1b0JG7_zSdbVSy8IzLqM0uT-uB12TBYWgd1lI,1015
IPython/utils/decorators.py,sha256=qsYLskFlT2bB_Q-87ttBA56lAf-knWLOe5WiOwPdXFE,2680
IPython/utils/dir2.py,sha256=sHkGDtE_A9lD1Why3rAHb888M4u3qcu7G7wFmbkqOBg,2232
IPython/utils/docs.py,sha256=QY8n0cFrTS6gePmT3Tppv9Tzgc3Pzlf33XuzAwiE_wE,86
IPython/utils/encoding.py,sha256=jcvinrdvt1o_5FmQFlfJciB3U_asdkYnG0KogaS5rUQ,2843
IPython/utils/eventful.py,sha256=idbcT7GyZ43hrX9e6cI2bz7kKr389yMBE9hos6amMzE,138
IPython/utils/frame.py,sha256=XPVbwf05vtvUA9Ast7oaOJibgtvyYEOc8rJQ5f1cQdo,3122
IPython/utils/generics.py,sha256=vRjgikNsLi0iZUtTTMN-l7rcZBPOG2uc7dyYFRHLUgs,706
IPython/utils/importstring.py,sha256=aBtB6LJz7Tsa5kIsi9HprC7g2pTc-a0Jx2SwxkqxPqQ,1050
IPython/utils/io.py,sha256=fRW-4UuHO7gA4-kSmNcPg1_bnSKglfPvl8o3nNoltCk,4674
IPython/utils/ipstruct.py,sha256=XPfl1b0rRjISwyG3P1e2kJLSFHawzcPY71UBoa5v7qo,11856
IPython/utils/jsonutil.py,sha256=2QsAXueCIbbmjyz37Im9yXU41XK_XGJ9aGA7NGilDIs,148
IPython/utils/localinterfaces.py,sha256=7Wt6gcjgHX4mcWbx_k3Z1GL4iR388ZRqM3Hhhh4_Uwg,169
IPython/utils/log.py,sha256=3tYJLqZEa40zTX0tgKnpb-bvj4NhI64PijiRV6mnc78,123
IPython/utils/module_paths.py,sha256=lBlarunvhPIzQ0cUoZ6yIIYRYYIqMaaGTZ8IIK5A0IY,2327
IPython/utils/openpy.py,sha256=qnS9T2kfyVw5CLXvTkNivnbMzLZkaZ2v5SVJC2YCWVQ,3417
IPython/utils/path.py,sha256=Qq1rMNrPC2u5IP2SBSEf01oXvzqQ5tKxnNkonEjNdgU,12367
IPython/utils/process.py,sha256=EZkyui1PQgpuDBPvGPxC5a-qWP90KbJYza5SOqr8ltw,1990
IPython/utils/py3compat.py,sha256=e6RvspsCQXYsxUuuUJuqkr12ViAcO2Mkz-OpcQYmxic,1602
IPython/utils/sentinel.py,sha256=pRzWbnR7gjM5R3g0ZqjSo94QNJpxgKWOLXT-yY18FmU,421
IPython/utils/shimmodule.py,sha256=Sf0hohTA4a6zu7lxyKL_CFOSbMi4Y4D530lejwdaAXQ,2669
IPython/utils/signatures.py,sha256=dIrPwSPou7WQWs5018lzs3-dEKwG-dX3g4_2_OCvdg4,474
IPython/utils/strdispatch.py,sha256=jvwykBXsdZSHlssigzbAE65E4bxFz3yy6gPwyiVm34U,1840
IPython/utils/sysinfo.py,sha256=B4K3iYJt0504CB8j0Jo35h6F3OKISNeiQR_cHIbz1ZE,4372
IPython/utils/syspathcontext.py,sha256=BEEEuRIjnDO63aDtCkmE631SoxbTvHewmtPQwviU00Y,1952
IPython/utils/tempdir.py,sha256=tHwshFbHXzS-QhDoJX61apoGmQ7k0HmPGvjUvqtZDps,1867
IPython/utils/terminal.py,sha256=6Hy6oxSANxAxaM9-d1hMhIDyCfAP8TCovXOsAIfIWTU,3387
IPython/utils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/utils/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_capture.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_decorators.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_deprecated.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_dir2.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_imports.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_importstring.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_io.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_module_paths.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_openpy.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_path.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_process.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_pycolorize.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_shimmodule.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_sysinfo.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_tempdir.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_text.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_tokenutil.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_wildcard.cpython-310.pyc,,
IPython/utils/tests/test_capture.py,sha256=VfRuGBA7n521oa_uIN9_c_Lmyo0Vm912wuLV9xtDlCE,5319
IPython/utils/tests/test_decorators.py,sha256=y_Hp5tjT8YbXe87HLe0-b5qhU9BcEzKMUdzAJdy-OWg,170
IPython/utils/tests/test_deprecated.py,sha256=5QUNHDR5re_BowvOQKvM4d7vrstS-IPIQB6YNjqw6_o,183
IPython/utils/tests/test_dir2.py,sha256=XDnqXd1kWF30KeInbzvAqotst7KgbxE-2nC3HbTJBNg,1446
IPython/utils/tests/test_imports.py,sha256=6SWziRNPdGfA47SpcjXDJG7B6173EHT6vT39juEfBd0,439
IPython/utils/tests/test_importstring.py,sha256=3ga_tyi0vkn-WbUylEI8EixFsB45NVBH8MR_-e8orWY,1213
IPython/utils/tests/test_io.py,sha256=Os7nFtHtUQoSiy9PiFrK5rShYHQqsx-5HxN0WKgnQDs,1426
IPython/utils/tests/test_module_paths.py,sha256=S4v2D8Lh9EQImTo8bXWNVxad21-i1-FEYGJs4r_ULlY,3272
IPython/utils/tests/test_openpy.py,sha256=M_IYXSZPabQQJl3SwXmZCwju4vezIHlcNT9OqRDOQ4w,1219
IPython/utils/tests/test_path.py,sha256=cRASvprr8rpiDPLe3MMKHje12x6ZBwTr9Ntr-mEYLgg,15993
IPython/utils/tests/test_process.py,sha256=RBEWt4efV9UJjO3JnihFuuA6ZFAJj4n4G5nckhOINiU,6134
IPython/utils/tests/test_pycolorize.py,sha256=T9qQuv3PoGAtUkOopqZYX398L9_i90wrHn6obe7EmN4,1761
IPython/utils/tests/test_shimmodule.py,sha256=JEFbjzpXdDE-QUzgitFpVtnb0ve5NBjaacgz1uil3Tg,387
IPython/utils/tests/test_sysinfo.py,sha256=omDs63QvyPXSlJT2jqDihBfVXPx5zLyhnG-25-2M6HE,464
IPython/utils/tests/test_tempdir.py,sha256=sf_g4uJBGELf7nGZsWQwQlN8-X2PVOv9j2_Zs8-P-oY,1108
IPython/utils/tests/test_text.py,sha256=2xdzUmdDPJgUCf4ZA8QJ0aDPEskvPiD74bHQYp616N8,7760
IPython/utils/tests/test_tokenutil.py,sha256=7tXFsKesEBxe_5rkhU5msbkk_fRMRlCo0MwmAV6jH88,3918
IPython/utils/tests/test_wildcard.py,sha256=VJVK7LxaEYJKv6Tynlj4ye_GRyMHEI_3iT80i3PJuZk,4729
IPython/utils/text.py,sha256=vL78UmlB0moQrmlXFw9FhZhwPb6UyQMdEZKzciyZ3uU,26354
IPython/utils/timing.py,sha256=nND-ZUBkHWfYevvbRG-YfOSIFczz_epzMqWK5PH6nqA,4275
IPython/utils/tokenutil.py,sha256=7DimAl4ZHtkJUhu1LRwk4bgkgqdOnuwkxNPh3XtpA0o,5083
IPython/utils/traitlets.py,sha256=SnRNQ20hSnvceaWsTIjZRe2R90NAC58PVJl9bMt-41A,143
IPython/utils/tz.py,sha256=1MelnaJ9CvkP9d9tijklgwak5vJ7Kl5Bf8Oj9ZBywns,2035
IPython/utils/ulinecache.py,sha256=WySMSMJDRsm1Z5CGY24KdmoA2eLDZEayqWR6V_sIuik,684
IPython/utils/version.py,sha256=9SuuYeztj_lwbL_o-RLjfC4vJfel3HCde6NWY4KLt60,1223
IPython/utils/wildcard.py,sha256=6EEc3OEYp-IuSoidL6nwpaHg--GxnzbAJTmFiz77CNE,4612
ipython-8.37.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
ipython-8.37.0.dist-info/METADATA,sha256=RE4HbK4Jiq33S2_u68n2iU9euq0G6wARRBAEudBuJxY,5051
ipython-8.37.0.dist-info/RECORD,,
ipython-8.37.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ipython-8.37.0.dist-info/WHEEL,sha256=3jiOLqf2nBeayXpKb2zAE5Lj7nhy9rF38t4x0Rzf37s,91
ipython-8.37.0.dist-info/entry_points.txt,sha256=1078eGaXVuhZyDuwlmp1pWDc3HtDrYO9CZoXGAcGCw8,244
ipython-8.37.0.dist-info/licenses/COPYING.rst,sha256=NBr8vXKYh7cEb-e5j8T07f867Y048G7v2bMGcPBD3xc,1639
ipython-8.37.0.dist-info/licenses/LICENSE,sha256=4OOQdI7UQKuJPKHxNaiKkgqvVAnbuQpbQnx1xeUSaPs,1720
ipython-8.37.0.dist-info/top_level.txt,sha256=PKjvHtNCBZ9EHTmd2mwJ1J_k3j0F6D1lTFzIcJFFPEU,8
