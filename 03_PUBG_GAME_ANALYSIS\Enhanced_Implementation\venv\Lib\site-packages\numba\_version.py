
# This file was generated by 'versioneer.py' (0.28) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-04-07T21:57:15+0530",
 "dirty": false,
 "error": null,
 "full-revisionid": "1e70d8ceba56a135e046e32e1e7ad2fcd22fd8ab",
 "version": "0.61.2"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
