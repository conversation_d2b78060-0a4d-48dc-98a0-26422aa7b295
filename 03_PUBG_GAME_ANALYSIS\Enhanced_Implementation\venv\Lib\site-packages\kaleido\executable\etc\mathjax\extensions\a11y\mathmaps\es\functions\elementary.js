[{"locale": "es"}, {"key": "log", "names": ["log"], "mappings": {"default": {"default": "logaritmo"}}, "category": "Logarithm"}, {"key": "ln", "names": ["ln"], "mappings": {"default": {"default": "logarit<PERSON>"}}, "category": "Logarithm"}, {"key": "lg", "names": ["lg"], "mappings": {"default": {"default": "logaritmo base 10"}}, "category": "Logarithm"}, {"key": "exp", "names": ["exp", "expt"], "mappings": {"default": {"default": "exponente"}}, "category": "Elementary"}, {"key": "gcd", "names": ["gcd"], "mappings": {"default": {"default": "MCD"}}, "category": "Elementary"}, {"key": "lcm", "names": ["lcm"], "mappings": {"default": {"default": "mcm"}}, "category": "Elementary"}, {"key": "arg", "names": ["arg"], "mappings": {"default": {"default": "argumento"}}, "category": "Complex"}, {"key": "im", "names": ["im"], "mappings": {"default": {"default": "parte imaginaria"}}, "category": "Complex"}, {"key": "re", "names": ["re"], "mappings": {"default": {"default": "residuo"}}, "category": "Complex"}, {"key": "inf", "names": ["inf"], "mappings": {"default": {"default": "extremo inferior"}}, "category": "Limits"}, {"key": "lim", "names": ["lim"], "mappings": {"default": {"default": "límite"}}, "category": "Limits"}, {"key": "max", "names": ["max"], "mappings": {"default": {"default": "máxi<PERSON>"}}, "category": "Limits"}, {"key": "min", "names": ["min"], "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}, "category": "Limits"}, {"key": "sup", "names": ["sup"], "mappings": {"default": {"default": "superior"}}, "category": "Limits"}, {"key": "lim inf", "names": ["lim inf", "liminf"], "mappings": {"default": {"default": "límite inferior"}}, "category": "Limits"}, {"key": "lim sup", "names": ["lim sup", "limsup"], "mappings": {"default": {"default": "límite superior"}}, "category": "Limits"}, {"key": "<PERSON><PERSON><PERSON>", "names": ["<PERSON><PERSON><PERSON>", "inj lim"], "mappings": {"default": {"default": "límite directo"}}, "category": "Limits"}, {"key": "proj<PERSON>", "names": ["proj<PERSON>", "proj lim"], "mappings": {"default": {"default": "límite inverso"}}, "category": "Limits"}, {"key": "mod", "names": ["mod"], "mappings": {"default": {"default": "m<PERSON><PERSON><PERSON>"}}, "category": "Elementary"}, {"key": "Pr", "names": ["Pr"], "mappings": {"default": {"default": "probabilidad"}}, "category": "Probability"}]