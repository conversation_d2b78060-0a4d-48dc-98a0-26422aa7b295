/*************************************************************
 *
 *  MathJax/jax/output/CommonHTML/fonts/TeX/Fraktur-Regular.js
 *
 *  Copyright (c) 2015-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

(function (CHTML) {

var font = 'MathJax_Fraktur';

CHTML.FONTDATA.FONTS[font] = {
  className: CHTML.FONTDATA.familyName(font),
  centerline: 258, ascent: 740, descent: 224,
  0x20: [0,0,250,0,0],               // SPACE
  0x21: [689,12,296,91,204],         // EXCLAMATION MARK
  0x22: [695,-432,215,8,196],        // QUOTATION MARK
  0x26: [698,11,738,49,733],         // AMPERSAND
  0x27: [695,-436,212,69,134],       // APOSTROPHE
  0x28: [737,186,389,114,293],       // LEFT PARENTHESIS
  0x29: [735,187,389,89,276],        // RIGHT PARENTHESIS
  0x2A: [692,-449,278,33,234],       // ASTERISK
  0x2B: [598,82,756,47,709],         // PLUS SIGN
  0x2C: [107,191,278,99,213],        // COMMA
  0x2D: [275,-236,756,46,706],       // HYPHEN-MINUS
  0x2E: [102,15,278,87,200],         // FULL STOP
  0x2F: [721,182,502,34,466],        // SOLIDUS
  0x30: [492,13,502,42,456],         // DIGIT ZERO
  0x31: [468,2,502,47,460],          // DIGIT ONE
  0x32: [474,-1,502,60,484],         // DIGIT TWO
  0x33: [473,182,502,39,429],        // DIGIT THREE
  0x34: [476,191,502,10,481],        // DIGIT FOUR
  0x35: [458,184,502,47,440],        // DIGIT FIVE
  0x36: [700,13,502,45,471],         // DIGIT SIX
  0x37: [468,181,502,37,498],        // DIGIT SEVEN
  0x38: [705,10,502,40,461],         // DIGIT EIGHT
  0x39: [469,182,502,28,466],        // DIGIT NINE
  0x3A: [457,12,216,50,168],         // COLON
  0x3B: [458,189,216,47,179],        // SEMICOLON
  0x3D: [368,-132,756,54,725],       // EQUALS SIGN
  0x3F: [693,11,362,46,357],         // QUESTION MARK
  0x41: [696,26,718,22,708],         // LATIN CAPITAL LETTER A
  0x42: [691,27,884,48,820],         // LATIN CAPITAL LETTER B
  0x43: [685,24,613,59,607],         // LATIN CAPITAL LETTER C
  0x44: [685,27,832,27,745],         // LATIN CAPITAL LETTER D
  0x45: [685,24,663,86,634],         // LATIN CAPITAL LETTER E
  0x46: [686,153,611,11,612],        // LATIN CAPITAL LETTER F
  0x47: [690,26,785,66,710],         // LATIN CAPITAL LETTER G
  0x48: [666,133,720,1,644],         // LATIN CAPITAL LETTER H
  0x49: [686,26,554,30,532],         // LATIN CAPITAL LETTER I
  0x4A: [686,139,552,-10,522],       // LATIN CAPITAL LETTER J
  0x4B: [680,27,668,17,682],         // LATIN CAPITAL LETTER K
  0x4C: [686,26,666,33,644],         // LATIN CAPITAL LETTER L
  0x4D: [692,27,1050,27,1048],       // LATIN CAPITAL LETTER M
  0x4E: [686,25,832,27,825],         // LATIN CAPITAL LETTER N
  0x4F: [729,27,827,12,744],         // LATIN CAPITAL LETTER O
  0x50: [692,218,828,28,804],        // LATIN CAPITAL LETTER P
  0x51: [729,69,827,11,782],         // LATIN CAPITAL LETTER Q
  0x52: [686,26,828,27,824],         // LATIN CAPITAL LETTER R
  0x53: [692,27,829,66,756],         // LATIN CAPITAL LETTER S
  0x54: [701,27,669,34,676],         // LATIN CAPITAL LETTER T
  0x55: [697,27,646,-25,665],        // LATIN CAPITAL LETTER U
  0x56: [686,26,831,26,825],         // LATIN CAPITAL LETTER V
  0x57: [686,27,1046,32,1054],       // LATIN CAPITAL LETTER W
  0x58: [688,27,719,28,709],         // LATIN CAPITAL LETTER X
  0x59: [686,218,833,27,740],        // LATIN CAPITAL LETTER Y
  0x5A: [729,139,602,11,532],        // LATIN CAPITAL LETTER Z
  0x5B: [740,130,278,117,278],       // LEFT SQUARE BRACKET
  0x5D: [738,131,278,-4,160],        // RIGHT SQUARE BRACKET
  0x5E: [734,-452,500,0,495],        // CIRCUMFLEX ACCENT
  0x61: [470,35,500,66,497],         // LATIN SMALL LETTER A
  0x62: [685,31,513,87,442],         // LATIN SMALL LETTER B
  0x63: [466,29,389,72,359],         // LATIN SMALL LETTER C
  0x64: [609,33,499,13,428],         // LATIN SMALL LETTER D
  0x65: [467,30,401,70,364],         // LATIN SMALL LETTER E
  0x66: [681,221,326,30,323],        // LATIN SMALL LETTER F
  0x67: [470,209,504,17,455],        // LATIN SMALL LETTER G
  0x68: [688,205,521,77,434],        // LATIN SMALL LETTER H
  0x69: [673,20,279,14,267],         // LATIN SMALL LETTER I
  0x6A: [672,208,281,-9,196],        // LATIN SMALL LETTER J
  0x6B: [689,25,389,24,362],         // LATIN SMALL LETTER K
  0x6C: [685,20,280,98,276],         // LATIN SMALL LETTER L
  0x6D: [475,26,767,8,753],          // LATIN SMALL LETTER M
  0x6E: [475,22,527,20,514],         // LATIN SMALL LETTER N
  0x6F: [480,28,489,67,412],         // LATIN SMALL LETTER O
  0x70: [541,212,500,12,430],        // LATIN SMALL LETTER P
  0x71: [479,219,489,60,419],        // LATIN SMALL LETTER Q
  0x72: [474,21,389,17,387],         // LATIN SMALL LETTER R
  0x73: [478,29,443,-18,406],        // LATIN SMALL LETTER S
  0x74: [640,20,333,27,348],         // LATIN SMALL LETTER T
  0x75: [474,23,517,9,513],          // LATIN SMALL LETTER U
  0x76: [530,28,512,55,434],         // LATIN SMALL LETTER V
  0x77: [532,28,774,45,688],         // LATIN SMALL LETTER W
  0x78: [472,188,389,10,363],        // LATIN SMALL LETTER X
  0x79: [528,218,499,45,431],        // LATIN SMALL LETTER Y
  0x7A: [471,214,391,-7,314],        // LATIN SMALL LETTER Z
  0xA0: [0,0,250,0,0],               // NO-BREAK SPACE
  0x2018: [708,-410,215,45,158],     // LEFT SINGLE QUOTATION MARK
  0x2019: [692,-395,215,49,163],     // RIGHT SINGLE QUOTATION MARK
  0xE300: [683,32,497,75,430],       // stix-capital Gamma, Greek slashed
  0xE301: [616,30,498,35,432],       // stix-MATHEMATICAL BOLD CAPITAL GAMMA SLASHED
  0xE302: [680,215,333,29,339],      // stix-capital Delta, Greek slashed
  0xE303: [679,224,329,28,318],      // stix-MATHEMATICAL BOLD CAPITAL DELTA SLASHED
  0xE304: [471,214,503,52,449],      // stix-capital Epsilon, Greek slashed
  0xE305: [686,20,333,26,315],       // stix-MATHEMATICAL BOLD CAPITAL EPSILON SLASHED
  0xE306: [577,21,334,29,347],       // stix-capital Zeta, Greek slashed
  0xE307: [475,22,501,10,514]        // stix-MATHEMATICAL BOLD CAPITAL ZETA SLASHED
};

CHTML.fontLoaded("TeX/"+font.substr(8));

})(MathJax.OutputJax.CommonHTML);
