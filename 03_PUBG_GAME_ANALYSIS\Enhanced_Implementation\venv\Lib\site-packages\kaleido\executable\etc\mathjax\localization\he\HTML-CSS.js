/*************************************************************
 *
 *  MathJax/localization/he/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("he","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "\u05D8\u05E2\u05D9\u05E0\u05EA \u05D2\u05D5\u05E4\u05DF \u05D4\u05E8\u05E9\u05EA %1",
          CantLoadWebFont: "\u05DC\u05D0 \u05E0\u05D9\u05EA\u05DF \u05DC\u05D8\u05E2\u05D5\u05DF \u05D0\u05EA \u05D2\u05D5\u05E4\u05DF \u05D4\u05E8\u05E9\u05EA %1",
          FirefoxCantLoadWebFont: "\u05E4\u05D9\u05D9\u05E8\u05E4\u05D5\u05E7\u05E1 \u05D0\u05D9\u05E0\u05D5 \u05D9\u05DB\u05D5\u05DC \u05DC\u05D8\u05E2\u05D5\u05DF \u05D2\u05D5\u05E4\u05E0\u05D9 \u05E8\u05E9\u05EA \u05DE\u05E9\u05E8\u05EA \u05DE\u05E8\u05D5\u05D7\u05E7",
          CantFindFontUsing: "\u05DC\u05D0 \u05E0\u05DE\u05E6\u05D0 \u05D2\u05D5\u05E4\u05DF \u05EA\u05E7\u05D9\u05DF \u05D1\u05D0\u05DE\u05E6\u05E2\u05D5\u05EA %1",
          WebFontsNotAvailable: "\u05D2\u05D5\u05E4\u05E0\u05D9 \u05E8\u05E9\u05EA \u05D0\u05D9\u05E0\u05DD \u05D6\u05DE\u05D9\u05E0\u05D9\u05DD \u2013 \u05D1\u05DE\u05E7\u05D5\u05DE\u05DD \u05DE\u05E9\u05DE\u05E9\u05D9\u05DD \u05D2\u05D5\u05E4\u05E0\u05D9 \u05EA\u05DE\u05D5\u05E0\u05D4"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/he/HTML-CSS.js");
