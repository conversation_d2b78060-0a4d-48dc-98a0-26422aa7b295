/*************************************************************
 *
 *  MathJax/localization/ca/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ca","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "Mostra f\u00F3rmules com a",
          MathMLcode: "Codi MathML",
          OriginalMathML: "MathML original",
          TeXCommands: "Comands TeX",
          AsciiMathInput: "Entrada de AsciiMathML",
          Original: "Forma original",
          ErrorMessage: "Missatge d'error",
          Annotation: "Anotaci\u00F3",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "Contingut MathML",
          OpenMath: "OpenMath",
          texHints: "Mostra ajudes TeX en MathML",
          Settings: "Configuraci\u00F3 de MathML",
          ZoomTrigger: "Control de zoom",
          Hover: "Passar el ratol\u00ED per sobre",
          Click: "Clic",
          DoubleClick: "Doble-clic",
          NoZoom: "Sense zoom",
          TriggerRequires: "El control requereix:",
          Option: "Opci\u00F3",
          Alt: "Alt",
          Command: "Comand",
          Control: "Control",
          Shift: "Maj\u00FAscules",
          ZoomFactor: "Factor de zoom",
          Renderer: "Processador de f\u00F3rmules",
          MPHandles: "Permetre que MathPlayer controli:",
          MenuEvents: "Events de men\u00FA",
          MouseEvents: "Events de ratol\u00ED",
          MenuAndMouse: "Events de ratol\u00ED i de men\u00FA",
          FontPrefs: "Prefer\u00E8ncies de font",
          ForHTMLCSS: "Per HTML-CSS:",
          Auto: "Auto",
          TeXLocal: "TeX (local)",
          TeXWeb: "TeX (web)",
          TeXImage: "TeX (imatge)",
          STIXLocal: "STIX (local)",
          STIXWeb: "STIX (web)",
          AsanaMathWeb: "Asana Math (web)",
          GyrePagellaWeb: "Gyre Pagella (web)",
          GyreTermesWeb: "Gyre Termes (web)",
          LatinModernWeb: "Latin Modern (web)",
          NeoEulerWeb: "Neo Euler (web)",
          ContextMenu: "Men\u00FA contextual",
          Browser: "Navegador",
          Scale: "Escalar totes les f\u00F3mules...",
          Discoverable: "Iluminar quan el ratol\u00ED passi per sobre",
          Locale: "Idioma",
          LoadLocale: "Carrega des de l'URL...",
          About: "Sobre MathJax",
          Help: "Ajuda de MathJax",
          localTeXfonts: "usar fonts TeX locals",
          webTeXfonts: "usar font web TeX",
          imagefonts: "usar fonts d'imatge",
          localSTIXfonts: "usar fonts STIX locals",
          webSVGfonts: "usar fonts SVG web",
          genericfonts: "usar fonts gen\u00E8riques Unicode",
          wofforotffonts: "fonts WOFF o OTF",
          eotffonts: "fonts EOT",
          svgfonts: "fonts SVG",
          WebkitNativeMMLWarning: "Sembla que el vostre navegador no permet MathML de forma nativa; si canvieu a la visualitzaci\u00F3 MathML pot ser que les f\u00F3rmules de la p\u00E0gina no es puguin llegir correctament",
          MSIENativeMMLWarning: "Internet Explorer requereix el plugin MathPlayer per processar el resultat de MathML",
          OperaNativeMMLWarning: "El suport d'Opera per MathML \u00E9s limitat; si canvieu a la visualitzaci\u00F3 MathML pot ser que algunes expressions no es visualitzin correctament.",
          SafariNativeMMLWarning: "El MathML del vostre navegador no implementa totes les caracter\u00EDstiques usades per MathJax; algunes expressions poden no visualitzar-se correctament.",
          FirefoxNativeMMLWarning: "El MathML del vostre navegador no implementa totes les caracter\u00EDstiques usades per MathJax; algunes expressions poden no visualitzar-se correctament.",
          MSIESVGWarning: "SVG no est\u00E0 implementat en Internet Explorer anterior a IE9 o quan est\u00E0 emulant IE8 o anterior. Si canvieu a visualitzaci\u00F3 SVG, les f\u00F3rmules no es visualitzaran correctament.",
          LoadURL: "Carregar les dades de traducci\u00F3 d'aquesta URL:",
          BadURL: "La URL ha de ser per un arxiu JavaScript que defineixi les dades de traducci\u00F3 per MathJax. Els noms d'arxius JavaScript han de finalitzar amb '.js'",
          BadData: "No s'han pogut carregar les dades de traducci\u00F3 des de %1",
          SwitchAnyway: "Canviar la visualitzaci\u00F3 de totes maneres?\n\n(Premeu Acceptar per canviar, Cancel\u00B7lar per continuar amb la visualitzaci\u00F3 actual)",
          ScaleMath: "Escalar totes les f\u00F3rmules (en comparaci\u00F3 amb el text adjacent) per",
          NonZeroScale: "L'escalat no pot ser zero",
          PercentScale: "L'escalat ha de ser un percentatge (per exemple 120%%)",
          IE8warning: "Aix\u00F2 desactivar\u00E0 les caracter\u00EDstiques de men\u00FA i zoom de MathJax, per\u00F2 podeu fer Alt-clic sobre una expressi\u00F3 per mostrar el men\u00FA MathJax.\n\nRealment voleu canviar la configuraci\u00F3 de MathPlayer?",
          IE9warning: "El men\u00FA contextual de MathJax es desactivar\u00E0, per\u00F2 podeu fer Alt-clic sobre una expressi\u00F3 per mostrar el men\u00FA MathJax.",
          NoOriginalForm: "No s'ha trobat cap forma original",
          Close: "Tanca",
          EqSource: "Codi font d'equaci\u00F3 MathJax",
          CloseAboutDialog: "Tanca el di\u00E0leg de quant al MathJax",
          FastPreview: "Vista pr\u00E8via r\u00E0pida",
          AssistiveMML: "MathML auxiliar",
          InTabOrder: "Inclou en l'ordre de pestanyes"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ca/MathMenu.js");
