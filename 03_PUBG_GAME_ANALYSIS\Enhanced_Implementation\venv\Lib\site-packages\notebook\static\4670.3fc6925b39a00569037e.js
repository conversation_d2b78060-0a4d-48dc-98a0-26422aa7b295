"use strict";
(self["webpackChunk_JUPYTERLAB_CORE_OUTPUT"] = self["webpackChunk_JUPYTERLAB_CORE_OUTPUT"] || []).push([[4670],{

/***/ 44670:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  json: () => (/* binding */ json),
  jsonLanguage: () => (/* binding */ jsonLanguage),
  jsonParseLinter: () => (/* binding */ jsonParseLinter)
});

// EXTERNAL MODULE: ../node_modules/@lezer/lr/dist/index.js
var dist = __webpack_require__(49906);
// EXTERNAL MODULE: consume shared module (default) @lezer/highlight@~1.2.0 (singleton) (fallback: ../node_modules/@lezer/highlight/dist/index.js)
var index_js_ = __webpack_require__(92209);
;// CONCATENATED MODULE: ../node_modules/@lezer/json/dist/index.js



const jsonHighlighting = (0,index_js_.styleTags)({
  String: index_js_.tags.string,
  Number: index_js_.tags.number,
  "True False": index_js_.tags.bool,
  PropertyName: index_js_.tags.propertyName,
  Null: index_js_.tags.null,
  ",": index_js_.tags.separator,
  "[ ]": index_js_.tags.squareBracket,
  "{ }": index_js_.tags.brace
});

// This file was generated by lezer-generator. You probably shouldn't edit it.
const parser = dist/* LRParser */.WQ.deserialize({
  version: 14,
  states: "$bOVQPOOOOQO'#Cb'#CbOnQPO'#CeOvQPO'#CjOOQO'#Cp'#CpQOQPOOOOQO'#Cg'#CgO}QPO'#CfO!SQPO'#CrOOQO,59P,59PO![QPO,59PO!aQPO'#CuOOQO,59U,59UO!iQPO,59UOVQPO,59QOqQPO'#CkO!nQPO,59^OOQO1G.k1G.kOVQPO'#ClO!vQPO,59aOOQO1G.p1G.pOOQO1G.l1G.lOOQO,59V,59VOOQO-E6i-E6iOOQO,59W,59WOOQO-E6j-E6j",
  stateData: "#O~OcOS~OQSORSOSSOTSOWQO]ROePO~OVXOeUO~O[[O~PVOg^O~Oh_OVfX~OVaO~OhbO[iX~O[dO~Oh_OVfa~OhbO[ia~O",
  goto: "!kjPPPPPPkPPkqwPPk{!RPPP!XP!ePP!hXSOR^bQWQRf_TVQ_Q`WRg`QcZRicQTOQZRQe^RhbRYQR]R",
  nodeNames: "⚠ JsonText True False Null Number String } { Object Property PropertyName ] [ Array",
  maxTerm: 25,
  nodeProps: [
    ["openedBy", 7,"{",12,"["],
    ["closedBy", 8,"}",13,"]"]
  ],
  propSources: [jsonHighlighting],
  skippedNodes: [0],
  repeatNodeCount: 2,
  tokenData: "(|~RaXY!WYZ!W]^!Wpq!Wrs!]|}$u}!O$z!Q!R%T!R![&c![!]&t!}#O&y#P#Q'O#Y#Z'T#b#c'r#h#i(Z#o#p(r#q#r(w~!]Oc~~!`Wpq!]qr!]rs!xs#O!]#O#P!}#P;'S!];'S;=`$o<%lO!]~!}Oe~~#QXrs!]!P!Q!]#O#P!]#U#V!]#Y#Z!]#b#c!]#f#g!]#h#i!]#i#j#m~#pR!Q![#y!c!i#y#T#Z#y~#|R!Q![$V!c!i$V#T#Z$V~$YR!Q![$c!c!i$c#T#Z$c~$fR!Q![!]!c!i!]#T#Z!]~$rP;=`<%l!]~$zOh~~$}Q!Q!R%T!R![&c~%YRT~!O!P%c!g!h%w#X#Y%w~%fP!Q![%i~%nRT~!Q![%i!g!h%w#X#Y%w~%zR{|&T}!O&T!Q![&Z~&WP!Q![&Z~&`PT~!Q![&Z~&hST~!O!P%c!Q![&c!g!h%w#X#Y%w~&yOg~~'OO]~~'TO[~~'WP#T#U'Z~'^P#`#a'a~'dP#g#h'g~'jP#X#Y'm~'rOR~~'uP#i#j'x~'{P#`#a(O~(RP#`#a(U~(ZOS~~(^P#f#g(a~(dP#i#j(g~(jP#X#Y(m~(rOQ~~(wOW~~(|OV~",
  tokenizers: [0],
  topRules: {"JsonText":[0,1]},
  tokenPrec: 0
});



// EXTERNAL MODULE: consume shared module (default) @codemirror/language@^6.11.0 (strict) (fallback: ../node_modules/@codemirror/language/dist/index.js)
var dist_index_js_ = __webpack_require__(27914);
;// CONCATENATED MODULE: ../node_modules/@codemirror/lang-json/dist/index.js



/**
Calls
[`JSON.parse`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse)
on the document and, if that throws an error, reports it as a
single diagnostic.
*/
const jsonParseLinter = () => (view) => {
    try {
        JSON.parse(view.state.doc.toString());
    }
    catch (e) {
        if (!(e instanceof SyntaxError))
            throw e;
        const pos = getErrorPosition(e, view.state.doc);
        return [{
                from: pos,
                message: e.message,
                severity: 'error',
                to: pos
            }];
    }
    return [];
};
function getErrorPosition(error, doc) {
    let m;
    if (m = error.message.match(/at position (\d+)/))
        return Math.min(+m[1], doc.length);
    if (m = error.message.match(/at line (\d+) column (\d+)/))
        return Math.min(doc.line(+m[1]).from + (+m[2]) - 1, doc.length);
    return 0;
}

/**
A language provider that provides JSON parsing.
*/
const jsonLanguage = /*@__PURE__*/dist_index_js_.LRLanguage.define({
    name: "json",
    parser: /*@__PURE__*/parser.configure({
        props: [
            /*@__PURE__*/dist_index_js_.indentNodeProp.add({
                Object: /*@__PURE__*/(0,dist_index_js_.continuedIndent)({ except: /^\s*\}/ }),
                Array: /*@__PURE__*/(0,dist_index_js_.continuedIndent)({ except: /^\s*\]/ })
            }),
            /*@__PURE__*/dist_index_js_.foldNodeProp.add({
                "Object Array": dist_index_js_.foldInside
            })
        ]
    }),
    languageData: {
        closeBrackets: { brackets: ["[", "{", '"'] },
        indentOnInput: /^\s*[\}\]]$/
    }
});
/**
JSON language support.
*/
function json() {
    return new dist_index_js_.LanguageSupport(jsonLanguage);
}




/***/ })

}]);
//# sourceMappingURL=4670.3fc6925b39a00569037e.js.map?v=3fc6925b39a00569037e