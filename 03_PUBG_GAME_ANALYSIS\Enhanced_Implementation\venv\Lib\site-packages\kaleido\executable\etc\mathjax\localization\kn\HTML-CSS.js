/*************************************************************
 *
 *  MathJax/localization/kn/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("kn","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "\u0CB5\u0CC6\u0CAC\u0CCD \u0CAB\u0CBE\u0C82\u0C9F\u0CCD  %1 \u0C85\u0CA8\u0CCD\u0CA8\u0CC1 \u0CA1\u0CCC\u0CA8\u0CCD\u0CB2\u0CCB\u0CA1\u0CCD \u0CAE\u0CBE\u0CA1\u0CC1\u0CA4 \u0C89\u0C82\u0C9F\u0CC1",
          CantLoadWebFont: " \u0CB5\u0CC6\u0CAC\u0CCD \u0CAB\u0CBE\u0C82\u0C9F\u0CCD  %1 \u0C85\u0CA8\u0CCD\u0CA8\u0CC1 \u0CA1\u0CCC\u0CA8\u0CCD\u0CB2\u0CCB\u0CA1\u0CCD \u0CAE\u0CBE\u0CA1\u0CB2\u0CBF\u0C95\u0CCD\u0C95\u0CC6 \u0C86\u0C97\u0CC1\u0CA6\u0CC1 \u0C87\u0CB2\u0CCD\u0CB2",
          FirefoxCantLoadWebFont: "\u0CAB\u0CC8\u0CB0\u0CCD\u0CAB\u0CBE\u0C95\u0CCD\u0CB8\u0CCD \u0C87\u0C97\u0CC6 \u0C92\u0C82\u0CA6\u0CC1 \u0CA6\u0CC2\u0CB0\u0CA6\u0CCD\u0CA6\u0CC1 \u0CB9\u0CCB\u0CB8\u0CCD\u0C9F\u0CCD \u0C87\u0C82\u0CA6 \u0CB5\u0CC6\u0CAC\u0CCD \u0CAB\u0CBE\u0C82\u0C9F\u0CCD \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0C87\u0CA8\u0CCD\u0CB8\u0CCD\u0C9F\u0CBE\u0CB2\u0CCD \u0CAE\u0CBE\u0CA1\u0CB2\u0CBF\u0C95\u0CCD\u0C95\u0CC6 \u0C86\u0C97\u0CC1\u0CA6\u0CBF\u0CB2\u0CCD\u0CB2.",
          CantFindFontUsing: "%1 \u0C92\u0C9F\u0CCD\u0C9F\u0CBF\u0C97\u0CC6 \u0C92\u0C82\u0CA6\u0CC1  \u0CB8\u0CB0\u0CBF\u0CAF\u0CBE\u0CA6 \u0CAB\u0CBE\u0C82\u0C9F\u0CCD \u0CB8\u0CBF\u0C97\u0CC1\u0CA4 \u0C87\u0CB2\u0CCD\u0CB2",
          WebFontsNotAvailable: "\u0CB5\u0CC6\u0CAC\u0CCD \u0CAB\u0CBE\u0C82\u0C9F\u0CCD \u0C97\u0CB3\u0CC1 \u0CB8\u0CBF\u0C97\u0CC1\u0CA4 \u0C87\u0CB2\u0CCD\u0CB2, \u0C9A\u0CBF\u0CA4\u0CCD\u0CB0 \u0CAB\u0CBE\u0C82\u0C9F\u0CCD \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0CAE\u0CBE\u0CA1\u0CBF."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/kn/HTML-CSS.js");
