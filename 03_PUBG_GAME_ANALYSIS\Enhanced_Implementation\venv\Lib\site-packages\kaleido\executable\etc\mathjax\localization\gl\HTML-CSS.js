/*************************************************************
 *
 *  MathJax/localization/gl/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("gl","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "Cargando o tipo de letra web %1",
          CantLoadWebFont: "Non se pode cargar o tipo de letra web %1",
          FirefoxCantLoadWebFont: "Firefox non pode cargar os tipos de letra web desde un servidor remoto",
          CantFindFontUsing: "Non se pode atopar un tipo de letra v\u00E1lido utilizando %1",
          WebFontsNotAvailable: "Os tipos de letra web non est\u00E1n dispo\u00F1ibles; use os tipos de letra de imaxe no seu lugar"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/gl/HTML-CSS.js");
