<!--
  ~ Copyright (c) Jupyter Development Team.
  ~ Distributed under the terms of the Modified BSD License.
-->

{# Copy so we do not modify the page_config with updates. #}
{% set page_config_full = page_config.copy() %}

{# Set a dummy variable - we just want the side effect of the update. #}
{% set _ = page_config_full.update(baseUrl=base_url, wsUrl=ws_url) %}

  <script id="jupyter-config-data" type="application/json">
    {{ page_config_full | tojson }}
  </script>

  {% block favicon %}
  <link rel="icon" type="image/x-icon" href="{{ base_url | escape }}static/favicons/favicon.ico" class="idle favicon">
  <link rel="" type="image/x-icon" href="{{ base_url | escape }}static/favicons/favicon-busy-1.ico" class="busy favicon">
  {% endblock %}

  {% if custom_css %}
  <link rel="stylesheet" type="text/css" href="{{ base_url | escape }}custom/custom.css">
  {% endif %}
