/*************************************************************
 *
 *  MathJax/localization/en/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("en","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "MathJax Help",
          MathJax: "*MathJax* is a JavaScript library that allows page authors to include mathematics within their web pages. As a reader, you do not need to do anything to make that happen.",
          Browsers: "*Browsers*: MathJax works with all modern browsers including IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ and most mobile browsers.",
          Menu: "*Math menu*: MathJax adds a contextual menu to equations. Right-click or Ctrl-click on any mathematics to access the menu.",
          ShowMath: "*Show math as* allows you to view the formula's source markup for copy \u0026 paste (as MathML or in its original format).",
          Settings: "*Settings* gives you control over features of MathJax, such as the size of the mathematics, and the mechanism used to display equations.",
          Language: "*Language* lets you select the language used by MathJax for its menus and warning messages.",
          Zoom: "*Math zoom*: If you are having difficulty reading an equation, MathJax can enlarge it to help you see it better.",
          Accessibilty: "*Accessibility*: MathJax will automatically work with screen readers to make mathematics accessible to the visually impaired.",
          Fonts: "*Fonts*: MathJax will use certain math fonts if they are installed on your computer; otherwise, it will use web-based fonts. Although not required, locally installed fonts will speed up typesetting. We suggest installing the [STIX fonts](%1).",
          CloseDialog: "Close help dialog"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/en/HelpDialog.js");
