/*************************************************************
 *
 *  MathJax/localization/mk/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("mk","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "\u0413\u043E \u0432\u0447\u0438\u0442\u0443\u0432\u0430\u043C \u043C\u0440\u0435\u0436\u043D\u0438\u043E\u0442 \u0444\u043E\u043D\u0442 %1",
          CantLoadWebFont: "\u041D\u0435 \u043C\u043E\u0436\u0430\u043C \u0434\u0430 \u0433\u043E \u0432\u0447\u0438\u0442\u0430\u043C \u043C\u0440\u0435\u0436\u043D\u0438\u043E\u0442 \u0444\u043E\u043D\u0442 %1",
          FirefoxCantLoadWebFont: "Firefox \u043D\u0435 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0432\u0447\u0438\u0442\u0430 \u043C\u0440\u0435\u0436\u043D\u0438 \u0444\u043E\u043D\u0442\u043E\u0432\u0438 \u043E\u0434 \u0434\u0430\u043B\u0435\u0447\u0438\u043D\u0441\u043A\u0438 \u0434\u043E\u043C\u0430\u045C\u0438\u043D",
          CantFindFontUsing: "\u041D\u0435 \u043C\u043E\u0436\u0430\u043C \u0434\u0430 \u043D\u0430\u0458\u0434\u0430\u043C \u043F\u0440\u0438\u043A\u043B\u0430\u0434\u0435\u043D \u0444\u043E\u043D\u0442 \u0448\u0442\u043E \u043A\u043E\u0440\u0438\u0441\u0442\u0438 %1",
          WebFontsNotAvailable: "\u041C\u0440\u0435\u0436\u043D\u0438\u0442\u0435 \u0444\u043E\u043D\u0442\u043E\u0432\u0438 \u0441\u0435 \u043D\u0435\u0434\u043E\u0441\u0442\u0430\u043F\u043D\u0438. \u040C\u0435 \u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043C \u0441\u043B\u0438\u043A\u043E\u0432\u043D\u0438."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/mk/HTML-CSS.js");
