/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Main/Bold/GeometricShapes.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Main-bold'],
  {
    // WHITE UP-POINTING TRIANGLE
    0x25B3: [711,-1,1022,69,953,'91 1Q69 10 69 31Q69 39 81 59T168 197Q327 447 485 697Q493 711 510 711Q523 711 532 702Q536 697 743 371T951 41Q953 35 953 31Q953 12 931 1H91ZM690 340Q651 401 604 476T534 586L512 621Q511 622 507 616Q498 604 332 342L154 62L333 61H689L867 62L690 340'],

    // WHITE RIGHT-POINTING SMALL TRIANGLE
    0x25B9: [540,39,575,33,542,'33 518Q45 540 63 540H65Q72 540 174 481Q247 439 302 407Q529 276 533 272Q542 263 542 250Q542 238 533 229Q528 224 304 95T71 -38Q69 -39 63 -39Q42 -39 33 -16V518ZM449 251L94 456Q93 456 93 251Q93 45 94 45L106 52Q119 59 139 71T186 98T242 131T301 165T357 197T404 225T437 244L449 251'],

    // WHITE DOWN-POINTING TRIANGLE
    0x25BD: [500,210,1022,68,953,'68 470Q68 481 75 489T91 499H93Q296 500 512 500H931Q932 499 937 496T945 490T950 482T953 469Q953 465 951 459Q950 455 743 129T532 -202Q524 -210 511 -210Q497 -210 489 -202Q486 -199 281 124T71 456Q68 462 68 470ZM154 439Q155 437 332 158T510 -122Q510 -123 533 -87T600 18T688 157Q866 437 866 438Q867 439 805 439T511 439H154'],

    // WHITE LEFT-POINTING SMALL TRIANGLE
    0x25C3: [539,38,575,33,542,'542 -14Q533 -38 514 -38H512Q503 -38 498 -35Q494 -34 270 95T42 229Q33 238 33 251Q33 259 35 264Q36 265 38 268T42 272Q48 278 271 407T504 539H508Q533 539 542 515V-14ZM481 251Q481 456 480 456Q125 252 124 251Q124 250 301 148T480 45T481 251'],

    // LARGE CIRCLE
    0x25EF: [711,212,1150,65,1084,'65 42T65 250T204 584T574 711Q795 711 935 594Q955 577 974 555T1022 490T1067 385T1084 250Q1084 42 945 -84T574 -211T204 -85ZM1024 250Q1024 431 903 540T578 650Q482 650 404 627T274 565T189 474T140 366T125 250Q125 123 186 31T347 -106T573 -150Q772 -150 898 -45T1024 250']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/GeometricShapes.js");
