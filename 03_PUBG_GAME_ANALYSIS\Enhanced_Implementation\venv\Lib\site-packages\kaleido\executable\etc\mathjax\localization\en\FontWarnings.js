/*************************************************************
 *
 *  MathJax/localization/en/FontWarnings.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("en","FontWarnings",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          webFont: "MathJax is using web-based fonts to display the mathematics on this page.  These take time to download, so the page would render faster if you installed math fonts directly in your system's font folder.",
          imageFonts: "MathJax is using its image fonts rather than local or web-based fonts. This will render slower than usual, and the mathematics may not print at the full resolution of your printer.",
          noFonts: "MathJax is unable to locate a font to use to display its mathematics, and image fonts are not available, so it is falling back on generic Unicode characters in hopes that your browser will be able to display them. Some characters may not show up properly, or possibly not at all.",
          webFonts: "Most modern browsers allow for fonts to be downloaded over the web. Updating to a more recent version of your browser (or changing browsers) could improve the quality of the mathematics on this page.",
          fonts: "MathJax can use either the [STIX fonts](%1) or the [MathJax TeX fonts](%2).  Download and install one of those fonts to improve your MathJax experience.",
          STIXPage: "This page is designed to use the [STIX fonts](%1).  Download and install those fonts to improve your MathJax experience.",
          TeXPage: "This page is designed to use the [MathJax TeX fonts](%1).  Download and install those fonts to improve your MathJax experience."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/en/FontWarnings.js");
