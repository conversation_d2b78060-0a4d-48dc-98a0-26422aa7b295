{"name": "@jupyterlab/celltags-extension", "version": "4.4.3", "description": "An extension for manipulating tags in cell metadata", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.{d.ts,js,js.map}", "style/*.css", "style/index.js", "src/**/*.{ts,tsx}", "schema/*.json"], "scripts": {"build": "tsc", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.4.3", "@jupyterlab/notebook": "^4.4.3", "@jupyterlab/translation": "^4.4.3", "@jupyterlab/ui-components": "^4.4.3", "@lumino/algorithm": "^2.0.3", "@rjsf/utils": "^5.13.4", "react": "^18.2.0"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}