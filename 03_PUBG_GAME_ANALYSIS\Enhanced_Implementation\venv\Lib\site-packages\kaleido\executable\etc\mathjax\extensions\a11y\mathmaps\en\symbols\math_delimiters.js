[{"locale": "en"}, {"category": "Ps", "mappings": {"default": {"default": "left parenthesis", "alternative": "opening parenthesis"}, "mathspeak": {"default": "left-parenthesis", "brief": "left-p'ren", "sbrief": "L p'ren"}}, "key": "0028"}, {"category": "Pe", "mappings": {"default": {"default": "right parenthesis", "alternative": "closing parenthesis"}, "mathspeak": {"default": "right-parenthesis", "brief": "right-p'ren", "sbrief": "R p'ren"}}, "key": "0029"}, {"category": "Ps", "mappings": {"default": {"default": "left square bracket", "alternative": "opening square bracket"}, "mathspeak": {"default": "left-bracket", "brief": "left-brack", "sbrief": "L brack"}}, "key": "005B"}, {"category": "Pe", "mappings": {"default": {"default": "right square bracket", "alternative": "closing square bracket"}, "mathspeak": {"default": "right-bracket", "brief": "right-brack", "sbrief": "R brack"}}, "key": "005D"}, {"category": "Ps", "mappings": {"default": {"default": "left curly bracket", "alternative": "opening curly bracket"}, "mathspeak": {"default": "left-brace", "sbrief": "L brace"}}, "key": "007B"}, {"category": "Pe", "mappings": {"default": {"default": "right curly bracket", "alternative": "closing curly bracket"}, "mathspeak": {"default": "right-brace", "sbrief": "R brace"}}, "key": "007D"}, {"category": "Ps", "mappings": {"default": {"default": "left square bracket with quill"}, "mathspeak": {"default": "left-bracket with quill", "brief": "left-brack with quill", "sbrief": "L brack with quill"}}, "key": "2045"}, {"category": "Pe", "mappings": {"default": {"default": "right square bracket with quill"}, "mathspeak": {"default": "right-bracket with quill", "brief": "right-brack with quill", "sbrief": "R brack with quill"}}, "key": "2046"}, {"category": "Sm", "mappings": {"default": {"default": "left ceiling"}}, "key": "2308"}, {"category": "Sm", "mappings": {"default": {"default": "right ceiling"}}, "key": "2309"}, {"category": "Sm", "mappings": {"default": {"default": "left floor"}}, "key": "230A"}, {"category": "Sm", "mappings": {"default": {"default": "right floor"}}, "key": "230B"}, {"category": "So", "mappings": {"default": {"default": "bottom right crop"}}, "key": "230C"}, {"category": "So", "mappings": {"default": {"default": "bottom left crop"}}, "key": "230D"}, {"category": "So", "mappings": {"default": {"default": "top right crop"}}, "key": "230E"}, {"category": "So", "mappings": {"default": {"default": "top left crop"}}, "key": "230F"}, {"category": "So", "mappings": {"default": {"default": "top left corner"}}, "key": "231C"}, {"category": "So", "mappings": {"default": {"default": "top right corner"}}, "key": "231D"}, {"category": "So", "mappings": {"default": {"default": "bottom left corner"}}, "key": "231E"}, {"category": "So", "mappings": {"default": {"default": "bottom right corner"}}, "key": "231F"}, {"category": "Sm", "mappings": {"default": {"default": "top half integral"}}, "key": "2320"}, {"category": "Sm", "mappings": {"default": {"default": "bottom half integral"}}, "key": "2321"}, {"category": "Ps", "mappings": {"default": {"default": "left pointing angle bracket", "alternative": "bra"}, "mathspeak": {"default": "left pointing angle"}}, "key": "2329"}, {"category": "Pe", "mappings": {"default": {"default": "right pointing angle bracket", "alternative": "ket"}, "mathspeak": {"default": "right pointing angle"}}, "key": "232A"}, {"category": "Sm", "mappings": {"default": {"default": "left parenthesis upper hook"}, "mathspeak": {"default": "left-parenthesis upper hook", "brief": "left-p'ren upper hook", "sbrief": "L p'ren upper hook"}}, "key": "239B"}, {"category": "Sm", "mappings": {"default": {"default": "left parenthesis extension"}, "mathspeak": {"default": "left-parenthesis extension", "brief": "left-p'ren extension", "sbrief": "L p'ren extension"}}, "key": "239C"}, {"category": "Sm", "mappings": {"default": {"default": "left parenthesis lower hook"}, "mathspeak": {"default": "left-parenthesis lower hook", "brief": "left-p'ren lower hook", "sbrief": "L p'ren lower hook"}}, "key": "239D"}, {"category": "Sm", "mappings": {"default": {"default": "right parenthesis upper hook"}, "mathspeak": {"default": "right-parenthesis upper hook", "brief": "right-p'ren upper hook", "sbrief": "R p'ren upper hook"}}, "key": "239E"}, {"category": "Sm", "mappings": {"default": {"default": "right parenthesis extension"}, "mathspeak": {"default": "right-parenthesis extension", "brief": "right-p'ren extension", "sbrief": "R p'ren extension"}}, "key": "239F"}, {"category": "Sm", "mappings": {"default": {"default": "right parenthesis lower hook"}, "mathspeak": {"default": "right-parenthesis lower hook", "brief": "right-p'ren lower hook", "sbrief": "R p'ren lower hook"}}, "key": "23A0"}, {"category": "Sm", "mappings": {"default": {"default": "left square bracket upper corner"}, "mathspeak": {"default": "left-bracket upper corner", "brief": "left-brack upper corner", "sbrief": "L brack upper corner"}}, "key": "23A1"}, {"category": "Sm", "mappings": {"default": {"default": "left square bracket extension"}, "mathspeak": {"default": "left-bracket extension", "brief": "left-brack extension", "sbrief": "L brack extension"}}, "key": "23A2"}, {"category": "Sm", "mappings": {"default": {"default": "left square bracket lower corner"}, "mathspeak": {"default": "left-bracket lower corner", "brief": "left-brack lower corner", "sbrief": "L brack lower corner"}}, "key": "23A3"}, {"category": "Sm", "mappings": {"default": {"default": "right square bracket upper corner"}, "mathspeak": {"default": "right-bracket upper corner", "brief": "right-brack upper corner", "sbrief": "R brack upper corner"}}, "key": "23A4"}, {"category": "Sm", "mappings": {"default": {"default": "right square bracket extension"}, "mathspeak": {"default": "right-bracket extension", "brief": "right-brack extension", "sbrief": "R brack extension"}}, "key": "23A5"}, {"category": "Sm", "mappings": {"default": {"default": "right square bracket lower corner"}, "mathspeak": {"default": "right-bracket lower corner", "brief": "right-brack lower corner", "sbrief": "R brack lower corner"}}, "key": "23A6"}, {"category": "Sm", "mappings": {"default": {"default": "left curly bracket upper hook"}, "mathspeak": {"default": "left-brace upper hook", "sbrief": "L brace upper hook"}}, "key": "23A7"}, {"category": "Sm", "mappings": {"default": {"default": "left curly bracket middle piece"}, "mathspeak": {"default": "left-brace middle piece", "sbrief": "L brace middle piece"}}, "key": "23A8"}, {"category": "Sm", "mappings": {"default": {"default": "left curly bracket lower hook"}, "mathspeak": {"default": "left-brace lower hook", "sbrief": "L brace lower hook"}}, "key": "23A9"}, {"category": "Sm", "mappings": {"default": {"default": "curly bracket extension"}, "mathspeak": {"default": "brace extension"}}, "key": "23AA"}, {"category": "Sm", "mappings": {"default": {"default": "right curly bracket upper hook"}, "mathspeak": {"default": "right-brace upper hook", "sbrief": "R brace upper hook"}}, "key": "23AB"}, {"category": "Sm", "mappings": {"default": {"default": "right curly bracket middle piece"}, "mathspeak": {"default": "right-brace middle piece", "sbrief": "R brace middle piece"}}, "key": "23AC"}, {"category": "Sm", "mappings": {"default": {"default": "right curly bracket lower hook"}, "mathspeak": {"default": "right-brace lower hook", "sbrief": "R brace lower hook"}}, "key": "23AD"}, {"category": "Sm", "mappings": {"default": {"default": "integral extension"}}, "key": "23AE"}, {"category": "Sm", "mappings": {"default": {"default": "horizontal line extension"}}, "key": "23AF"}, {"category": "Sm", "mappings": {"default": {"default": "upper left or lower right curly bracket section"}, "mathspeak": {"default": "upper left or lower right-brace section"}}, "key": "23B0"}, {"category": "Sm", "mappings": {"default": {"default": "upper right or lower left curly bracket section"}, "mathspeak": {"default": "upper right or lower left-brace section"}}, "key": "23B1"}, {"category": "Sm", "mappings": {"default": {"default": "summation top"}}, "key": "23B2"}, {"category": "Sm", "mappings": {"default": {"default": "summation bottom"}}, "key": "23B3"}, {"category": "So", "mappings": {"default": {"default": "top square bracket"}, "mathspeak": {"default": "top-bracket", "brief": "top-brack", "sbrief": "T brack"}}, "key": "23B4"}, {"category": "So", "mappings": {"default": {"default": "bottom square bracket"}, "mathspeak": {"default": "bottom-bracket", "brief": "bottom-brack", "sbrief": "B brack"}}, "key": "23B5"}, {"category": "So", "mappings": {"default": {"default": "bottom square bracket over top square bracket"}, "mathspeak": {"default": "bottom-bracket over top-bracket", "brief": "bottom-brack over top-brack", "sbrief": "B brack over T brack"}}, "key": "23B6"}, {"category": "So", "mappings": {"default": {"default": "radical symbol bottom"}}, "key": "23B7"}, {"category": "So", "mappings": {"default": {"default": "left vertical box line"}}, "key": "23B8"}, {"category": "So", "mappings": {"default": {"default": "right vertical box line"}}, "key": "23B9"}, {"category": "Sm", "mappings": {"default": {"default": "top parenthesis"}, "mathspeak": {"default": "top-parenthesis", "brief": "top-p'ren", "sbrief": "t p'ren"}}, "key": "23DC"}, {"category": "Sm", "mappings": {"default": {"default": "bottom parenthesis"}, "mathspeak": {"default": "bottom-parenthesis", "brief": "bottom-p'ren", "sbrief": "b p'ren"}}, "key": "23DD"}, {"category": "Sm", "mappings": {"default": {"default": "top curly bracket"}, "mathspeak": {"default": "top-brace", "sbrief": "T brace"}}, "key": "23DE"}, {"category": "Sm", "mappings": {"default": {"default": "bottom curly bracket"}, "mathspeak": {"default": "bottom-brace", "sbrief": "B brace"}}, "key": "23DF"}, {"category": "Sm", "mappings": {"default": {"default": "top tortoise shell bracket"}}, "key": "23E0"}, {"category": "Sm", "mappings": {"default": {"default": "bottom tortoise shell bracket"}}, "key": "23E1"}, {"category": "Ps", "mappings": {"default": {"default": "medium left parenthesis ornament"}, "mathspeak": {"default": "medium left-parenthesis ornament", "brief": "medium left-p'ren ornament", "sbrief": "medium L p'ren ornament"}}, "key": "2768"}, {"category": "Pe", "mappings": {"default": {"default": "medium right parenthesis ornament"}, "mathspeak": {"default": "medium right-parenthesis ornament", "brief": "medium right-p'ren ornament", "sbrief": "medium R p'ren ornament"}}, "key": "2769"}, {"category": "Ps", "mappings": {"default": {"default": "medium flattened left parenthesis ornament"}, "mathspeak": {"default": "medium flattened left-parenthesis ornament", "brief": "medium flattened left-p'ren ornament", "sbrief": "medium flattened L p'ren ornament"}}, "key": "276A"}, {"category": "Pe", "mappings": {"default": {"default": "medium flattened right parenthesis ornament"}, "mathspeak": {"default": "medium flattened right-parenthesis ornament", "brief": "medium flattened right-p'ren ornament", "sbrief": "medium flattened R p'ren ornament"}}, "key": "276B"}, {"category": "Ps", "mappings": {"default": {"default": "medium left pointing angle bracket ornament"}, "mathspeak": {"default": "medium left pointing angle ornament"}}, "key": "276C"}, {"category": "Pe", "mappings": {"default": {"default": "medium right pointing angle bracket ornament"}, "mathspeak": {"default": "medium right pointing angle ornament"}}, "key": "276D"}, {"category": "Ps", "mappings": {"default": {"default": "heavy left pointing angle quotation mark ornament"}}, "key": "276E"}, {"category": "Pe", "mappings": {"default": {"default": "heavy right pointing angle quotation mark ornament"}}, "key": "276F"}, {"category": "Ps", "mappings": {"default": {"default": "heavy left pointing angle bracket ornament"}, "mathspeak": {"default": "heavy left pointing angle ornament"}}, "key": "2770"}, {"category": "Pe", "mappings": {"default": {"default": "heavy right pointing angle bracket ornament"}, "mathspeak": {"default": "heavy right pointing angle ornament"}}, "key": "2771"}, {"category": "Ps", "mappings": {"default": {"default": "light left tortoise shell bracket ornament"}}, "key": "2772"}, {"category": "Pe", "mappings": {"default": {"default": "light right tortoise shell bracket ornament"}}, "key": "2773"}, {"category": "Ps", "mappings": {"default": {"default": "medium left curly bracket ornament"}, "mathspeak": {"default": "medium left-brace ornament", "sbrief": "medium L brace ornament"}}, "key": "2774"}, {"category": "Pe", "mappings": {"default": {"default": "medium right curly bracket ornament"}, "mathspeak": {"default": "medium right-brace ornament", "sbrief": "medium R brace ornament"}}, "key": "2775"}, {"category": "Ps", "mappings": {"default": {"default": "left s shaped bag delimiter"}}, "key": "27C5"}, {"category": "Pe", "mappings": {"default": {"default": "right s shaped bag delimiter"}}, "key": "27C6"}, {"category": "Ps", "mappings": {"default": {"default": "mathematical left white square bracket"}, "mathspeak": {"default": "mathematical left white bracket"}}, "key": "27E6"}, {"category": "Pe", "mappings": {"default": {"default": "mathematical right white square bracket"}, "mathspeak": {"default": "mathematical right white bracket"}}, "key": "27E7"}, {"category": "Ps", "mappings": {"default": {"default": "mathematical left angle bracket"}, "mathspeak": {"default": "mathematical left-angle", "sbrief": "mathematical l angle"}}, "key": "27E8"}, {"category": "Pe", "mappings": {"default": {"default": "mathematical right angle bracket"}, "mathspeak": {"default": "mathematical right-angle", "sbrief": "mathematical r angle"}}, "key": "27E9"}, {"category": "Ps", "mappings": {"default": {"default": "mathematical left double angle bracket"}, "mathspeak": {"default": "mathematical left double angle"}}, "key": "27EA"}, {"category": "Pe", "mappings": {"default": {"default": "mathematical right double angle bracket"}, "mathspeak": {"default": "mathematical right double angle"}}, "key": "27EB"}, {"category": "Ps", "mappings": {"default": {"default": "mathematical left white tortoise shell bracket"}}, "key": "27EC"}, {"category": "Pe", "mappings": {"default": {"default": "mathematical right white tortoise shell bracket"}}, "key": "27ED"}, {"category": "Ps", "mappings": {"default": {"default": "mathematical left flattened parenthesis"}, "mathspeak": {"default": "mathematical flattened left-parenthesis", "brief": "mathematical flattened left-p'ren", "sbrief": "mathematical flattened L p'ren"}}, "key": "27EE"}, {"category": "Pe", "mappings": {"default": {"default": "mathematical right flattened parenthesis"}, "mathspeak": {"default": "mathematical flattened right-parenthesis", "brief": "mathematical flattened right-p'ren", "sbrief": "mathematical flattened R p'ren"}}, "key": "27EF"}, {"category": "Ps", "mappings": {"default": {"default": "left white curly bracket"}, "mathspeak": {"default": "left white brace"}}, "key": "2983"}, {"category": "Pe", "mappings": {"default": {"default": "right white curly bracket"}, "mathspeak": {"default": "right white brace"}}, "key": "2984"}, {"category": "Ps", "mappings": {"default": {"default": "left white parenthesis"}, "mathspeak": {"default": "white left-parenthesis", "brief": "white left-p'ren", "sbrief": "white L p'ren"}}, "key": "2985"}, {"category": "Pe", "mappings": {"default": {"default": "right white parenthesis"}, "mathspeak": {"default": "white right-parenthesis", "brief": "white right-p'ren", "sbrief": "white R p'ren"}}, "key": "2986"}, {"category": "Ps", "mappings": {"default": {"default": "z notation left image bracket"}}, "key": "2987"}, {"category": "Pe", "mappings": {"default": {"default": "z notation right image bracket"}}, "key": "2988"}, {"category": "Ps", "mappings": {"default": {"default": "z notation left binding bracket"}}, "key": "2989"}, {"category": "Pe", "mappings": {"default": {"default": "z notation right binding bracket"}}, "key": "298A"}, {"category": "Ps", "mappings": {"default": {"default": "left square bracket with underbar"}, "mathspeak": {"default": "left-bracket with underbar", "brief": "left-brack with underbar", "sbrief": "L brack with underbar"}}, "key": "298B"}, {"category": "Pe", "mappings": {"default": {"default": "right square bracket with underbar"}, "mathspeak": {"default": "right-bracket with underbar", "brief": "right-brack with underbar", "sbrief": "R brack with underbar"}}, "key": "298C"}, {"category": "Ps", "mappings": {"default": {"default": "left square bracket with tick in top corner"}, "mathspeak": {"default": "left-bracket with tick in top corner", "brief": "left-brack with tick in top corner", "sbrief": "L brack with tick in top corner"}}, "key": "298D"}, {"category": "Pe", "mappings": {"default": {"default": "right square bracket with tick in bottom corner"}, "mathspeak": {"default": "right-bracket with tick in bottom corner", "brief": "right-brack with tick in bottom corner", "sbrief": "R brack with tick in bottom corner"}}, "key": "298E"}, {"category": "Ps", "mappings": {"default": {"default": "left square bracket with tick in bottom corner"}, "mathspeak": {"default": "left-bracket with tick in bottom corner", "brief": "left-brack with tick in bottom corner", "sbrief": "L brack with tick in bottom corner"}}, "key": "298F"}, {"category": "Pe", "mappings": {"default": {"default": "right square bracket with tick in top corner"}, "mathspeak": {"default": "right-bracket with tick in top corner", "brief": "right-brack with tick in top corner", "sbrief": "R brack with tick in top corner"}}, "key": "2990"}, {"category": "Ps", "mappings": {"default": {"default": "left angle bracket with dot"}, "mathspeak": {"default": "left-angle with dot", "sbrief": "l angle with dot"}}, "key": "2991"}, {"category": "Pe", "mappings": {"default": {"default": "right angle bracket with dot"}, "mathspeak": {"default": "right-angle with dot", "sbrief": "r angle with dot"}}, "key": "2992"}, {"category": "Ps", "mappings": {"default": {"default": "left arc less than bracket"}}, "key": "2993"}, {"category": "Pe", "mappings": {"default": {"default": "right arc greater than bracket"}}, "key": "2994"}, {"category": "Ps", "mappings": {"default": {"default": "double left arc greater than bracket"}}, "key": "2995"}, {"category": "Pe", "mappings": {"default": {"default": "double right arc less than bracket"}}, "key": "2996"}, {"category": "Ps", "mappings": {"default": {"default": "left black tortoise shell bracket"}}, "key": "2997"}, {"category": "Pe", "mappings": {"default": {"default": "right black tortoise shell bracket"}}, "key": "2998"}, {"category": "Ps", "mappings": {"default": {"default": "left wiggly fence"}}, "key": "29D8"}, {"category": "Pe", "mappings": {"default": {"default": "right wiggly fence"}}, "key": "29D9"}, {"category": "Ps", "mappings": {"default": {"default": "left double wiggly fence"}}, "key": "29DA"}, {"category": "Pe", "mappings": {"default": {"default": "right double wiggly fence"}}, "key": "29DB"}, {"category": "Ps", "mappings": {"default": {"default": "left pointing curved angle bracket"}, "mathspeak": {"default": "left pointing curved angle"}}, "key": "29FC"}, {"category": "Pe", "mappings": {"default": {"default": "right pointing curved angle bracket"}, "mathspeak": {"default": "right pointing curved angle"}}, "key": "29FD"}, {"category": "Ps", "mappings": {"default": {"default": "top left half bracket"}, "mathspeak": {"default": "top half left-bracket", "brief": "top half left-brack", "sbrief": "top half L brack"}}, "key": "2E22"}, {"category": "Pe", "mappings": {"default": {"default": "top right half bracket"}, "mathspeak": {"default": "top half right-bracket", "brief": "top half right-brack", "sbrief": "top half R brack"}}, "key": "2E23"}, {"category": "Ps", "mappings": {"default": {"default": "bottom left half bracket"}, "mathspeak": {"default": "bottom half left-bracket", "brief": "bottom half left-brack", "sbrief": "bottom half L brack"}}, "key": "2E24"}, {"category": "Pe", "mappings": {"default": {"default": "bottom right half bracket"}, "mathspeak": {"default": "bottom half right-bracket", "brief": "bottom half right-brack", "sbrief": "bottom half R brack"}}, "key": "2E25"}, {"category": "Ps", "mappings": {"default": {"default": "left sideways U bracket"}}, "key": "2E26"}, {"category": "Pe", "mappings": {"default": {"default": "right sideways U bracket"}}, "key": "2E27"}, {"category": "Ps", "mappings": {"default": {"default": "left double parenthesis"}, "mathspeak": {"default": "double left-parenthesis", "brief": "double left-p'ren", "sbrief": "double L p'ren"}}, "key": "2E28"}, {"category": "Pe", "mappings": {"default": {"default": "right double parenthesis"}, "mathspeak": {"default": "double right-parenthesis", "brief": "double right-p'ren", "sbrief": "double R p'ren"}}, "key": "2E29"}, {"category": "Ps", "mappings": {"default": {"default": "left angle bracket", "alternative": "opening angle bracket"}, "mathspeak": {"default": "left-angle", "sbrief": "l angle"}}, "key": "3008"}, {"category": "Pe", "mappings": {"default": {"default": "right angle bracket", "alternative": "closing angle bracket"}, "mathspeak": {"default": "right-angle", "sbrief": "r angle"}}, "key": "3009"}, {"category": "Ps", "mappings": {"default": {"default": "left double angle bracket", "alternative": "opening double angle bracket"}, "mathspeak": {"default": "left double angle"}}, "key": "300A"}, {"category": "Pe", "mappings": {"default": {"default": "right double angle bracket", "alternative": "closing double angle bracket"}, "mathspeak": {"default": "right double angle"}}, "key": "300B"}, {"category": "Ps", "mappings": {"default": {"default": "left corner bracket", "alternative": "opening corner bracket"}}, "key": "300C"}, {"category": "Pe", "mappings": {"default": {"default": "right corner bracket", "alternative": "closing corner bracket"}}, "key": "300D"}, {"category": "Ps", "mappings": {"default": {"default": "left white corner bracket", "alternative": "opening white corner bracket"}}, "key": "300E"}, {"category": "Pe", "mappings": {"default": {"default": "right white corner bracket", "alternative": "closing white corner bracket"}}, "key": "300F"}, {"category": "Ps", "mappings": {"default": {"default": "left black lenticular bracket", "alternative": "opening black lenticular bracket"}}, "key": "3010"}, {"category": "Pe", "mappings": {"default": {"default": "right black lenticular bracket", "alternative": "closing black lenticular bracket"}}, "key": "3011"}, {"category": "Ps", "mappings": {"default": {"default": "left tortoise shell bracket", "alternative": "opening tortoise shell bracket"}}, "key": "3014"}, {"category": "Pe", "mappings": {"default": {"default": "right tortoise shell bracket", "alternative": "closing tortoise shell bracket"}}, "key": "3015"}, {"category": "Ps", "mappings": {"default": {"default": "left white lenticular bracket", "alternative": "opening white lenticular bracket"}}, "key": "3016"}, {"category": "Pe", "mappings": {"default": {"default": "right white lenticular bracket", "alternative": "closing white lenticular bracket"}}, "key": "3017"}, {"category": "Ps", "mappings": {"default": {"default": "left white tortoise shell bracket", "alternative": "opening white tortoise shell bracket"}}, "key": "3018"}, {"category": "Pe", "mappings": {"default": {"default": "right white tortoise shell bracket", "alternative": "closing white tortoise shell bracket"}}, "key": "3019"}, {"category": "Ps", "mappings": {"default": {"default": "left white square bracket", "alternative": "opening white square bracket"}, "mathspeak": {"default": "left white bracket"}}, "key": "301A"}, {"category": "Pe", "mappings": {"default": {"default": "right white square bracket", "alternative": "closing white square bracket"}, "mathspeak": {"default": "right white bracket"}}, "key": "301B"}, {"category": "Ps", "mappings": {"default": {"default": "reversed double prime quotation mark"}}, "key": "301D"}, {"category": "Pe", "mappings": {"default": {"default": "double prime quotation mark"}}, "key": "301E"}, {"category": "Pe", "mappings": {"default": {"default": "low double prime quotation mark"}}, "key": "301F"}, {"category": "Ps", "mappings": {"default": {"default": "ornate left parenthesis"}, "mathspeak": {"default": "ornate left-parenthesis", "brief": "ornate left-p'ren", "sbrief": "ornate L p'ren"}}, "key": "FD3E"}, {"category": "Pe", "mappings": {"default": {"default": "ornate right parenthesis"}, "mathspeak": {"default": "ornate right-parenthesis", "brief": "ornate right-p'ren", "sbrief": "ornate R p'ren"}}, "key": "FD3F"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left white lenticular bracket"}}, "key": "FE17"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right white lenticular brakcet"}}, "key": "FE18"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left parenthesis", "alternative": "glyph for vertical opening parenthesis"}, "mathspeak": {"default": "presentation form for vertical left-parenthesis", "brief": "presentation form for vertical left-p'ren", "sbrief": "presentation form for vertical L p'ren"}}, "key": "FE35"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right parenthesis", "alternative": "glyph for vertical closing parenthesis"}, "mathspeak": {"default": "presentation form for vertical right-parenthesis", "brief": "presentation form for vertical right-p'ren", "sbrief": "presentation form for vertical R p'ren"}}, "key": "FE36"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left curly bracket", "alternative": "glyph for vertical opening curly bracket"}, "mathspeak": {"default": "presentation form for vertical left-brace", "sbrief": "presentation form for vertical L brace"}}, "key": "FE37"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right curly bracket", "alternative": "glyph for vertical closing curly bracket"}, "mathspeak": {"default": "presentation form for vertical right-brace", "sbrief": "presentation form for vertical r brace"}}, "key": "FE38"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left tortoise shell bracket", "alternative": "glyph for vertical opening tortoise shell bracket"}}, "key": "FE39"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right tortoise shell bracket", "alternative": "glyph for vertical closing tortoise shell bracket"}}, "key": "FE3A"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left black lenticular bracket", "alternative": "glyph for vertical opening black lenticular bracket"}}, "key": "FE3B"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right black lenticular bracket", "alternative": "glyph for vertical closing black lenticular bracket"}}, "key": "FE3C"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left double angle bracket", "alternative": "glyph for vertical opening double angle bracket"}, "mathspeak": {"default": "presentation form for vertical left double angle"}}, "key": "FE3D"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right double angle bracket", "alternative": "glyph for vertical closing double angle bracket"}, "mathspeak": {"default": "presentation form for vertical right double angle"}}, "key": "FE3E"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left angle bracket", "alternative": "glyph for vertical opening angle bracket"}, "mathspeak": {"default": "presentation form for vertical left-angle", "sbrief": "presentation form for vertical l angle"}}, "key": "FE3F"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right angle bracket", "alternative": "glyph for vertical closing angle bracket"}, "mathspeak": {"default": "presentation form for vertical right-angle", "sbrief": "presentation form for vertical r angle"}}, "key": "FE40"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left corner bracket", "alternative": "glyph for vertical opening corner bracket"}}, "key": "FE41"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right corner bracket", "alternative": "glyph for vertical closing corner bracket"}}, "key": "FE42"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left white corner bracket", "alternative": "glyph for vertical opening white corner bracket"}}, "key": "FE43"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right white corner bracket", "alternative": "glyph for vertical closing white corner bracket"}}, "key": "FE44"}, {"category": "Ps", "mappings": {"default": {"default": "presentation form for vertical left square bracket"}, "mathspeak": {"default": "presentation form for vertical left-bracket", "brief": "presentation form for vertical left-brack", "sbrief": "presentation form for vertical L brack"}}, "key": "FE47"}, {"category": "Pe", "mappings": {"default": {"default": "presentation form for vertical right square bracket"}, "mathspeak": {"default": "presentation form for vertical right-bracket", "brief": "presentation form for vertical right-brack", "sbrief": "presentation form for vertical r brack"}}, "key": "FE48"}, {"category": "Ps", "mappings": {"default": {"default": "small left parenthesis", "alternative": "small opening parenthesis"}, "mathspeak": {"default": "small left-parenthesis", "brief": "small left-p'ren", "sbrief": "small L p'ren"}}, "key": "FE59"}, {"category": "Pe", "mappings": {"default": {"default": "small right parenthesis", "alternative": "small closing parenthesis"}, "mathspeak": {"default": "small right-parenthesis", "brief": "small right-p'ren", "sbrief": "small R p'ren"}}, "key": "FE5A"}, {"category": "Ps", "mappings": {"default": {"default": "small left curly bracket", "alternative": "small opening curly bracket"}, "mathspeak": {"default": "small left-brace", "sbrief": "small L brace"}}, "key": "FE5B"}, {"category": "Pe", "mappings": {"default": {"default": "small right curly bracket", "alternative": "small closing curly bracket"}, "mathspeak": {"default": "small right-brace", "sbrief": "small r brace"}}, "key": "FE5C"}, {"category": "Ps", "mappings": {"default": {"default": "small left tortoise shell bracket", "alternative": "small opening tortoise shell bracket"}}, "key": "FE5D"}, {"category": "Pe", "mappings": {"default": {"default": "small right tortoise shell bracket", "alternative": "small closing tortoise shell bracket"}}, "key": "FE5E"}, {"category": "Ps", "mappings": {"default": {"default": "fullwidth left parenthesis", "alternative": "fullwidth opening parenthesis"}, "mathspeak": {"default": "fullwidth left-parenthesis", "brief": "fullwidth left-p'ren", "sbrief": "fullwidth L p'ren"}}, "key": "FF08"}, {"category": "Pe", "mappings": {"default": {"default": "fullwidth right parenthesis", "alternative": "fullwidth closing parenthesis"}, "mathspeak": {"default": "fullwidth right-parenthesis", "brief": "fullwidth right-p'ren", "sbrief": "fullwidth R p'ren"}}, "key": "FF09"}, {"category": "Ps", "mappings": {"default": {"default": "fullwidth left square bracket", "alternative": "fullwidth opening square bracket"}, "mathspeak": {"default": "fullwidth left-bracket", "brief": "fullwidth left-brack", "sbrief": "fullwidth L brack"}}, "key": "FF3B"}, {"category": "Pe", "mappings": {"default": {"default": "fullwidth right square bracket", "alternative": "fullwidth closing square bracket"}, "mathspeak": {"default": "fullwidth right-bracket", "brief": "fullwidth right-brack", "sbrief": "fullwidth r brack"}}, "key": "FF3D"}, {"category": "Ps", "mappings": {"default": {"default": "fullwidth left curly bracket", "alternative": "fullwidth opening curly bracket"}, "mathspeak": {"default": "fullwidth left-brace", "sbrief": "fullwidth L brace"}}, "key": "FF5B"}, {"category": "Pe", "mappings": {"default": {"default": "fullwidth right curly bracket", "alternative": "fullwidth closing curly bracket"}, "mathspeak": {"default": "fullwidth right-brace", "sbrief": "fullwidth r brace"}}, "key": "FF5D"}, {"category": "Ps", "mappings": {"default": {"default": "fullwidth white left parenthesis"}, "mathspeak": {"default": "fullwidth white left-parenthesis", "brief": "fullwidth white left-p'ren", "sbrief": "fullwidth white L p'ren"}}, "key": "FF5F"}, {"category": "Pe", "mappings": {"default": {"default": "fullwidth white right parenthesis"}, "mathspeak": {"default": "fullwidth white right-parenthesis", "brief": "fullwidth white right-p'ren", "sbrief": "fullwidth white R p'ren"}}, "key": "FF60"}, {"category": "Ps", "mappings": {"default": {"default": "halfwidth left corner bracket", "alternative": "halfwidth opening corner bracket"}}, "key": "FF62"}, {"category": "Pe", "mappings": {"default": {"default": "halfwidth right corner bracket", "alternative": "halfwidth closing corner bracket"}}, "key": "FF63"}]