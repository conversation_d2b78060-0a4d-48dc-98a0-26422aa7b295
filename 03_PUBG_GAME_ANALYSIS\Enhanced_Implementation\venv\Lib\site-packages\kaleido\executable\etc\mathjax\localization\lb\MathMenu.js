/*************************************************************
 *
 *  MathJax/localization/lb/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("lb","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          MathMLcode: "MathML Code",
          OriginalMathML: "Original MathML",
          Original: "Original Formulaire",
          ErrorMessage: "Feelermessage",
          Annotation: "Notiz",
          TeX: "TeX",
          StarMath: "StarMath",
          <PERSON>: "Maple",
          OpenMath: "OpenMath",
          Settings: "Math Astellungen",
          Hover: "Driwwerfueren",
          Click: "Klicken",
          DoubleClick: "Duebel-Klick",
          NoZoom: "Kee Zoom",
          Option: "Optioun",
          Alt: "Alt",
          Control: "Ctrl",
          Auto: "Automatesch",
          TeXImage: "TeX (Bild)",
          STIXLocal: "STIX (lokal)",
          ContextMenu: "kontextuelle Men\u00FC",
          Browser: "Browser",
          Locale: "Sprooch",
          LoadLocale: "Luede vun der URL ...",
          About: "Iwwer MathJax",
          Help: "MathJax H\u00EBllef",
          LoadURL: "Iwwersetzungsdonn\u00E9e\u00EB vun d\u00EBser URL lueden:",
          Close: "Zoumaachen"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/lb/MathMenu.js");
