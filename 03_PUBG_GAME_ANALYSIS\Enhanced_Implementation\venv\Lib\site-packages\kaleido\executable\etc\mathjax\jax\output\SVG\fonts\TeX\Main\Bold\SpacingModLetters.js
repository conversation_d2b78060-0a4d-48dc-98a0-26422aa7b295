/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Main/Bold/SpacingModLetters.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Main-bold'],
  {
    // MODIFIER LETTER CIRCUMFLEX ACCENT
    0x2C6: [694,-520,575,126,449,'207 632L287 694Q289 693 368 632T448 570T431 545T413 520Q410 520 350 559L287 597L224 559Q164 520 161 520Q160 520 143 544T126 570T207 632'],

    // CARON
    0x2C7: [660,-515,575,130,443,'131 603Q130 604 136 618T150 646T158 659L223 635L287 611L351 635L416 659Q417 660 424 647T437 619T443 603Q440 601 364 558T287 515T210 558T131 603'],

    // MODIFIER LETTER MACRON
    0x2C9: [607,-540,575,80,494,'80 540V607H494V540H80'],

    // MODIFIER LETTER ACUTE ACCENT
    0x2CA: [706,-503,575,236,460,'391 706Q419 706 439 683T460 634Q460 608 441 593T366 550Q356 545 351 543L275 503L256 527Q236 552 237 553Q242 558 292 620Q299 629 309 641T324 659T336 673T346 685T354 693T363 699T371 703T380 705T391 706'],

    // MODIFIER LETTER GRAVE ACCENT
    0x2CB: [706,-503,575,113,338,'114 634Q114 663 136 684T183 706Q191 706 196 705T208 700T219 693T232 681T245 666T262 645T282 620Q332 558 337 553Q338 552 318 527L299 503L223 543Q215 547 202 553T183 563T167 571T153 580T141 587T131 595T124 603T118 612T115 622T114 634'],

    // BREVE
    0x2D8: [694,-500,575,102,472,'287 500Q208 500 155 558T102 689V694H153V685Q153 681 154 674T164 648T186 615T226 590T287 578Q347 578 382 611T421 685V694H472V689Q472 623 422 562T287 500'],

    // DOT ABOVE
    0x2D9: [695,-525,575,202,372,'202 610Q202 647 227 671T283 695Q324 695 348 669T372 610T350 551T287 525Q248 525 225 551T202 610'],

    // RING ABOVE
    0x2DA: [702,-535,575,160,414,'160 618Q160 653 193 677T279 702H284Q381 702 407 647Q414 634 414 618Q414 607 410 596T395 570T355 546T287 536T220 545T181 568T165 594T160 618ZM352 618Q352 645 341 652T301 659H292Q286 659 278 659T268 660Q247 660 236 653T224 638T222 619Q222 591 234 585T287 578Q315 578 326 580T345 590T352 618'],

    // SMALL TILDE
    0x2DC: [694,-552,575,96,478,'343 552Q320 552 278 575T215 599Q181 599 146 564L134 552L115 569Q111 572 106 576T98 584L96 586Q158 656 165 663Q199 694 230 694Q239 694 244 693Q262 689 300 668T359 647Q393 647 428 682L440 694L459 677Q463 674 468 670T476 662L478 660Q416 590 409 583Q375 552 343 552']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/SpacingModLetters.js");
