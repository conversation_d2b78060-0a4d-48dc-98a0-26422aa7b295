/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/AMS/Regular/EnclosedAlphanum.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_AMS'],
  {
    // CIRCLED LATIN CAPITAL LETTER S
    0x24C8: [709,175,902,8,894,'451 -175Q328 -175 226 -115T66 47T8 267Q8 303 15 342T39 431T94 531T186 622Q239 663 307 686T424 709H440Q604 709 716 622Q757 592 788 555T838 482T869 414T886 350T892 301T894 267Q894 147 835 45T674 -116T451 -175ZM854 268Q854 375 802 467T657 614T450 670Q283 670 166 552T49 267Q49 99 167 -18T453 -136Q617 -136 735 -18T854 268ZM273 378Q273 430 309 474T409 527Q411 527 417 527T428 528Q498 528 549 484L567 505Q583 528 590 528H594Q600 528 606 522V350L600 344H586Q577 344 574 344T569 347T566 357Q542 491 432 491Q389 491 365 465T340 407Q340 391 344 378T358 356T377 340T400 328T421 321T443 316T459 313Q499 305 517 300T559 279T601 238Q629 195 629 148Q629 80 583 33T471 -14Q392 -14 330 30Q312 6 293 -13Q292 -14 285 -14Q279 -14 273 -8V77V138Q273 160 275 165T286 170H294H307Q313 164 313 158Q313 108 350 67T471 26Q512 26 537 54T562 119Q562 137 558 151T544 176T527 193T504 205T483 212T459 218T441 222Q391 232 368 241T318 273Q273 316 273 378']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/AMS/Regular/EnclosedAlphanum.js");
