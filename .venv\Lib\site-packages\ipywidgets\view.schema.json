{"$schema": "http://json-schema.org/draft-07/schema#", "description": "Jupyter Interactive Widget View JSON schema.", "type": "object", "properties": {"version_major": {"description": "Format version (major)", "type": "number", "minimum": 1, "maximum": 1}, "version_minor": {"description": "Format version (minor)", "type": "number"}, "model_id": {"description": "Unique identifier of the widget model to be displayed", "type": "string"}, "required": ["model_id"]}, "additionalProperties": false}