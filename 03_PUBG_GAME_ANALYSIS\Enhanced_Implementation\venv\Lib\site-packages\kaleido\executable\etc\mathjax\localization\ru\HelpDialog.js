/*************************************************************
 *
 *  MathJax/localization/ru/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ru","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "\u041F\u043E\u043C\u043E\u0447\u044C \u043F\u043E MathJax",
          MathJax: "*MathJax*\u00A0\u2014 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430 \u043D\u0430\u00A0JavaScript, \u043F\u043E\u0437\u0432\u043E\u043B\u044F\u044E\u0449\u0430\u044F \u0432\u044B\u0432\u043E\u0434\u0438\u0442\u044C \u0444\u043E\u0440\u043C\u0443\u043B\u044B \u043D\u0430\u00A0\u0432\u0435\u0431-\u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430\u0445. \u0418\u0445\u00A0\u0447\u0438\u0442\u0430\u0442\u0435\u043B\u044F\u043C \u0434\u043B\u044F\u00A0\u044D\u0442\u043E\u0433\u043E \u043D\u0438\u0447\u0435\u0433\u043E \u043D\u0435\u00A0\u043D\u0430\u0434\u043E \u0434\u0435\u043B\u0430\u0442\u044C.",
          Browsers: "*\u0411\u0440\u0430\u0443\u0437\u0435\u0440\u044B*: MathJax \u0440\u0430\u0431\u043E\u0442\u0430\u0435\u0442 \u0441\u043E\u00A0\u0432\u0441\u0435\u043C\u0438 \u0441\u043E\u0432\u0440\u0435\u043C\u0435\u043D\u043D\u044B\u043C\u0438 \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430\u043C\u0438, \u0432\u043A\u043B\u044E\u0447\u0430\u044F IE6+, Firefox 3+, Chrome 0.2+, Safari 2+. Opera 9.6+ \u0438\u00A0\u0431\u043E\u043B\u044C\u0448\u0438\u043D\u0441\u0442\u0432\u043E \u043C\u043E\u0431\u0438\u043B\u044C\u043D\u044B\u0445",
          Menu: "*\u041C\u0435\u043D\u044E \u0444\u043E\u0440\u043C\u0443\u043B*: MathJax \u0434\u043E\u0431\u0430\u0432\u043B\u044F\u0435\u0442 \u043A\u00A0\u0444\u043E\u0440\u043C\u0443\u043B\u0430\u043C \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u0435 \u043C\u0435\u043D\u044E, \u0432\u044B\u0437\u044B\u0432\u0430\u0435\u043C\u043E\u0435 \u043F\u0440\u0430\u0432\u043E\u0439 \u043A\u043D\u043E\u043F\u043A\u043E\u0439 \u043C\u044B\u0448\u0438 \u0438\u043B\u0438\u00A0\u0449\u0435\u043B\u0447\u043A\u043E\u043C \u0441\u00A0\u043D\u0430\u0436\u0430\u0442\u043E\u0439 Ctrl.",
          ShowMath: "*\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0444\u043E\u0440\u043C\u0443\u043B\u0443 \u043A\u0430\u043A* \u043F\u043E\u0437\u0432\u043E\u043B\u044F\u0435\u0442 \u0443\u0432\u0438\u0434\u0435\u0442\u044C \u0438\u00A0\u0441\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0438\u0441\u0445\u043E\u0434\u043D\u044B\u0439 \u043A\u043E\u0434 \u0444\u043E\u0440\u043C\u0443\u043B\u044B \u0432\u00A0\u0444\u043E\u0440\u043C\u0430\u0442\u0435 MathML \u0438\u043B\u0438\u00A0\u043F\u0435\u0440\u0432\u043E\u043D\u0430\u0447\u0430\u043B\u044C\u043D\u043E\u043C.",
          Settings: "*\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438* \u043F\u043E\u0437\u0432\u043E\u043B\u044F\u044E\u0442 \u043D\u0430\u0441\u0442\u0440\u0430\u0438\u0432\u0430\u0442\u044C \u0432\u043E\u0437\u043C\u043E\u0436\u043D\u043E\u0441\u0442\u0438 MathJax, \u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440, \u0440\u0430\u0437\u043C\u0435\u0440 \u0444\u043E\u0440\u043C\u0443\u043B \u0438\u00A0\u043C\u0435\u0445\u0430\u043D\u0438\u0437\u043C \u0438\u0445\u00A0\u043F\u043E\u043A\u0430\u0437\u0430.",
          Language: "*\u042F\u0437\u044B\u043A* \u043F\u043E\u0437\u0432\u043E\u043B\u044F\u0435\u0442 \u0432\u044B\u0431\u0440\u0430\u0442\u044C \u044F\u0437\u044B\u043A \u043C\u0435\u043D\u044E \u0438\u00A0\u0441\u043E\u043E\u0431\u0449\u0435\u043D\u0438\u0439 MathJax.",
          Zoom: "*\u0423\u0432\u0435\u043B\u0438\u0447\u0435\u043D\u0438\u0435* \u043F\u043E\u0437\u0432\u043E\u043B\u044F\u0435\u0442 \u0443\u0432\u0435\u043B\u0438\u0447\u0438\u0442\u044C \u0444\u043E\u0440\u043C\u0443\u043B\u0443.",
          Accessibilty: "*\u0421\u043F\u0435\u0446\u0438\u0430\u043B\u044C\u043D\u044B\u0435 \u0432\u043E\u0437\u043C\u043E\u0436\u043D\u043E\u0441\u0442\u0438*: MathJax \u0430\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u0438 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442 \u043F\u0440\u043E\u0433\u0440\u0430\u043C\u043C\u044B \u0447\u0442\u0435\u043D\u0438\u044F, \u043E\u0437\u0432\u0443\u0447\u0438\u0432\u0430\u044F \u0444\u043E\u0440\u043C\u0443\u043B\u044B \u0434\u043B\u044F\u00A0\u0441\u043B\u0435\u043F\u044B\u0445 \u0438\u00A0\u0441\u043B\u0430\u0431\u043E\u0432\u0438\u0434\u044F\u0449\u0438\u0445.",
          Fonts: "*\u0428\u0440\u0438\u0444\u0442\u044B*: MathJax \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442 \u043E\u043F\u0440\u0435\u0434\u0435\u043B\u0451\u043D\u043D\u044B\u0435 \u0448\u0440\u0438\u0444\u0442\u044B, \u0435\u0441\u043B\u0438\u00A0\u043E\u043D\u0438 \u0443\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D\u044B \u043D\u0430\u00A0\u0412\u0430\u0448\u0435\u043C \u043A\u043E\u043C\u043F\u044C\u044E\u0442\u0435\u0440\u0435, \u0432\u00A0\u043F\u0440\u043E\u0442\u0438\u0432\u043D\u043E\u043C \u0441\u043B\u0443\u0447\u0430\u0435, \u0437\u0430\u0433\u0440\u0443\u0436\u0430\u044E\u0442\u0441\u044F \u0441\u0435\u0442\u0435\u0432\u044B\u0435. \u041C\u044B \u0440\u0435\u043A\u043E\u043C\u0435\u043D\u0434\u0443\u0435\u043C \u0443\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u044C [STIX fonts](%1), \u0447\u0442\u043E\u0431\u044B\u00A0\u0443\u0441\u043A\u043E\u0440\u0438\u0442\u044C \u0432\u044B\u0432\u043E\u0434 \u0444\u043E\u0440\u043C\u0443\u043B.",
          CloseDialog: "\u0417\u0430\u043A\u0440\u044B\u0442\u044C \u0434\u0438\u0430\u043B\u043E\u0433 \u0441\u043F\u0440\u0430\u0432\u043A\u0438"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ru/HelpDialog.js");
