(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[4010,5606],{83173:(e,t,n)=>{"use strict";n.d(t,{z:()=>g,H:()=>m});function r(e){return e.charCodeAt(0)}function i(e,t){if(Array.isArray(e)){e.forEach((function(e){t.push(e)}))}else{t.push(e)}}function s(e,t){if(e[t]===true){throw"duplicate flag "+t}const n=e[t];e[t]=true}function a(e){if(e===undefined){throw Error("Internal Error - Should never get here!")}return true}function o(){throw Error("Internal Error - Should never get here!")}function c(e){return e["type"]==="Character"}const u=[];for(let y=r("0");y<=r("9");y++){u.push(y)}const l=[r("_")].concat(u);for(let y=r("a");y<=r("z");y++){l.push(y)}for(let y=r("A");y<=r("Z");y++){l.push(y)}const d=[r(" "),r("\f"),r("\n"),r("\r"),r("\t"),r("\v"),r("\t"),r(" "),r(" "),r(" "),r(" "),r(" "),r(" "),r(" "),r(" "),r(" "),r(" "),r(" "),r(" "),r(" "),r("\u2028"),r("\u2029"),r(" "),r(" "),r("　"),r("\ufeff")];const f=/[0-9a-fA-F]/;const h=/[0-9]/;const p=/[1-9]/;class m{constructor(){this.idx=0;this.input="";this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx;this.input=e.input;this.groupIdx=e.groupIdx}pattern(e){this.idx=0;this.input=e;this.groupIdx=0;this.consumeChar("/");const t=this.disjunction();this.consumeChar("/");const n={type:"Flags",loc:{begin:this.idx,end:e.length},global:false,ignoreCase:false,multiLine:false,unicode:false,sticky:false};while(this.isRegExpFlag()){switch(this.popChar()){case"g":s(n,"global");break;case"i":s(n,"ignoreCase");break;case"m":s(n,"multiLine");break;case"u":s(n,"unicode");break;case"y":s(n,"sticky");break}}if(this.idx!==this.input.length){throw Error("Redundant input: "+this.input.substring(this.idx))}return{type:"Pattern",flags:n,value:t,loc:this.loc(0)}}disjunction(){const e=[];const t=this.idx;e.push(this.alternative());while(this.peekChar()==="|"){this.consumeChar("|");e.push(this.alternative())}return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){const e=[];const t=this.idx;while(this.isTerm()){e.push(this.term())}return{type:"Alternative",value:e,loc:this.loc(t)}}term(){if(this.isAssertion()){return this.assertion()}else{return this.atom()}}assertion(){const e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let t;switch(this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead";break}a(t);const n=this.disjunction();this.consumeChar(")");return{type:t,value:n,loc:this.loc(e)}}return o()}quantifier(e=false){let t=undefined;const n=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:Infinity};break;case"+":t={atLeast:1,atMost:Infinity};break;case"?":t={atLeast:0,atMost:1};break;case"{":const n=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:n,atMost:n};break;case",":let e;if(this.isDigit()){e=this.integerIncludingZero();t={atLeast:n,atMost:e}}else{t={atLeast:n,atMost:Infinity}}this.consumeChar("}");break}if(e===true&&t===undefined){return undefined}a(t);break}if(e===true&&t===undefined){return undefined}if(a(t)){if(this.peekChar(0)==="?"){this.consumeChar("?");t.greedy=false}else{t.greedy=true}t.type="Quantifier";t.loc=this.loc(n);return t}}atom(){let e;const t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group();break}if(e===undefined&&this.isPatternCharacter()){e=this.patternCharacter()}if(a(e)){e.loc=this.loc(t);if(this.isQuantifier()){e.quantifier=this.quantifier()}return e}return o()}dotAll(){this.consumeChar(".");return{type:"Set",complement:true,value:[r("\n"),r("\r"),r("\u2028"),r("\u2029")]}}atomEscape(){this.consumeChar("\\");switch(this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){const e=this.positiveInteger();return{type:"GroupBackReference",value:e}}characterClassEscape(){let e;let t=false;switch(this.popChar()){case"d":e=u;break;case"D":e=u;t=true;break;case"s":e=d;break;case"S":e=d;t=true;break;case"w":e=l;break;case"W":e=l;t=true;break}if(a(e)){return{type:"Set",value:e,complement:t}}return o()}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=r("\f");break;case"n":e=r("\n");break;case"r":e=r("\r");break;case"t":e=r("\t");break;case"v":e=r("\v");break}if(a(e)){return{type:"Character",value:e}}return o()}controlLetterEscapeAtom(){this.consumeChar("c");const e=this.popChar();if(/[a-zA-Z]/.test(e)===false){throw Error("Invalid ")}const t=e.toUpperCase().charCodeAt(0)-64;return{type:"Character",value:t}}nulCharacterAtom(){this.consumeChar("0");return{type:"Character",value:r("\0")}}hexEscapeSequenceAtom(){this.consumeChar("x");return this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){this.consumeChar("u");return this.parseHexDigits(4)}identityEscapeAtom(){const e=this.popChar();return{type:"Character",value:r(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case"\n":case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:const e=this.popChar();return{type:"Character",value:r(e)}}}characterClass(){const e=[];let t=false;this.consumeChar("[");if(this.peekChar(0)==="^"){this.consumeChar("^");t=true}while(this.isClassAtom()){const t=this.classAtom();const n=t.type==="Character";if(c(t)&&this.isRangeDash()){this.consumeChar("-");const n=this.classAtom();const s=n.type==="Character";if(c(n)){if(n.value<t.value){throw Error("Range out of order in character class")}e.push({from:t.value,to:n.value})}else{i(t.value,e);e.push(r("-"));i(n.value,e)}}else{i(t.value,e)}}this.consumeChar("]");return{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case"\n":case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){this.consumeChar("\\");switch(this.peekChar()){case"b":this.consumeChar("b");return{type:"Character",value:r("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=true;this.consumeChar("(");switch(this.peekChar(0)){case"?":this.consumeChar("?");this.consumeChar(":");e=false;break;default:this.groupIdx++;break}const t=this.disjunction();this.consumeChar(")");const n={type:"Group",capturing:e,value:t};if(e){n["idx"]=this.groupIdx}return n}positiveInteger(){let e=this.popChar();if(p.test(e)===false){throw Error("Expecting a positive integer")}while(h.test(this.peekChar(0))){e+=this.popChar()}return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(h.test(e)===false){throw Error("Expecting an integer")}while(h.test(this.peekChar(0))){e+=this.popChar()}return parseInt(e,10)}patternCharacter(){const e=this.popChar();switch(e){case"\n":case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:r(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return true;default:return false}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return h.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case"\n":case"\r":case"\u2028":case"\u2029":return false;default:return true}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter()){return true}switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return true;default:return false}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return true;case"\\":switch(this.peekChar(1)){case"b":case"B":return true;default:return false}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return false}}isQuantifier(){const e=this.saveState();try{return this.quantifier(true)!==undefined}catch(t){return false}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case"\n":case"\r":case"\u2028":case"\u2029":return false;default:return true}}parseHexDigits(e){let t="";for(let r=0;r<e;r++){const e=this.popChar();if(f.test(e)===false){throw Error("Expecting a HexDecimal digits")}t+=e}const n=parseInt(t,16);return{type:"Character",value:n}}peekChar(e=0){return this.input[this.idx+e]}popChar(){const e=this.peekChar(0);this.consumeChar(undefined);return e}consumeChar(e){if(e!==undefined&&this.input[this.idx]!==e){throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx)}if(this.idx>=this.input.length){throw Error("Unexpected end of input")}this.idx++}loc(e){return{begin:e,end:this.idx}}}class g{visitChildren(e){for(const t in e){const n=e[t];if(e.hasOwnProperty(t)){if(n.type!==undefined){this.visit(n)}else if(Array.isArray(n)){n.forEach((e=>{this.visit(e)}),this)}}}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e);break}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}}},87290:(e,t,n)=>{"use strict";n.d(t,{b:()=>u});var r=n(74888);var i=n(6052);var s=n(41281);var a=n(37810);var o=class extends r.mR{static{(0,r.K2)(this,"GitGraphTokenBuilder")}constructor(){super(["gitGraph"])}};var c={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new r.Tm),"ValueConverter")}};function u(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr);const n=(0,s.WQ)((0,a.t)({shared:t}),r.eZ,c);t.ServiceRegistry.register(n);return{shared:t,GitGraph:n}}(0,r.K2)(u,"createGitGraphServices")},36578:(e,t,n)=>{"use strict";n.d(t,{f:()=>u});var r=n(74888);var i=n(6052);var s=n(41281);var a=n(37810);var o=class extends r.mR{static{(0,r.K2)(this,"RadarTokenBuilder")}constructor(){super(["radar-beta"])}};var c={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new r.Tm),"ValueConverter")}};function u(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr);const n=(0,s.WQ)((0,a.t)({shared:t}),r.YP,c);t.ServiceRegistry.register(n);return{shared:t,Radar:n}}(0,r.K2)(u,"createRadarServices")},74888:(e,t,n)=>{"use strict";n.d(t,{mR:()=>Le,dg:()=>Ce,jE:()=>Ee,Tm:()=>Ne,eZ:()=>ke,e5:()=>Ae,sr:()=>ve,AM:()=>Te,KX:()=>Re,YP:()=>xe,K2:()=>g});var r=n(64032);var i=n(37810);var s=n(41281);var a=n(85684);var o=n(6052);var c=n(14247);const u={Grammar:()=>undefined,LanguageMetaData:()=>({caseInsensitive:false,fileExtensions:[".langium"],languageId:"langium"})};const l={AstReflection:()=>new a.QX};function d(){const e=(0,s.WQ)((0,i.u)(o.D),l);const t=(0,s.WQ)((0,i.t)({shared:e}),u);e.ServiceRegistry.register(t);return t}function f(e){var t;const n=d();const r=n.serializer.JsonSerializer.deserialize(e);n.shared.workspace.LangiumDocumentFactory.fromModel(r,c.r.parse(`memory://${(t=r.name)!==null&&t!==void 0?t:"grammar"}.langium`));return r}var h=n(14480);var p=n(25355);var m=Object.defineProperty;var g=(e,t)=>m(e,"name",{value:t,configurable:true});var y="Statement";var v="Architecture";function A(e){return J.isInstance(e,v)}g(A,"isArchitecture");var T="Axis";var R="Branch";function E(e){return J.isInstance(e,R)}g(E,"isBranch");var k="Checkout";var x="CherryPicking";var $="Commit";function w(e){return J.isInstance(e,$)}g(w,"isCommit");var I="Common";function S(e){return J.isInstance(e,I)}g(S,"isCommon");var C="Curve";var N="Edge";var L="Entry";var b="GitGraph";function _(e){return J.isInstance(e,b)}g(_,"isGitGraph");var O="Group";var P="Info";function M(e){return J.isInstance(e,P)}g(M,"isInfo");var D="Junction";var U="Merge";function F(e){return J.isInstance(e,U)}g(F,"isMerge");var G="Option";var B="Packet";function K(e){return J.isInstance(e,B)}g(K,"isPacket");var j="PacketBlock";function V(e){return J.isInstance(e,j)}g(V,"isPacketBlock");var W="Pie";function H(e){return J.isInstance(e,W)}g(H,"isPie");var z="PieSection";function Y(e){return J.isInstance(e,z)}g(Y,"isPieSection");var q="Radar";var X="Service";var Q="Direction";var Z=class extends r.kD{static{g(this,"MermaidAstReflection")}getAllTypes(){return[v,T,R,k,x,$,I,C,Q,N,L,b,O,P,D,U,G,B,j,W,z,q,X,y]}computeIsSubtype(e,t){switch(e){case R:case k:case x:case $:case U:{return this.isSubtype(y,t)}case Q:{return this.isSubtype(b,t)}default:{return false}}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Entry:axis":{return T}default:{throw new Error(`${t} is not a valid reference id.`)}}}getTypeMetaData(e){switch(e){case v:{return{name:v,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]}}case T:{return{name:T,properties:[{name:"label"},{name:"name"}]}}case R:{return{name:R,properties:[{name:"name"},{name:"order"}]}}case k:{return{name:k,properties:[{name:"branch"}]}}case x:{return{name:x,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]}}case $:{return{name:$,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]}}case I:{return{name:I,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]}}case C:{return{name:C,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]}}case N:{return{name:N,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:false},{name:"lhsId"},{name:"lhsInto",defaultValue:false},{name:"rhsDir"},{name:"rhsGroup",defaultValue:false},{name:"rhsId"},{name:"rhsInto",defaultValue:false},{name:"title"}]}}case L:{return{name:L,properties:[{name:"axis"},{name:"value"}]}}case b:{return{name:b,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]}}case O:{return{name:O,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]}}case P:{return{name:P,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]}}case D:{return{name:D,properties:[{name:"id"},{name:"in"}]}}case U:{return{name:U,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]}}case G:{return{name:G,properties:[{name:"name"},{name:"value",defaultValue:false}]}}case B:{return{name:B,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]}}case j:{return{name:j,properties:[{name:"end"},{name:"label"},{name:"start"}]}}case W:{return{name:W,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:false},{name:"title"}]}}case z:{return{name:z,properties:[{name:"label"},{name:"value"}]}}case q:{return{name:q,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]}}case X:{return{name:X,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]}}case Q:{return{name:Q,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]}}default:{return{name:e,properties:[]}}}}};var J=new Z;var ee;var te=g((()=>ee??(ee=f('{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}'))),"InfoGrammar");var ne;var re=g((()=>ne??(ne=f(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"?"},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}`))),"PacketGrammar");var ie;var se=g((()=>ie??(ie=f('{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"PIE_SECTION_LABEL","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]+\\"/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"PIE_SECTION_VALUE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}'))),"PieGrammar");var ae;var oe=g((()=>ae??(ae=f('{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ID","definition":{"$type":"RegexToken","regex":"/[\\\\w]+/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TEXT_ICON","definition":{"$type":"RegexToken","regex":"/\\\\(\\"[^\\"]+\\"\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}'))),"ArchitectureGrammar");var ce;var ue=g((()=>ce??(ce=f(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+(?=\\\\s)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`))),"GitGraphGrammar");var le;var de=g((()=>le??(le=f(`{"$type":"Grammar","isDeclared":true,"name":"Radar","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@12"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@12"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9\\\\-_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`))),"RadarGrammar");var fe={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:false,mode:"production"};var he={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:false,mode:"production"};var pe={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:false,mode:"production"};var me={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:false,mode:"production"};var ge={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:false,mode:"production"};var ye={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:false,mode:"production"};var ve={AstReflection:g((()=>new Z),"AstReflection")};var Ae={Grammar:g((()=>te()),"Grammar"),LanguageMetaData:g((()=>fe),"LanguageMetaData"),parser:{}};var Te={Grammar:g((()=>re()),"Grammar"),LanguageMetaData:g((()=>he),"LanguageMetaData"),parser:{}};var Re={Grammar:g((()=>se()),"Grammar"),LanguageMetaData:g((()=>pe),"LanguageMetaData"),parser:{}};var Ee={Grammar:g((()=>oe()),"Grammar"),LanguageMetaData:g((()=>me),"LanguageMetaData"),parser:{}};var ke={Grammar:g((()=>ue()),"Grammar"),LanguageMetaData:g((()=>ge),"LanguageMetaData"),parser:{}};var xe={Grammar:g((()=>de()),"Grammar"),LanguageMetaData:g((()=>ye),"LanguageMetaData"),parser:{}};var $e=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/;var we=/accTitle[\t ]*:([^\n\r]*)/;var Ie=/title([\t ][^\n\r]*|)/;var Se={ACC_DESCR:$e,ACC_TITLE:we,TITLE:Ie};var Ce=class extends h.d{static{g(this,"AbstractMermaidValueConverter")}runConverter(e,t,n){let r=this.runCommonConverter(e,t,n);if(r===void 0){r=this.runCustomConverter(e,t,n)}if(r===void 0){return super.runConverter(e,t,n)}return r}runCommonConverter(e,t,n){const r=Se[e.name];if(r===void 0){return void 0}const i=r.exec(t);if(i===null){return void 0}if(i[1]!==void 0){return i[1].trim().replace(/[\t ]{2,}/gm," ")}if(i[2]!==void 0){return i[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,"\n")}return void 0}};var Ne=class extends Ce{static{g(this,"CommonValueConverter")}runCustomConverter(e,t,n){return void 0}};var Le=class extends p.Q{static{g(this,"AbstractMermaidTokenBuilder")}constructor(e){super();this.keywords=new Set(e)}buildKeywordTokens(e,t,n){const r=super.buildKeywordTokens(e,t,n);r.forEach((e=>{if(this.keywords.has(e.name)&&e.PATTERN!==void 0){e.PATTERN=new RegExp(e.PATTERN.toString()+"(?:(?=%%)|(?!\\S))")}}));return r}};var be=class extends Le{static{g(this,"CommonTokenBuilder")}}},77018:(e,t,n)=>{"use strict";n.d(t,{S:()=>l});var r=n(74888);var i=n(6052);var s=n(41281);var a=n(37810);var o=class extends r.mR{static{(0,r.K2)(this,"ArchitectureTokenBuilder")}constructor(){super(["architecture"])}};var c=class extends r.dg{static{(0,r.K2)(this,"ArchitectureValueConverter")}runCustomConverter(e,t,n){if(e.name==="ARCH_ICON"){return t.replace(/[()]/g,"").trim()}else if(e.name==="ARCH_TEXT_ICON"){return t.replace(/["()]/g,"")}else if(e.name==="ARCH_TITLE"){return t.replace(/[[\]]/g,"").trim()}return void 0}};var u={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new c),"ValueConverter")}};function l(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr);const n=(0,s.WQ)((0,a.t)({shared:t}),r.jE,u);t.ServiceRegistry.register(n);return{shared:t,Architecture:n}}(0,r.K2)(l,"createArchitectureServices")},25996:(e,t,n)=>{"use strict";n.d(t,{v:()=>u});var r=n(74888);var i=n(6052);var s=n(41281);var a=n(37810);var o=class extends r.mR{static{(0,r.K2)(this,"InfoTokenBuilder")}constructor(){super(["info","showInfo"])}};var c={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new r.Tm),"ValueConverter")}};function u(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr);const n=(0,s.WQ)((0,a.t)({shared:t}),r.e5,c);t.ServiceRegistry.register(n);return{shared:t,Info:n}}(0,r.K2)(u,"createInfoServices")},62409:(e,t,n)=>{"use strict";n.d(t,{f:()=>l});var r=n(74888);var i=n(6052);var s=n(41281);var a=n(37810);var o=class extends r.mR{static{(0,r.K2)(this,"PieTokenBuilder")}constructor(){super(["pie","showData"])}};var c=class extends r.dg{static{(0,r.K2)(this,"PieValueConverter")}runCustomConverter(e,t,n){if(e.name!=="PIE_SECTION_LABEL"){return void 0}return t.replace(/"/g,"").trim()}};var u={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new c),"ValueConverter")}};function l(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr);const n=(0,s.WQ)((0,a.t)({shared:t}),r.KX,u);t.ServiceRegistry.register(n);return{shared:t,Pie:n}}(0,r.K2)(l,"createPieServices")},69602:(e,t,n)=>{"use strict";n.d(t,{$:()=>u});var r=n(74888);var i=n(6052);var s=n(41281);var a=n(37810);var o=class extends r.mR{static{(0,r.K2)(this,"PacketTokenBuilder")}constructor(){super(["packet-beta"])}};var c={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new r.Tm),"ValueConverter")}};function u(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr);const n=(0,s.WQ)((0,a.t)({shared:t}),r.AM,c);t.ServiceRegistry.register(n);return{shared:t,Packet:n}}(0,r.K2)(u,"createPacketServices")},24010:(e,t,n)=>{"use strict";n.d(t,{qg:()=>f});var r=n(87290);var i=n(25996);var s=n(69602);var a=n(62409);var o=n(77018);var c=n(36578);var u=n(74888);var l={};var d={info:(0,u.K2)((async()=>{const{createInfoServices:e}=await n.e(9136).then(n.bind(n,49136));const t=e().Info.parser.LangiumParser;l.info=t}),"info"),packet:(0,u.K2)((async()=>{const{createPacketServices:e}=await n.e(3122).then(n.bind(n,63122));const t=e().Packet.parser.LangiumParser;l.packet=t}),"packet"),pie:(0,u.K2)((async()=>{const{createPieServices:e}=await n.e(1462).then(n.bind(n,91462));const t=e().Pie.parser.LangiumParser;l.pie=t}),"pie"),architecture:(0,u.K2)((async()=>{const{createArchitectureServices:e}=await n.e(9359).then(n.bind(n,79359));const t=e().Architecture.parser.LangiumParser;l.architecture=t}),"architecture"),gitGraph:(0,u.K2)((async()=>{const{createGitGraphServices:e}=await n.e(8354).then(n.bind(n,68354));const t=e().GitGraph.parser.LangiumParser;l.gitGraph=t}),"gitGraph"),radar:(0,u.K2)((async()=>{const{createRadarServices:e}=await n.e(7741).then(n.bind(n,97741));const t=e().Radar.parser.LangiumParser;l.radar=t}),"radar")};async function f(e,t){const n=d[e];if(!n){throw new Error(`Unknown diagram type: ${e}`)}if(!l[e]){await n()}const r=l[e];const i=r.parse(t);if(i.lexerErrors.length>0||i.parserErrors.length>0){throw new h(i)}return i.value}(0,u.K2)(f,"parse");var h=class extends Error{constructor(e){const t=e.lexerErrors.map((e=>e.message)).join("\n");const n=e.parserErrors.map((e=>e.message)).join("\n");super(`Parsing failed: ${t} ${n}`);this.result=e}static{(0,u.K2)(this,"MermaidParseError")}}},50450:(e,t,n)=>{"use strict";n.d(t,{ak:()=>J,mT:()=>Ds,LT:()=>sr,jr:()=>Gs,T6:()=>Xi,JG:()=>Vn,wL:()=>W,c$:()=>Y,Y2:()=>Q,$P:()=>q,Cy:()=>X,Pp:()=>Z,BK:()=>ee,PW:()=>Bn,my:()=>cr,jk:()=>Fr,Sk:()=>Wn,G:()=>or});var r=n(69769);var i=n(44882);var s=n(74650);var a=n(8937);var o=n(2850);var c=n(97134);function u(e){function t(){}t.prototype=e;const n=new t;function r(){return typeof n.bar}r();r();if(true)return e;(0,eval)(e)}function l(e,t,n){var r=-1,i=e.length;if(t<0){t=-t>i?0:i+t}n=n>i?i:n;if(n<0){n+=i}i=t>n?0:n-t>>>0;t>>>=0;var s=Array(i);while(++r<i){s[r]=e[r+t]}return s}const d=l;var f=n(29914);function h(e,t,n){var r=e==null?0:e.length;if(!r){return[]}t=n||t===undefined?1:(0,f.A)(t);return d(e,t<0?0:t,r)}const p=h;var m=n(86378);var g=n(16542);var y=n(376);var v=n(56280);var A=n(21585);var T=n(690);var R=n(37947);var E=Object.prototype;var k=E.hasOwnProperty;var x=(0,v.A)((function(e,t){if((0,T.A)(t)||(0,A.A)(t)){(0,y.A)(t,(0,R.A)(t),e);return}for(var n in t){if(k.call(t,n)){(0,g.A)(e,n,t[n])}}}));const $=x;var w=n(20900);var I=n(1121);var S=n(44835);var C=n(37138);function N(e,t){if(e==null){return{}}var n=(0,w.A)((0,C.A)(e),(function(e){return[e]}));t=(0,I.A)(t);return(0,S.A)(e,n,(function(e,n){return t(e,n[0])}))}const L=N;var b=n(64128);var _=n(53315);var O="[object RegExp]";function P(e){return(0,_.A)(e)&&(0,b.A)(e)==O}const M=P;var D=n(26132);var U=n(89986);var F=U.A&&U.A.isRegExp;var G=F?(0,D.A)(F):M;const B=G;function K(e){if(j(e)){return e.LABEL}else{return e.name}}function j(e){return(0,m.A)(e.LABEL)&&e.LABEL!==""}class V{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this);(0,r.A)(this.definition,(t=>{t.accept(e)}))}}class W extends V{constructor(e){super([]);this.idx=1;$(this,L(e,(e=>e!==undefined)))}set definition(e){}get definition(){if(this.referencedRule!==undefined){return this.referencedRule.definition}return[]}accept(e){e.visit(this)}}class H extends V{constructor(e){super(e.definition);this.orgText="";$(this,L(e,(e=>e!==undefined)))}}class z extends V{constructor(e){super(e.definition);this.ignoreAmbiguities=false;$(this,L(e,(e=>e!==undefined)))}}class Y extends V{constructor(e){super(e.definition);this.idx=1;$(this,L(e,(e=>e!==undefined)))}}class q extends V{constructor(e){super(e.definition);this.idx=1;$(this,L(e,(e=>e!==undefined)))}}class X extends V{constructor(e){super(e.definition);this.idx=1;$(this,L(e,(e=>e!==undefined)))}}class Q extends V{constructor(e){super(e.definition);this.idx=1;$(this,L(e,(e=>e!==undefined)))}}class Z extends V{constructor(e){super(e.definition);this.idx=1;$(this,L(e,(e=>e!==undefined)))}}class J extends V{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition);this.idx=1;this.ignoreAmbiguities=false;this.hasPredicates=false;$(this,L(e,(e=>e!==undefined)))}}class ee{constructor(e){this.idx=1;$(this,L(e,(e=>e!==undefined)))}accept(e){e.visit(this)}}function te(e){return(0,a.A)(e,ne)}function ne(e){function t(e){return(0,a.A)(e,ne)}if(e instanceof W){const t={type:"NonTerminal",name:e.nonTerminalName,idx:e.idx};if((0,m.A)(e.label)){t.label=e.label}return t}else if(e instanceof z){return{type:"Alternative",definition:t(e.definition)}}else if(e instanceof Y){return{type:"Option",idx:e.idx,definition:t(e.definition)}}else if(e instanceof q){return{type:"RepetitionMandatory",idx:e.idx,definition:t(e.definition)}}else if(e instanceof X){return{type:"RepetitionMandatoryWithSeparator",idx:e.idx,separator:ne(new ee({terminalType:e.separator})),definition:t(e.definition)}}else if(e instanceof Z){return{type:"RepetitionWithSeparator",idx:e.idx,separator:ne(new ee({terminalType:e.separator})),definition:t(e.definition)}}else if(e instanceof Q){return{type:"Repetition",idx:e.idx,definition:t(e.definition)}}else if(e instanceof J){return{type:"Alternation",idx:e.idx,definition:t(e.definition)}}else if(e instanceof ee){const t={type:"Terminal",name:e.terminalType.name,label:K(e.terminalType),idx:e.idx};if((0,m.A)(e.label)){t.terminalLabel=e.label}const n=e.terminalType.PATTERN;if(e.terminalType.PATTERN){t.pattern=B(n)?n.source:n}return t}else if(e instanceof H){return{type:"Rule",name:e.name,orgText:e.orgText,definition:t(e.definition)}}else{throw Error("non exhaustive match")}}class re{visit(e){const t=e;switch(t.constructor){case W:return this.visitNonTerminal(t);case z:return this.visitAlternative(t);case Y:return this.visitOption(t);case q:return this.visitRepetitionMandatory(t);case X:return this.visitRepetitionMandatoryWithSeparator(t);case Z:return this.visitRepetitionWithSeparator(t);case Q:return this.visitRepetition(t);case J:return this.visitAlternation(t);case ee:return this.visitTerminal(t);case H:return this.visitRule(t);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}}var ie=n(95345);var se=n(15912);function ae(e,t){var n;(0,se.A)(e,(function(e,r,i){n=t(e,r,i);return!n}));return!!n}const oe=ae;var ce=n(39990);var ue=n(31943);function le(e,t,n){var r=(0,ce.A)(e)?ie.A:oe;if(n&&(0,ue.A)(e,t,n)){t=undefined}return r(e,(0,I.A)(t,3))}const de=le;var fe=n(54949);var he=Math.max;function pe(e,t,n,r){e=(0,A.A)(e)?e:(0,i.A)(e);n=n&&!r?(0,f.A)(n):0;var s=e.length;if(n<0){n=he(s+n,0)}return(0,m.A)(e)?n<=s&&e.indexOf(t,n)>-1:!!s&&(0,fe.A)(e,t,n)>-1}const me=pe;function ge(e,t){var n=-1,r=e==null?0:e.length;while(++n<r){if(!t(e[n],n,e)){return false}}return true}const ye=ge;function ve(e,t){var n=true;(0,se.A)(e,(function(e,r,i){n=!!t(e,r,i);return n}));return n}const Ae=ve;function Te(e,t,n){var r=(0,ce.A)(e)?ye:Ae;if(n&&(0,ue.A)(e,t,n)){t=undefined}return r(e,(0,I.A)(t,3))}const Re=Te;function Ee(e){return e instanceof z||e instanceof Y||e instanceof Q||e instanceof q||e instanceof X||e instanceof Z||e instanceof ee||e instanceof H}function ke(e,t=[]){const n=e instanceof Y||e instanceof Q||e instanceof Z;if(n){return true}if(e instanceof J){return de(e.definition,(e=>ke(e,t)))}else if(e instanceof W&&me(t,e)){return false}else if(e instanceof V){if(e instanceof W){t.push(e)}return Re(e.definition,(e=>ke(e,t)))}else{return false}}function xe(e){return e instanceof J}function $e(e){if(e instanceof W){return"SUBRULE"}else if(e instanceof Y){return"OPTION"}else if(e instanceof J){return"OR"}else if(e instanceof q){return"AT_LEAST_ONE"}else if(e instanceof X){return"AT_LEAST_ONE_SEP"}else if(e instanceof Z){return"MANY_SEP"}else if(e instanceof Q){return"MANY"}else if(e instanceof ee){return"CONSUME"}else{throw Error("non exhaustive match")}}class we{walk(e,t=[]){(0,r.A)(e.definition,((n,r)=>{const i=p(e.definition,r+1);if(n instanceof W){this.walkProdRef(n,i,t)}else if(n instanceof ee){this.walkTerminal(n,i,t)}else if(n instanceof z){this.walkFlat(n,i,t)}else if(n instanceof Y){this.walkOption(n,i,t)}else if(n instanceof q){this.walkAtLeastOne(n,i,t)}else if(n instanceof X){this.walkAtLeastOneSep(n,i,t)}else if(n instanceof Z){this.walkManySep(n,i,t)}else if(n instanceof Q){this.walkMany(n,i,t)}else if(n instanceof J){this.walkOr(n,i,t)}else{throw Error("non exhaustive match")}}))}walkTerminal(e,t,n){}walkProdRef(e,t,n){}walkFlat(e,t,n){const r=t.concat(n);this.walk(e,r)}walkOption(e,t,n){const r=t.concat(n);this.walk(e,r)}walkAtLeastOne(e,t,n){const r=[new Y({definition:e.definition})].concat(t,n);this.walk(e,r)}walkAtLeastOneSep(e,t,n){const r=Ie(e,t,n);this.walk(e,r)}walkMany(e,t,n){const r=[new Y({definition:e.definition})].concat(t,n);this.walk(e,r)}walkManySep(e,t,n){const r=Ie(e,t,n);this.walk(e,r)}walkOr(e,t,n){const i=t.concat(n);(0,r.A)(e.definition,(e=>{const t=new z({definition:[e]});this.walk(t,i)}))}}function Ie(e,t,n){const r=[new Y({definition:[new ee({terminalType:e.separator})].concat(e.definition)})];const i=r.concat(t,n);return i}var Se=n(19363);function Ce(e){return e&&e.length?(0,Se.A)(e):[]}const Ne=Ce;var Le=n(74033);function be(e){if(e instanceof W){return be(e.referencedRule)}else if(e instanceof ee){return Pe(e)}else if(Ee(e)){return _e(e)}else if(xe(e)){return Oe(e)}else{throw Error("non exhaustive match")}}function _e(e){let t=[];const n=e.definition;let r=0;let i=n.length>r;let s;let a=true;while(i&&a){s=n[r];a=ke(s);t=t.concat(be(s));r=r+1;i=n.length>r}return Ne(t)}function Oe(e){const t=(0,a.A)(e.definition,(e=>be(e)));return Ne((0,Le.A)(t))}function Pe(e){return[e.terminalType]}const Me="_~IN~_";class De extends we{constructor(e){super();this.topProd=e;this.follows={}}startWalking(){this.walk(this.topProd);return this.follows}walkTerminal(e,t,n){}walkProdRef(e,t,n){const r=Fe(e.referencedRule,e.idx)+this.topProd.name;const i=t.concat(n);const s=new z({definition:i});const a=be(s);this.follows[r]=a}}function Ue(e){const t={};(0,r.A)(e,(e=>{const n=new De(e).startWalking();$(t,n)}));return t}function Fe(e,t){return e.name+t+Me}function Ge(e){const t=e.terminalType.name;return t+e.idx+IN}var Be=n(89523);var Ke=n(83173);var je=n(38693);var Ve=n(89191);var We=n(64725);var He="Expected a function";function ze(e){if(typeof e!="function"){throw new TypeError(He)}return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}const Ye=ze;function qe(e,t){var n=(0,ce.A)(e)?Ve.A:We.A;return n(e,Ye((0,I.A)(t,3)))}const Xe=qe;var Qe=n(58807);var Ze=Math.max;function Je(e,t,n){var r=e==null?0:e.length;if(!r){return-1}var i=n==null?0:(0,f.A)(n);if(i<0){i=Ze(r+i,0)}return(0,fe.A)(e,t,i)}const et=Je;var tt=n(65339);var nt=n(97133);var rt=n(63344);var it=n(43212);var st=n(7348);var at=n(4832);var ot=200;function ct(e,t,n,r){var i=-1,s=it.A,a=true,o=e.length,c=[],u=t.length;if(!o){return c}if(n){t=(0,w.A)(t,(0,D.A)(n))}if(r){s=st.A;a=false}else if(t.length>=ot){s=at.A;a=false;t=new rt.A(t)}e:while(++i<o){var l=e[i],d=n==null?l:n(l);l=r||l!==0?l:0;if(a&&d===d){var f=u;while(f--){if(t[f]===d){continue e}}c.push(l)}else if(!s(t,d,r)){c.push(l)}}return c}const ut=ct;var lt=n(62040);var dt=n(55881);var ft=n(10654);var ht=(0,dt.A)((function(e,t){return(0,ft.A)(e)?ut(e,(0,lt.A)(t,1,ft.A,true)):[]}));const pt=ht;function mt(e){var t=-1,n=e==null?0:e.length,r=0,i=[];while(++t<n){var s=e[t];if(s){i[r++]=s}}return i}const gt=mt;function yt(e){return e&&e.length?e[0]:undefined}const vt=yt;var At=n(85075);function Tt(e){if(console&&console.error){console.error(`Error: ${e}`)}}function Rt(e){if(console&&console.warn){console.warn(`Warning: ${e}`)}}let Et={};const kt=new Ke.H;function xt(e){const t=e.toString();if(Et.hasOwnProperty(t)){return Et[t]}else{const e=kt.pattern(t);Et[t]=e;return e}}function $t(){Et={}}const wt="Complement Sets are not supported for first char optimization";const It='Unable to use "first char" lexer optimizations:\n';function St(e,t=false){try{const t=xt(e);const n=Ct(t.value,{},t.flags.ignoreCase);return n}catch(n){if(n.message===wt){if(t){Rt(`${It}`+`\tUnable to optimize: < ${e.toString()} >\n`+"\tComplement Sets cannot be automatically optimized.\n"+"\tThis will disable the lexer's first char optimizations.\n"+"\tSee: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.")}}else{let n="";if(t){n="\n\tThis will disable the lexer's first char optimizations.\n"+"\tSee: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details."}Tt(`${It}\n`+`\tFailed parsing: < ${e.toString()} >\n`+`\tUsing the @chevrotain/regexp-to-ast library\n`+"\tPlease open an issue at: https://github.com/chevrotain/chevrotain/issues"+n)}}return[]}function Ct(e,t,n){switch(e.type){case"Disjunction":for(let r=0;r<e.value.length;r++){Ct(e.value[r],t,n)}break;case"Alternative":const i=e.value;for(let e=0;e<i.length;e++){const s=i[e];switch(s.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}const a=s;switch(a.type){case"Character":Nt(a.value,t,n);break;case"Set":if(a.complement===true){throw Error(wt)}(0,r.A)(a.value,(e=>{if(typeof e==="number"){Nt(e,t,n)}else{const r=e;if(n===true){for(let e=r.from;e<=r.to;e++){Nt(e,t,n)}}else{for(let e=r.from;e<=r.to&&e<vn;e++){Nt(e,t,n)}if(r.to>=vn){const e=r.from>=vn?r.from:vn;const n=r.to;const i=Tn(e);const s=Tn(n);for(let r=i;r<=s;r++){t[r]=r}}}}}));break;case"Group":Ct(a.value,t,n);break;default:throw Error("Non Exhaustive Match")}const o=a.quantifier!==undefined&&a.quantifier.atLeast===0;if(a.type==="Group"&&_t(a)===false||a.type!=="Group"&&o===false){break}}break;default:throw Error("non exhaustive match!")}return(0,i.A)(t)}function Nt(e,t,n){const r=Tn(e);t[r]=r;if(n===true){Lt(e,t)}}function Lt(e,t){const n=String.fromCharCode(e);const r=n.toUpperCase();if(r!==n){const e=Tn(r.charCodeAt(0));t[e]=e}else{const e=n.toLowerCase();if(e!==n){const n=Tn(e.charCodeAt(0));t[n]=n}}}function bt(e,t){return(0,At.A)(e.value,(e=>{if(typeof e==="number"){return me(t,e)}else{const n=e;return(0,At.A)(t,(e=>n.from<=e&&e<=n.to))!==undefined}}))}function _t(e){const t=e.quantifier;if(t&&t.atLeast===0){return true}if(!e.value){return false}return(0,ce.A)(e.value)?Re(e.value,_t):_t(e.value)}class Ot extends Ke.z{constructor(e){super();this.targetCharCodes=e;this.found=false}visitChildren(e){if(this.found===true){return}switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}visitCharacter(e){if(me(this.targetCharCodes,e.value)){this.found=true}}visitSet(e){if(e.complement){if(bt(e,this.targetCharCodes)===undefined){this.found=true}}else{if(bt(e,this.targetCharCodes)!==undefined){this.found=true}}}}function Pt(e,t){if(t instanceof RegExp){const n=xt(t);const r=new Ot(e);r.visit(n);return r.found}else{return(0,At.A)(t,(t=>me(e,t.charCodeAt(0))))!==undefined}}const Mt="PATTERN";const Dt="defaultMode";const Ut="modes";let Ft=typeof new RegExp("(?:)").sticky==="boolean";function Gt(){Ft=false}function Bt(){Ft=true}function Kt(e,t){t=(0,je.A)(t,{useSticky:Ft,debug:false,safeMode:false,positionTracking:"full",lineTerminatorCharacters:["\r","\n"],tracer:(e,t)=>t()});const n=t.tracer;n("initCharCodeToOptimizedIndexMap",(()=>{Rn()}));let i;n("Reject Lexer.NA",(()=>{i=Xe(e,(e=>e[Mt]===Vn.NA))}));let c=false;let u;n("Transform Patterns",(()=>{c=false;u=(0,a.A)(i,(e=>{const n=e[Mt];if(B(n)){const e=n.source;if(e.length===1&&e!=="^"&&e!=="$"&&e!=="."&&!n.ignoreCase){return e}else if(e.length===2&&e[0]==="\\"&&!me(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],e[1])){return e[1]}else{return t.useSticky?on(n):an(n)}}else if((0,Qe.A)(n)){c=true;return{exec:n}}else if(typeof n==="object"){c=true;return n}else if(typeof n==="string"){if(n.length===1){return n}else{const e=n.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&");const r=new RegExp(e);return t.useSticky?on(r):an(r)}}else{throw Error("non exhaustive match")}}))}));let l;let d;let f;let h;let p;n("misc mapping",(()=>{l=(0,a.A)(i,(e=>e.tokenTypeIdx));d=(0,a.A)(i,(e=>{const t=e.GROUP;if(t===Vn.SKIPPED){return undefined}else if((0,m.A)(t)){return t}else if((0,Be.A)(t)){return false}else{throw Error("non exhaustive match")}}));f=(0,a.A)(i,(e=>{const t=e.LONGER_ALT;if(t){const e=(0,ce.A)(t)?(0,a.A)(t,(e=>et(i,e))):[et(i,t)];return e}}));h=(0,a.A)(i,(e=>e.PUSH_MODE));p=(0,a.A)(i,(e=>(0,o.A)(e,"POP_MODE")))}));let g;n("Line Terminator Handling",(()=>{const e=gn(t.lineTerminatorCharacters);g=(0,a.A)(i,(e=>false));if(t.positionTracking!=="onlyOffset"){g=(0,a.A)(i,(t=>{if((0,o.A)(t,"LINE_BREAKS")){return!!t.LINE_BREAKS}else{return pn(t,e)===false&&Pt(e,t.PATTERN)}}))}}));let y;let v;let A;let T;n("Misc Mapping #2",(()=>{y=(0,a.A)(i,dn);v=(0,a.A)(u,fn);A=(0,tt.A)(i,((e,t)=>{const n=t.GROUP;if((0,m.A)(n)&&!(n===Vn.SKIPPED)){e[n]=[]}return e}),{});T=(0,a.A)(u,((e,t)=>({pattern:u[t],longerAlt:f[t],canLineTerminator:g[t],isCustom:y[t],short:v[t],group:d[t],push:h[t],pop:p[t],tokenTypeIdx:l[t],tokenType:i[t]})))}));let R=true;let E=[];if(!t.safeMode){n("First Char Optimization",(()=>{E=(0,tt.A)(i,((e,n,i)=>{if(typeof n.PATTERN==="string"){const t=n.PATTERN.charCodeAt(0);const r=Tn(t);yn(e,r,T[i])}else if((0,ce.A)(n.START_CHARS_HINT)){let t;(0,r.A)(n.START_CHARS_HINT,(n=>{const r=typeof n==="string"?n.charCodeAt(0):n;const s=Tn(r);if(t!==s){t=s;yn(e,s,T[i])}}))}else if(B(n.PATTERN)){if(n.PATTERN.unicode){R=false;if(t.ensureOptimizations){Tt(`${It}`+`\tUnable to analyze < ${n.PATTERN.toString()} > pattern.\n`+"\tThe regexp unicode flag is not currently supported by the regexp-to-ast library.\n"+"\tThis will disable the lexer's first char optimizations.\n"+"\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE")}}else{const a=St(n.PATTERN,t.ensureOptimizations);if((0,s.A)(a)){R=false}(0,r.A)(a,(t=>{yn(e,t,T[i])}))}}else{if(t.ensureOptimizations){Tt(`${It}`+`\tTokenType: <${n.name}> is using a custom token pattern without providing <start_chars_hint> parameter.\n`+"\tThis will disable the lexer's first char optimizations.\n"+"\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE")}R=false}return e}),[])}))}return{emptyGroups:A,patternIdxToConfig:T,charCodeToPatternIdxToConfig:E,hasCustom:c,canBeOptimized:R}}function jt(e,t){let n=[];const r=Wt(e);n=n.concat(r.errors);const i=Ht(r.valid);const s=i.valid;n=n.concat(i.errors);n=n.concat(Vt(s));n=n.concat(en(s));n=n.concat(tn(s,t));n=n.concat(nn(s));return n}function Vt(e){let t=[];const n=(0,nt.A)(e,(e=>B(e[Mt])));t=t.concat(Yt(n));t=t.concat(Qt(n));t=t.concat(Zt(n));t=t.concat(Jt(n));t=t.concat(qt(n));return t}function Wt(e){const t=(0,nt.A)(e,(e=>!(0,o.A)(e,Mt)));const n=(0,a.A)(t,(e=>({message:"Token Type: ->"+e.name+"<- missing static 'PATTERN' property",type:Kn.MISSING_PATTERN,tokenTypes:[e]})));const r=pt(e,t);return{errors:n,valid:r}}function Ht(e){const t=(0,nt.A)(e,(e=>{const t=e[Mt];return!B(t)&&!(0,Qe.A)(t)&&!(0,o.A)(t,"exec")&&!(0,m.A)(t)}));const n=(0,a.A)(t,(e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' can only be a RegExp, a"+" Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:Kn.INVALID_PATTERN,tokenTypes:[e]})));const r=pt(e,t);return{errors:n,valid:r}}const zt=/[^\\][$]/;function Yt(e){class t extends Ke.z{constructor(){super(...arguments);this.found=false}visitEndAnchor(e){this.found=true}}const n=(0,nt.A)(e,(e=>{const n=e.PATTERN;try{const e=xt(n);const r=new t;r.visit(e);return r.found}catch(r){return zt.test(n.source)}}));const r=(0,a.A)(n,(e=>({message:"Unexpected RegExp Anchor Error:\n"+"\tToken Type: ->"+e.name+"<- static 'PATTERN' cannot contain end of input anchor '$'\n"+"\tSee chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS"+"\tfor details.",type:Kn.EOI_ANCHOR_FOUND,tokenTypes:[e]})));return r}function qt(e){const t=(0,nt.A)(e,(e=>{const t=e.PATTERN;return t.test("")}));const n=(0,a.A)(t,(e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' must not match an empty string",type:Kn.EMPTY_MATCH_PATTERN,tokenTypes:[e]})));return n}const Xt=/[^\\[][\^]|^\^/;function Qt(e){class t extends Ke.z{constructor(){super(...arguments);this.found=false}visitStartAnchor(e){this.found=true}}const n=(0,nt.A)(e,(e=>{const n=e.PATTERN;try{const e=xt(n);const r=new t;r.visit(e);return r.found}catch(r){return Xt.test(n.source)}}));const r=(0,a.A)(n,(e=>({message:"Unexpected RegExp Anchor Error:\n"+"\tToken Type: ->"+e.name+"<- static 'PATTERN' cannot contain start of input anchor '^'\n"+"\tSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS"+"\tfor details.",type:Kn.SOI_ANCHOR_FOUND,tokenTypes:[e]})));return r}function Zt(e){const t=(0,nt.A)(e,(e=>{const t=e[Mt];return t instanceof RegExp&&(t.multiline||t.global)}));const n=(0,a.A)(t,(e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:Kn.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[e]})));return n}function Jt(e){const t=[];let n=(0,a.A)(e,(n=>(0,tt.A)(e,((e,r)=>{if(n.PATTERN.source===r.PATTERN.source&&!me(t,r)&&r.PATTERN!==Vn.NA){t.push(r);e.push(r);return e}return e}),[])));n=gt(n);const r=(0,nt.A)(n,(e=>e.length>1));const i=(0,a.A)(r,(e=>{const t=(0,a.A)(e,(e=>e.name));const n=vt(e).PATTERN;return{message:`The same RegExp pattern ->${n}<-`+`has been used in all of the following Token Types: ${t.join(", ")} <-`,type:Kn.DUPLICATE_PATTERNS_FOUND,tokenTypes:e}}));return i}function en(e){const t=(0,nt.A)(e,(e=>{if(!(0,o.A)(e,"GROUP")){return false}const t=e.GROUP;return t!==Vn.SKIPPED&&t!==Vn.NA&&!(0,m.A)(t)}));const n=(0,a.A)(t,(e=>({message:"Token Type: ->"+e.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:Kn.INVALID_GROUP_TYPE_FOUND,tokenTypes:[e]})));return n}function tn(e,t){const n=(0,nt.A)(e,(e=>e.PUSH_MODE!==undefined&&!me(t,e.PUSH_MODE)));const r=(0,a.A)(n,(e=>{const t=`Token Type: ->${e.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${e.PUSH_MODE}<-`+`which does not exist`;return{message:t,type:Kn.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[e]}}));return r}function nn(e){const t=[];const n=(0,tt.A)(e,((e,t,n)=>{const r=t.PATTERN;if(r===Vn.NA){return e}if((0,m.A)(r)){e.push({str:r,idx:n,tokenType:t})}else if(B(r)&&sn(r)){e.push({str:r.source,idx:n,tokenType:t})}return e}),[]);(0,r.A)(e,((e,i)=>{(0,r.A)(n,(({str:n,idx:r,tokenType:s})=>{if(i<r&&rn(n,e.PATTERN)){const n=`Token: ->${s.name}<- can never be matched.\n`+`Because it appears AFTER the Token Type ->${e.name}<-`+`in the lexer's definition.\n`+`See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;t.push({message:n,type:Kn.UNREACHABLE_PATTERN,tokenTypes:[e,s]})}}))}));return t}function rn(e,t){if(B(t)){const n=t.exec(e);return n!==null&&n.index===0}else if((0,Qe.A)(t)){return t(e,0,[],{})}else if((0,o.A)(t,"exec")){return t.exec(e,0,[],{})}else if(typeof t==="string"){return t===e}else{throw Error("non exhaustive match")}}function sn(e){const t=[".","\\","[","]","|","^","$","(",")","?","*","+","{"];return(0,At.A)(t,(t=>e.source.indexOf(t)!==-1))===undefined}function an(e){const t=e.ignoreCase?"i":"";return new RegExp(`^(?:${e.source})`,t)}function on(e){const t=e.ignoreCase?"iy":"y";return new RegExp(`${e.source}`,t)}function cn(e,t,n){const i=[];if(!(0,o.A)(e,Dt)){i.push({message:"A MultiMode Lexer cannot be initialized without a <"+Dt+"> property in its definition\n",type:Kn.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE})}if(!(0,o.A)(e,Ut)){i.push({message:"A MultiMode Lexer cannot be initialized without a <"+Ut+"> property in its definition\n",type:Kn.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY})}if((0,o.A)(e,Ut)&&(0,o.A)(e,Dt)&&!(0,o.A)(e.modes,e.defaultMode)){i.push({message:`A MultiMode Lexer cannot be initialized with a ${Dt}: <${e.defaultMode}>`+`which does not exist\n`,type:Kn.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST})}if((0,o.A)(e,Ut)){(0,r.A)(e.modes,((e,t)=>{(0,r.A)(e,((n,s)=>{if((0,Be.A)(n)){i.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:`+`<${t}> at index: <${s}>\n`,type:Kn.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED})}else if((0,o.A)(n,"LONGER_ALT")){const s=(0,ce.A)(n.LONGER_ALT)?n.LONGER_ALT:[n.LONGER_ALT];(0,r.A)(s,(r=>{if(!(0,Be.A)(r)&&!me(e,r)){i.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${r.name}> on token <${n.name}> outside of mode <${t}>\n`,type:Kn.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})}}))}}))}))}return i}function un(e,t,n){const s=[];let a=false;const c=gt((0,Le.A)((0,i.A)(e.modes)));const u=Xe(c,(e=>e[Mt]===Vn.NA));const l=gn(n);if(t){(0,r.A)(u,(e=>{const t=pn(e,l);if(t!==false){const n=mn(e,t);const r={message:n,type:t.issue,tokenType:e};s.push(r)}else{if((0,o.A)(e,"LINE_BREAKS")){if(e.LINE_BREAKS===true){a=true}}else{if(Pt(l,e.PATTERN)){a=true}}}}))}if(t&&!a){s.push({message:"Warning: No LINE_BREAKS Found.\n"+"\tThis Lexer has been defined to track line and column information,\n"+"\tBut none of the Token Types can be identified as matching a line terminator.\n"+"\tSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS \n"+"\tfor details.",type:Kn.NO_LINE_BREAKS_FLAGS})}return s}function ln(e){const t={};const n=(0,R.A)(e);(0,r.A)(n,(n=>{const r=e[n];if((0,ce.A)(r)){t[n]=[]}else{throw Error("non exhaustive match")}}));return t}function dn(e){const t=e.PATTERN;if(B(t)){return false}else if((0,Qe.A)(t)){return true}else if((0,o.A)(t,"exec")){return true}else if((0,m.A)(t)){return false}else{throw Error("non exhaustive match")}}function fn(e){if((0,m.A)(e)&&e.length===1){return e.charCodeAt(0)}else{return false}}const hn={test:function(e){const t=e.length;for(let n=this.lastIndex;n<t;n++){const t=e.charCodeAt(n);if(t===10){this.lastIndex=n+1;return true}else if(t===13){if(e.charCodeAt(n+1)===10){this.lastIndex=n+2}else{this.lastIndex=n+1}return true}}return false},lastIndex:0};function pn(e,t){if((0,o.A)(e,"LINE_BREAKS")){return false}else{if(B(e.PATTERN)){try{Pt(t,e.PATTERN)}catch(n){return{issue:Kn.IDENTIFY_TERMINATOR,errMsg:n.message}}return false}else if((0,m.A)(e.PATTERN)){return false}else if(dn(e)){return{issue:Kn.CUSTOM_LINE_BREAK}}else{throw Error("non exhaustive match")}}}function mn(e,t){if(t.issue===Kn.IDENTIFY_TERMINATOR){return"Warning: unable to identify line terminator usage in pattern.\n"+`\tThe problem is in the <${e.name}> Token Type\n`+`\t Root cause: ${t.errMsg}.\n`+"\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR"}else if(t.issue===Kn.CUSTOM_LINE_BREAK){return"Warning: A Custom Token Pattern should specify the <line_breaks> option.\n"+`\tThe problem is in the <${e.name}> Token Type\n`+"\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK"}else{throw Error("non exhaustive match")}}function gn(e){const t=(0,a.A)(e,(e=>{if((0,m.A)(e)){return e.charCodeAt(0)}else{return e}}));return t}function yn(e,t,n){if(e[t]===undefined){e[t]=[n]}else{e[t].push(n)}}const vn=256;let An=[];function Tn(e){return e<vn?e:An[e]}function Rn(){if((0,s.A)(An)){An=new Array(65536);for(let e=0;e<65536;e++){An[e]=e>255?255+~~(e/255):e}}}var En=n(63077);var kn=n(42111);var xn=n(80359);function $n(e){const t=(new Date).getTime();const n=e();const r=(new Date).getTime();const i=r-t;return{time:i,value:n}}function wn(e,t){const n=e.tokenTypeIdx;if(n===t.tokenTypeIdx){return true}else{return t.isParent===true&&t.categoryMatchesMap[n]===true}}function In(e,t){return e.tokenTypeIdx===t.tokenTypeIdx}let Sn=1;const Cn={};function Nn(e){const t=Ln(e);bn(t);On(t);_n(t);(0,r.A)(t,(e=>{e.isParent=e.categoryMatches.length>0}))}function Ln(e){let t=(0,c.A)(e);let n=e;let r=true;while(r){n=gt((0,Le.A)((0,a.A)(n,(e=>e.CATEGORIES))));const e=pt(n,t);t=t.concat(e);if((0,s.A)(e)){r=false}else{n=e}}return t}function bn(e){(0,r.A)(e,(e=>{if(!Mn(e)){Cn[Sn]=e;e.tokenTypeIdx=Sn++}if(Dn(e)&&!(0,ce.A)(e.CATEGORIES)){e.CATEGORIES=[e.CATEGORIES]}if(!Dn(e)){e.CATEGORIES=[]}if(!Un(e)){e.categoryMatches=[]}if(!Fn(e)){e.categoryMatchesMap={}}}))}function _n(e){(0,r.A)(e,(e=>{e.categoryMatches=[];(0,r.A)(e.categoryMatchesMap,((t,n)=>{e.categoryMatches.push(Cn[n].tokenTypeIdx)}))}))}function On(e){(0,r.A)(e,(e=>{Pn([],e)}))}function Pn(e,t){(0,r.A)(e,(e=>{t.categoryMatchesMap[e.tokenTypeIdx]=true}));(0,r.A)(t.CATEGORIES,(n=>{const r=e.concat(t);if(!me(r,n)){Pn(r,n)}}))}function Mn(e){return(0,o.A)(e,"tokenTypeIdx")}function Dn(e){return(0,o.A)(e,"CATEGORIES")}function Un(e){return(0,o.A)(e,"categoryMatches")}function Fn(e){return(0,o.A)(e,"categoryMatchesMap")}function Gn(e){return(0,o.A)(e,"tokenTypeIdx")}const Bn={buildUnableToPopLexerModeMessage(e){return`Unable to pop Lexer Mode after encountering Token ->${e.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(e,t,n,r,i){return`unexpected character: ->${e.charAt(t)}<- at offset: ${t},`+` skipped ${n} characters.`}};var Kn;(function(e){e[e["MISSING_PATTERN"]=0]="MISSING_PATTERN";e[e["INVALID_PATTERN"]=1]="INVALID_PATTERN";e[e["EOI_ANCHOR_FOUND"]=2]="EOI_ANCHOR_FOUND";e[e["UNSUPPORTED_FLAGS_FOUND"]=3]="UNSUPPORTED_FLAGS_FOUND";e[e["DUPLICATE_PATTERNS_FOUND"]=4]="DUPLICATE_PATTERNS_FOUND";e[e["INVALID_GROUP_TYPE_FOUND"]=5]="INVALID_GROUP_TYPE_FOUND";e[e["PUSH_MODE_DOES_NOT_EXIST"]=6]="PUSH_MODE_DOES_NOT_EXIST";e[e["MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE"]=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE";e[e["MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY"]=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY";e[e["MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST"]=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST";e[e["LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED"]=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED";e[e["SOI_ANCHOR_FOUND"]=11]="SOI_ANCHOR_FOUND";e[e["EMPTY_MATCH_PATTERN"]=12]="EMPTY_MATCH_PATTERN";e[e["NO_LINE_BREAKS_FLAGS"]=13]="NO_LINE_BREAKS_FLAGS";e[e["UNREACHABLE_PATTERN"]=14]="UNREACHABLE_PATTERN";e[e["IDENTIFY_TERMINATOR"]=15]="IDENTIFY_TERMINATOR";e[e["CUSTOM_LINE_BREAK"]=16]="CUSTOM_LINE_BREAK";e[e["MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"]=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(Kn||(Kn={}));const jn={deferDefinitionErrorsHandling:false,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:["\n","\r"],ensureOptimizations:false,safeMode:false,errorMessageProvider:Bn,traceInitPerf:false,skipValidations:false,recoveryEnabled:true};Object.freeze(jn);class Vn{constructor(e,t=jn){this.lexerDefinition=e;this.lexerDefinitionErrors=[];this.lexerDefinitionWarning=[];this.patternIdxToConfig={};this.charCodeToPatternIdxToConfig={};this.modes=[];this.emptyGroups={};this.trackStartLines=true;this.trackEndLines=true;this.hasCustom=false;this.canModeBeOptimized={};this.TRACE_INIT=(e,t)=>{if(this.traceInitPerf===true){this.traceInitIndent++;const n=new Array(this.traceInitIndent+1).join("\t");if(this.traceInitIndent<this.traceInitMaxIdent){console.log(`${n}--\x3e <${e}>`)}const{time:r,value:i}=$n(t);const s=r>10?console.warn:console.log;if(this.traceInitIndent<this.traceInitMaxIdent){s(`${n}<-- <${e}> time: ${r}ms`)}this.traceInitIndent--;return i}else{return t()}};if(typeof t==="boolean"){throw Error("The second argument to the Lexer constructor is now an ILexerConfig Object.\n"+"a boolean 2nd argument is no longer supported")}this.config=$({},jn,t);const n=this.config.traceInitPerf;if(n===true){this.traceInitMaxIdent=Infinity;this.traceInitPerf=true}else if(typeof n==="number"){this.traceInitMaxIdent=n;this.traceInitPerf=true}this.traceInitIndent=-1;this.TRACE_INIT("Lexer Constructor",(()=>{let n;let i=true;this.TRACE_INIT("Lexer Config handling",(()=>{if(this.config.lineTerminatorsPattern===jn.lineTerminatorsPattern){this.config.lineTerminatorsPattern=hn}else{if(this.config.lineTerminatorCharacters===jn.lineTerminatorCharacters){throw Error("Error: Missing <lineTerminatorCharacters> property on the Lexer config.\n"+"\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS")}}if(t.safeMode&&t.ensureOptimizations){throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.')}this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking);this.trackEndLines=/full/i.test(this.config.positionTracking);if((0,ce.A)(e)){n={modes:{defaultMode:(0,c.A)(e)},defaultMode:Dt}}else{i=false;n=(0,c.A)(e)}}));if(this.config.skipValidations===false){this.TRACE_INIT("performRuntimeChecks",(()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(cn(n,this.trackStartLines,this.config.lineTerminatorCharacters))}));this.TRACE_INIT("performWarningRuntimeChecks",(()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(un(n,this.trackStartLines,this.config.lineTerminatorCharacters))}))}n.modes=n.modes?n.modes:{};(0,r.A)(n.modes,((e,t)=>{n.modes[t]=Xe(e,(e=>(0,Be.A)(e)))}));const o=(0,R.A)(n.modes);(0,r.A)(n.modes,((e,n)=>{this.TRACE_INIT(`Mode: <${n}> processing`,(()=>{this.modes.push(n);if(this.config.skipValidations===false){this.TRACE_INIT(`validatePatterns`,(()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(jt(e,o))}))}if((0,s.A)(this.lexerDefinitionErrors)){Nn(e);let r;this.TRACE_INIT(`analyzeTokenTypes`,(()=>{r=Kt(e,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})}));this.patternIdxToConfig[n]=r.patternIdxToConfig;this.charCodeToPatternIdxToConfig[n]=r.charCodeToPatternIdxToConfig;this.emptyGroups=$({},this.emptyGroups,r.emptyGroups);this.hasCustom=r.hasCustom||this.hasCustom;this.canModeBeOptimized[n]=r.canBeOptimized}}))}));this.defaultMode=n.defaultMode;if(!(0,s.A)(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){const e=(0,a.A)(this.lexerDefinitionErrors,(e=>e.message));const t=e.join("-----------------------\n");throw new Error("Errors detected in definition of Lexer:\n"+t)}(0,r.A)(this.lexerDefinitionWarning,(e=>{Rt(e.message)}));this.TRACE_INIT("Choosing sub-methods implementations",(()=>{if(Ft){this.chopInput=En.A;this.match=this.matchWithTest}else{this.updateLastIndex=kn.A;this.match=this.matchWithExec}if(i){this.handleModes=kn.A}if(this.trackStartLines===false){this.computeNewColumn=En.A}if(this.trackEndLines===false){this.updateTokenEndLineColumnLocation=kn.A}if(/full/i.test(this.config.positionTracking)){this.createTokenInstance=this.createFullToken}else if(/onlyStart/i.test(this.config.positionTracking)){this.createTokenInstance=this.createStartOnlyToken}else if(/onlyOffset/i.test(this.config.positionTracking)){this.createTokenInstance=this.createOffsetOnlyToken}else{throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`)}if(this.hasCustom){this.addToken=this.addTokenUsingPush;this.handlePayload=this.handlePayloadWithCustom}else{this.addToken=this.addTokenUsingMemberAccess;this.handlePayload=this.handlePayloadNoCustom}}));this.TRACE_INIT("Failed Optimization Warnings",(()=>{const e=(0,tt.A)(this.canModeBeOptimized,((e,t,n)=>{if(t===false){e.push(n)}return e}),[]);if(t.ensureOptimizations&&!(0,s.A)(e)){throw Error(`Lexer Modes: < ${e.join(", ")} > cannot be optimized.\n`+'\t Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.\n'+"\t Or inspect the console log for details on how to resolve these issues.")}}));this.TRACE_INIT("clearRegExpParserCache",(()=>{$t()}));this.TRACE_INIT("toFastProperties",(()=>{u(this)}))}))}tokenize(e,t=this.defaultMode){if(!(0,s.A)(this.lexerDefinitionErrors)){const e=(0,a.A)(this.lexerDefinitionErrors,(e=>e.message));const t=e.join("-----------------------\n");throw new Error("Unable to Tokenize because Errors detected in definition of Lexer:\n"+t)}return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let n,r,i,s,a,o,c,u,l,d,f,h,p,m,g,y;const v=e;const A=v.length;let T=0;let R=0;const E=this.hasCustom?0:Math.floor(e.length/10);const k=new Array(E);const x=[];let $=this.trackStartLines?1:undefined;let w=this.trackStartLines?1:undefined;const I=ln(this.emptyGroups);const S=this.trackStartLines;const C=this.config.lineTerminatorsPattern;let N=0;let L=[];let b=[];const _=[];const O=[];Object.freeze(O);let P;function M(){return L}function D(e){const t=Tn(e);const n=b[t];if(n===undefined){return O}else{return n}}const U=e=>{if(_.length===1&&e.tokenType.PUSH_MODE===undefined){const t=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(e);x.push({offset:e.startOffset,line:e.startLine,column:e.startColumn,length:e.image.length,message:t})}else{_.pop();const e=(0,xn.A)(_);L=this.patternIdxToConfig[e];b=this.charCodeToPatternIdxToConfig[e];N=L.length;const t=this.canModeBeOptimized[e]&&this.config.safeMode===false;if(b&&t){P=D}else{P=M}}};function F(e){_.push(e);b=this.charCodeToPatternIdxToConfig[e];L=this.patternIdxToConfig[e];N=L.length;N=L.length;const t=this.canModeBeOptimized[e]&&this.config.safeMode===false;if(b&&t){P=D}else{P=M}}F.call(this,t);let G;const B=this.config.recoveryEnabled;while(T<A){o=null;const t=v.charCodeAt(T);const m=P(t);const E=m.length;for(n=0;n<E;n++){G=m[n];const r=G.pattern;c=null;const l=G.short;if(l!==false){if(t===l){o=r}}else if(G.isCustom===true){y=r.exec(v,T,k,I);if(y!==null){o=y[0];if(y.payload!==undefined){c=y.payload}}else{o=null}}else{this.updateLastIndex(r,T);o=this.match(r,e,T)}if(o!==null){a=G.longerAlt;if(a!==undefined){const t=a.length;for(i=0;i<t;i++){const t=L[a[i]];const n=t.pattern;u=null;if(t.isCustom===true){y=n.exec(v,T,k,I);if(y!==null){s=y[0];if(y.payload!==undefined){u=y.payload}}else{s=null}}else{this.updateLastIndex(n,T);s=this.match(n,e,T)}if(s&&s.length>o.length){o=s;c=u;G=t;break}}}break}}if(o!==null){l=o.length;d=G.group;if(d!==undefined){f=G.tokenTypeIdx;h=this.createTokenInstance(o,T,f,G.tokenType,$,w,l);this.handlePayload(h,c);if(d===false){R=this.addToken(k,R,h)}else{I[d].push(h)}}e=this.chopInput(e,l);T=T+l;w=this.computeNewColumn(w,l);if(S===true&&G.canLineTerminator===true){let e=0;let t;let n;C.lastIndex=0;do{t=C.test(o);if(t===true){n=C.lastIndex-1;e++}}while(t===true);if(e!==0){$=$+e;w=l-n;this.updateTokenEndLineColumnLocation(h,d,n,e,$,w,l)}}this.handleModes(G,U,F,h)}else{const t=T;const n=$;const i=w;let s=B===false;while(s===false&&T<A){e=this.chopInput(e,1);T++;for(r=0;r<N;r++){const t=L[r];const n=t.pattern;const i=t.short;if(i!==false){if(v.charCodeAt(T)===i){s=true}}else if(t.isCustom===true){s=n.exec(v,T,k,I)!==null}else{this.updateLastIndex(n,T);s=n.exec(e)!==null}if(s===true){break}}}p=T-t;w=this.computeNewColumn(w,p);g=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(v,t,p,n,i);x.push({offset:t,line:n,column:i,length:p,message:g});if(B===false){break}}}if(!this.hasCustom){k.length=R}return{tokens:k,groups:I,errors:x}}handleModes(e,t,n,r){if(e.pop===true){const i=e.push;t(r);if(i!==undefined){n.call(this,i)}}else if(e.push!==undefined){n.call(this,e.push)}}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,n,r,i,s,a){let o,c;if(t!==undefined){o=n===a-1;c=o?-1:0;if(!(r===1&&o===true)){e.endLine=i+c;e.endColumn=s-1+-c}}}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,n,r){return{image:e,startOffset:t,tokenTypeIdx:n,tokenType:r}}createStartOnlyToken(e,t,n,r,i,s){return{image:e,startOffset:t,startLine:i,startColumn:s,tokenTypeIdx:n,tokenType:r}}createFullToken(e,t,n,r,i,s,a){return{image:e,startOffset:t,endOffset:t+a-1,startLine:i,endLine:i,startColumn:s,endColumn:s+a-1,tokenTypeIdx:n,tokenType:r}}addTokenUsingPush(e,t,n){e.push(n);return t}addTokenUsingMemberAccess(e,t,n){e[t]=n;t++;return t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){if(t!==null){e.payload=t}}matchWithTest(e,t,n){const r=e.test(t);if(r===true){return t.substring(n,e.lastIndex)}return null}matchWithExec(e,t){const n=e.exec(t);return n!==null?n[0]:null}}Vn.SKIPPED="This marks a skipped Token pattern, this means each token identified by it will"+"be consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";Vn.NA=/NOT_APPLICABLE/;function Wn(e){if(zn(e)){return e.LABEL}else{return e.name}}function Hn(e){return e.name}function zn(e){return(0,m.A)(e.LABEL)&&e.LABEL!==""}const Yn="parent";const qn="categories";const Xn="label";const Qn="group";const Zn="push_mode";const Jn="pop_mode";const er="longer_alt";const tr="line_breaks";const nr="start_chars_hint";function rr(e){return ir(e)}function ir(e){const t=e.pattern;const n={};n.name=e.name;if(!(0,Be.A)(t)){n.PATTERN=t}if((0,o.A)(e,Yn)){throw"The parent property is no longer supported.\n"+"See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details."}if((0,o.A)(e,qn)){n.CATEGORIES=e[qn]}Nn([n]);if((0,o.A)(e,Xn)){n.LABEL=e[Xn]}if((0,o.A)(e,Qn)){n.GROUP=e[Qn]}if((0,o.A)(e,Jn)){n.POP_MODE=e[Jn]}if((0,o.A)(e,Zn)){n.PUSH_MODE=e[Zn]}if((0,o.A)(e,er)){n.LONGER_ALT=e[er]}if((0,o.A)(e,tr)){n.LINE_BREAKS=e[tr]}if((0,o.A)(e,nr)){n.START_CHARS_HINT=e[nr]}return n}const sr=rr({name:"EOF",pattern:Vn.NA});Nn([sr]);function ar(e,t,n,r,i,s,a,o){return{image:t,startOffset:n,endOffset:r,startLine:i,endLine:s,startColumn:a,endColumn:o,tokenTypeIdx:e.tokenTypeIdx,tokenType:e}}function or(e,t){return wn(e,t)}const cr={buildMismatchTokenMessage({expected:e,actual:t,previous:n,ruleName:r}){const i=zn(e);const s=i?`--\x3e ${Wn(e)} <--`:`token of type --\x3e ${e.name} <--`;const a=`Expecting ${s} but found --\x3e '${t.image}' <--`;return a},buildNotAllInputParsedMessage({firstRedundant:e,ruleName:t}){return"Redundant input, expecting EOF but found: "+e.image},buildNoViableAltMessage({expectedPathsPerAlt:e,actual:t,previous:n,customUserDescription:r,ruleName:i}){const s="Expecting: ";const o=vt(t).image;const c="\nbut found: '"+o+"'";if(r){return s+r+c}else{const t=(0,tt.A)(e,((e,t)=>e.concat(t)),[]);const n=(0,a.A)(t,(e=>`[${(0,a.A)(e,(e=>Wn(e))).join(", ")}]`));const r=(0,a.A)(n,((e,t)=>`  ${t+1}. ${e}`));const i=`one of these possible Token sequences:\n${r.join("\n")}`;return s+i+c}},buildEarlyExitMessage({expectedIterationPaths:e,actual:t,customUserDescription:n,ruleName:r}){const i="Expecting: ";const s=vt(t).image;const o="\nbut found: '"+s+"'";if(n){return i+n+o}else{const t=(0,a.A)(e,(e=>`[${(0,a.A)(e,(e=>Wn(e))).join(",")}]`));const n=`expecting at least one iteration which starts with one of these possible Token sequences::\n  `+`<${t.join(" ,")}>`;return i+n+o}}};Object.freeze(cr);const ur={buildRuleNotFoundError(e,t){const n="Invalid grammar, reference to a rule which is not defined: ->"+t.nonTerminalName+"<-\n"+"inside top level rule: ->"+e.name+"<-";return n}};const lr={buildDuplicateFoundError(e,t){function n(e){if(e instanceof ee){return e.terminalType.name}else if(e instanceof W){return e.nonTerminalName}else{return""}}const r=e.name;const i=vt(t);const s=i.idx;const a=$e(i);const o=n(i);const c=s>0;let u=`->${a}${c?s:""}<- ${o?`with argument: ->${o}<-`:""}\n                  appears more than once (${t.length} times) in the top level rule: ->${r}<-.                  \n                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES \n                  `;u=u.replace(/[ \t]+/g," ");u=u.replace(/\s\s+/g,"\n");return u},buildNamespaceConflictError(e){const t=`Namespace conflict found in grammar.\n`+`The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${e.name}>.\n`+`To resolve this make sure each Terminal and Non-Terminal names are unique\n`+`This is easy to accomplish by using the convention that Terminal names start with an uppercase letter\n`+`and Non-Terminal names start with a lower case letter.`;return t},buildAlternationPrefixAmbiguityError(e){const t=(0,a.A)(e.prefixPath,(e=>Wn(e))).join(", ");const n=e.alternation.idx===0?"":e.alternation.idx;const r=`Ambiguous alternatives: <${e.ambiguityIndices.join(" ,")}> due to common lookahead prefix\n`+`in <OR${n}> inside <${e.topLevelRule.name}> Rule,\n`+`<${t}> may appears as a prefix path in all these alternatives.\n`+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX\n`+`For Further details.`;return r},buildAlternationAmbiguityError(e){const t=(0,a.A)(e.prefixPath,(e=>Wn(e))).join(", ");const n=e.alternation.idx===0?"":e.alternation.idx;let r=`Ambiguous Alternatives Detected: <${e.ambiguityIndices.join(" ,")}> in <OR${n}>`+` inside <${e.topLevelRule.name}> Rule,\n`+`<${t}> may appears as a prefix path in all these alternatives.\n`;r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\n`+`For Further details.`;return r},buildEmptyRepetitionError(e){let t=$e(e.repetition);if(e.repetition.idx!==0){t+=e.repetition.idx}const n=`The repetition <${t}> within Rule <${e.topLevelRule.name}> can never consume any tokens.\n`+`This could lead to an infinite loop.`;return n},buildTokenNameError(e){return"deprecated"},buildEmptyAlternationError(e){const t=`Ambiguous empty alternative: <${e.emptyChoiceIdx+1}>`+` in <OR${e.alternation.idx}> inside <${e.topLevelRule.name}> Rule.\n`+`Only the last alternative may be an empty alternative.`;return t},buildTooManyAlternativesError(e){const t=`An Alternation cannot have more than 256 alternatives:\n`+`<OR${e.alternation.idx}> inside <${e.topLevelRule.name}> Rule.\n has ${e.alternation.definition.length+1} alternatives.`;return t},buildLeftRecursionError(e){const t=e.topLevelRule.name;const n=(0,a.A)(e.leftRecursionPath,(e=>e.name));const r=`${t} --\x3e ${n.concat([t]).join(" --\x3e ")}`;const i=`Left Recursion found in grammar.\n`+`rule: <${t}> can be invoked from itself (directly or indirectly)\n`+`without consuming any Tokens. The grammar path that causes this is: \n ${r}\n`+` To fix this refactor your grammar to remove the left recursion.\n`+`see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`;return i},buildInvalidRuleNameError(e){return"deprecated"},buildDuplicateRuleNameError(e){let t;if(e.topLevelRule instanceof H){t=e.topLevelRule.name}else{t=e.topLevelRule}const n=`Duplicate definition, rule: ->${t}<- is already defined in the grammar: ->${e.grammarName}<-`;return n}};function dr(e,t){const n=new fr(e,t);n.resolveRefs();return n.errors}class fr extends re{constructor(e,t){super();this.nameToTopRule=e;this.errMsgProvider=t;this.errors=[]}resolveRefs(){(0,r.A)((0,i.A)(this.nameToTopRule),(e=>{this.currTopLevel=e;e.accept(this)}))}visitNonTerminal(e){const t=this.nameToTopRule[e.nonTerminalName];if(!t){const t=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:t,type:Ms.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}else{e.referencedRule=t}}}var hr=n(57852);var pr=n(48657);function mr(e,t,n,r){var i=-1,s=e==null?0:e.length;while(++i<s){var a=e[i];t(r,a,n(a),e)}return r}const gr=mr;function yr(e,t,n,r){(0,se.A)(e,(function(e,i,s){t(r,e,n(e),s)}));return r}const vr=yr;function Ar(e,t){return function(n,r){var i=(0,ce.A)(n)?gr:vr,s=t?t():{};return i(n,e,(0,I.A)(r,2),s)}}const Tr=Ar;var Rr=Object.prototype;var Er=Rr.hasOwnProperty;var kr=Tr((function(e,t,n){if(Er.call(e,n)){e[n].push(t)}else{(0,pr.A)(e,n,[t])}}));const xr=kr;function $r(e,t,n){var r=e==null?0:e.length;if(!r){return[]}t=n||t===undefined?1:(0,f.A)(t);t=r-t;return d(e,0,t<0?0:t)}const wr=$r;class Ir extends we{constructor(e,t){super();this.topProd=e;this.path=t;this.possibleTokTypes=[];this.nextProductionName="";this.nextProductionOccurrence=0;this.found=false;this.isAtEndOfPath=false}startWalking(){this.found=false;if(this.path.ruleStack[0]!==this.topProd.name){throw Error("The path does not start with the walker's top Rule!")}this.ruleStack=(0,c.A)(this.path.ruleStack).reverse();this.occurrenceStack=(0,c.A)(this.path.occurrenceStack).reverse();this.ruleStack.pop();this.occurrenceStack.pop();this.updateExpectedNext();this.walk(this.topProd);return this.possibleTokTypes}walk(e,t=[]){if(!this.found){super.walk(e,t)}}walkProdRef(e,t,n){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){const r=t.concat(n);this.updateExpectedNext();this.walk(e.referencedRule,r)}}updateExpectedNext(){if((0,s.A)(this.ruleStack)){this.nextProductionName="";this.nextProductionOccurrence=0;this.isAtEndOfPath=true}else{this.nextProductionName=this.ruleStack.pop();this.nextProductionOccurrence=this.occurrenceStack.pop()}}}class Sr extends Ir{constructor(e,t){super(e,t);this.path=t;this.nextTerminalName="";this.nextTerminalOccurrence=0;this.nextTerminalName=this.path.lastTok.name;this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,n){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){const e=t.concat(n);const r=new z({definition:e});this.possibleTokTypes=be(r);this.found=true}}}class Cr extends we{constructor(e,t){super();this.topRule=e;this.occurrence=t;this.result={token:undefined,occurrence:undefined,isEndOfRule:undefined}}startWalking(){this.walk(this.topRule);return this.result}}class Nr extends Cr{walkMany(e,t,n){if(e.idx===this.occurrence){const e=vt(t.concat(n));this.result.isEndOfRule=e===undefined;if(e instanceof ee){this.result.token=e.terminalType;this.result.occurrence=e.idx}}else{super.walkMany(e,t,n)}}}class Lr extends Cr{walkManySep(e,t,n){if(e.idx===this.occurrence){const e=vt(t.concat(n));this.result.isEndOfRule=e===undefined;if(e instanceof ee){this.result.token=e.terminalType;this.result.occurrence=e.idx}}else{super.walkManySep(e,t,n)}}}class br extends Cr{walkAtLeastOne(e,t,n){if(e.idx===this.occurrence){const e=vt(t.concat(n));this.result.isEndOfRule=e===undefined;if(e instanceof ee){this.result.token=e.terminalType;this.result.occurrence=e.idx}}else{super.walkAtLeastOne(e,t,n)}}}class _r extends Cr{walkAtLeastOneSep(e,t,n){if(e.idx===this.occurrence){const e=vt(t.concat(n));this.result.isEndOfRule=e===undefined;if(e instanceof ee){this.result.token=e.terminalType;this.result.occurrence=e.idx}}else{super.walkAtLeastOneSep(e,t,n)}}}function Or(e,t,n=[]){n=(0,c.A)(n);let i=[];let a=0;function o(t){return t.concat(p(e,a+1))}function u(e){const r=Or(o(e),t,n);return i.concat(r)}while(n.length<t&&a<e.length){const t=e[a];if(t instanceof z){return u(t.definition)}else if(t instanceof W){return u(t.definition)}else if(t instanceof Y){i=u(t.definition)}else if(t instanceof q){const e=t.definition.concat([new Q({definition:t.definition})]);return u(e)}else if(t instanceof X){const e=[new z({definition:t.definition}),new Q({definition:[new ee({terminalType:t.separator})].concat(t.definition)})];return u(e)}else if(t instanceof Z){const e=t.definition.concat([new Q({definition:[new ee({terminalType:t.separator})].concat(t.definition)})]);i=u(e)}else if(t instanceof Q){const e=t.definition.concat([new Q({definition:t.definition})]);i=u(e)}else if(t instanceof J){(0,r.A)(t.definition,(e=>{if((0,s.A)(e.definition)===false){i=u(e.definition)}}));return i}else if(t instanceof ee){n.push(t.terminalType)}else{throw Error("non exhaustive match")}a++}i.push({partialPath:n,suffixDef:p(e,a)});return i}function Pr(e,t,n,r){const i="EXIT_NONE_TERMINAL";const a=[i];const o="EXIT_ALTERNATIVE";let u=false;const l=t.length;const d=l-r-1;const f=[];const h=[];h.push({idx:-1,def:e,ruleStack:[],occurrenceStack:[]});while(!(0,s.A)(h)){const e=h.pop();if(e===o){if(u&&(0,xn.A)(h).idx<=d){h.pop()}continue}const r=e.def;const m=e.idx;const g=e.ruleStack;const y=e.occurrenceStack;if((0,s.A)(r)){continue}const v=r[0];if(v===i){const e={idx:m,def:p(r),ruleStack:wr(g),occurrenceStack:wr(y)};h.push(e)}else if(v instanceof ee){if(m<l-1){const e=m+1;const i=t[e];if(n(i,v.terminalType)){const t={idx:e,def:p(r),ruleStack:g,occurrenceStack:y};h.push(t)}}else if(m===l-1){f.push({nextTokenType:v.terminalType,nextTokenOccurrence:v.idx,ruleStack:g,occurrenceStack:y});u=true}else{throw Error("non exhaustive match")}}else if(v instanceof W){const e=(0,c.A)(g);e.push(v.nonTerminalName);const t=(0,c.A)(y);t.push(v.idx);const n={idx:m,def:v.definition.concat(a,p(r)),ruleStack:e,occurrenceStack:t};h.push(n)}else if(v instanceof Y){const e={idx:m,def:p(r),ruleStack:g,occurrenceStack:y};h.push(e);h.push(o);const t={idx:m,def:v.definition.concat(p(r)),ruleStack:g,occurrenceStack:y};h.push(t)}else if(v instanceof q){const e=new Q({definition:v.definition,idx:v.idx});const t=v.definition.concat([e],p(r));const n={idx:m,def:t,ruleStack:g,occurrenceStack:y};h.push(n)}else if(v instanceof X){const e=new ee({terminalType:v.separator});const t=new Q({definition:[e].concat(v.definition),idx:v.idx});const n=v.definition.concat([t],p(r));const i={idx:m,def:n,ruleStack:g,occurrenceStack:y};h.push(i)}else if(v instanceof Z){const e={idx:m,def:p(r),ruleStack:g,occurrenceStack:y};h.push(e);h.push(o);const t=new ee({terminalType:v.separator});const n=new Q({definition:[t].concat(v.definition),idx:v.idx});const i=v.definition.concat([n],p(r));const s={idx:m,def:i,ruleStack:g,occurrenceStack:y};h.push(s)}else if(v instanceof Q){const e={idx:m,def:p(r),ruleStack:g,occurrenceStack:y};h.push(e);h.push(o);const t=new Q({definition:v.definition,idx:v.idx});const n=v.definition.concat([t],p(r));const i={idx:m,def:n,ruleStack:g,occurrenceStack:y};h.push(i)}else if(v instanceof J){for(let e=v.definition.length-1;e>=0;e--){const t=v.definition[e];const n={idx:m,def:t.definition.concat(p(r)),ruleStack:g,occurrenceStack:y};h.push(n);h.push(o)}}else if(v instanceof z){h.push({idx:m,def:v.definition.concat(p(r)),ruleStack:g,occurrenceStack:y})}else if(v instanceof H){h.push(Mr(v,m,g,y))}else{throw Error("non exhaustive match")}}return f}function Mr(e,t,n,r){const i=(0,c.A)(n);i.push(e.name);const s=(0,c.A)(r);s.push(1);return{idx:t,def:e.definition,ruleStack:i,occurrenceStack:s}}var Dr;(function(e){e[e["OPTION"]=0]="OPTION";e[e["REPETITION"]=1]="REPETITION";e[e["REPETITION_MANDATORY"]=2]="REPETITION_MANDATORY";e[e["REPETITION_MANDATORY_WITH_SEPARATOR"]=3]="REPETITION_MANDATORY_WITH_SEPARATOR";e[e["REPETITION_WITH_SEPARATOR"]=4]="REPETITION_WITH_SEPARATOR";e[e["ALTERNATION"]=5]="ALTERNATION"})(Dr||(Dr={}));function Ur(e){if(e instanceof Y||e==="Option"){return Dr.OPTION}else if(e instanceof Q||e==="Repetition"){return Dr.REPETITION}else if(e instanceof q||e==="RepetitionMandatory"){return Dr.REPETITION_MANDATORY}else if(e instanceof X||e==="RepetitionMandatoryWithSeparator"){return Dr.REPETITION_MANDATORY_WITH_SEPARATOR}else if(e instanceof Z||e==="RepetitionWithSeparator"){return Dr.REPETITION_WITH_SEPARATOR}else if(e instanceof J||e==="Alternation"){return Dr.ALTERNATION}else{throw Error("non exhaustive match")}}function Fr(e){const{occurrence:t,rule:n,prodType:r,maxLookahead:i}=e;const s=Ur(r);if(s===Dr.ALTERNATION){return Xr(t,n,i)}else{return Qr(t,n,s,i)}}function Gr(e,t,n,r,i,s){const a=Xr(e,t,n);const o=ei(a)?In:wn;return s(a,r,o,i)}function Br(e,t,n,r,i,s){const a=Qr(e,t,i,n);const o=ei(a)?In:wn;return s(a[0],o,r)}function Kr(e,t,n,i){const s=e.length;const c=Re(e,(e=>Re(e,(e=>e.length===1))));if(t){return function(t){const r=(0,a.A)(t,(e=>e.GATE));for(let i=0;i<s;i++){const t=e[i];const s=t.length;const a=r[i];if(a!==undefined&&a.call(this)===false){continue}e:for(let e=0;e<s;e++){const r=t[e];const s=r.length;for(let e=0;e<s;e++){const t=this.LA(e+1);if(n(t,r[e])===false){continue e}}return i}}return undefined}}else if(c&&!i){const t=(0,a.A)(e,(e=>(0,Le.A)(e)));const n=(0,tt.A)(t,((e,t,n)=>{(0,r.A)(t,(t=>{if(!(0,o.A)(e,t.tokenTypeIdx)){e[t.tokenTypeIdx]=n}(0,r.A)(t.categoryMatches,(t=>{if(!(0,o.A)(e,t)){e[t]=n}}))}));return e}),{});return function(){const e=this.LA(1);return n[e.tokenTypeIdx]}}else{return function(){for(let t=0;t<s;t++){const r=e[t];const i=r.length;e:for(let e=0;e<i;e++){const i=r[e];const s=i.length;for(let e=0;e<s;e++){const t=this.LA(e+1);if(n(t,i[e])===false){continue e}}return t}}return undefined}}}function jr(e,t,n){const i=Re(e,(e=>e.length===1));const a=e.length;if(i&&!n){const t=(0,Le.A)(e);if(t.length===1&&(0,s.A)(t[0].categoryMatches)){const e=t[0];const n=e.tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===n}}else{const e=(0,tt.A)(t,((e,t,n)=>{e[t.tokenTypeIdx]=true;(0,r.A)(t.categoryMatches,(t=>{e[t]=true}));return e}),[]);return function(){const t=this.LA(1);return e[t.tokenTypeIdx]===true}}}else{return function(){e:for(let n=0;n<a;n++){const r=e[n];const i=r.length;for(let e=0;e<i;e++){const n=this.LA(e+1);if(t(n,r[e])===false){continue e}}return true}return false}}}class Vr extends we{constructor(e,t,n){super();this.topProd=e;this.targetOccurrence=t;this.targetProdType=n}startWalking(){this.walk(this.topProd);return this.restDef}checkIsTarget(e,t,n,r){if(e.idx===this.targetOccurrence&&this.targetProdType===t){this.restDef=n.concat(r);return true}return false}walkOption(e,t,n){if(!this.checkIsTarget(e,Dr.OPTION,t,n)){super.walkOption(e,t,n)}}walkAtLeastOne(e,t,n){if(!this.checkIsTarget(e,Dr.REPETITION_MANDATORY,t,n)){super.walkOption(e,t,n)}}walkAtLeastOneSep(e,t,n){if(!this.checkIsTarget(e,Dr.REPETITION_MANDATORY_WITH_SEPARATOR,t,n)){super.walkOption(e,t,n)}}walkMany(e,t,n){if(!this.checkIsTarget(e,Dr.REPETITION,t,n)){super.walkOption(e,t,n)}}walkManySep(e,t,n){if(!this.checkIsTarget(e,Dr.REPETITION_WITH_SEPARATOR,t,n)){super.walkOption(e,t,n)}}}class Wr extends re{constructor(e,t,n){super();this.targetOccurrence=e;this.targetProdType=t;this.targetRef=n;this.result=[]}checkIsTarget(e,t){if(e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.targetRef===undefined||e===this.targetRef)){this.result=e.definition}}visitOption(e){this.checkIsTarget(e,Dr.OPTION)}visitRepetition(e){this.checkIsTarget(e,Dr.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,Dr.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,Dr.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,Dr.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,Dr.ALTERNATION)}}function Hr(e){const t=new Array(e);for(let n=0;n<e;n++){t[n]=[]}return t}function zr(e){let t=[""];for(let n=0;n<e.length;n++){const r=e[n];const i=[];for(let e=0;e<t.length;e++){const n=t[e];i.push(n+"_"+r.tokenTypeIdx);for(let e=0;e<r.categoryMatches.length;e++){const t="_"+r.categoryMatches[e];i.push(n+t)}}t=i}return t}function Yr(e,t,n){for(let r=0;r<e.length;r++){if(r===n){continue}const i=e[r];for(let e=0;e<t.length;e++){const n=t[e];if(i[n]===true){return false}}}return true}function qr(e,t){const n=(0,a.A)(e,(e=>Or([e],1)));const i=Hr(n.length);const o=(0,a.A)(n,(e=>{const t={};(0,r.A)(e,(e=>{const n=zr(e.partialPath);(0,r.A)(n,(e=>{t[e]=true}))}));return t}));let c=n;for(let a=1;a<=t;a++){const e=c;c=Hr(e.length);for(let n=0;n<e.length;n++){const u=e[n];for(let e=0;e<u.length;e++){const l=u[e].partialPath;const d=u[e].suffixDef;const f=zr(l);const h=Yr(o,f,n);if(h||(0,s.A)(d)||l.length===t){const e=i[n];if(Zr(e,l)===false){e.push(l);for(let e=0;e<f.length;e++){const t=f[e];o[n][t]=true}}}else{const e=Or(d,a+1,l);c[n]=c[n].concat(e);(0,r.A)(e,(e=>{const t=zr(e.partialPath);(0,r.A)(t,(e=>{o[n][e]=true}))}))}}}}return i}function Xr(e,t,n,r){const i=new Wr(e,Dr.ALTERNATION,r);t.accept(i);return qr(i.result,n)}function Qr(e,t,n,r){const i=new Wr(e,n);t.accept(i);const s=i.result;const a=new Vr(t,e,n);const o=a.startWalking();const c=new z({definition:s});const u=new z({definition:o});return qr([c,u],r)}function Zr(e,t){e:for(let n=0;n<e.length;n++){const r=e[n];if(r.length!==t.length){continue}for(let e=0;e<r.length;e++){const n=t[e];const i=r[e];const s=n===i||i.categoryMatchesMap[n.tokenTypeIdx]!==undefined;if(s===false){continue e}}return true}return false}function Jr(e,t){return e.length<t.length&&Re(e,((e,n)=>{const r=t[n];return e===r||r.categoryMatchesMap[e.tokenTypeIdx]}))}function ei(e){return Re(e,(e=>Re(e,(e=>Re(e,(e=>(0,s.A)(e.categoryMatches)))))))}function ti(e){const t=e.lookaheadStrategy.validate({rules:e.rules,tokenTypes:e.tokenTypes,grammarName:e.grammarName});return(0,a.A)(t,(e=>Object.assign({type:Ms.CUSTOM_LOOKAHEAD_VALIDATION},e)))}function ni(e,t,n,r){const i=(0,hr.A)(e,(e=>ri(e,n)));const s=Ai(e,t,n);const a=(0,hr.A)(e,(e=>mi(e,n)));const o=(0,hr.A)(e,(t=>oi(t,e,r,n)));return i.concat(s,a,o)}function ri(e,t){const n=new ai;e.accept(n);const r=n.allProductions;const s=xr(r,ii);const o=L(s,(e=>e.length>1));const c=(0,a.A)((0,i.A)(o),(n=>{const r=vt(n);const i=t.buildDuplicateFoundError(e,n);const s=$e(r);const a={message:i,type:Ms.DUPLICATE_PRODUCTIONS,ruleName:e.name,dslName:s,occurrence:r.idx};const o=si(r);if(o){a.parameter=o}return a}));return c}function ii(e){return`${$e(e)}_#_${e.idx}_#_${si(e)}`}function si(e){if(e instanceof ee){return e.terminalType.name}else if(e instanceof W){return e.nonTerminalName}else{return""}}class ai extends re{constructor(){super(...arguments);this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}}function oi(e,t,n,r){const i=[];const s=(0,tt.A)(t,((t,n)=>{if(n.name===e.name){return t+1}return t}),0);if(s>1){const t=r.buildDuplicateRuleNameError({topLevelRule:e,grammarName:n});i.push({message:t,type:Ms.DUPLICATE_RULE_NAME,ruleName:e.name})}return i}function ci(e,t,n){const r=[];let i;if(!me(t,e)){i=`Invalid rule override, rule: ->${e}<- cannot be overridden in the grammar: ->${n}<-`+`as it is not defined in any of the super grammars `;r.push({message:i,type:Ms.INVALID_RULE_OVERRIDE,ruleName:e})}return r}function ui(e,t,n,r=[]){const i=[];const a=li(t.definition);if((0,s.A)(a)){return[]}else{const t=e.name;const s=me(a,e);if(s){i.push({message:n.buildLeftRecursionError({topLevelRule:e,leftRecursionPath:r}),type:Ms.LEFT_RECURSION,ruleName:t})}const o=pt(a,r.concat([e]));const u=(0,hr.A)(o,(t=>{const i=(0,c.A)(r);i.push(t);return ui(e,t,n,i)}));return i.concat(u)}}function li(e){let t=[];if((0,s.A)(e)){return t}const n=vt(e);if(n instanceof W){t.push(n.referencedRule)}else if(n instanceof z||n instanceof Y||n instanceof q||n instanceof X||n instanceof Z||n instanceof Q){t=t.concat(li(n.definition))}else if(n instanceof J){t=(0,Le.A)((0,a.A)(n.definition,(e=>li(e.definition))))}else if(n instanceof ee){}else{throw Error("non exhaustive match")}const r=ke(n);const i=e.length>1;if(r&&i){const n=p(e);return t.concat(li(n))}else{return t}}class di extends re{constructor(){super(...arguments);this.alternations=[]}visitAlternation(e){this.alternations.push(e)}}function fi(e,t){const n=new di;e.accept(n);const r=n.alternations;const i=(0,hr.A)(r,(n=>{const r=wr(n.definition);return(0,hr.A)(r,((r,i)=>{const a=Pr([r],[],wn,1);if((0,s.A)(a)){return[{message:t.buildEmptyAlternationError({topLevelRule:e,alternation:n,emptyChoiceIdx:i}),type:Ms.NONE_LAST_EMPTY_ALT,ruleName:e.name,occurrence:n.idx,alternative:i+1}]}else{return[]}}))}));return i}function hi(e,t,n){const r=new di;e.accept(r);let i=r.alternations;i=Xe(i,(e=>e.ignoreAmbiguities===true));const s=(0,hr.A)(i,(r=>{const i=r.idx;const s=r.maxLookahead||t;const a=Xr(i,e,s,r);const o=yi(a,r,e,n);const c=vi(a,r,e,n);return o.concat(c)}));return s}class pi extends re{constructor(){super(...arguments);this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}}function mi(e,t){const n=new di;e.accept(n);const r=n.alternations;const i=(0,hr.A)(r,(n=>{if(n.definition.length>255){return[{message:t.buildTooManyAlternativesError({topLevelRule:e,alternation:n}),type:Ms.TOO_MANY_ALTS,ruleName:e.name,occurrence:n.idx}]}else{return[]}}));return i}function gi(e,t,n){const i=[];(0,r.A)(e,(e=>{const a=new pi;e.accept(a);const o=a.allProductions;(0,r.A)(o,(r=>{const a=Ur(r);const o=r.maxLookahead||t;const c=r.idx;const u=Qr(c,e,a,o);const l=u[0];if((0,s.A)((0,Le.A)(l))){const t=n.buildEmptyRepetitionError({topLevelRule:e,repetition:r});i.push({message:t,type:Ms.NO_NON_EMPTY_LOOKAHEAD,ruleName:e.name})}}))}));return i}function yi(e,t,n,i){const s=[];const o=(0,tt.A)(e,((n,i,a)=>{if(t.definition[a].ignoreAmbiguities===true){return n}(0,r.A)(i,(i=>{const o=[a];(0,r.A)(e,((e,n)=>{if(a!==n&&Zr(e,i)&&t.definition[n].ignoreAmbiguities!==true){o.push(n)}}));if(o.length>1&&!Zr(s,i)){s.push(i);n.push({alts:o,path:i})}}));return n}),[]);const c=(0,a.A)(o,(e=>{const r=(0,a.A)(e.alts,(e=>e+1));const s=i.buildAlternationAmbiguityError({topLevelRule:n,alternation:t,ambiguityIndices:r,prefixPath:e.path});return{message:s,type:Ms.AMBIGUOUS_ALTS,ruleName:n.name,occurrence:t.idx,alternatives:e.alts}}));return c}function vi(e,t,n,r){const i=(0,tt.A)(e,((e,t,n)=>{const r=(0,a.A)(t,(e=>({idx:n,path:e})));return e.concat(r)}),[]);const s=gt((0,hr.A)(i,(e=>{const s=t.definition[e.idx];if(s.ignoreAmbiguities===true){return[]}const o=e.idx;const c=e.path;const u=(0,nt.A)(i,(e=>t.definition[e.idx].ignoreAmbiguities!==true&&e.idx<o&&Jr(e.path,c)));const l=(0,a.A)(u,(e=>{const i=[e.idx+1,o+1];const s=t.idx===0?"":t.idx;const a=r.buildAlternationPrefixAmbiguityError({topLevelRule:n,alternation:t,ambiguityIndices:i,prefixPath:e.path});return{message:a,type:Ms.AMBIGUOUS_PREFIX_ALTS,ruleName:n.name,occurrence:s,alternatives:i}}));return l})));return s}function Ai(e,t,n){const i=[];const s=(0,a.A)(t,(e=>e.name));(0,r.A)(e,(e=>{const t=e.name;if(me(s,t)){const r=n.buildNamespaceConflictError(e);i.push({message:r,type:Ms.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:t})}}));return i}function Ti(e){const t=(0,je.A)(e,{errMsgProvider:ur});const n={};(0,r.A)(e.rules,(e=>{n[e.name]=e}));return dr(n,t.errMsgProvider)}function Ri(e){e=(0,je.A)(e,{errMsgProvider:lr});return ni(e.rules,e.tokenTypes,e.errMsgProvider,e.grammarName)}const Ei="MismatchedTokenException";const ki="NoViableAltException";const xi="EarlyExitException";const $i="NotAllInputParsedException";const wi=[Ei,ki,xi,$i];Object.freeze(wi);function Ii(e){return me(wi,e.name)}class Si extends Error{constructor(e,t){super(e);this.token=t;this.resyncedTokens=[];Object.setPrototypeOf(this,new.target.prototype);if(Error.captureStackTrace){Error.captureStackTrace(this,this.constructor)}}}class Ci extends Si{constructor(e,t,n){super(e,t);this.previousToken=n;this.name=Ei}}class Ni extends Si{constructor(e,t,n){super(e,t);this.previousToken=n;this.name=ki}}class Li extends Si{constructor(e,t){super(e,t);this.name=$i}}class bi extends Si{constructor(e,t,n){super(e,t);this.previousToken=n;this.name=xi}}const _i={};const Oi="InRuleRecoveryException";class Pi extends Error{constructor(e){super(e);this.name=Oi}}class Mi{initRecoverable(e){this.firstAfterRepMap={};this.resyncFollows={};this.recoveryEnabled=(0,o.A)(e,"recoveryEnabled")?e.recoveryEnabled:Os.recoveryEnabled;if(this.recoveryEnabled){this.attemptInRepetitionRecovery=Di}}getTokenToInsert(e){const t=ar(e,"",NaN,NaN,NaN,NaN,NaN,NaN);t.isInsertedInRecovery=true;return t}canTokenTypeBeInsertedInRecovery(e){return true}canTokenTypeBeDeletedInRecovery(e){return true}tryInRepetitionRecovery(e,t,n,r){const i=this.findReSyncTokenType();const s=this.exportLexerState();const a=[];let o=false;const c=this.LA(1);let u=this.LA(1);const l=()=>{const e=this.LA(0);const t=this.errorMessageProvider.buildMismatchTokenMessage({expected:r,actual:c,previous:e,ruleName:this.getCurrRuleFullName()});const n=new Ci(t,c,this.LA(0));n.resyncedTokens=wr(a);this.SAVE_ERROR(n)};while(!o){if(this.tokenMatcher(u,r)){l();return}else if(n.call(this)){l();e.apply(this,t);return}else if(this.tokenMatcher(u,i)){o=true}else{u=this.SKIP_TOKEN();this.addToResyncTokens(u,a)}}this.importLexerState(s)}shouldInRepetitionRecoveryBeTried(e,t,n){if(n===false){return false}if(this.tokenMatcher(this.LA(1),e)){return false}if(this.isBackTracking()){return false}if(this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,t))){return false}return true}getFollowsForInRuleRecovery(e,t){const n=this.getCurrentGrammarPath(e,t);const r=this.getNextPossibleTokenTypes(n);return r}tryInRuleRecovery(e,t){if(this.canRecoverWithSingleTokenInsertion(e,t)){const t=this.getTokenToInsert(e);return t}if(this.canRecoverWithSingleTokenDeletion(e)){const e=this.SKIP_TOKEN();this.consumeToken();return e}throw new Pi("sad sad panda")}canPerformInRuleRecovery(e,t){return this.canRecoverWithSingleTokenInsertion(e,t)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,t){if(!this.canTokenTypeBeInsertedInRecovery(e)){return false}if((0,s.A)(t)){return false}const n=this.LA(1);const r=(0,At.A)(t,(e=>this.tokenMatcher(n,e)))!==undefined;return r}canRecoverWithSingleTokenDeletion(e){if(!this.canTokenTypeBeDeletedInRecovery(e)){return false}const t=this.tokenMatcher(this.LA(2),e);return t}isInCurrentRuleReSyncSet(e){const t=this.getCurrFollowKey();const n=this.getFollowSetFromFollowKey(t);return me(n,e)}findReSyncTokenType(){const e=this.flattenFollowSet();let t=this.LA(1);let n=2;while(true){const r=(0,At.A)(e,(e=>{const n=or(t,e);return n}));if(r!==undefined){return r}t=this.LA(n);n++}}getCurrFollowKey(){if(this.RULE_STACK.length===1){return _i}const e=this.getLastExplicitRuleShortName();const t=this.getLastExplicitRuleOccurrenceIndex();const n=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:t,inRule:this.shortRuleNameToFullName(n)}}buildFullFollowKeyStack(){const e=this.RULE_STACK;const t=this.RULE_OCCURRENCE_STACK;return(0,a.A)(e,((n,r)=>{if(r===0){return _i}return{ruleName:this.shortRuleNameToFullName(n),idxInCallingRule:t[r],inRule:this.shortRuleNameToFullName(e[r-1])}}))}flattenFollowSet(){const e=(0,a.A)(this.buildFullFollowKeyStack(),(e=>this.getFollowSetFromFollowKey(e)));return(0,Le.A)(e)}getFollowSetFromFollowKey(e){if(e===_i){return[sr]}const t=e.ruleName+e.idxInCallingRule+Me+e.inRule;return this.resyncFollows[t]}addToResyncTokens(e,t){if(!this.tokenMatcher(e,sr)){t.push(e)}return t}reSyncTo(e){const t=[];let n=this.LA(1);while(this.tokenMatcher(n,e)===false){n=this.SKIP_TOKEN();this.addToResyncTokens(n,t)}return wr(t)}attemptInRepetitionRecovery(e,t,n,r,i,s,a){}getCurrentGrammarPath(e,t){const n=this.getHumanReadableRuleStack();const r=(0,c.A)(this.RULE_OCCURRENCE_STACK);const i={ruleStack:n,occurrenceStack:r,lastTok:e,lastTokOccurrence:t};return i}getHumanReadableRuleStack(){return(0,a.A)(this.RULE_STACK,(e=>this.shortRuleNameToFullName(e)))}}function Di(e,t,n,r,i,s,a){const o=this.getKeyForAutomaticLookahead(r,i);let c=this.firstAfterRepMap[o];if(c===undefined){const e=this.getCurrRuleFullName();const t=this.getGAstProductions()[e];const n=new s(t,i);c=n.startWalking();this.firstAfterRepMap[o]=c}let u=c.token;let l=c.occurrence;const d=c.isEndOfRule;if(this.RULE_STACK.length===1&&d&&u===undefined){u=sr;l=1}if(u===undefined||l===undefined){return}if(this.shouldInRepetitionRecoveryBeTried(u,l,a)){this.tryInRepetitionRecovery(e,t,n,u)}}const Ui=4;const Fi=8;const Gi=12;const Bi=8;const Ki=1<<Fi;const ji=2<<Fi;const Vi=3<<Fi;const Wi=4<<Fi;const Hi=5<<Fi;const zi=6<<Fi;function Yi(e,t,n){return n|t|e}const qi=32-Bi;class Xi{constructor(e){var t;this.maxLookahead=(t=e===null||e===void 0?void 0:e.maxLookahead)!==null&&t!==void 0?t:Os.maxLookahead}validate(e){const t=this.validateNoLeftRecursion(e.rules);if((0,s.A)(t)){const n=this.validateEmptyOrAlternatives(e.rules);const r=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead);const i=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);const s=[...t,...n,...r,...i];return s}return t}validateNoLeftRecursion(e){return(0,hr.A)(e,(e=>ui(e,e,lr)))}validateEmptyOrAlternatives(e){return(0,hr.A)(e,(e=>fi(e,lr)))}validateAmbiguousAlternationAlternatives(e,t){return(0,hr.A)(e,(e=>hi(e,t,lr)))}validateSomeNonEmptyLookaheadPath(e,t){return gi(e,t,lr)}buildLookaheadForAlternation(e){return Gr(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,Kr)}buildLookaheadForOptional(e){return Br(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,Ur(e.prodType),jr)}}class Qi{initLooksAhead(e){this.dynamicTokensEnabled=(0,o.A)(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:Os.dynamicTokensEnabled;this.maxLookahead=(0,o.A)(e,"maxLookahead")?e.maxLookahead:Os.maxLookahead;this.lookaheadStrategy=(0,o.A)(e,"lookaheadStrategy")?e.lookaheadStrategy:new Xi({maxLookahead:this.maxLookahead});this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){(0,r.A)(e,(e=>{this.TRACE_INIT(`${e.name} Rule Lookahead`,(()=>{const{alternation:t,repetition:n,option:i,repetitionMandatory:s,repetitionMandatoryWithSeparator:a,repetitionWithSeparator:o}=es(e);(0,r.A)(t,(t=>{const n=t.idx===0?"":t.idx;this.TRACE_INIT(`${$e(t)}${n}`,(()=>{const n=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:t.idx,rule:e,maxLookahead:t.maxLookahead||this.maxLookahead,hasPredicates:t.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled});const r=Yi(this.fullRuleNameToShort[e.name],Ki,t.idx);this.setLaFuncCache(r,n)}))}));(0,r.A)(n,(t=>{this.computeLookaheadFunc(e,t.idx,Vi,"Repetition",t.maxLookahead,$e(t))}));(0,r.A)(i,(t=>{this.computeLookaheadFunc(e,t.idx,ji,"Option",t.maxLookahead,$e(t))}));(0,r.A)(s,(t=>{this.computeLookaheadFunc(e,t.idx,Wi,"RepetitionMandatory",t.maxLookahead,$e(t))}));(0,r.A)(a,(t=>{this.computeLookaheadFunc(e,t.idx,zi,"RepetitionMandatoryWithSeparator",t.maxLookahead,$e(t))}));(0,r.A)(o,(t=>{this.computeLookaheadFunc(e,t.idx,Hi,"RepetitionWithSeparator",t.maxLookahead,$e(t))}))}))}))}computeLookaheadFunc(e,t,n,r,i,s){this.TRACE_INIT(`${s}${t===0?"":t}`,(()=>{const s=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:t,rule:e,maxLookahead:i||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:r});const a=Yi(this.fullRuleNameToShort[e.name],n,t);this.setLaFuncCache(a,s)}))}getKeyForAutomaticLookahead(e,t){const n=this.getLastExplicitRuleShortName();return Yi(n,e,t)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,t){this.lookAheadFuncsCache.set(e,t)}}class Zi extends re{constructor(){super(...arguments);this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}}const Ji=new Zi;function es(e){Ji.reset();e.accept(Ji);const t=Ji.dslMethods;Ji.reset();return t}function ts(e,t){if(isNaN(e.startOffset)===true){e.startOffset=t.startOffset;e.endOffset=t.endOffset}else if(e.endOffset<t.endOffset===true){e.endOffset=t.endOffset}}function ns(e,t){if(isNaN(e.startOffset)===true){e.startOffset=t.startOffset;e.startColumn=t.startColumn;e.startLine=t.startLine;e.endOffset=t.endOffset;e.endColumn=t.endColumn;e.endLine=t.endLine}else if(e.endOffset<t.endOffset===true){e.endOffset=t.endOffset;e.endColumn=t.endColumn;e.endLine=t.endLine}}function rs(e,t,n){if(e.children[n]===undefined){e.children[n]=[t]}else{e.children[n].push(t)}}function is(e,t,n){if(e.children[t]===undefined){e.children[t]=[n]}else{e.children[t].push(n)}}const ss="name";function as(e,t){Object.defineProperty(e,ss,{enumerable:false,configurable:true,writable:false,value:t})}function os(e,t){const n=(0,R.A)(e);const r=n.length;for(let i=0;i<r;i++){const r=n[i];const s=e[r];const a=s.length;for(let e=0;e<a;e++){const n=s[e];if(n.tokenTypeIdx===undefined){this[n.name](n.children,t)}}}}function cs(e,t){const n=function(){};as(n,e+"BaseSemantics");const r={visit:function(e,t){if((0,ce.A)(e)){e=e[0]}if((0,Be.A)(e)){return undefined}return this[e.name](e.children,t)},validateVisitor:function(){const e=ds(this,t);if(!(0,s.A)(e)){const t=(0,a.A)(e,(e=>e.msg));throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:\n\t`+`${t.join("\n\n").replace(/\n/g,"\n\t")}`)}}};n.prototype=r;n.prototype.constructor=n;n._RULE_NAMES=t;return n}function us(e,t,n){const i=function(){};as(i,e+"BaseSemanticsWithDefaults");const s=Object.create(n.prototype);(0,r.A)(t,(e=>{s[e]=os}));i.prototype=s;i.prototype.constructor=i;return i}var ls;(function(e){e[e["REDUNDANT_METHOD"]=0]="REDUNDANT_METHOD";e[e["MISSING_METHOD"]=1]="MISSING_METHOD"})(ls||(ls={}));function ds(e,t){const n=fs(e,t);return n}function fs(e,t){const n=(0,nt.A)(t,(t=>(0,Qe.A)(e[t])===false));const r=(0,a.A)(n,(t=>({msg:`Missing visitor method: <${t}> on ${e.constructor.name} CST Visitor.`,type:ls.MISSING_METHOD,methodName:t})));return gt(r)}class hs{initTreeBuilder(e){this.CST_STACK=[];this.outputCst=e.outputCst;this.nodeLocationTracking=(0,o.A)(e,"nodeLocationTracking")?e.nodeLocationTracking:Os.nodeLocationTracking;if(!this.outputCst){this.cstInvocationStateUpdate=kn.A;this.cstFinallyStateUpdate=kn.A;this.cstPostTerminal=kn.A;this.cstPostNonTerminal=kn.A;this.cstPostRule=kn.A}else{if(/full/i.test(this.nodeLocationTracking)){if(this.recoveryEnabled){this.setNodeLocationFromToken=ns;this.setNodeLocationFromNode=ns;this.cstPostRule=kn.A;this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery}else{this.setNodeLocationFromToken=kn.A;this.setNodeLocationFromNode=kn.A;this.cstPostRule=this.cstPostRuleFull;this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular}}else if(/onlyOffset/i.test(this.nodeLocationTracking)){if(this.recoveryEnabled){this.setNodeLocationFromToken=ts;this.setNodeLocationFromNode=ts;this.cstPostRule=kn.A;this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery}else{this.setNodeLocationFromToken=kn.A;this.setNodeLocationFromNode=kn.A;this.cstPostRule=this.cstPostRuleOnlyOffset;this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular}}else if(/none/i.test(this.nodeLocationTracking)){this.setNodeLocationFromToken=kn.A;this.setNodeLocationFromNode=kn.A;this.cstPostRule=kn.A;this.setInitialNodeLocation=kn.A}else{throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}}}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){const t=this.LA(1);e.location={startOffset:t.startOffset,startLine:t.startLine,startColumn:t.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){const t={name:e,children:Object.create(null)};this.setInitialNodeLocation(t);this.CST_STACK.push(t)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){const t=this.LA(0);const n=e.location;if(n.startOffset<=t.startOffset===true){n.endOffset=t.endOffset;n.endLine=t.endLine;n.endColumn=t.endColumn}else{n.startOffset=NaN;n.startLine=NaN;n.startColumn=NaN}}cstPostRuleOnlyOffset(e){const t=this.LA(0);const n=e.location;if(n.startOffset<=t.startOffset===true){n.endOffset=t.endOffset}else{n.startOffset=NaN}}cstPostTerminal(e,t){const n=this.CST_STACK[this.CST_STACK.length-1];rs(n,t,e);this.setNodeLocationFromToken(n.location,t)}cstPostNonTerminal(e,t){const n=this.CST_STACK[this.CST_STACK.length-1];is(n,t,e);this.setNodeLocationFromNode(n.location,e.location)}getBaseCstVisitorConstructor(){if((0,Be.A)(this.baseCstVisitorConstructor)){const e=cs(this.className,(0,R.A)(this.gastProductionsCache));this.baseCstVisitorConstructor=e;return e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if((0,Be.A)(this.baseCstVisitorWithDefaultsConstructor)){const e=us(this.className,(0,R.A)(this.gastProductionsCache),this.getBaseCstVisitorConstructor());this.baseCstVisitorWithDefaultsConstructor=e;return e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){const e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}}class ps{initLexerAdapter(){this.tokVector=[];this.tokVectorLength=0;this.currIdx=-1}set input(e){if(this.selfAnalysisDone!==true){throw Error(`Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.`)}this.reset();this.tokVector=e;this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){if(this.currIdx<=this.tokVector.length-2){this.consumeToken();return this.LA(1)}else{return _s}}LA(e){const t=this.currIdx+e;if(t<0||this.tokVectorLength<=t){return _s}else{return this.tokVector[t]}}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}}class ms{ACTION(e){return e.call(this)}consume(e,t,n){return this.consumeInternal(t,e,n)}subrule(e,t,n){return this.subruleInternal(t,e,n)}option(e,t){return this.optionInternal(t,e)}or(e,t){return this.orInternal(t,e)}many(e,t){return this.manyInternal(e,t)}atLeastOne(e,t){return this.atLeastOneInternal(e,t)}CONSUME(e,t){return this.consumeInternal(e,0,t)}CONSUME1(e,t){return this.consumeInternal(e,1,t)}CONSUME2(e,t){return this.consumeInternal(e,2,t)}CONSUME3(e,t){return this.consumeInternal(e,3,t)}CONSUME4(e,t){return this.consumeInternal(e,4,t)}CONSUME5(e,t){return this.consumeInternal(e,5,t)}CONSUME6(e,t){return this.consumeInternal(e,6,t)}CONSUME7(e,t){return this.consumeInternal(e,7,t)}CONSUME8(e,t){return this.consumeInternal(e,8,t)}CONSUME9(e,t){return this.consumeInternal(e,9,t)}SUBRULE(e,t){return this.subruleInternal(e,0,t)}SUBRULE1(e,t){return this.subruleInternal(e,1,t)}SUBRULE2(e,t){return this.subruleInternal(e,2,t)}SUBRULE3(e,t){return this.subruleInternal(e,3,t)}SUBRULE4(e,t){return this.subruleInternal(e,4,t)}SUBRULE5(e,t){return this.subruleInternal(e,5,t)}SUBRULE6(e,t){return this.subruleInternal(e,6,t)}SUBRULE7(e,t){return this.subruleInternal(e,7,t)}SUBRULE8(e,t){return this.subruleInternal(e,8,t)}SUBRULE9(e,t){return this.subruleInternal(e,9,t)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,t,n=Ps){if(me(this.definedRulesNames,e)){const t=lr.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className});const n={message:t,type:Ms.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(n)}this.definedRulesNames.push(e);const r=this.defineRule(e,t,n);this[e]=r;return r}OVERRIDE_RULE(e,t,n=Ps){const r=ci(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(r);const i=this.defineRule(e,t,n);this[e]=i;return i}BACKTRACK(e,t){return function(){this.isBackTrackingStack.push(1);const n=this.saveRecogState();try{e.apply(this,t);return true}catch(r){if(Ii(r)){return false}else{throw r}}finally{this.reloadRecogState(n);this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return te((0,i.A)(this.gastProductionsCache))}}var gs=n(85356);class ys{initRecognizerEngine(e,t){this.className=this.constructor.name;this.shortRuleNameToFull={};this.fullRuleNameToShort={};this.ruleShortNameIdx=256;this.tokenMatcher=In;this.subruleIdx=0;this.definedRulesNames=[];this.tokensMap={};this.isBackTrackingStack=[];this.RULE_STACK=[];this.RULE_OCCURRENCE_STACK=[];this.gastProductionsCache={};if((0,o.A)(t,"serializedGrammar")){throw Error("The Parser's configuration can no longer contain a <serializedGrammar> property.\n"+"\tSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0\n"+"\tFor Further details.")}if((0,ce.A)(e)){if((0,s.A)(e)){throw Error("A Token Vocabulary cannot be empty.\n"+"\tNote that the first argument for the parser constructor\n"+"\tis no longer a Token vector (since v4.0).")}if(typeof e[0].startOffset==="number"){throw Error("The Parser constructor no longer accepts a token vector as the first argument.\n"+"\tSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0\n"+"\tFor Further details.")}}if((0,ce.A)(e)){this.tokensMap=(0,tt.A)(e,((e,t)=>{e[t.name]=t;return e}),{})}else if((0,o.A)(e,"modes")&&Re((0,Le.A)((0,i.A)(e.modes)),Gn)){const t=(0,Le.A)((0,i.A)(e.modes));const n=Ne(t);this.tokensMap=(0,tt.A)(n,((e,t)=>{e[t.name]=t;return e}),{})}else if((0,gs.A)(e)){this.tokensMap=(0,c.A)(e)}else{throw new Error("<tokensDictionary> argument must be An Array of Token constructors,"+" A dictionary of Token constructors or an IMultiModeLexerDefinition")}this.tokensMap["EOF"]=sr;const n=(0,o.A)(e,"modes")?(0,Le.A)((0,i.A)(e.modes)):(0,i.A)(e);const r=Re(n,(e=>(0,s.A)(e.categoryMatches)));this.tokenMatcher=r?In:wn;Nn((0,i.A)(this.tokensMap))}defineRule(e,t,n){if(this.selfAnalysisDone){throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'\n`+`Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`)}const r=(0,o.A)(n,"resyncEnabled")?n.resyncEnabled:Ps.resyncEnabled;const i=(0,o.A)(n,"recoveryValueFunc")?n.recoveryValueFunc:Ps.recoveryValueFunc;const s=this.ruleShortNameIdx<<Ui+Fi;this.ruleShortNameIdx++;this.shortRuleNameToFull[s]=e;this.fullRuleNameToShort[e]=s;let a;if(this.outputCst===true){a=function n(...a){try{this.ruleInvocationStateUpdate(s,e,this.subruleIdx);t.apply(this,a);const n=this.CST_STACK[this.CST_STACK.length-1];this.cstPostRule(n);return n}catch(o){return this.invokeRuleCatch(o,r,i)}finally{this.ruleFinallyStateUpdate()}}}else{a=function n(...a){try{this.ruleInvocationStateUpdate(s,e,this.subruleIdx);return t.apply(this,a)}catch(o){return this.invokeRuleCatch(o,r,i)}finally{this.ruleFinallyStateUpdate()}}}const c=Object.assign(a,{ruleName:e,originalGrammarAction:t});return c}invokeRuleCatch(e,t,n){const r=this.RULE_STACK.length===1;const i=t&&!this.isBackTracking()&&this.recoveryEnabled;if(Ii(e)){const t=e;if(i){const r=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(r)){t.resyncedTokens=this.reSyncTo(r);if(this.outputCst){const e=this.CST_STACK[this.CST_STACK.length-1];e.recoveredNode=true;return e}else{return n(e)}}else{if(this.outputCst){const e=this.CST_STACK[this.CST_STACK.length-1];e.recoveredNode=true;t.partialCstResult=e}throw t}}else if(r){this.moveToTerminatedState();return n(e)}else{throw t}}else{throw e}}optionInternal(e,t){const n=this.getKeyForAutomaticLookahead(ji,t);return this.optionInternalLogic(e,t,n)}optionInternalLogic(e,t,n){let r=this.getLaFuncFromCache(n);let i;if(typeof e!=="function"){i=e.DEF;const t=e.GATE;if(t!==undefined){const e=r;r=()=>t.call(this)&&e.call(this)}}else{i=e}if(r.call(this)===true){return i.call(this)}return undefined}atLeastOneInternal(e,t){const n=this.getKeyForAutomaticLookahead(Wi,e);return this.atLeastOneInternalLogic(e,t,n)}atLeastOneInternalLogic(e,t,n){let r=this.getLaFuncFromCache(n);let i;if(typeof t!=="function"){i=t.DEF;const e=t.GATE;if(e!==undefined){const t=r;r=()=>e.call(this)&&t.call(this)}}else{i=t}if(r.call(this)===true){let e=this.doSingleRepetition(i);while(r.call(this)===true&&e===true){e=this.doSingleRepetition(i)}}else{throw this.raiseEarlyExitException(e,Dr.REPETITION_MANDATORY,t.ERR_MSG)}this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,t],r,Wi,e,br)}atLeastOneSepFirstInternal(e,t){const n=this.getKeyForAutomaticLookahead(zi,e);this.atLeastOneSepFirstInternalLogic(e,t,n)}atLeastOneSepFirstInternalLogic(e,t,n){const r=t.DEF;const i=t.SEP;const s=this.getLaFuncFromCache(n);if(s.call(this)===true){r.call(this);const t=()=>this.tokenMatcher(this.LA(1),i);while(this.tokenMatcher(this.LA(1),i)===true){this.CONSUME(i);r.call(this)}this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,i,t,r,_r],t,zi,e,_r)}else{throw this.raiseEarlyExitException(e,Dr.REPETITION_MANDATORY_WITH_SEPARATOR,t.ERR_MSG)}}manyInternal(e,t){const n=this.getKeyForAutomaticLookahead(Vi,e);return this.manyInternalLogic(e,t,n)}manyInternalLogic(e,t,n){let r=this.getLaFuncFromCache(n);let i;if(typeof t!=="function"){i=t.DEF;const e=t.GATE;if(e!==undefined){const t=r;r=()=>e.call(this)&&t.call(this)}}else{i=t}let s=true;while(r.call(this)===true&&s===true){s=this.doSingleRepetition(i)}this.attemptInRepetitionRecovery(this.manyInternal,[e,t],r,Vi,e,Nr,s)}manySepFirstInternal(e,t){const n=this.getKeyForAutomaticLookahead(Hi,e);this.manySepFirstInternalLogic(e,t,n)}manySepFirstInternalLogic(e,t,n){const r=t.DEF;const i=t.SEP;const s=this.getLaFuncFromCache(n);if(s.call(this)===true){r.call(this);const t=()=>this.tokenMatcher(this.LA(1),i);while(this.tokenMatcher(this.LA(1),i)===true){this.CONSUME(i);r.call(this)}this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,i,t,r,Lr],t,Hi,e,Lr)}}repetitionSepSecondInternal(e,t,n,r,i){while(n()){this.CONSUME(t);r.call(this)}this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,t,n,r,i],n,zi,e,i)}doSingleRepetition(e){const t=this.getLexerPosition();e.call(this);const n=this.getLexerPosition();return n>t}orInternal(e,t){const n=this.getKeyForAutomaticLookahead(Ki,t);const r=(0,ce.A)(e)?e:e.DEF;const i=this.getLaFuncFromCache(n);const s=i.call(this,r);if(s!==undefined){const e=r[s];return e.ALT.call(this)}this.raiseNoAltException(t,e.ERR_MSG)}ruleFinallyStateUpdate(){this.RULE_STACK.pop();this.RULE_OCCURRENCE_STACK.pop();this.cstFinallyStateUpdate();if(this.RULE_STACK.length===0&&this.isAtEndOfInput()===false){const e=this.LA(1);const t=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new Li(t,e))}}subruleInternal(e,t,n){let r;try{const i=n!==undefined?n.ARGS:undefined;this.subruleIdx=t;r=e.apply(this,i);this.cstPostNonTerminal(r,n!==undefined&&n.LABEL!==undefined?n.LABEL:e.ruleName);return r}catch(i){throw this.subruleInternalError(i,n,e.ruleName)}}subruleInternalError(e,t,n){if(Ii(e)&&e.partialCstResult!==undefined){this.cstPostNonTerminal(e.partialCstResult,t!==undefined&&t.LABEL!==undefined?t.LABEL:n);delete e.partialCstResult}throw e}consumeInternal(e,t,n){let r;try{const t=this.LA(1);if(this.tokenMatcher(t,e)===true){this.consumeToken();r=t}else{this.consumeInternalError(e,t,n)}}catch(i){r=this.consumeInternalRecovery(e,t,i)}this.cstPostTerminal(n!==undefined&&n.LABEL!==undefined?n.LABEL:e.name,r);return r}consumeInternalError(e,t,n){let r;const i=this.LA(0);if(n!==undefined&&n.ERR_MSG){r=n.ERR_MSG}else{r=this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:t,previous:i,ruleName:this.getCurrRuleFullName()})}throw this.SAVE_ERROR(new Ci(r,t,i))}consumeInternalRecovery(e,t,n){if(this.recoveryEnabled&&n.name==="MismatchedTokenException"&&!this.isBackTracking()){const i=this.getFollowsForInRuleRecovery(e,t);try{return this.tryInRuleRecovery(e,i)}catch(r){if(r.name===Oi){throw n}else{throw r}}}else{throw n}}saveRecogState(){const e=this.errors;const t=(0,c.A)(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:t,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors;this.importLexerState(e.lexerState);this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,t,n){this.RULE_OCCURRENCE_STACK.push(n);this.RULE_STACK.push(e);this.cstInvocationStateUpdate(t)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){const e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),sr)}reset(){this.resetLexerState();this.subruleIdx=0;this.isBackTrackingStack=[];this.errors=[];this.RULE_STACK=[];this.CST_STACK=[];this.RULE_OCCURRENCE_STACK=[]}}class vs{initErrorHandler(e){this._errors=[];this.errorMessageProvider=(0,o.A)(e,"errorMessageProvider")?e.errorMessageProvider:Os.errorMessageProvider}SAVE_ERROR(e){if(Ii(e)){e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:(0,c.A)(this.RULE_OCCURRENCE_STACK)};this._errors.push(e);return e}else{throw Error("Trying to save an Error which is not a RecognitionException")}}get errors(){return(0,c.A)(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,t,n){const r=this.getCurrRuleFullName();const i=this.getGAstProductions()[r];const s=Qr(e,i,t,this.maxLookahead);const a=s[0];const o=[];for(let u=1;u<=this.maxLookahead;u++){o.push(this.LA(u))}const c=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:a,actual:o,previous:this.LA(0),customUserDescription:n,ruleName:r});throw this.SAVE_ERROR(new bi(c,this.LA(1),this.LA(0)))}raiseNoAltException(e,t){const n=this.getCurrRuleFullName();const r=this.getGAstProductions()[n];const i=Xr(e,r,this.maxLookahead);const s=[];for(let c=1;c<=this.maxLookahead;c++){s.push(this.LA(c))}const a=this.LA(0);const o=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:i,actual:s,previous:a,customUserDescription:t,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new Ni(o,this.LA(1),a))}}class As{initContentAssist(){}computeContentAssist(e,t){const n=this.gastProductionsCache[e];if((0,Be.A)(n)){throw Error(`Rule ->${e}<- does not exist in this grammar.`)}return Pr([n],t,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){const t=vt(e.ruleStack);const n=this.getGAstProductions();const r=n[t];const i=new Sr(r,e).startWalking();return i}}const Ts={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(Ts);const Rs=true;const Es=Math.pow(2,Fi)-1;const ks=rr({name:"RECORDING_PHASE_TOKEN",pattern:Vn.NA});Nn([ks]);const xs=ar(ks,"This IToken indicates the Parser is in Recording Phase\n\t"+""+"See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details",-1,-1,-1,-1,-1,-1);Object.freeze(xs);const $s={name:"This CSTNode indicates the Parser is in Recording Phase\n\t"+"See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details",children:{}};class ws{initGastRecorder(e){this.recordingProdStack=[];this.RECORDING_PHASE=false}enableRecording(){this.RECORDING_PHASE=true;this.TRACE_INIT("Enable Recording",(()=>{for(let e=0;e<10;e++){const t=e>0?e:"";this[`CONSUME${t}`]=function(t,n){return this.consumeInternalRecord(t,e,n)};this[`SUBRULE${t}`]=function(t,n){return this.subruleInternalRecord(t,e,n)};this[`OPTION${t}`]=function(t){return this.optionInternalRecord(t,e)};this[`OR${t}`]=function(t){return this.orInternalRecord(t,e)};this[`MANY${t}`]=function(t){this.manyInternalRecord(e,t)};this[`MANY_SEP${t}`]=function(t){this.manySepFirstInternalRecord(e,t)};this[`AT_LEAST_ONE${t}`]=function(t){this.atLeastOneInternalRecord(e,t)};this[`AT_LEAST_ONE_SEP${t}`]=function(t){this.atLeastOneSepFirstInternalRecord(e,t)}}this[`consume`]=function(e,t,n){return this.consumeInternalRecord(t,e,n)};this[`subrule`]=function(e,t,n){return this.subruleInternalRecord(t,e,n)};this[`option`]=function(e,t){return this.optionInternalRecord(t,e)};this[`or`]=function(e,t){return this.orInternalRecord(t,e)};this[`many`]=function(e,t){this.manyInternalRecord(e,t)};this[`atLeastOne`]=function(e,t){this.atLeastOneInternalRecord(e,t)};this.ACTION=this.ACTION_RECORD;this.BACKTRACK=this.BACKTRACK_RECORD;this.LA=this.LA_RECORD}))}disableRecording(){this.RECORDING_PHASE=false;this.TRACE_INIT("Deleting Recording methods",(()=>{const e=this;for(let t=0;t<10;t++){const n=t>0?t:"";delete e[`CONSUME${n}`];delete e[`SUBRULE${n}`];delete e[`OPTION${n}`];delete e[`OR${n}`];delete e[`MANY${n}`];delete e[`MANY_SEP${n}`];delete e[`AT_LEAST_ONE${n}`];delete e[`AT_LEAST_ONE_SEP${n}`]}delete e[`consume`];delete e[`subrule`];delete e[`option`];delete e[`or`];delete e[`many`];delete e[`atLeastOne`];delete e.ACTION;delete e.BACKTRACK;delete e.LA}))}ACTION_RECORD(e){}BACKTRACK_RECORD(e,t){return()=>true}LA_RECORD(e){return _s}topLevelRuleRecord(e,t){try{const n=new H({definition:[],name:e});n.name=e;this.recordingProdStack.push(n);t.call(this);this.recordingProdStack.pop();return n}catch(n){if(n.KNOWN_RECORDER_ERROR!==true){try{n.message=n.message+'\n\t This error was thrown during the "grammar recording phase" For more info see:\n\t'+"https://chevrotain.io/docs/guide/internals.html#grammar-recording"}catch(r){throw n}}throw n}}optionInternalRecord(e,t){return Is.call(this,Y,e,t)}atLeastOneInternalRecord(e,t){Is.call(this,q,t,e)}atLeastOneSepFirstInternalRecord(e,t){Is.call(this,X,t,e,Rs)}manyInternalRecord(e,t){Is.call(this,Q,t,e)}manySepFirstInternalRecord(e,t){Is.call(this,Z,t,e,Rs)}orInternalRecord(e,t){return Ss.call(this,e,t)}subruleInternalRecord(e,t,n){Ns(t);if(!e||(0,o.A)(e,"ruleName")===false){const n=new Error(`<SUBRULE${Cs(t)}> argument is invalid`+` expecting a Parser method reference but got: <${JSON.stringify(e)}>`+`\n inside top level rule: <${this.recordingProdStack[0].name}>`);n.KNOWN_RECORDER_ERROR=true;throw n}const r=(0,xn.A)(this.recordingProdStack);const i=e.ruleName;const s=new W({idx:t,nonTerminalName:i,label:n===null||n===void 0?void 0:n.LABEL,referencedRule:undefined});r.definition.push(s);return this.outputCst?$s:Ts}consumeInternalRecord(e,t,n){Ns(t);if(!Mn(e)){const n=new Error(`<CONSUME${Cs(t)}> argument is invalid`+` expecting a TokenType reference but got: <${JSON.stringify(e)}>`+`\n inside top level rule: <${this.recordingProdStack[0].name}>`);n.KNOWN_RECORDER_ERROR=true;throw n}const r=(0,xn.A)(this.recordingProdStack);const i=new ee({idx:t,terminalType:e,label:n===null||n===void 0?void 0:n.LABEL});r.definition.push(i);return xs}}function Is(e,t,n,r=false){Ns(n);const i=(0,xn.A)(this.recordingProdStack);const s=(0,Qe.A)(t)?t:t.DEF;const a=new e({definition:[],idx:n});if(r){a.separator=t.SEP}if((0,o.A)(t,"MAX_LOOKAHEAD")){a.maxLookahead=t.MAX_LOOKAHEAD}this.recordingProdStack.push(a);s.call(this);i.definition.push(a);this.recordingProdStack.pop();return Ts}function Ss(e,t){Ns(t);const n=(0,xn.A)(this.recordingProdStack);const i=(0,ce.A)(e)===false;const s=i===false?e:e.DEF;const a=new J({definition:[],idx:t,ignoreAmbiguities:i&&e.IGNORE_AMBIGUITIES===true});if((0,o.A)(e,"MAX_LOOKAHEAD")){a.maxLookahead=e.MAX_LOOKAHEAD}const c=de(s,(e=>(0,Qe.A)(e.GATE)));a.hasPredicates=c;n.definition.push(a);(0,r.A)(s,(e=>{const t=new z({definition:[]});a.definition.push(t);if((0,o.A)(e,"IGNORE_AMBIGUITIES")){t.ignoreAmbiguities=e.IGNORE_AMBIGUITIES}else if((0,o.A)(e,"GATE")){t.ignoreAmbiguities=true}this.recordingProdStack.push(t);e.ALT.call(this);this.recordingProdStack.pop()}));return Ts}function Cs(e){return e===0?"":`${e}`}function Ns(e){if(e<0||e>Es){const t=new Error(`Invalid DSL Method idx value: <${e}>\n\t`+`Idx value must be a none negative value smaller than ${Es+1}`);t.KNOWN_RECORDER_ERROR=true;throw t}}class Ls{initPerformanceTracer(e){if((0,o.A)(e,"traceInitPerf")){const t=e.traceInitPerf;const n=typeof t==="number";this.traceInitMaxIdent=n?t:Infinity;this.traceInitPerf=n?t>0:t}else{this.traceInitMaxIdent=0;this.traceInitPerf=Os.traceInitPerf}this.traceInitIndent=-1}TRACE_INIT(e,t){if(this.traceInitPerf===true){this.traceInitIndent++;const n=new Array(this.traceInitIndent+1).join("\t");if(this.traceInitIndent<this.traceInitMaxIdent){console.log(`${n}--\x3e <${e}>`)}const{time:r,value:i}=$n(t);const s=r>10?console.warn:console.log;if(this.traceInitIndent<this.traceInitMaxIdent){s(`${n}<-- <${e}> time: ${r}ms`)}this.traceInitIndent--;return i}else{return t()}}}function bs(e,t){t.forEach((t=>{const n=t.prototype;Object.getOwnPropertyNames(n).forEach((r=>{if(r==="constructor"){return}const i=Object.getOwnPropertyDescriptor(n,r);if(i&&(i.get||i.set)){Object.defineProperty(e.prototype,r,i)}else{e.prototype[r]=t.prototype[r]}}))}))}const _s=ar(sr,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(_s);const Os=Object.freeze({recoveryEnabled:false,maxLookahead:3,dynamicTokensEnabled:false,outputCst:true,errorMessageProvider:cr,nodeLocationTracking:"none",traceInitPerf:false,skipValidations:false});const Ps=Object.freeze({recoveryValueFunc:()=>undefined,resyncEnabled:true});var Ms;(function(e){e[e["INVALID_RULE_NAME"]=0]="INVALID_RULE_NAME";e[e["DUPLICATE_RULE_NAME"]=1]="DUPLICATE_RULE_NAME";e[e["INVALID_RULE_OVERRIDE"]=2]="INVALID_RULE_OVERRIDE";e[e["DUPLICATE_PRODUCTIONS"]=3]="DUPLICATE_PRODUCTIONS";e[e["UNRESOLVED_SUBRULE_REF"]=4]="UNRESOLVED_SUBRULE_REF";e[e["LEFT_RECURSION"]=5]="LEFT_RECURSION";e[e["NONE_LAST_EMPTY_ALT"]=6]="NONE_LAST_EMPTY_ALT";e[e["AMBIGUOUS_ALTS"]=7]="AMBIGUOUS_ALTS";e[e["CONFLICT_TOKENS_RULES_NAMESPACE"]=8]="CONFLICT_TOKENS_RULES_NAMESPACE";e[e["INVALID_TOKEN_NAME"]=9]="INVALID_TOKEN_NAME";e[e["NO_NON_EMPTY_LOOKAHEAD"]=10]="NO_NON_EMPTY_LOOKAHEAD";e[e["AMBIGUOUS_PREFIX_ALTS"]=11]="AMBIGUOUS_PREFIX_ALTS";e[e["TOO_MANY_ALTS"]=12]="TOO_MANY_ALTS";e[e["CUSTOM_LOOKAHEAD_VALIDATION"]=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(Ms||(Ms={}));function Ds(e=undefined){return function(){return e}}class Us{static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated."+"\t\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",(()=>{let e;this.selfAnalysisDone=true;const t=this.className;this.TRACE_INIT("toFastProps",(()=>{u(this)}));this.TRACE_INIT("Grammar Recording",(()=>{try{this.enableRecording();(0,r.A)(this.definedRulesNames,(e=>{const t=this[e];const n=t["originalGrammarAction"];let r;this.TRACE_INIT(`${e} Rule`,(()=>{r=this.topLevelRuleRecord(e,n)}));this.gastProductionsCache[e]=r}))}finally{this.disableRecording()}}));let n=[];this.TRACE_INIT("Grammar Resolving",(()=>{n=Ti({rules:(0,i.A)(this.gastProductionsCache)});this.definitionErrors=this.definitionErrors.concat(n)}));this.TRACE_INIT("Grammar Validations",(()=>{if((0,s.A)(n)&&this.skipValidations===false){const e=Ri({rules:(0,i.A)(this.gastProductionsCache),tokenTypes:(0,i.A)(this.tokensMap),errMsgProvider:lr,grammarName:t});const n=ti({lookaheadStrategy:this.lookaheadStrategy,rules:(0,i.A)(this.gastProductionsCache),tokenTypes:(0,i.A)(this.tokensMap),grammarName:t});this.definitionErrors=this.definitionErrors.concat(e,n)}}));if((0,s.A)(this.definitionErrors)){if(this.recoveryEnabled){this.TRACE_INIT("computeAllProdsFollows",(()=>{const e=Ue((0,i.A)(this.gastProductionsCache));this.resyncFollows=e}))}this.TRACE_INIT("ComputeLookaheadFunctions",(()=>{var e,t;(t=(e=this.lookaheadStrategy).initialize)===null||t===void 0?void 0:t.call(e,{rules:(0,i.A)(this.gastProductionsCache)});this.preComputeLookaheadFunctions((0,i.A)(this.gastProductionsCache))}))}if(!Us.DEFER_DEFINITION_ERRORS_HANDLING&&!(0,s.A)(this.definitionErrors)){e=(0,a.A)(this.definitionErrors,(e=>e.message));throw new Error(`Parser Definition Errors detected:\n ${e.join("\n-------------------------------\n")}`)}}))}constructor(e,t){this.definitionErrors=[];this.selfAnalysisDone=false;const n=this;n.initErrorHandler(t);n.initLexerAdapter();n.initLooksAhead(t);n.initRecognizerEngine(e,t);n.initRecoverable(t);n.initTreeBuilder(t);n.initContentAssist();n.initGastRecorder(t);n.initPerformanceTracer(t);if((0,o.A)(t,"ignoredIssues")){throw new Error("The <ignoredIssues> IParserConfig property has been deprecated.\n\t"+"Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.\n\t"+"See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES\n\t"+"For further details.")}this.skipValidations=(0,o.A)(t,"skipValidations")?t.skipValidations:Os.skipValidations}}Us.DEFER_DEFINITION_ERRORS_HANDLING=false;bs(Us,[Mi,Qi,hs,ps,ys,ms,vs,As,ws,Ls]);class Fs extends Us{constructor(e,t=Os){const n=(0,c.A)(t);n.outputCst=true;super(e,n)}}class Gs extends Us{constructor(e,t=Os){const n=(0,c.A)(t);n.outputCst=false;super(e,n)}}function Bs(e){const t=new Ks;const n=values(e);return map(n,(e=>t.visitRule(e)))}class Ks extends(null&&GAstVisitor){visitRule(e){const t=this.visitEach(e.definition);const n=groupBy(t,(e=>e.propertyName));const r=map(n,((e,t)=>{const n=!some(e,(e=>!e.canBeNull));let r=e[0].type;if(e.length>1){r=map(e,(e=>e.type))}return{name:t,type:r,optional:n}}));return{name:e.name,properties:r}}visitAlternative(e){return this.visitEachAndOverrideWith(e.definition,{canBeNull:true})}visitOption(e){return this.visitEachAndOverrideWith(e.definition,{canBeNull:true})}visitRepetition(e){return this.visitEachAndOverrideWith(e.definition,{canBeNull:true})}visitRepetitionMandatory(e){return this.visitEach(e.definition)}visitRepetitionMandatoryWithSeparator(e){return this.visitEach(e.definition).concat({propertyName:e.separator.name,canBeNull:true,type:js(e.separator)})}visitRepetitionWithSeparator(e){return this.visitEachAndOverrideWith(e.definition,{canBeNull:true}).concat({propertyName:e.separator.name,canBeNull:true,type:js(e.separator)})}visitAlternation(e){return this.visitEachAndOverrideWith(e.definition,{canBeNull:true})}visitTerminal(e){return[{propertyName:e.label||e.terminalType.name,canBeNull:false,type:js(e)}]}visitNonTerminal(e){return[{propertyName:e.label||e.nonTerminalName,canBeNull:false,type:js(e)}]}visitEachAndOverrideWith(e,t){return map(this.visitEach(e),(e=>assign({},e,t)))}visitEach(e){return flatten(map(e,(e=>this.visit(e))))}}function js(e){if(e instanceof NonTerminal){return{kind:"rule",name:e.referencedRule.name}}return{kind:"token"}}const Vs={includeVisitorInterface:true,visitorInterfaceName:"ICstNodeVisitor"};function Ws(e,t){const n=Object.assign(Object.assign({},Vs),t);const r=buildModel(e);return genDts(r,n)}function Hs(){console.warn("The clearCache function was 'soft' removed from the Chevrotain API."+"\n\t It performs no action other than printing this message."+"\n\t Please avoid using it as it will be completely removed in the future")}class zs{constructor(){throw new Error("The Parser class has been deprecated, use CstParser or EmbeddedActionsParser instead.\t\n"+"See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_7-0-0")}}},37810:(e,t,n)=>{"use strict";n.d(t,{t:()=>Ji,u:()=>es});var r=n(5730);var i=n(70977);var s=n(65811);var a=n(85684);function o(e){const t=[];const n=e.Grammar;for(const r of n.rules){if((0,a.rE)(r)&&(0,i.eb)(r)&&(0,s.lU)((0,i.S)(r))){t.push(r.name)}}return{multilineCommentRules:t,nameRegexp:r.El}}var c=n(50450);var u=n(8937);var l=n(97133);function d(e,t,n){return`${e.name}_${t}_${n}`}const f=0;const h=1;const p=2;const m=4;const g=5;const y=6;const v=7;const A=8;const T=9;const R=10;const E=11;const k=12;class x{constructor(e){this.target=e}isEpsilon(){return false}}class $ extends x{constructor(e,t){super(e);this.tokenType=t}}class w extends x{constructor(e){super(e)}isEpsilon(){return true}}class I extends x{constructor(e,t,n){super(e);this.rule=t;this.followState=n}isEpsilon(){return true}}function S(e){const t={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};C(t,e);const n=e.length;for(let r=0;r<n;r++){const n=e[r];const i=D(t,n,n);if(i===undefined){continue}z(t,n,i)}return t}function C(e,t){const n=t.length;for(let r=0;r<n;r++){const n=t[r];const i=q(e,n,undefined,{type:p});const s=q(e,n,undefined,{type:v});i.stop=s;e.ruleToStartState.set(n,i);e.ruleToStopState.set(n,s)}}function N(e,t,n){if(n instanceof c.BK){return W(e,t,n.terminalType,n)}else if(n instanceof c.wL){return H(e,t,n)}else if(n instanceof c.ak){return P(e,t,n)}else if(n instanceof c.c$){return M(e,t,n)}else if(n instanceof c.Y2){return L(e,t,n)}else if(n instanceof c.Pp){return b(e,t,n)}else if(n instanceof c.$P){return _(e,t,n)}else if(n instanceof c.Cy){return O(e,t,n)}else{return D(e,t,n)}}function L(e,t,n){const r=q(e,t,n,{type:g});B(e,r);const i=K(e,t,r,n,D(e,t,n));return F(e,t,n,i)}function b(e,t,n){const r=q(e,t,n,{type:g});B(e,r);const i=K(e,t,r,n,D(e,t,n));const s=W(e,t,n.separator,n);return F(e,t,n,i,s)}function _(e,t,n){const r=q(e,t,n,{type:m});B(e,r);const i=K(e,t,r,n,D(e,t,n));return U(e,t,n,i)}function O(e,t,n){const r=q(e,t,n,{type:m});B(e,r);const i=K(e,t,r,n,D(e,t,n));const s=W(e,t,n.separator,n);return U(e,t,n,i,s)}function P(e,t,n){const r=q(e,t,n,{type:h});B(e,r);const i=(0,u.A)(n.definition,(n=>N(e,t,n)));const s=K(e,t,r,n,...i);return s}function M(e,t,n){const r=q(e,t,n,{type:h});B(e,r);const i=K(e,t,r,n,D(e,t,n));return G(e,t,n,i)}function D(e,t,n){const r=(0,l.A)((0,u.A)(n.definition,(n=>N(e,t,n))),(e=>e!==undefined));if(r.length===1){return r[0]}else if(r.length===0){return undefined}else{return V(e,r)}}function U(e,t,n,r,i){const s=r.left;const a=r.right;const o=q(e,t,n,{type:E});B(e,o);const c=q(e,t,n,{type:k});s.loopback=o;c.loopback=o;e.decisionMap[d(t,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",n.idx)]=o;Y(a,o);if(i===undefined){Y(o,s);Y(o,c)}else{Y(o,c);Y(o,i.left);Y(i.right,s)}return{left:s,right:c}}function F(e,t,n,r,i){const s=r.left;const a=r.right;const o=q(e,t,n,{type:R});B(e,o);const c=q(e,t,n,{type:k});const u=q(e,t,n,{type:T});o.loopback=u;c.loopback=u;Y(o,s);Y(o,c);Y(a,u);if(i!==undefined){Y(u,c);Y(u,i.left);Y(i.right,s)}else{Y(u,o)}e.decisionMap[d(t,i?"RepetitionWithSeparator":"Repetition",n.idx)]=o;return{left:o,right:c}}function G(e,t,n,r){const i=r.left;const s=r.right;Y(i,s);e.decisionMap[d(t,"Option",n.idx)]=i;return r}function B(e,t){e.decisionStates.push(t);t.decision=e.decisionStates.length-1;return t.decision}function K(e,t,n,r,...i){const s=q(e,t,r,{type:A,start:n});n.end=s;for(const o of i){if(o!==undefined){Y(n,o.left);Y(o.right,s)}else{Y(n,s)}}const a={left:n,right:s};e.decisionMap[d(t,j(r),r.idx)]=n;return a}function j(e){if(e instanceof c.ak){return"Alternation"}else if(e instanceof c.c$){return"Option"}else if(e instanceof c.Y2){return"Repetition"}else if(e instanceof c.Pp){return"RepetitionWithSeparator"}else if(e instanceof c.$P){return"RepetitionMandatory"}else if(e instanceof c.Cy){return"RepetitionMandatoryWithSeparator"}else{throw new Error("Invalid production type encountered")}}function V(e,t){const n=t.length;for(let s=0;s<n-1;s++){const n=t[s];let r;if(n.left.transitions.length===1){r=n.left.transitions[0]}const i=r instanceof I;const a=r;const o=t[s+1].left;if(n.left.type===h&&n.right.type===h&&r!==undefined&&(i&&a.followState===n.right||r.target===n.right)){if(i){a.followState=o}else{r.target=o}Q(e,n.right)}else{Y(n.right,o)}}const r=t[0];const i=t[n-1];return{left:r.left,right:i.right}}function W(e,t,n,r){const i=q(e,t,r,{type:h});const s=q(e,t,r,{type:h});X(i,new $(s,n));return{left:i,right:s}}function H(e,t,n){const r=n.referencedRule;const i=e.ruleToStartState.get(r);const s=q(e,t,n,{type:h});const a=q(e,t,n,{type:h});const o=new I(i,r,a);X(s,o);return{left:s,right:a}}function z(e,t,n){const r=e.ruleToStartState.get(t);Y(r,n.left);const i=e.ruleToStopState.get(t);Y(n.right,i);const s={left:r,right:i};return s}function Y(e,t){const n=new w(t);X(e,n)}function q(e,t,n,r){const i=Object.assign({atn:e,production:n,epsilonOnlyTransitions:false,rule:t,transitions:[],nextTokenWithinRule:[],stateNumber:e.states.length},r);e.states.push(i);return i}function X(e,t){if(e.transitions.length===0){e.epsilonOnlyTransitions=t.isEpsilon()}e.transitions.push(t)}function Q(e,t){e.states.splice(e.states.indexOf(t),1)}const Z={};class J{constructor(){this.map={};this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){const t=ee(e);if(!(t in this.map)){this.map[t]=this.configs.length;this.configs.push(e)}}get elements(){return this.configs}get alts(){return(0,u.A)(this.configs,(e=>e.alt))}get key(){let e="";for(const t in this.map){e+=t+":"}return e}}function ee(e,t=true){return`${t?`a${e.alt}`:""}s${e.state.stateNumber}:${e.stack.map((e=>e.stateNumber.toString())).join("_")}`}var te=n(963);var ne=n(57852);var re=n(1121);var ie=n(19363);function se(e,t){return e&&e.length?(0,ie.A)(e,(0,re.A)(t,2)):[]}const ae=se;var oe=n(74033);var ce=n(69769);var ue=n(74650);var le=n(65339);function de(e,t){const n={};return r=>{const i=r.toString();let s=n[i];if(s!==undefined){return s}else{s={atnStartState:e,decision:t,states:{}};n[i]=s;return s}}}class fe{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="";const t=this.predicates.length;for(let n=0;n<t;n++){e+=this.predicates[n]===true?"1":"0"}return e}}const he=new fe;class pe extends c.T6{constructor(e){var t;super();this.logging=(t=e===null||e===void 0?void 0:e.logging)!==null&&t!==void 0?t:e=>console.log(e)}initialize(e){this.atn=S(e.rules);this.dfas=ge(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){const{prodOccurrence:t,rule:n,hasPredicates:r,dynamicTokensEnabled:i}=e;const s=this.dfas;const a=this.logging;const o=d(n,"Alternation",t);const l=this.atn.decisionMap[o];const f=l.decision;const h=(0,u.A)((0,c.jk)({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:n}),(e=>(0,u.A)(e,(e=>e[0]))));if(me(h,false)&&!i){const e=(0,le.A)(h,((e,t,n)=>{(0,ce.A)(t,(t=>{if(t){e[t.tokenTypeIdx]=n;(0,ce.A)(t.categoryMatches,(t=>{e[t]=n}))}}));return e}),{});if(r){return function(t){var n;const r=this.LA(1);const i=e[r.tokenTypeIdx];if(t!==undefined&&i!==undefined){const e=(n=t[i])===null||n===void 0?void 0:n.GATE;if(e!==undefined&&e.call(this)===false){return undefined}}return i}}else{return function(){const t=this.LA(1);return e[t.tokenTypeIdx]}}}else if(r){return function(e){const t=new fe;const n=e===undefined?0:e.length;for(let i=0;i<n;i++){const n=e===null||e===void 0?void 0:e[i].GATE;t.set(i,n===undefined||n.call(this))}const r=ye.call(this,s,f,t,a);return typeof r==="number"?r:undefined}}else{return function(){const e=ye.call(this,s,f,he,a);return typeof e==="number"?e:undefined}}}buildLookaheadForOptional(e){const{prodOccurrence:t,rule:n,prodType:r,dynamicTokensEnabled:i}=e;const s=this.dfas;const a=this.logging;const o=d(n,r,t);const l=this.atn.decisionMap[o];const f=l.decision;const h=(0,u.A)((0,c.jk)({maxLookahead:1,occurrence:t,prodType:r,rule:n}),(e=>(0,u.A)(e,(e=>e[0]))));if(me(h)&&h[0][0]&&!i){const e=h[0];const t=(0,oe.A)(e);if(t.length===1&&(0,ue.A)(t[0].categoryMatches)){const e=t[0];const n=e.tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===n}}else{const e=(0,le.A)(t,((e,t)=>{if(t!==undefined){e[t.tokenTypeIdx]=true;(0,ce.A)(t.categoryMatches,(t=>{e[t]=true}))}return e}),{});return function(){const t=this.LA(1);return e[t.tokenTypeIdx]===true}}}return function(){const e=ye.call(this,s,f,he,a);return typeof e==="object"?false:e===0}}}function me(e,t=true){const n=new Set;for(const r of e){const e=new Set;for(const i of r){if(i===undefined){if(t){break}else{return false}}const r=[i.tokenTypeIdx].concat(i.categoryMatches);for(const t of r){if(n.has(t)){if(!e.has(t)){return false}}else{n.add(t);e.add(t)}}}}return true}function ge(e){const t=e.decisionStates.length;const n=Array(t);for(let r=0;r<t;r++){n[r]=de(e.decisionStates[r],r)}return n}function ye(e,t,n,r){const i=e[t](n);let s=i.start;if(s===undefined){const e=Le(i.atnStartState);s=Ne(i,Se(e));i.start=s}const a=ve.apply(this,[i,s,n,r]);return a}function ve(e,t,n,r){let i=t;let s=1;const a=[];let o=this.LA(s++);while(true){let t=xe(i,o);if(t===undefined){t=Ae.apply(this,[e,i,o,s,n,r])}if(t===Z){return ke(a,i,o)}if(t.isAcceptState===true){return t.prediction}i=t;a.push(o);o=this.LA(s++)}}function Ae(e,t,n,r,i,s){const a=$e(t.configs,n,i);if(a.size===0){Ce(e,t,n,Z);return Z}let o=Se(a);const c=Ie(a,i);if(c!==undefined){o.isAcceptState=true;o.prediction=c;o.configs.uniqueAlt=c}else if(Me(a)){const t=(0,te.A)(a.alts);o.isAcceptState=true;o.prediction=t;o.configs.uniqueAlt=t;Te.apply(this,[e,r,a.alts,s])}o=Ce(e,t,n,o);return o}function Te(e,t,n,r){const i=[];for(let u=1;u<=t;u++){i.push(this.LA(u).tokenType)}const s=e.atnStartState;const a=s.rule;const o=s.production;const c=Re({topLevelRule:a,ambiguityIndices:n,production:o,prefixPath:i});r(c)}function Re(e){const t=(0,u.A)(e.prefixPath,(e=>(0,c.Sk)(e))).join(", ");const n=e.production.idx===0?"":e.production.idx;let r=`Ambiguous Alternatives Detected: <${e.ambiguityIndices.join(", ")}> in <${Ee(e.production)}${n}>`+` inside <${e.topLevelRule.name}> Rule,\n`+`<${t}> may appears as a prefix path in all these alternatives.\n`;r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\n`+`For Further details.`;return r}function Ee(e){if(e instanceof c.wL){return"SUBRULE"}else if(e instanceof c.c$){return"OPTION"}else if(e instanceof c.ak){return"OR"}else if(e instanceof c.$P){return"AT_LEAST_ONE"}else if(e instanceof c.Cy){return"AT_LEAST_ONE_SEP"}else if(e instanceof c.Pp){return"MANY_SEP"}else if(e instanceof c.Y2){return"MANY"}else if(e instanceof c.BK){return"CONSUME"}else{throw Error("non exhaustive match")}}function ke(e,t,n){const r=(0,ne.A)(t.configs.elements,(e=>e.state.transitions));const i=ae(r.filter((e=>e instanceof $)).map((e=>e.tokenType)),(e=>e.tokenTypeIdx));return{actualToken:n,possibleTokenTypes:i,tokenPath:e}}function xe(e,t){return e.edges[t.tokenTypeIdx]}function $e(e,t,n){const r=new J;const i=[];for(const a of e.elements){if(n.is(a.alt)===false){continue}if(a.state.type===v){i.push(a);continue}const e=a.state.transitions.length;for(let n=0;n<e;n++){const e=a.state.transitions[n];const i=we(e,t);if(i!==undefined){r.add({state:i,alt:a.alt,stack:a.stack})}}}let s;if(i.length===0&&r.size===1){s=r}if(s===undefined){s=new J;for(const e of r.elements){be(e,s)}}if(i.length>0&&!Oe(s)){for(const e of i){s.add(e)}}return s}function we(e,t){if(e instanceof $&&(0,c.G)(t,e.tokenType)){return e.target}return undefined}function Ie(e,t){let n;for(const r of e.elements){if(t.is(r.alt)===true){if(n===undefined){n=r.alt}else if(n!==r.alt){return undefined}}}return n}function Se(e){return{configs:e,edges:{},isAcceptState:false,prediction:-1}}function Ce(e,t,n,r){r=Ne(e,r);t.edges[n.tokenTypeIdx]=r;return r}function Ne(e,t){if(t===Z){return t}const n=t.configs.key;const r=e.states[n];if(r!==undefined){return r}t.configs.finalize();e.states[n]=t;return t}function Le(e){const t=new J;const n=e.transitions.length;for(let r=0;r<n;r++){const n=e.transitions[r].target;const i={state:n,alt:r,stack:[]};be(i,t)}return t}function be(e,t){const n=e.state;if(n.type===v){if(e.stack.length>0){const n=[...e.stack];const r=n.pop();const i={state:r,alt:e.alt,stack:n};be(i,t)}else{t.add(e)}return}if(!n.epsilonOnlyTransitions){t.add(e)}const r=n.transitions.length;for(let i=0;i<r;i++){const r=n.transitions[i];const s=_e(e,r);if(s!==undefined){be(s,t)}}}function _e(e,t){if(t instanceof w){return{state:t.target,alt:e.alt,stack:e.stack}}else if(t instanceof I){const n=[...e.stack,t.followState];return{state:t.target,alt:e.alt,stack:n}}return undefined}function Oe(e){for(const t of e.elements){if(t.state.type===v){return true}}return false}function Pe(e){for(const t of e.elements){if(t.state.type!==v){return false}}return true}function Me(e){if(Pe(e)){return true}const t=De(e.elements);const n=Ue(t)&&!Fe(t);return n}function De(e){const t=new Map;for(const n of e){const e=ee(n,false);let r=t.get(e);if(r===undefined){r={};t.set(e,r)}r[n.alt]=true}return t}function Ue(e){for(const t of Array.from(e.values())){if(Object.keys(t).length>1){return true}}return false}function Fe(e){for(const t of Array.from(e.values())){if(Object.keys(t).length===1){return true}}return false}var Ge=n(63752);var Be;(function(e){function t(e){return typeof e==="string"}e.is=t})(Be||(Be={}));var Ke;(function(e){function t(e){return typeof e==="string"}e.is=t})(Ke||(Ke={}));var je;(function(e){e.MIN_VALUE=-2147483648;e.MAX_VALUE=2147483647;function t(t){return typeof t==="number"&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}e.is=t})(je||(je={}));var Ve;(function(e){e.MIN_VALUE=0;e.MAX_VALUE=2147483647;function t(t){return typeof t==="number"&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}e.is=t})(Ve||(Ve={}));var We;(function(e){function t(e,t){if(e===Number.MAX_VALUE){e=Ve.MAX_VALUE}if(t===Number.MAX_VALUE){t=Ve.MAX_VALUE}return{line:e,character:t}}e.create=t;function n(e){let t=e;return vn.objectLiteral(t)&&vn.uinteger(t.line)&&vn.uinteger(t.character)}e.is=n})(We||(We={}));var He;(function(e){function t(e,t,n,r){if(vn.uinteger(e)&&vn.uinteger(t)&&vn.uinteger(n)&&vn.uinteger(r)){return{start:We.create(e,t),end:We.create(n,r)}}else if(We.is(e)&&We.is(t)){return{start:e,end:t}}else{throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)}}e.create=t;function n(e){let t=e;return vn.objectLiteral(t)&&We.is(t.start)&&We.is(t.end)}e.is=n})(He||(He={}));var ze;(function(e){function t(e,t){return{uri:e,range:t}}e.create=t;function n(e){let t=e;return vn.objectLiteral(t)&&He.is(t.range)&&(vn.string(t.uri)||vn.undefined(t.uri))}e.is=n})(ze||(ze={}));var Ye;(function(e){function t(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}}e.create=t;function n(e){let t=e;return vn.objectLiteral(t)&&He.is(t.targetRange)&&vn.string(t.targetUri)&&He.is(t.targetSelectionRange)&&(He.is(t.originSelectionRange)||vn.undefined(t.originSelectionRange))}e.is=n})(Ye||(Ye={}));var qe;(function(e){function t(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}}e.create=t;function n(e){const t=e;return vn.objectLiteral(t)&&vn.numberRange(t.red,0,1)&&vn.numberRange(t.green,0,1)&&vn.numberRange(t.blue,0,1)&&vn.numberRange(t.alpha,0,1)}e.is=n})(qe||(qe={}));var Xe;(function(e){function t(e,t){return{range:e,color:t}}e.create=t;function n(e){const t=e;return vn.objectLiteral(t)&&He.is(t.range)&&qe.is(t.color)}e.is=n})(Xe||(Xe={}));var Qe;(function(e){function t(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}}e.create=t;function n(e){const t=e;return vn.objectLiteral(t)&&vn.string(t.label)&&(vn.undefined(t.textEdit)||at.is(t))&&(vn.undefined(t.additionalTextEdits)||vn.typedArray(t.additionalTextEdits,at.is))}e.is=n})(Qe||(Qe={}));var Ze;(function(e){e.Comment="comment";e.Imports="imports";e.Region="region"})(Ze||(Ze={}));var Je;(function(e){function t(e,t,n,r,i,s){const a={startLine:e,endLine:t};if(vn.defined(n)){a.startCharacter=n}if(vn.defined(r)){a.endCharacter=r}if(vn.defined(i)){a.kind=i}if(vn.defined(s)){a.collapsedText=s}return a}e.create=t;function n(e){const t=e;return vn.objectLiteral(t)&&vn.uinteger(t.startLine)&&vn.uinteger(t.startLine)&&(vn.undefined(t.startCharacter)||vn.uinteger(t.startCharacter))&&(vn.undefined(t.endCharacter)||vn.uinteger(t.endCharacter))&&(vn.undefined(t.kind)||vn.string(t.kind))}e.is=n})(Je||(Je={}));var et;(function(e){function t(e,t){return{location:e,message:t}}e.create=t;function n(e){let t=e;return vn.defined(t)&&ze.is(t.location)&&vn.string(t.message)}e.is=n})(et||(et={}));var tt;(function(e){e.Error=1;e.Warning=2;e.Information=3;e.Hint=4})(tt||(tt={}));var nt;(function(e){e.Unnecessary=1;e.Deprecated=2})(nt||(nt={}));var rt;(function(e){function t(e){const t=e;return vn.objectLiteral(t)&&vn.string(t.href)}e.is=t})(rt||(rt={}));var it;(function(e){function t(e,t,n,r,i,s){let a={range:e,message:t};if(vn.defined(n)){a.severity=n}if(vn.defined(r)){a.code=r}if(vn.defined(i)){a.source=i}if(vn.defined(s)){a.relatedInformation=s}return a}e.create=t;function n(e){var t;let n=e;return vn.defined(n)&&He.is(n.range)&&vn.string(n.message)&&(vn.number(n.severity)||vn.undefined(n.severity))&&(vn.integer(n.code)||vn.string(n.code)||vn.undefined(n.code))&&(vn.undefined(n.codeDescription)||vn.string((t=n.codeDescription)===null||t===void 0?void 0:t.href))&&(vn.string(n.source)||vn.undefined(n.source))&&(vn.undefined(n.relatedInformation)||vn.typedArray(n.relatedInformation,et.is))}e.is=n})(it||(it={}));var st;(function(e){function t(e,t,...n){let r={title:e,command:t};if(vn.defined(n)&&n.length>0){r.arguments=n}return r}e.create=t;function n(e){let t=e;return vn.defined(t)&&vn.string(t.title)&&vn.string(t.command)}e.is=n})(st||(st={}));var at;(function(e){function t(e,t){return{range:e,newText:t}}e.replace=t;function n(e,t){return{range:{start:e,end:e},newText:t}}e.insert=n;function r(e){return{range:e,newText:""}}e.del=r;function i(e){const t=e;return vn.objectLiteral(t)&&vn.string(t.newText)&&He.is(t.range)}e.is=i})(at||(at={}));var ot;(function(e){function t(e,t,n){const r={label:e};if(t!==undefined){r.needsConfirmation=t}if(n!==undefined){r.description=n}return r}e.create=t;function n(e){const t=e;return vn.objectLiteral(t)&&vn.string(t.label)&&(vn.boolean(t.needsConfirmation)||t.needsConfirmation===undefined)&&(vn.string(t.description)||t.description===undefined)}e.is=n})(ot||(ot={}));var ct;(function(e){function t(e){const t=e;return vn.string(t)}e.is=t})(ct||(ct={}));var ut;(function(e){function t(e,t,n){return{range:e,newText:t,annotationId:n}}e.replace=t;function n(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}}e.insert=n;function r(e,t){return{range:e,newText:"",annotationId:t}}e.del=r;function i(e){const t=e;return at.is(t)&&(ot.is(t.annotationId)||ct.is(t.annotationId))}e.is=i})(ut||(ut={}));var lt;(function(e){function t(e,t){return{textDocument:e,edits:t}}e.create=t;function n(e){let t=e;return vn.defined(t)&&Tt.is(t.textDocument)&&Array.isArray(t.edits)}e.is=n})(lt||(lt={}));var dt;(function(e){function t(e,t,n){let r={kind:"create",uri:e};if(t!==undefined&&(t.overwrite!==undefined||t.ignoreIfExists!==undefined)){r.options=t}if(n!==undefined){r.annotationId=n}return r}e.create=t;function n(e){let t=e;return t&&t.kind==="create"&&vn.string(t.uri)&&(t.options===undefined||(t.options.overwrite===undefined||vn.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===undefined||vn.boolean(t.options.ignoreIfExists)))&&(t.annotationId===undefined||ct.is(t.annotationId))}e.is=n})(dt||(dt={}));var ft;(function(e){function t(e,t,n,r){let i={kind:"rename",oldUri:e,newUri:t};if(n!==undefined&&(n.overwrite!==undefined||n.ignoreIfExists!==undefined)){i.options=n}if(r!==undefined){i.annotationId=r}return i}e.create=t;function n(e){let t=e;return t&&t.kind==="rename"&&vn.string(t.oldUri)&&vn.string(t.newUri)&&(t.options===undefined||(t.options.overwrite===undefined||vn.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===undefined||vn.boolean(t.options.ignoreIfExists)))&&(t.annotationId===undefined||ct.is(t.annotationId))}e.is=n})(ft||(ft={}));var ht;(function(e){function t(e,t,n){let r={kind:"delete",uri:e};if(t!==undefined&&(t.recursive!==undefined||t.ignoreIfNotExists!==undefined)){r.options=t}if(n!==undefined){r.annotationId=n}return r}e.create=t;function n(e){let t=e;return t&&t.kind==="delete"&&vn.string(t.uri)&&(t.options===undefined||(t.options.recursive===undefined||vn.boolean(t.options.recursive))&&(t.options.ignoreIfNotExists===undefined||vn.boolean(t.options.ignoreIfNotExists)))&&(t.annotationId===undefined||ct.is(t.annotationId))}e.is=n})(ht||(ht={}));var pt;(function(e){function t(e){let t=e;return t&&(t.changes!==undefined||t.documentChanges!==undefined)&&(t.documentChanges===undefined||t.documentChanges.every((e=>{if(vn.string(e.kind)){return dt.is(e)||ft.is(e)||ht.is(e)}else{return lt.is(e)}})))}e.is=t})(pt||(pt={}));class mt{constructor(e,t){this.edits=e;this.changeAnnotations=t}insert(e,t,n){let r;let i;if(n===undefined){r=at.insert(e,t)}else if(ct.is(n)){i=n;r=ut.insert(e,t,n)}else{this.assertChangeAnnotations(this.changeAnnotations);i=this.changeAnnotations.manage(n);r=ut.insert(e,t,i)}this.edits.push(r);if(i!==undefined){return i}}replace(e,t,n){let r;let i;if(n===undefined){r=at.replace(e,t)}else if(ct.is(n)){i=n;r=ut.replace(e,t,n)}else{this.assertChangeAnnotations(this.changeAnnotations);i=this.changeAnnotations.manage(n);r=ut.replace(e,t,i)}this.edits.push(r);if(i!==undefined){return i}}delete(e,t){let n;let r;if(t===undefined){n=at.del(e)}else if(ct.is(t)){r=t;n=ut.del(e,t)}else{this.assertChangeAnnotations(this.changeAnnotations);r=this.changeAnnotations.manage(t);n=ut.del(e,r)}this.edits.push(n);if(r!==undefined){return r}}add(e){this.edits.push(e)}all(){return this.edits}clear(){this.edits.splice(0,this.edits.length)}assertChangeAnnotations(e){if(e===undefined){throw new Error(`Text edit change is not configured to manage change annotations.`)}}}class gt{constructor(e){this._annotations=e===undefined?Object.create(null):e;this._counter=0;this._size=0}all(){return this._annotations}get size(){return this._size}manage(e,t){let n;if(ct.is(e)){n=e}else{n=this.nextId();t=e}if(this._annotations[n]!==undefined){throw new Error(`Id ${n} is already in use.`)}if(t===undefined){throw new Error(`No annotation provided for id ${n}`)}this._annotations[n]=t;this._size++;return n}nextId(){this._counter++;return this._counter.toString()}}class yt{constructor(e){this._textEditChanges=Object.create(null);if(e!==undefined){this._workspaceEdit=e;if(e.documentChanges){this._changeAnnotations=new gt(e.changeAnnotations);e.changeAnnotations=this._changeAnnotations.all();e.documentChanges.forEach((e=>{if(lt.is(e)){const t=new mt(e.edits,this._changeAnnotations);this._textEditChanges[e.textDocument.uri]=t}}))}else if(e.changes){Object.keys(e.changes).forEach((t=>{const n=new mt(e.changes[t]);this._textEditChanges[t]=n}))}}else{this._workspaceEdit={}}}get edit(){this.initDocumentChanges();if(this._changeAnnotations!==undefined){if(this._changeAnnotations.size===0){this._workspaceEdit.changeAnnotations=undefined}else{this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()}}return this._workspaceEdit}getTextEditChange(e){if(Tt.is(e)){this.initDocumentChanges();if(this._workspaceEdit.documentChanges===undefined){throw new Error("Workspace edit is not configured for document changes.")}const t={uri:e.uri,version:e.version};let n=this._textEditChanges[t.uri];if(!n){const e=[];const r={textDocument:t,edits:e};this._workspaceEdit.documentChanges.push(r);n=new mt(e,this._changeAnnotations);this._textEditChanges[t.uri]=n}return n}else{this.initChanges();if(this._workspaceEdit.changes===undefined){throw new Error("Workspace edit is not configured for normal text edit changes.")}let t=this._textEditChanges[e];if(!t){let n=[];this._workspaceEdit.changes[e]=n;t=new mt(n);this._textEditChanges[e]=t}return t}}initDocumentChanges(){if(this._workspaceEdit.documentChanges===undefined&&this._workspaceEdit.changes===undefined){this._changeAnnotations=new gt;this._workspaceEdit.documentChanges=[];this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()}}initChanges(){if(this._workspaceEdit.documentChanges===undefined&&this._workspaceEdit.changes===undefined){this._workspaceEdit.changes=Object.create(null)}}createFile(e,t,n){this.initDocumentChanges();if(this._workspaceEdit.documentChanges===undefined){throw new Error("Workspace edit is not configured for document changes.")}let r;if(ot.is(t)||ct.is(t)){r=t}else{n=t}let i;let s;if(r===undefined){i=dt.create(e,n)}else{s=ct.is(r)?r:this._changeAnnotations.manage(r);i=dt.create(e,n,s)}this._workspaceEdit.documentChanges.push(i);if(s!==undefined){return s}}renameFile(e,t,n,r){this.initDocumentChanges();if(this._workspaceEdit.documentChanges===undefined){throw new Error("Workspace edit is not configured for document changes.")}let i;if(ot.is(n)||ct.is(n)){i=n}else{r=n}let s;let a;if(i===undefined){s=ft.create(e,t,r)}else{a=ct.is(i)?i:this._changeAnnotations.manage(i);s=ft.create(e,t,r,a)}this._workspaceEdit.documentChanges.push(s);if(a!==undefined){return a}}deleteFile(e,t,n){this.initDocumentChanges();if(this._workspaceEdit.documentChanges===undefined){throw new Error("Workspace edit is not configured for document changes.")}let r;if(ot.is(t)||ct.is(t)){r=t}else{n=t}let i;let s;if(r===undefined){i=ht.create(e,n)}else{s=ct.is(r)?r:this._changeAnnotations.manage(r);i=ht.create(e,n,s)}this._workspaceEdit.documentChanges.push(i);if(s!==undefined){return s}}}var vt;(function(e){function t(e){return{uri:e}}e.create=t;function n(e){let t=e;return vn.defined(t)&&vn.string(t.uri)}e.is=n})(vt||(vt={}));var At;(function(e){function t(e,t){return{uri:e,version:t}}e.create=t;function n(e){let t=e;return vn.defined(t)&&vn.string(t.uri)&&vn.integer(t.version)}e.is=n})(At||(At={}));var Tt;(function(e){function t(e,t){return{uri:e,version:t}}e.create=t;function n(e){let t=e;return vn.defined(t)&&vn.string(t.uri)&&(t.version===null||vn.integer(t.version))}e.is=n})(Tt||(Tt={}));var Rt;(function(e){function t(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}}e.create=t;function n(e){let t=e;return vn.defined(t)&&vn.string(t.uri)&&vn.string(t.languageId)&&vn.integer(t.version)&&vn.string(t.text)}e.is=n})(Rt||(Rt={}));var Et;(function(e){e.PlainText="plaintext";e.Markdown="markdown";function t(t){const n=t;return n===e.PlainText||n===e.Markdown}e.is=t})(Et||(Et={}));var kt;(function(e){function t(e){const t=e;return vn.objectLiteral(e)&&Et.is(t.kind)&&vn.string(t.value)}e.is=t})(kt||(kt={}));var xt;(function(e){e.Text=1;e.Method=2;e.Function=3;e.Constructor=4;e.Field=5;e.Variable=6;e.Class=7;e.Interface=8;e.Module=9;e.Property=10;e.Unit=11;e.Value=12;e.Enum=13;e.Keyword=14;e.Snippet=15;e.Color=16;e.File=17;e.Reference=18;e.Folder=19;e.EnumMember=20;e.Constant=21;e.Struct=22;e.Event=23;e.Operator=24;e.TypeParameter=25})(xt||(xt={}));var $t;(function(e){e.PlainText=1;e.Snippet=2})($t||($t={}));var wt;(function(e){e.Deprecated=1})(wt||(wt={}));var It;(function(e){function t(e,t,n){return{newText:e,insert:t,replace:n}}e.create=t;function n(e){const t=e;return t&&vn.string(t.newText)&&He.is(t.insert)&&He.is(t.replace)}e.is=n})(It||(It={}));var St;(function(e){e.asIs=1;e.adjustIndentation=2})(St||(St={}));var Ct;(function(e){function t(e){const t=e;return t&&(vn.string(t.detail)||t.detail===undefined)&&(vn.string(t.description)||t.description===undefined)}e.is=t})(Ct||(Ct={}));var Nt;(function(e){function t(e){return{label:e}}e.create=t})(Nt||(Nt={}));var Lt;(function(e){function t(e,t){return{items:e?e:[],isIncomplete:!!t}}e.create=t})(Lt||(Lt={}));var bt;(function(e){function t(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}e.fromPlainText=t;function n(e){const t=e;return vn.string(t)||vn.objectLiteral(t)&&vn.string(t.language)&&vn.string(t.value)}e.is=n})(bt||(bt={}));var _t;(function(e){function t(e){let t=e;return!!t&&vn.objectLiteral(t)&&(kt.is(t.contents)||bt.is(t.contents)||vn.typedArray(t.contents,bt.is))&&(e.range===undefined||He.is(e.range))}e.is=t})(_t||(_t={}));var Ot;(function(e){function t(e,t){return t?{label:e,documentation:t}:{label:e}}e.create=t})(Ot||(Ot={}));var Pt;(function(e){function t(e,t,...n){let r={label:e};if(vn.defined(t)){r.documentation=t}if(vn.defined(n)){r.parameters=n}else{r.parameters=[]}return r}e.create=t})(Pt||(Pt={}));var Mt;(function(e){e.Text=1;e.Read=2;e.Write=3})(Mt||(Mt={}));var Dt;(function(e){function t(e,t){let n={range:e};if(vn.number(t)){n.kind=t}return n}e.create=t})(Dt||(Dt={}));var Ut;(function(e){e.File=1;e.Module=2;e.Namespace=3;e.Package=4;e.Class=5;e.Method=6;e.Property=7;e.Field=8;e.Constructor=9;e.Enum=10;e.Interface=11;e.Function=12;e.Variable=13;e.Constant=14;e.String=15;e.Number=16;e.Boolean=17;e.Array=18;e.Object=19;e.Key=20;e.Null=21;e.EnumMember=22;e.Struct=23;e.Event=24;e.Operator=25;e.TypeParameter=26})(Ut||(Ut={}));var Ft;(function(e){e.Deprecated=1})(Ft||(Ft={}));var Gt;(function(e){function t(e,t,n,r,i){let s={name:e,kind:t,location:{uri:r,range:n}};if(i){s.containerName=i}return s}e.create=t})(Gt||(Gt={}));var Bt;(function(e){function t(e,t,n,r){return r!==undefined?{name:e,kind:t,location:{uri:n,range:r}}:{name:e,kind:t,location:{uri:n}}}e.create=t})(Bt||(Bt={}));var Kt;(function(e){function t(e,t,n,r,i,s){let a={name:e,detail:t,kind:n,range:r,selectionRange:i};if(s!==undefined){a.children=s}return a}e.create=t;function n(e){let t=e;return t&&vn.string(t.name)&&vn.number(t.kind)&&He.is(t.range)&&He.is(t.selectionRange)&&(t.detail===undefined||vn.string(t.detail))&&(t.deprecated===undefined||vn.boolean(t.deprecated))&&(t.children===undefined||Array.isArray(t.children))&&(t.tags===undefined||Array.isArray(t.tags))}e.is=n})(Kt||(Kt={}));var jt;(function(e){e.Empty="";e.QuickFix="quickfix";e.Refactor="refactor";e.RefactorExtract="refactor.extract";e.RefactorInline="refactor.inline";e.RefactorRewrite="refactor.rewrite";e.Source="source";e.SourceOrganizeImports="source.organizeImports";e.SourceFixAll="source.fixAll"})(jt||(jt={}));var Vt;(function(e){e.Invoked=1;e.Automatic=2})(Vt||(Vt={}));var Wt;(function(e){function t(e,t,n){let r={diagnostics:e};if(t!==undefined&&t!==null){r.only=t}if(n!==undefined&&n!==null){r.triggerKind=n}return r}e.create=t;function n(e){let t=e;return vn.defined(t)&&vn.typedArray(t.diagnostics,it.is)&&(t.only===undefined||vn.typedArray(t.only,vn.string))&&(t.triggerKind===undefined||t.triggerKind===Vt.Invoked||t.triggerKind===Vt.Automatic)}e.is=n})(Wt||(Wt={}));var Ht;(function(e){function t(e,t,n){let r={title:e};let i=true;if(typeof t==="string"){i=false;r.kind=t}else if(st.is(t)){r.command=t}else{r.edit=t}if(i&&n!==undefined){r.kind=n}return r}e.create=t;function n(e){let t=e;return t&&vn.string(t.title)&&(t.diagnostics===undefined||vn.typedArray(t.diagnostics,it.is))&&(t.kind===undefined||vn.string(t.kind))&&(t.edit!==undefined||t.command!==undefined)&&(t.command===undefined||st.is(t.command))&&(t.isPreferred===undefined||vn.boolean(t.isPreferred))&&(t.edit===undefined||pt.is(t.edit))}e.is=n})(Ht||(Ht={}));var zt;(function(e){function t(e,t){let n={range:e};if(vn.defined(t)){n.data=t}return n}e.create=t;function n(e){let t=e;return vn.defined(t)&&He.is(t.range)&&(vn.undefined(t.command)||st.is(t.command))}e.is=n})(zt||(zt={}));var Yt;(function(e){function t(e,t){return{tabSize:e,insertSpaces:t}}e.create=t;function n(e){let t=e;return vn.defined(t)&&vn.uinteger(t.tabSize)&&vn.boolean(t.insertSpaces)}e.is=n})(Yt||(Yt={}));var qt;(function(e){function t(e,t,n){return{range:e,target:t,data:n}}e.create=t;function n(e){let t=e;return vn.defined(t)&&He.is(t.range)&&(vn.undefined(t.target)||vn.string(t.target))}e.is=n})(qt||(qt={}));var Xt;(function(e){function t(e,t){return{range:e,parent:t}}e.create=t;function n(t){let n=t;return vn.objectLiteral(n)&&He.is(n.range)&&(n.parent===undefined||e.is(n.parent))}e.is=n})(Xt||(Xt={}));var Qt;(function(e){e["namespace"]="namespace";e["type"]="type";e["class"]="class";e["enum"]="enum";e["interface"]="interface";e["struct"]="struct";e["typeParameter"]="typeParameter";e["parameter"]="parameter";e["variable"]="variable";e["property"]="property";e["enumMember"]="enumMember";e["event"]="event";e["function"]="function";e["method"]="method";e["macro"]="macro";e["keyword"]="keyword";e["modifier"]="modifier";e["comment"]="comment";e["string"]="string";e["number"]="number";e["regexp"]="regexp";e["operator"]="operator";e["decorator"]="decorator"})(Qt||(Qt={}));var Zt;(function(e){e["declaration"]="declaration";e["definition"]="definition";e["readonly"]="readonly";e["static"]="static";e["deprecated"]="deprecated";e["abstract"]="abstract";e["async"]="async";e["modification"]="modification";e["documentation"]="documentation";e["defaultLibrary"]="defaultLibrary"})(Zt||(Zt={}));var Jt;(function(e){function t(e){const t=e;return vn.objectLiteral(t)&&(t.resultId===undefined||typeof t.resultId==="string")&&Array.isArray(t.data)&&(t.data.length===0||typeof t.data[0]==="number")}e.is=t})(Jt||(Jt={}));var en;(function(e){function t(e,t){return{range:e,text:t}}e.create=t;function n(e){const t=e;return t!==undefined&&t!==null&&He.is(t.range)&&vn.string(t.text)}e.is=n})(en||(en={}));var tn;(function(e){function t(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}}e.create=t;function n(e){const t=e;return t!==undefined&&t!==null&&He.is(t.range)&&vn.boolean(t.caseSensitiveLookup)&&(vn.string(t.variableName)||t.variableName===undefined)}e.is=n})(tn||(tn={}));var nn;(function(e){function t(e,t){return{range:e,expression:t}}e.create=t;function n(e){const t=e;return t!==undefined&&t!==null&&He.is(t.range)&&(vn.string(t.expression)||t.expression===undefined)}e.is=n})(nn||(nn={}));var rn;(function(e){function t(e,t){return{frameId:e,stoppedLocation:t}}e.create=t;function n(e){const t=e;return vn.defined(t)&&He.is(e.stoppedLocation)}e.is=n})(rn||(rn={}));var sn;(function(e){e.Type=1;e.Parameter=2;function t(e){return e===1||e===2}e.is=t})(sn||(sn={}));var an;(function(e){function t(e){return{value:e}}e.create=t;function n(e){const t=e;return vn.objectLiteral(t)&&(t.tooltip===undefined||vn.string(t.tooltip)||kt.is(t.tooltip))&&(t.location===undefined||ze.is(t.location))&&(t.command===undefined||st.is(t.command))}e.is=n})(an||(an={}));var on;(function(e){function t(e,t,n){const r={position:e,label:t};if(n!==undefined){r.kind=n}return r}e.create=t;function n(e){const t=e;return vn.objectLiteral(t)&&We.is(t.position)&&(vn.string(t.label)||vn.typedArray(t.label,an.is))&&(t.kind===undefined||sn.is(t.kind))&&t.textEdits===undefined||vn.typedArray(t.textEdits,at.is)&&(t.tooltip===undefined||vn.string(t.tooltip)||kt.is(t.tooltip))&&(t.paddingLeft===undefined||vn.boolean(t.paddingLeft))&&(t.paddingRight===undefined||vn.boolean(t.paddingRight))}e.is=n})(on||(on={}));var cn;(function(e){function t(e){return{kind:"snippet",value:e}}e.createSnippet=t})(cn||(cn={}));var un;(function(e){function t(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}}e.create=t})(un||(un={}));var ln;(function(e){function t(e){return{items:e}}e.create=t})(ln||(ln={}));var dn;(function(e){e.Invoked=0;e.Automatic=1})(dn||(dn={}));var fn;(function(e){function t(e,t){return{range:e,text:t}}e.create=t})(fn||(fn={}));var hn;(function(e){function t(e,t){return{triggerKind:e,selectedCompletionInfo:t}}e.create=t})(hn||(hn={}));var pn;(function(e){function t(e){const t=e;return vn.objectLiteral(t)&&Ke.is(t.uri)&&vn.string(t.name)}e.is=t})(pn||(pn={}));const mn=null&&["\n","\r\n","\r"];var gn;(function(e){function t(e,t,n,r){return new yn(e,t,n,r)}e.create=t;function n(e){let t=e;return vn.defined(t)&&vn.string(t.uri)&&(vn.undefined(t.languageId)||vn.string(t.languageId))&&vn.uinteger(t.lineCount)&&vn.func(t.getText)&&vn.func(t.positionAt)&&vn.func(t.offsetAt)?true:false}e.is=n;function r(e,t){let n=e.getText();let r=i(t,((e,t)=>{let n=e.range.start.line-t.range.start.line;if(n===0){return e.range.start.character-t.range.start.character}return n}));let s=n.length;for(let i=r.length-1;i>=0;i--){let t=r[i];let a=e.offsetAt(t.range.start);let o=e.offsetAt(t.range.end);if(o<=s){n=n.substring(0,a)+t.newText+n.substring(o,n.length)}else{throw new Error("Overlapping edit")}s=a}return n}e.applyEdits=r;function i(e,t){if(e.length<=1){return e}const n=e.length/2|0;const r=e.slice(0,n);const s=e.slice(n);i(r,t);i(s,t);let a=0;let o=0;let c=0;while(a<r.length&&o<s.length){let n=t(r[a],s[o]);if(n<=0){e[c++]=r[a++]}else{e[c++]=s[o++]}}while(a<r.length){e[c++]=r[a++]}while(o<s.length){e[c++]=s[o++]}return e}})(gn||(gn={}));class yn{constructor(e,t,n,r){this._uri=e;this._languageId=t;this._version=n;this._content=r;this._lineOffsets=undefined}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start);let n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text;this._version=t;this._lineOffsets=undefined}getLineOffsets(){if(this._lineOffsets===undefined){let e=[];let t=this._content;let n=true;for(let r=0;r<t.length;r++){if(n){e.push(r);n=false}let i=t.charAt(r);n=i==="\r"||i==="\n";if(i==="\r"&&r+1<t.length&&t.charAt(r+1)==="\n"){r++}}if(n&&t.length>0){e.push(t.length)}this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets();let n=0,r=t.length;if(r===0){return We.create(0,e)}while(n<r){let i=Math.floor((n+r)/2);if(t[i]>e){r=i}else{n=i+1}}let i=n-1;return We.create(i,e-t[i])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length){return this._content.length}else if(e.line<0){return 0}let n=t[e.line];let r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}}var vn;(function(e){const t=Object.prototype.toString;function n(e){return typeof e!=="undefined"}e.defined=n;function r(e){return typeof e==="undefined"}e.undefined=r;function i(e){return e===true||e===false}e.boolean=i;function s(e){return t.call(e)==="[object String]"}e.string=s;function a(e){return t.call(e)==="[object Number]"}e.number=a;function o(e,n,r){return t.call(e)==="[object Number]"&&n<=e&&e<=r}e.numberRange=o;function c(e){return t.call(e)==="[object Number]"&&-2147483648<=e&&e<=2147483647}e.integer=c;function u(e){return t.call(e)==="[object Number]"&&0<=e&&e<=2147483647}e.uinteger=u;function l(e){return t.call(e)==="[object Function]"}e.func=l;function d(e){return e!==null&&typeof e==="object"}e.objectLiteral=d;function f(e,t){return Array.isArray(e)&&e.every(t)}e.typedArray=f})(vn||(vn={}));class An{constructor(){this.nodeStack=[]}get current(){var e;return(e=this.nodeStack[this.nodeStack.length-1])!==null&&e!==void 0?e:this.rootNode}buildRootNode(e){this.rootNode=new xn(e);this.rootNode.root=this.rootNode;this.nodeStack=[this.rootNode];return this.rootNode}buildCompositeNode(e){const t=new En;t.grammarSource=e;t.root=this.rootNode;this.current.content.push(t);this.nodeStack.push(t);return t}buildLeafNode(e,t){const n=new Rn(e.startOffset,e.image.length,(0,r.wf)(e),e.tokenType,!t);n.grammarSource=t;n.root=this.rootNode;this.current.content.push(n);return n}removeNode(e){const t=e.container;if(t){const n=t.content.indexOf(e);if(n>=0){t.content.splice(n,1)}}}addHiddenNodes(e){const t=[];for(const s of e){const e=new Rn(s.startOffset,s.image.length,(0,r.wf)(s),s.tokenType,true);e.root=this.rootNode;t.push(e)}let n=this.current;let i=false;if(n.content.length>0){n.content.push(...t);return}while(n.container){const e=n.container.content.indexOf(n);if(e>0){n.container.content.splice(e,0,...t);i=true;break}n=n.container}if(!i){this.rootNode.content.unshift(...t)}}construct(e){const t=this.current;if(typeof e.$type==="string"){this.current.astNode=e}e.$cstNode=t;const n=this.nodeStack.pop();if((n===null||n===void 0?void 0:n.content.length)===0){this.removeNode(n)}}}class Tn{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return false}get astNode(){var e,t;const n=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)==="string"?this._astNode:(t=this.container)===null||t===void 0?void 0:t.astNode;if(!n){throw new Error("This node has no associated AST element")}return n}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}}class Rn extends Tn{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,n,r,i=false){super();this._hidden=i;this._offset=e;this._tokenType=r;this._length=t;this._range=n}}class En extends Tn{constructor(){super(...arguments);this.content=new kn(this)}get children(){return this.content}get offset(){var e,t;return(t=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&t!==void 0?t:0}get length(){return this.end-this.offset}get end(){var e,t;return(t=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&t!==void 0?t:0}get range(){const e=this.firstNonHiddenNode;const t=this.lastNonHiddenNode;if(e&&t){if(this._rangeCache===undefined){const{range:n}=e;const{range:r}=t;this._rangeCache={start:n.start,end:r.end.line<n.start.line?n.start:r.end}}return this._rangeCache}else{return{start:We.create(0,0),end:We.create(0,0)}}}get firstNonHiddenNode(){for(const e of this.content){if(!e.hidden){return e}}return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){const t=this.content[e];if(!t.hidden){return t}}return this.content[this.content.length-1]}}class kn extends Array{constructor(e){super();this.parent=e;Object.setPrototypeOf(this,kn.prototype)}push(...e){this.addParents(e);return super.push(...e)}unshift(...e){this.addParents(e);return super.unshift(...e)}splice(e,t,...n){this.addParents(n);return super.splice(e,t,...n)}addParents(e){for(const t of e){t.container=this.parent}}}class xn extends En{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super();this._text="";this._text=e!==null&&e!==void 0?e:""}}const $n=Symbol("Datatype");function wn(e){return e.$type===$n}const In="​";const Sn=e=>e.endsWith(In)?e:e+In;class Cn{constructor(e){this._unorderedGroups=new Map;this.allRules=new Map;this.lexer=e.parser.Lexer;const t=this.lexer.definition;const n=e.LanguageMetaData.mode==="production";this.wrapper=new Pn(t,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:n,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}}class Nn extends Cn{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e);this.nodeBuilder=new An;this.stack=[];this.assignmentMap=new Map;this.linker=e.references.Linker;this.converter=e.parser.ValueConverter;this.astReflection=e.shared.AstReflection}rule(e,t){const n=this.computeRuleType(e);const r=this.wrapper.DEFINE_RULE(Sn(e.name),this.startImplementation(n,t).bind(this));this.allRules.set(e.name,r);if(e.entry){this.mainRule=r}return r}computeRuleType(e){if(e.fragment){return undefined}else if((0,i.Xq)(e)){return $n}else{const t=(0,i.PV)(e);return t!==null&&t!==void 0?t:e.name}}parse(e,t={}){this.nodeBuilder.buildRootNode(e);const n=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=n.tokens;const r=t.rule?this.allRules.get(t.rule):this.mainRule;if(!r){throw new Error(t.rule?`No rule found with name '${t.rule}'`:"No main rule available.")}const i=r.call(this.wrapper,{});this.nodeBuilder.addHiddenNodes(n.hidden);this.unorderedGroups.clear();this.lexerResult=undefined;return{value:i,lexerErrors:n.errors,lexerReport:n.report,parserErrors:this.wrapper.errors}}startImplementation(e,t){return n=>{const r=!this.isRecording()&&e!==undefined;if(r){const t={$type:e};this.stack.push(t);if(e===$n){t.value=""}}let i;try{i=t(n)}catch(s){i=undefined}if(i===undefined&&r){i=this.construct()}return i}}extractHiddenTokens(e){const t=this.lexerResult.hidden;if(!t.length){return[]}const n=e.startOffset;for(let r=0;r<t.length;r++){const e=t[r];if(e.startOffset>n){return t.splice(0,r)}}return t.splice(0,t.length)}consume(e,t,n){const r=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(r)){const e=this.extractHiddenTokens(r);this.nodeBuilder.addHiddenNodes(e);const t=this.nodeBuilder.buildLeafNode(r,n);const{assignment:i,isCrossRef:s}=this.getAssignment(n);const o=this.current;if(i){const e=(0,a.wb)(n)?r.image:this.converter.convert(r.image,t);this.assign(i.operator,i.feature,e,t,s)}else if(wn(o)){let e=r.image;if(!(0,a.wb)(n)){e=this.converter.convert(e,t).toString()}o.value+=e}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset==="number"&&!isNaN(e.endOffset)}subrule(e,t,n,r,i){let s;if(!this.isRecording()&&!n){s=this.nodeBuilder.buildCompositeNode(r)}const a=this.wrapper.wrapSubrule(e,t,i);if(!this.isRecording()&&s&&s.length>0){this.performSubruleAssignment(a,r,s)}}performSubruleAssignment(e,t,n){const{assignment:r,isCrossRef:i}=this.getAssignment(t);if(r){this.assign(r.operator,r.feature,e,n,i)}else if(!r){const t=this.current;if(wn(t)){t.value+=e.toString()}else if(typeof e==="object"&&e){const n=this.assignWithoutOverride(e,t);const r=n;this.stack.pop();this.stack.push(r)}}}action(e,t){if(!this.isRecording()){let n=this.current;if(t.feature&&t.operator){n=this.construct();this.nodeBuilder.removeNode(n.$cstNode);const r=this.nodeBuilder.buildCompositeNode(t);r.content.push(n.$cstNode);const i={$type:e};this.stack.push(i);this.assign(t.operator,t.feature,n,n.$cstNode,false)}else{n.$type=e}}}construct(){if(this.isRecording()){return undefined}const e=this.current;(0,Ge.SD)(e);this.nodeBuilder.construct(e);this.stack.pop();if(wn(e)){return this.converter.convert(e.value,e.$cstNode)}else{(0,Ge.OP)(this.astReflection,e)}return e}getAssignment(e){if(!this.assignmentMap.has(e)){const t=(0,Ge.XG)(e,a.wh);this.assignmentMap.set(e,{assignment:t,isCrossRef:t?(0,a._c)(t.terminal):false})}return this.assignmentMap.get(e)}assign(e,t,n,r,i){const s=this.current;let a;if(i&&typeof n==="string"){a=this.linker.buildReference(s,t,r,n)}else{a=n}switch(e){case"=":{s[t]=a;break}case"?=":{s[t]=true;break}case"+=":{if(!Array.isArray(s[t])){s[t]=[]}s[t].push(a)}}}assignWithoutOverride(e,t){for(const[r,i]of Object.entries(t)){const t=e[r];if(t===undefined){e[r]=i}else if(Array.isArray(t)&&Array.isArray(i)){i.push(...t);e[r]=i}}const n=e.$cstNode;if(n){n.astNode=undefined;e.$cstNode=undefined}return e}get definitionErrors(){return this.wrapper.definitionErrors}}class Ln{buildMismatchTokenMessage(e){return c.my.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return c.my.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return c.my.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return c.my.buildEarlyExitMessage(e)}}class bn extends Ln{buildMismatchTokenMessage({expected:e,actual:t}){const n=e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`;return`Expecting ${n} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}}class _n extends Cn{constructor(){super(...arguments);this.tokens=[];this.elementStack=[];this.lastElementStack=[];this.nextTokenIndex=0;this.stackSize=0}action(){}construct(){return undefined}parse(e){this.resetState();const t=this.lexer.tokenize(e,{mode:"partial"});this.tokens=t.tokens;this.wrapper.input=[...this.tokens];this.mainRule.call(this.wrapper,{});this.unorderedGroups.clear();return{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){const n=this.wrapper.DEFINE_RULE(Sn(e.name),this.startImplementation(t).bind(this));this.allRules.set(e.name,n);if(e.entry){this.mainRule=n}return n}resetState(){this.elementStack=[];this.lastElementStack=[];this.nextTokenIndex=0;this.stackSize=0}startImplementation(e){return t=>{const n=this.keepStackSize();try{e(t)}finally{this.resetStackSize(n)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){const e=this.elementStack.length;this.stackSize=e;return e}resetStackSize(e){this.removeUnexpectedElements();this.stackSize=e}consume(e,t,n){this.wrapper.wrapConsume(e,t);if(!this.isRecording()){this.lastElementStack=[...this.elementStack,n];this.nextTokenIndex=this.currIdx+1}}subrule(e,t,n,r,i){this.before(r);this.wrapper.wrapSubrule(e,t,i);this.after(r)}before(e){if(!this.isRecording()){this.elementStack.push(e)}}after(e){if(!this.isRecording()){const t=this.elementStack.lastIndexOf(e);if(t>=0){this.elementStack.splice(t)}}}get currIdx(){return this.wrapper.currIdx}}const On={recoveryEnabled:true,nodeLocationTracking:"full",skipValidations:true,errorMessageProvider:new bn};class Pn extends c.jr{constructor(e,t){const n=t&&"maxLookahead"in t;super(e,Object.assign(Object.assign(Object.assign({},On),{lookaheadStrategy:n?new c.T6({maxLookahead:t.maxLookahead}):new pe({logging:t.skipValidations?()=>{}:undefined})}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,n){return this.subrule(e,t,{ARGS:[n]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}}var Mn=n(73101);var Dn=n(64386);function Un(e,t,n){const r={parser:t,tokens:n,ruleNames:new Map};Fn(r,e);return t}function Fn(e,t){const n=(0,i.YV)(t,false);const r=(0,Dn.Td)(t.rules).filter(a.s7).filter((e=>n.has(e)));for(const i of r){const t=Object.assign(Object.assign({},e),{consume:1,optional:1,subrule:1,many:1,or:1});e.parser.rule(i,Gn(t,i.definition))}}function Gn(e,t,n=false){let r;if((0,a.wb)(t)){r=Xn(e,t)}else if((0,a.ve)(t)){r=Bn(e,t)}else if((0,a.wh)(t)){r=Gn(e,t.terminal)}else if((0,a._c)(t)){r=qn(e,t)}else if((0,a.$g)(t)){r=Kn(e,t)}else if((0,a.jp)(t)){r=Wn(e,t)}else if((0,a.cY)(t)){r=Hn(e,t)}else if((0,a.IZ)(t)){r=zn(e,t)}else if((0,a.FO)(t)){const n=e.consume++;r=()=>e.parser.consume(n,c.LT,t)}else{throw new Mn.W(t.$cstNode,`Unexpected element type: ${t.$type}`)}return Qn(e,n?undefined:Yn(t),r,t.cardinality)}function Bn(e,t){const n=(0,i.Uz)(t);return()=>e.parser.action(n,t)}function Kn(e,t){const n=t.rule.ref;if((0,a.s7)(n)){const r=e.subrule++;const i=n.fragment;const s=t.arguments.length>0?jn(n,t.arguments):()=>({});return a=>e.parser.subrule(r,Zn(e,n),i,t,s(a))}else if((0,a.rE)(n)){const r=e.consume++;const i=er(e,n.name);return()=>e.parser.consume(r,i,t)}else if(!n){throw new Mn.W(t.$cstNode,`Undefined rule: ${t.rule.$refText}`)}else{(0,Mn.d)(n)}}function jn(e,t){const n=t.map((e=>Vn(e.value)));return t=>{const r={};for(let i=0;i<n.length;i++){const s=e.parameters[i];const a=n[i];r[s.name]=a(t)}return r}}function Vn(e){if((0,a.RP)(e)){const t=Vn(e.left);const n=Vn(e.right);return e=>t(e)||n(e)}else if((0,a.Tu)(e)){const t=Vn(e.left);const n=Vn(e.right);return e=>t(e)&&n(e)}else if((0,a.Ct)(e)){const t=Vn(e.value);return e=>!t(e)}else if((0,a.TF)(e)){const t=e.parameter.ref.name;return e=>e!==undefined&&e[t]===true}else if((0,a.Cz)(e)){const t=Boolean(e.true);return()=>t}(0,Mn.d)(e)}function Wn(e,t){if(t.elements.length===1){return Gn(e,t.elements[0])}else{const n=[];for(const i of t.elements){const t={ALT:Gn(e,i,true)};const r=Yn(i);if(r){t.GATE=Vn(r)}n.push(t)}const r=e.or++;return t=>e.parser.alternatives(r,n.map((e=>{const n={ALT:()=>e.ALT(t)};const r=e.GATE;if(r){n.GATE=()=>r(t)}return n})))}}function Hn(e,t){if(t.elements.length===1){return Gn(e,t.elements[0])}const n=[];for(const o of t.elements){const t={ALT:Gn(e,o,true)};const r=Yn(o);if(r){t.GATE=Vn(r)}n.push(t)}const r=e.or++;const i=(e,t)=>{const n=t.getRuleStack().join("-");return`uGroup_${e}_${n}`};const s=t=>e.parser.alternatives(r,n.map(((n,s)=>{const a={ALT:()=>true};const o=e.parser;a.ALT=()=>{n.ALT(t);if(!o.isRecording()){const e=i(r,o);if(!o.unorderedGroups.get(e)){o.unorderedGroups.set(e,[])}const t=o.unorderedGroups.get(e);if(typeof(t===null||t===void 0?void 0:t[s])==="undefined"){t[s]=true}}};const c=n.GATE;if(c){a.GATE=()=>c(t)}else{a.GATE=()=>{const e=o.unorderedGroups.get(i(r,o));const t=!(e===null||e===void 0?void 0:e[s]);return t}}return a})));const a=Qn(e,Yn(t),s,"*");return t=>{a(t);if(!e.parser.isRecording()){e.parser.unorderedGroups.delete(i(r,e.parser))}}}function zn(e,t){const n=t.elements.map((t=>Gn(e,t)));return e=>n.forEach((t=>t(e)))}function Yn(e){if((0,a.IZ)(e)){return e.guardCondition}return undefined}function qn(e,t,n=t.terminal){if(!n){if(!t.type.ref){throw new Error("Could not resolve reference to type: "+t.type.$refText)}const n=(0,i.U5)(t.type.ref);const r=n===null||n===void 0?void 0:n.terminal;if(!r){throw new Error("Could not find name assignment for type: "+(0,i.Uz)(t.type.ref))}return qn(e,t,r)}else if((0,a.$g)(n)&&(0,a.s7)(n.rule.ref)){const r=n.rule.ref;const i=e.subrule++;return n=>e.parser.subrule(i,Zn(e,r),false,t,n)}else if((0,a.$g)(n)&&(0,a.rE)(n.rule.ref)){const r=e.consume++;const i=er(e,n.rule.ref.name);return()=>e.parser.consume(r,i,t)}else if((0,a.wb)(n)){const r=e.consume++;const i=er(e,n.value);return()=>e.parser.consume(r,i,t)}else{throw new Error("Could not build cross reference parser")}}function Xn(e,t){const n=e.consume++;const r=e.tokens[t.value];if(!r){throw new Error("Could not find token for keyword: "+t.value)}return()=>e.parser.consume(n,r,t)}function Qn(e,t,n,r){const i=t&&Vn(t);if(!r){if(i){const t=e.or++;return r=>e.parser.alternatives(t,[{ALT:()=>n(r),GATE:()=>i(r)},{ALT:(0,c.mT)(),GATE:()=>!i(r)}])}else{return n}}if(r==="*"){const t=e.many++;return r=>e.parser.many(t,{DEF:()=>n(r),GATE:i?()=>i(r):undefined})}else if(r==="+"){const t=e.many++;if(i){const r=e.or++;return s=>e.parser.alternatives(r,[{ALT:()=>e.parser.atLeastOne(t,{DEF:()=>n(s)}),GATE:()=>i(s)},{ALT:(0,c.mT)(),GATE:()=>!i(s)}])}else{return r=>e.parser.atLeastOne(t,{DEF:()=>n(r)})}}else if(r==="?"){const t=e.optional++;return r=>e.parser.optional(t,{DEF:()=>n(r),GATE:i?()=>i(r):undefined})}else{(0,Mn.d)(r)}}function Zn(e,t){const n=Jn(e,t);const r=e.parser.getRule(n);if(!r)throw new Error(`Rule "${n}" not found."`);return r}function Jn(e,t){if((0,a.s7)(t)){return t.name}else if(e.ruleNames.has(t)){return e.ruleNames.get(t)}else{let n=t;let r=n.$container;let i=t.$type;while(!(0,a.s7)(r)){if((0,a.IZ)(r)||(0,a.jp)(r)||(0,a.cY)(r)){const e=r.elements.indexOf(n);i=e.toString()+":"+i}n=r;r=r.$container}const s=r;i=s.name+":"+i;e.ruleNames.set(t,i);return i}}function er(e,t){const n=e.tokens[t];if(!n)throw new Error(`Token "${t}" not found."`);return n}function tr(e){const t=e.Grammar;const n=e.parser.Lexer;const r=new _n(e);Un(t,r,n.definition);r.finalize();return r}function nr(e){const t=rr(e);t.finalize();return t}function rr(e){const t=e.Grammar;const n=e.parser.Lexer;const r=new Nn(e);return Un(t,r,n.definition)}var ir=n(25355);var sr=n(14480);var ar=n(59850);var or=n(64032);function cr(){return new Promise((e=>{if(typeof setImmediate==="undefined"){setTimeout(e,0)}else{setImmediate(e)}}))}let ur=0;let lr=10;function dr(){ur=performance.now();return new ar.CancellationTokenSource}function fr(e){lr=e}const hr=Symbol("OperationCancelled");function pr(e){return e===hr}async function mr(e){if(e===ar.CancellationToken.None){return}const t=performance.now();if(t-ur>=lr){ur=t;await cr();ur=performance.now()}if(e.isCancellationRequested){throw hr}}class gr{constructor(){this.promise=new Promise(((e,t)=>{this.resolve=t=>{e(t);return this};this.reject=e=>{t(e);return this}}))}}class yr{constructor(e,t,n,r){this._uri=e;this._languageId=t;this._version=n;this._content=r;this._lineOffsets=undefined}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start);const n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){for(const n of e){if(yr.isIncremental(n)){const e=Er(n.range);const t=this.offsetAt(e.start);const r=this.offsetAt(e.end);this._content=this._content.substring(0,t)+n.text+this._content.substring(r,this._content.length);const i=Math.max(e.start.line,0);const s=Math.max(e.end.line,0);let a=this._lineOffsets;const o=Tr(n.text,false,t);if(s-i===o.length){for(let e=0,t=o.length;e<t;e++){a[e+i+1]=o[e]}}else{if(o.length<1e4){a.splice(i+1,s-i,...o)}else{this._lineOffsets=a=a.slice(0,i+1).concat(o,a.slice(s+1))}}const c=n.text.length-(r-t);if(c!==0){for(let e=i+1+o.length,t=a.length;e<t;e++){a[e]=a[e]+c}}}else if(yr.isFull(n)){this._content=n.text;this._lineOffsets=undefined}else{throw new Error("Unknown change event received")}}this._version=t}getLineOffsets(){if(this._lineOffsets===undefined){this._lineOffsets=Tr(this._content,true)}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let n=0,r=t.length;if(r===0){return{line:0,character:e}}while(n<r){const i=Math.floor((n+r)/2);if(t[i]>e){r=i}else{n=i+1}}const i=n-1;e=this.ensureBeforeEOL(e,t[i]);return{line:i,character:e-t[i]}}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length){return this._content.length}else if(e.line<0){return 0}const n=t[e.line];if(e.character<=0){return n}const r=e.line+1<t.length?t[e.line+1]:this._content.length;const i=Math.min(n+e.character,r);return this.ensureBeforeEOL(i,n)}ensureBeforeEOL(e,t){while(e>t&&Rr(this._content.charCodeAt(e-1))){e--}return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const t=e;return t!==undefined&&t!==null&&typeof t.text==="string"&&t.range!==undefined&&(t.rangeLength===undefined||typeof t.rangeLength==="number")}static isFull(e){const t=e;return t!==undefined&&t!==null&&typeof t.text==="string"&&t.range===undefined&&t.rangeLength===undefined}}var vr;(function(e){function t(e,t,n,r){return new yr(e,t,n,r)}e.create=t;function n(e,t,n){if(e instanceof yr){e.update(t,n);return e}else{throw new Error("TextDocument.update: document must be created by TextDocument.create")}}e.update=n;function r(e,t){const n=e.getText();const r=Ar(t.map(kr),((e,t)=>{const n=e.range.start.line-t.range.start.line;if(n===0){return e.range.start.character-t.range.start.character}return n}));let i=0;const s=[];for(const a of r){const t=e.offsetAt(a.range.start);if(t<i){throw new Error("Overlapping edit")}else if(t>i){s.push(n.substring(i,t))}if(a.newText.length){s.push(a.newText)}i=e.offsetAt(a.range.end)}s.push(n.substr(i));return s.join("")}e.applyEdits=r})(vr||(vr={}));function Ar(e,t){if(e.length<=1){return e}const n=e.length/2|0;const r=e.slice(0,n);const i=e.slice(n);Ar(r,t);Ar(i,t);let s=0;let a=0;let o=0;while(s<r.length&&a<i.length){const n=t(r[s],i[a]);if(n<=0){e[o++]=r[s++]}else{e[o++]=i[a++]}}while(s<r.length){e[o++]=r[s++]}while(a<i.length){e[o++]=i[a++]}return e}function Tr(e,t,n=0){const r=t?[n]:[];for(let i=0;i<e.length;i++){const t=e.charCodeAt(i);if(Rr(t)){if(t===13&&i+1<e.length&&e.charCodeAt(i+1)===10){i++}r.push(n+i+1)}}return r}function Rr(e){return e===13||e===10}function Er(e){const t=e.start;const n=e.end;if(t.line>n.line||t.line===n.line&&t.character>n.character){return{start:n,end:t}}return e}function kr(e){const t=Er(e.range);if(t!==e.range){return{newText:e.newText,range:t}}return e}var xr=n(14247);var $r;(function(e){e[e["Changed"]=0]="Changed";e[e["Parsed"]=1]="Parsed";e[e["IndexedContent"]=2]="IndexedContent";e[e["ComputedScopes"]=3]="ComputedScopes";e[e["Linked"]=4]="Linked";e[e["IndexedReferences"]=5]="IndexedReferences";e[e["Validated"]=6]="Validated"})($r||($r={}));class wr{constructor(e){this.serviceRegistry=e.ServiceRegistry;this.textDocuments=e.workspace.TextDocuments;this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,t=ar.CancellationToken.None){const n=await this.fileSystemProvider.readFile(e);return this.createAsync(e,n,t)}fromTextDocument(e,t,n){t=t!==null&&t!==void 0?t:xr.r.parse(e.uri);if(ar.CancellationToken.is(n)){return this.createAsync(t,e,n)}else{return this.create(t,e,n)}}fromString(e,t,n){if(ar.CancellationToken.is(n)){return this.createAsync(t,e,n)}else{return this.create(t,e,n)}}fromModel(e,t){return this.create(t,{$model:e})}create(e,t,n){if(typeof t==="string"){const r=this.parse(e,t,n);return this.createLangiumDocument(r,e,undefined,t)}else if("$model"in t){const n={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(n,e)}else{const r=this.parse(e,t.getText(),n);return this.createLangiumDocument(r,e,t)}}async createAsync(e,t,n){if(typeof t==="string"){const r=await this.parseAsync(e,t,n);return this.createLangiumDocument(r,e,undefined,t)}else{const r=await this.parseAsync(e,t.getText(),n);return this.createLangiumDocument(r,e,t)}}createLangiumDocument(e,t,n,r){let i;if(n){i={parseResult:e,uri:t,state:$r.Parsed,references:[],textDocument:n}}else{const n=this.createTextDocumentGetter(t,r);i={parseResult:e,uri:t,state:$r.Parsed,references:[],get textDocument(){return n()}}}e.value.$document=i;return i}async update(e,t){var n,r;const i=(n=e.parseResult.value.$cstNode)===null||n===void 0?void 0:n.root.fullText;const s=(r=this.textDocuments)===null||r===void 0?void 0:r.get(e.uri.toString());const a=s?s.getText():await this.fileSystemProvider.readFile(e.uri);if(s){Object.defineProperty(e,"textDocument",{value:s})}else{const t=this.createTextDocumentGetter(e.uri,a);Object.defineProperty(e,"textDocument",{get:t})}if(i!==a){e.parseResult=await this.parseAsync(e.uri,a,t);e.parseResult.value.$document=e}e.state=$r.Parsed;return e}parse(e,t,n){const r=this.serviceRegistry.getServices(e);return r.parser.LangiumParser.parse(t,n)}parseAsync(e,t,n){const r=this.serviceRegistry.getServices(e);return r.parser.AsyncParser.parse(t,n)}createTextDocumentGetter(e,t){const n=this.serviceRegistry;let r=undefined;return()=>r!==null&&r!==void 0?r:r=vr.create(e.toString(),n.getServices(e).LanguageMetaData.languageId,0,t!==null&&t!==void 0?t:"")}}class Ir{constructor(e){this.documentMap=new Map;this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory;this.serviceRegistry=e.ServiceRegistry}get all(){return(0,Dn.Td)(this.documentMap.values())}addDocument(e){const t=e.uri.toString();if(this.documentMap.has(t)){throw new Error(`A document with the URI '${t}' is already present.`)}this.documentMap.set(t,e)}getDocument(e){const t=e.toString();return this.documentMap.get(t)}async getOrCreateDocument(e,t){let n=this.getDocument(e);if(n){return n}n=await this.langiumDocumentFactory.fromUri(e,t);this.addDocument(n);return n}createDocument(e,t,n){if(n){return this.langiumDocumentFactory.fromString(t,e,n).then((e=>{this.addDocument(e);return e}))}else{const n=this.langiumDocumentFactory.fromString(t,e);this.addDocument(n);return n}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){const t=e.toString();const n=this.documentMap.get(t);if(n){const t=this.serviceRegistry.getServices(e).references.Linker;t.unlink(n);n.state=$r.Changed;n.precomputedScopes=undefined;n.diagnostics=undefined}return n}deleteDocument(e){const t=e.toString();const n=this.documentMap.get(t);if(n){n.state=$r.Changed;this.documentMap.delete(t)}return n}}const Sr=Symbol("ref_resolving");class Cr{constructor(e){this.reflection=e.shared.AstReflection;this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments;this.scopeProvider=e.references.ScopeProvider;this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,t=ar.CancellationToken.None){for(const n of(0,Ge.jm)(e.parseResult.value)){await mr(t);(0,Ge.DM)(n).forEach((t=>this.doLink(t,e)))}}doLink(e,t){var n;const r=e.reference;if(r._ref===undefined){r._ref=Sr;try{const t=this.getCandidate(e);if((0,or.Zl)(t)){r._ref=t}else{r._nodeDescription=t;if(this.langiumDocuments().hasDocument(t.documentUri)){const n=this.loadAstNode(t);r._ref=n!==null&&n!==void 0?n:this.createLinkingError(e,t)}else{r._ref=undefined}}}catch(i){console.error(`An error occurred while resolving reference to '${r.$refText}':`,i);const t=(n=i.message)!==null&&n!==void 0?n:String(i);r._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${r.$refText}': ${t}`})}t.references.push(r)}}unlink(e){for(const t of e.references){delete t._ref;delete t._nodeDescription}e.references=[]}getCandidate(e){const t=this.scopeProvider.getScope(e);const n=t.getElement(e.reference.$refText);return n!==null&&n!==void 0?n:this.createLinkingError(e)}buildReference(e,t,n,r){const i=this;const s={$refNode:n,$refText:r,get ref(){var n;if((0,or.ng)(this._ref)){return this._ref}else if((0,or.Nr)(this._nodeDescription)){const n=i.loadAstNode(this._nodeDescription);this._ref=n!==null&&n!==void 0?n:i.createLinkingError({reference:s,container:e,property:t},this._nodeDescription)}else if(this._ref===undefined){this._ref=Sr;const r=(0,Ge.cQ)(e).$document;const a=i.getLinkedNode({reference:s,container:e,property:t});if(a.error&&r&&r.state<$r.ComputedScopes){return this._ref=undefined}this._ref=(n=a.node)!==null&&n!==void 0?n:a.error;this._nodeDescription=a.descr;r===null||r===void 0?void 0:r.references.push(this)}else if(this._ref===Sr){throw new Error(`Cyclic reference resolution detected: ${i.astNodeLocator.getAstNodePath(e)}/${t} (symbol '${r}')`)}return(0,or.ng)(this._ref)?this._ref:undefined},get $nodeDescription(){return this._nodeDescription},get error(){return(0,or.Zl)(this._ref)?this._ref:undefined}};return s}getLinkedNode(e){var t;try{const t=this.getCandidate(e);if((0,or.Zl)(t)){return{error:t}}const n=this.loadAstNode(t);if(n){return{node:n,descr:t}}else{return{descr:t,error:this.createLinkingError(e,t)}}}catch(n){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,n);const r=(t=n.message)!==null&&t!==void 0?t:String(n);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${r}`})}}}loadAstNode(e){if(e.node){return e.node}const t=this.langiumDocuments().getDocument(e.documentUri);if(!t){return undefined}return this.astNodeLocator.getAstNode(t.parseResult.value,e.path)}createLinkingError(e,t){const n=(0,Ge.cQ)(e.container).$document;if(n&&n.state<$r.ComputedScopes){console.warn(`Attempted reference resolution before document reached ComputedScopes state (${n.uri}).`)}const r=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${r} named '${e.reference.$refText}'.`,targetDescription:t})}}function Nr(e){return typeof e.name==="string"}class Lr{getName(e){if(Nr(e)){return e.name}return undefined}getNameNode(e){return(0,i.qO)(e.$cstNode,"name")}}var br;(function(e){e.basename=xr.A.basename;e.dirname=xr.A.dirname;e.extname=xr.A.extname;e.joinPath=xr.A.joinPath;e.resolvePath=xr.A.resolvePath;function t(e,t){return(e===null||e===void 0?void 0:e.toString())===(t===null||t===void 0?void 0:t.toString())}e.equals=t;function n(e,t){const n=typeof e==="string"?e:e.path;const r=typeof t==="string"?t:t.path;const i=n.split("/").filter((e=>e.length>0));const s=r.split("/").filter((e=>e.length>0));let a=0;for(;a<i.length;a++){if(i[a]!==s[a]){break}}const o="../".repeat(i.length-a);const c=s.slice(a).join("/");return o+c}e.relative=n;function r(e){return xr.r.parse(e.toString()).toString()}e.normalize=r})(br||(br={}));class _r{constructor(e){this.nameProvider=e.references.NameProvider;this.index=e.shared.workspace.IndexManager;this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){const t=(0,i.Rp)(e);const n=e.astNode;if(t&&n){const r=n[t.feature];if((0,or.A_)(r)){return r.ref}else if(Array.isArray(r)){for(const t of r){if((0,or.A_)(t)&&t.$refNode&&t.$refNode.offset<=e.offset&&t.$refNode.end>=e.end){return t.ref}}}}if(n){const t=this.nameProvider.getNameNode(n);if(t&&(t===e||(0,r.pO)(e,t))){return n}}}return undefined}findDeclarationNode(e){const t=this.findDeclaration(e);if(t===null||t===void 0?void 0:t.$cstNode){const e=this.nameProvider.getNameNode(t);return e!==null&&e!==void 0?e:t.$cstNode}return undefined}findReferences(e,t){const n=[];if(t.includeDeclaration){const t=this.getReferenceToSelf(e);if(t){n.push(t)}}let r=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));if(t.documentUri){r=r.filter((e=>br.equals(e.sourceUri,t.documentUri)))}n.push(...r);return(0,Dn.Td)(n)}getReferenceToSelf(e){const t=this.nameProvider.getNameNode(e);if(t){const n=(0,Ge.YE)(e);const i=this.nodeLocator.getAstNodePath(e);return{sourceUri:n.uri,sourcePath:i,targetUri:n.uri,targetPath:i,segment:(0,r.SX)(t),local:true}}return undefined}}class Or{constructor(e){this.map=new Map;if(e){for(const[t,n]of e){this.add(t,n)}}}get size(){return Dn.iD.sum((0,Dn.Td)(this.map.values()).map((e=>e.length)))}clear(){this.map.clear()}delete(e,t){if(t===undefined){return this.map.delete(e)}else{const n=this.map.get(e);if(n){const r=n.indexOf(t);if(r>=0){if(n.length===1){this.map.delete(e)}else{n.splice(r,1)}return true}}return false}}get(e){var t;return(t=this.map.get(e))!==null&&t!==void 0?t:[]}has(e,t){if(t===undefined){return this.map.has(e)}else{const n=this.map.get(e);if(n){return n.indexOf(t)>=0}return false}}add(e,t){if(this.map.has(e)){this.map.get(e).push(t)}else{this.map.set(e,[t])}return this}addAll(e,t){if(this.map.has(e)){this.map.get(e).push(...t)}else{this.map.set(e,Array.from(t))}return this}forEach(e){this.map.forEach(((t,n)=>t.forEach((t=>e(t,n,this)))))}[Symbol.iterator](){return this.entries().iterator()}entries(){return(0,Dn.Td)(this.map.entries()).flatMap((([e,t])=>t.map((t=>[e,t]))))}keys(){return(0,Dn.Td)(this.map.keys())}values(){return(0,Dn.Td)(this.map.values()).flat()}entriesGroupedByKey(){return(0,Dn.Td)(this.map.entries())}}class Pr{get size(){return this.map.size}constructor(e){this.map=new Map;this.inverse=new Map;if(e){for(const[t,n]of e){this.set(t,n)}}}clear(){this.map.clear();this.inverse.clear()}set(e,t){this.map.set(e,t);this.inverse.set(t,e);return this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){const t=this.map.get(e);if(t!==undefined){this.map.delete(e);this.inverse.delete(t);return true}return false}}class Mr{constructor(e){this.nameProvider=e.references.NameProvider;this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,t=ar.CancellationToken.None){return this.computeExportsForNode(e.parseResult.value,e,undefined,t)}async computeExportsForNode(e,t,n=Ge.VN,r=ar.CancellationToken.None){const i=[];this.exportNode(e,i,t);for(const s of n(e)){await mr(r);this.exportNode(s,i,t)}return i}exportNode(e,t,n){const r=this.nameProvider.getName(e);if(r){t.push(this.descriptions.createDescription(e,r,n))}}async computeLocalScopes(e,t=ar.CancellationToken.None){const n=e.parseResult.value;const r=new Or;for(const i of(0,Ge.Uo)(n)){await mr(t);this.processNode(i,e,r)}return r}processNode(e,t,n){const r=e.$container;if(r){const i=this.nameProvider.getName(e);if(i){n.add(r,this.descriptions.createDescription(e,i,t))}}}}class Dr{constructor(e,t,n){var r;this.elements=e;this.outerScope=t;this.caseInsensitive=(r=n===null||n===void 0?void 0:n.caseInsensitive)!==null&&r!==void 0?r:false}getAllElements(){if(this.outerScope){return this.elements.concat(this.outerScope.getAllElements())}else{return this.elements}}getElement(e){const t=this.caseInsensitive?this.elements.find((t=>t.name.toLowerCase()===e.toLowerCase())):this.elements.find((t=>t.name===e));if(t){return t}if(this.outerScope){return this.outerScope.getElement(e)}return undefined}}class Ur{constructor(e,t,n){var r;this.elements=new Map;this.caseInsensitive=(r=n===null||n===void 0?void 0:n.caseInsensitive)!==null&&r!==void 0?r:false;for(const i of e){const e=this.caseInsensitive?i.name.toLowerCase():i.name;this.elements.set(e,i)}this.outerScope=t}getElement(e){const t=this.caseInsensitive?e.toLowerCase():e;const n=this.elements.get(t);if(n){return n}if(this.outerScope){return this.outerScope.getElement(e)}return undefined}getAllElements(){let e=(0,Dn.Td)(this.elements.values());if(this.outerScope){e=e.concat(this.outerScope.getAllElements())}return e}}const Fr={getElement(){return undefined},getAllElements(){return Dn.B5}};class Gr{constructor(){this.toDispose=[];this.isDisposed=false}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed();this.clear();this.isDisposed=true;this.toDispose.forEach((e=>e.dispose()))}throwIfDisposed(){if(this.isDisposed){throw new Error("This cache has already been disposed")}}}class Br extends Gr{constructor(){super(...arguments);this.cache=new Map}has(e){this.throwIfDisposed();return this.cache.has(e)}set(e,t){this.throwIfDisposed();this.cache.set(e,t)}get(e,t){this.throwIfDisposed();if(this.cache.has(e)){return this.cache.get(e)}else if(t){const n=t();this.cache.set(e,n);return n}else{return undefined}}delete(e){this.throwIfDisposed();return this.cache.delete(e)}clear(){this.throwIfDisposed();this.cache.clear()}}class Kr extends Gr{constructor(e){super();this.cache=new Map;this.converter=e!==null&&e!==void 0?e:e=>e}has(e,t){this.throwIfDisposed();return this.cacheForContext(e).has(t)}set(e,t,n){this.throwIfDisposed();this.cacheForContext(e).set(t,n)}get(e,t,n){this.throwIfDisposed();const r=this.cacheForContext(e);if(r.has(t)){return r.get(t)}else if(n){const e=n();r.set(t,e);return e}else{return undefined}}delete(e,t){this.throwIfDisposed();return this.cacheForContext(e).delete(t)}clear(e){this.throwIfDisposed();if(e){const t=this.converter(e);this.cache.delete(t)}else{this.cache.clear()}}cacheForContext(e){const t=this.converter(e);let n=this.cache.get(t);if(!n){n=new Map;this.cache.set(t,n)}return n}}class jr extends Kr{constructor(e,t){super((e=>e.toString()));if(t){this.toDispose.push(e.workspace.DocumentBuilder.onDocumentPhase(t,(e=>{this.clear(e.uri.toString())})));this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(((e,t)=>{for(const n of t){this.clear(n)}})))}else{this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(((e,t)=>{const n=e.concat(t);for(const r of n){this.clear(r)}})))}}}class Vr extends Br{constructor(e,t){super();if(t){this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(t,(()=>{this.clear()})));this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(((e,t)=>{if(t.length>0){this.clear()}})))}else{this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((()=>{this.clear()})))}}}class Wr{constructor(e){this.reflection=e.shared.AstReflection;this.nameProvider=e.references.NameProvider;this.descriptions=e.workspace.AstNodeDescriptionProvider;this.indexManager=e.shared.workspace.IndexManager;this.globalScopeCache=new Vr(e.shared)}getScope(e){const t=[];const n=this.reflection.getReferenceType(e);const r=(0,Ge.YE)(e.container).precomputedScopes;if(r){let i=e.container;do{const e=r.get(i);if(e.length>0){t.push((0,Dn.Td)(e).filter((e=>this.reflection.isSubtype(e.type,n))))}i=i.$container}while(i)}let i=this.getGlobalScope(n,e);for(let s=t.length-1;s>=0;s--){i=this.createScope(t[s],i)}return i}createScope(e,t,n){return new Dr((0,Dn.Td)(e),t,n)}createScopeForNodes(e,t,n){const r=(0,Dn.Td)(e).map((e=>{const t=this.nameProvider.getName(e);if(t){return this.descriptions.createDescription(e,t)}return undefined})).nonNullable();return new Dr(r,t,n)}getGlobalScope(e,t){return this.globalScopeCache.get(e,(()=>new Ur(this.indexManager.allElements(e))))}}function Hr(e){return typeof e.$comment==="string"}function zr(e){return typeof e==="object"&&!!e&&("$ref"in e||"$error"in e)}class Yr{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]);this.langiumDocuments=e.shared.workspace.LangiumDocuments;this.astNodeLocator=e.workspace.AstNodeLocator;this.nameProvider=e.references.NameProvider;this.commentProvider=e.documentation.CommentProvider}serialize(e,t){const n=t!==null&&t!==void 0?t:{};const r=t===null||t===void 0?void 0:t.replacer;const i=(e,t)=>this.replacer(e,t,n);const s=r?(e,t)=>r(e,t,i):i;try{this.currentDocument=(0,Ge.YE)(e);return JSON.stringify(e,s,t===null||t===void 0?void 0:t.space)}finally{this.currentDocument=undefined}}deserialize(e,t){const n=t!==null&&t!==void 0?t:{};const r=JSON.parse(e);this.linkNode(r,r,n);return r}replacer(e,t,{refText:n,sourceText:r,textRegions:i,comments:s,uriConverter:a}){var o,c,u,l;if(this.ignoreProperties.has(e)){return undefined}else if((0,or.A_)(t)){const e=t.ref;const r=n?t.$refText:undefined;if(e){const n=(0,Ge.YE)(e);let i="";if(this.currentDocument&&this.currentDocument!==n){if(a){i=a(n.uri,t)}else{i=n.uri.toString()}}const s=this.astNodeLocator.getAstNodePath(e);return{$ref:`${i}#${s}`,$refText:r}}else{return{$error:(c=(o=t.error)===null||o===void 0?void 0:o.message)!==null&&c!==void 0?c:"Could not resolve reference",$refText:r}}}else if((0,or.ng)(t)){let n=undefined;if(i){n=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t));if((!e||t.$document)&&(n===null||n===void 0?void 0:n.$textRegion)){n.$textRegion.documentURI=(u=this.currentDocument)===null||u===void 0?void 0:u.uri.toString()}}if(r&&!e){n!==null&&n!==void 0?n:n=Object.assign({},t);n.$sourceText=(l=t.$cstNode)===null||l===void 0?void 0:l.text}if(s){n!==null&&n!==void 0?n:n=Object.assign({},t);const e=this.commentProvider.getComment(t);if(e){n.$comment=e.replace(/\r/g,"")}}return n!==null&&n!==void 0?n:t}else{return t}}addAstNodeRegionWithAssignmentsTo(e){const t=e=>({offset:e.offset,end:e.end,length:e.length,range:e.range});if(e.$cstNode){const n=e.$textRegion=t(e.$cstNode);const r=n.assignments={};Object.keys(e).filter((e=>!e.startsWith("$"))).forEach((n=>{const s=(0,i.Bd)(e.$cstNode,n).map(t);if(s.length!==0){r[n]=s}}));return e}return undefined}linkNode(e,t,n,r,i,s){for(const[o,c]of Object.entries(e)){if(Array.isArray(c)){for(let r=0;r<c.length;r++){const i=c[r];if(zr(i)){c[r]=this.reviveReference(e,o,t,i,n)}else if((0,or.ng)(i)){this.linkNode(i,t,n,e,o,r)}}}else if(zr(c)){e[o]=this.reviveReference(e,o,t,c,n)}else if((0,or.ng)(c)){this.linkNode(c,t,n,e,o)}}const a=e;a.$container=r;a.$containerProperty=i;a.$containerIndex=s}reviveReference(e,t,n,r,i){let s=r.$refText;let a=r.$error;if(r.$ref){const e=this.getRefNode(n,r.$ref,i.uriConverter);if((0,or.ng)(e)){if(!s){s=this.nameProvider.getName(e)}return{$refText:s!==null&&s!==void 0?s:"",ref:e}}else{a=e}}if(a){const n={$refText:s!==null&&s!==void 0?s:""};n.error={container:e,property:t,message:a,reference:n};return n}else{return undefined}}getRefNode(e,t,n){try{const r=t.indexOf("#");if(r===0){const n=this.astNodeLocator.getAstNode(e,t.substring(1));if(!n){return"Could not resolve path: "+t}return n}if(r<0){const e=n?n(t):xr.r.parse(t);const r=this.langiumDocuments.getDocument(e);if(!r){return"Could not find document for URI: "+t}return r.parseResult.value}const i=n?n(t.substring(0,r)):xr.r.parse(t.substring(0,r));const s=this.langiumDocuments.getDocument(i);if(!s){return"Could not find document for URI: "+t}if(r===t.length-1){return s.parseResult.value}const a=this.astNodeLocator.getAstNode(s.parseResult.value,t.substring(r+1));if(!a){return"Could not resolve URI: "+t}return a}catch(r){return String(r)}}}class qr{get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map;this.fileExtensionMap=new Map;this.textDocuments=e===null||e===void 0?void 0:e.workspace.TextDocuments}register(e){const t=e.LanguageMetaData;for(const n of t.fileExtensions){if(this.fileExtensionMap.has(n)){console.warn(`The file extension ${n} is used by multiple languages. It is now assigned to '${t.languageId}'.`)}this.fileExtensionMap.set(n,e)}this.languageIdMap.set(t.languageId,e);if(this.languageIdMap.size===1){this.singleton=e}else{this.singleton=undefined}}getServices(e){var t,n;if(this.singleton!==undefined){return this.singleton}if(this.languageIdMap.size===0){throw new Error("The service registry is empty. Use `register` to register the services of a language.")}const r=(n=(t=this.textDocuments)===null||t===void 0?void 0:t.get(e))===null||n===void 0?void 0:n.languageId;if(r!==undefined){const e=this.languageIdMap.get(r);if(e){return e}}const i=br.extname(e);const s=this.fileExtensionMap.get(i);if(!s){if(r){throw new Error(`The service registry contains no services for the extension '${i}' for language '${r}'.`)}else{throw new Error(`The service registry contains no services for the extension '${i}'.`)}}return s}hasServices(e){try{this.getServices(e);return true}catch(t){return false}}get all(){return Array.from(this.languageIdMap.values())}}function Xr(e){return{code:e}}var Qr;(function(e){e.all=["fast","slow","built-in"]})(Qr||(Qr={}));class Zr{constructor(e){this.entries=new Or;this.entriesBefore=[];this.entriesAfter=[];this.reflection=e.shared.AstReflection}register(e,t=this,n="fast"){if(n==="built-in"){throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.")}for(const[r,i]of Object.entries(e)){const e=i;if(Array.isArray(e)){for(const i of e){const e={check:this.wrapValidationException(i,t),category:n};this.addEntry(r,e)}}else if(typeof e==="function"){const i={check:this.wrapValidationException(e,t),category:n};this.addEntry(r,i)}else{(0,Mn.d)(e)}}}wrapValidationException(e,t){return async(n,r,i)=>{await this.handleException((()=>e.call(t,n,r,i)),"An error occurred during validation",r,n)}}async handleException(e,t,n,r){try{await e()}catch(i){if(pr(i)){throw i}console.error(`${t}:`,i);if(i instanceof Error&&i.stack){console.error(i.stack)}const e=i instanceof Error?i.message:String(i);n("error",`${t}: ${e}`,{node:r})}}addEntry(e,t){if(e==="AstNode"){this.entries.add("AstNode",t);return}for(const n of this.reflection.getAllSubTypes(e)){this.entries.add(n,t)}}getChecks(e,t){let n=(0,Dn.Td)(this.entries.get(e)).concat(this.entries.get("AstNode"));if(t){n=n.filter((e=>t.includes(e.category)))}return n.map((e=>e.check))}registerBeforeDocument(e,t=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",t))}registerAfterDocument(e,t=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",t))}wrapPreparationException(e,t,n){return async(r,i,s,a)=>{await this.handleException((()=>e.call(n,r,i,s,a)),t,i,r)}}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}}class Jr{constructor(e){this.validationRegistry=e.validation.ValidationRegistry;this.metadata=e.LanguageMetaData}async validateDocument(e,t={},n=ar.CancellationToken.None){const r=e.parseResult;const i=[];await mr(n);if(!t.categories||t.categories.includes("built-in")){this.processLexingErrors(r,i,t);if(t.stopAfterLexingErrors&&i.some((e=>{var t;return((t=e.data)===null||t===void 0?void 0:t.code)===ri.LexingError}))){return i}this.processParsingErrors(r,i,t);if(t.stopAfterParsingErrors&&i.some((e=>{var t;return((t=e.data)===null||t===void 0?void 0:t.code)===ri.ParsingError}))){return i}this.processLinkingErrors(e,i,t);if(t.stopAfterLinkingErrors&&i.some((e=>{var t;return((t=e.data)===null||t===void 0?void 0:t.code)===ri.LinkingError}))){return i}}try{i.push(...await this.validateAst(r.value,t,n))}catch(s){if(pr(s)){throw s}console.error("An error occurred during validation:",s)}await mr(n);return i}processLexingErrors(e,t,n){var r,i,s;const a=[...e.lexerErrors,...(i=(r=e.lexerReport)===null||r===void 0?void 0:r.diagnostics)!==null&&i!==void 0?i:[]];for(const o of a){const e=(s=o.severity)!==null&&s!==void 0?s:"error";const n={severity:ti(e),range:{start:{line:o.line-1,character:o.column-1},end:{line:o.line-1,character:o.column+o.length-1}},message:o.message,data:ni(e),source:this.getSource()};t.push(n)}}processParsingErrors(e,t,n){for(const i of e.parserErrors){let e=undefined;if(isNaN(i.token.startOffset)){if("previousToken"in i){const t=i.previousToken;if(!isNaN(t.startOffset)){const n={line:t.endLine-1,character:t.endColumn};e={start:n,end:n}}else{const t={line:0,character:0};e={start:t,end:t}}}}else{e=(0,r.wf)(i.token)}if(e){const n={severity:ti("error"),range:e,message:i.message,data:Xr(ri.ParsingError),source:this.getSource()};t.push(n)}}}processLinkingErrors(e,t,n){for(const r of e.references){const e=r.error;if(e){const n={node:e.container,property:e.property,index:e.index,data:{code:ri.LinkingError,containerType:e.container.$type,property:e.property,refText:e.reference.$refText}};t.push(this.toDiagnostic("error",e.message,n))}}}async validateAst(e,t,n=ar.CancellationToken.None){const r=[];const i=(e,t,n)=>{r.push(this.toDiagnostic(e,t,n))};await this.validateAstBefore(e,t,i,n);await this.validateAstNodes(e,t,i,n);await this.validateAstAfter(e,t,i,n);return r}async validateAstBefore(e,t,n,r=ar.CancellationToken.None){var i;const s=this.validationRegistry.checksBefore;for(const a of s){await mr(r);await a(e,n,(i=t.categories)!==null&&i!==void 0?i:[],r)}}async validateAstNodes(e,t,n,r=ar.CancellationToken.None){await Promise.all((0,Ge.jm)(e).map((async e=>{await mr(r);const i=this.validationRegistry.getChecks(e.$type,t.categories);for(const t of i){await t(e,n,r)}})))}async validateAstAfter(e,t,n,r=ar.CancellationToken.None){var i;const s=this.validationRegistry.checksAfter;for(const a of s){await mr(r);await a(e,n,(i=t.categories)!==null&&i!==void 0?i:[],r)}}toDiagnostic(e,t,n){return{message:t,range:ei(n),severity:ti(e),code:n.code,codeDescription:n.codeDescription,tags:n.tags,relatedInformation:n.relatedInformation,data:n.data,source:this.getSource()}}getSource(){return this.metadata.languageId}}function ei(e){if(e.range){return e.range}let t;if(typeof e.property==="string"){t=(0,i.qO)(e.node.$cstNode,e.property,e.index)}else if(typeof e.keyword==="string"){t=(0,i.SS)(e.node.$cstNode,e.keyword,e.index)}t!==null&&t!==void 0?t:t=e.node.$cstNode;if(!t){return{start:{line:0,character:0},end:{line:0,character:0}}}return t.range}function ti(e){switch(e){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+e)}}function ni(e){switch(e){case"error":return Xr(ri.LexingError);case"warning":return Xr(ri.LexingWarning);case"info":return Xr(ri.LexingInfo);case"hint":return Xr(ri.LexingHint);default:throw new Error("Invalid diagnostic severity: "+e)}}var ri;(function(e){e.LexingError="lexing-error";e.LexingWarning="lexing-warning";e.LexingInfo="lexing-info";e.LexingHint="lexing-hint";e.ParsingError="parsing-error";e.LinkingError="linking-error"})(ri||(ri={}));class ii{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator;this.nameProvider=e.references.NameProvider}createDescription(e,t,n){const i=n!==null&&n!==void 0?n:(0,Ge.YE)(e);t!==null&&t!==void 0?t:t=this.nameProvider.getName(e);const s=this.astNodeLocator.getAstNodePath(e);if(!t){throw new Error(`Node at path ${s} has no name.`)}let a;const o=()=>{var t;return a!==null&&a!==void 0?a:a=(0,r.SX)((t=this.nameProvider.getNameNode(e))!==null&&t!==void 0?t:e.$cstNode)};return{node:e,name:t,get nameSegment(){return o()},selectionSegment:(0,r.SX)(e.$cstNode),type:e.$type,documentUri:i.uri,path:s}}}class si{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,t=ar.CancellationToken.None){const n=[];const r=e.parseResult.value;for(const i of(0,Ge.jm)(r)){await mr(t);(0,Ge.DM)(i).filter((e=>!(0,or.Zl)(e))).forEach((e=>{const t=this.createDescription(e);if(t){n.push(t)}}))}return n}createDescription(e){const t=e.reference.$nodeDescription;const n=e.reference.$refNode;if(!t||!n){return undefined}const i=(0,Ge.YE)(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:(0,r.SX)(n),local:br.equals(t.documentUri,i)}}}class ai{constructor(){this.segmentSeparator="/";this.indexSeparator="@"}getAstNodePath(e){if(e.$container){const t=this.getAstNodePath(e.$container);const n=this.getPathSegment(e);const r=t+this.segmentSeparator+n;return r}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e){throw new Error("Missing '$containerProperty' in AST node.")}if(t!==undefined){return e+this.indexSeparator+t}return e}getAstNode(e,t){const n=t.split(this.segmentSeparator);return n.reduce(((e,t)=>{if(!e||t.length===0){return e}const n=t.indexOf(this.indexSeparator);if(n>0){const r=t.substring(0,n);const i=parseInt(t.substring(n+1));const s=e[r];return s===null||s===void 0?void 0:s[i]}return e[t]}),e)}}var oi=n(62676);class ci{constructor(e){this._ready=new gr;this.settings={};this.workspaceConfig=false;this.onConfigurationSectionUpdateEmitter=new oi.Emitter;this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,n;this.workspaceConfig=(n=(t=e.capabilities.workspace)===null||t===void 0?void 0:t.configuration)!==null&&n!==void 0?n:false}async initialized(e){if(this.workspaceConfig){if(e.register){const t=this.serviceRegistry.all;e.register({section:t.map((e=>this.toSectionName(e.LanguageMetaData.languageId)))})}if(e.fetchConfiguration){const t=this.serviceRegistry.all.map((e=>({section:this.toSectionName(e.LanguageMetaData.languageId)})));const n=await e.fetchConfiguration(t);t.forEach(((e,t)=>{this.updateSectionConfiguration(e.section,n[t])}))}}this._ready.resolve()}updateConfiguration(e){if(!e.settings){return}Object.keys(e.settings).forEach((t=>{const n=e.settings[t];this.updateSectionConfiguration(t,n);this.onConfigurationSectionUpdateEmitter.fire({section:t,configuration:n})}))}updateSectionConfiguration(e,t){this.settings[e]=t}async getConfiguration(e,t){await this.ready;const n=this.toSectionName(e);if(this.settings[n]){return this.settings[n][t]}}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}}var ui;(function(e){function t(e){return{dispose:async()=>await e()}}e.create=t})(ui||(ui={}));class li{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}};this.updateListeners=[];this.buildPhaseListeners=new Or;this.documentPhaseListeners=new Or;this.buildState=new Map;this.documentBuildWaiters=new Map;this.currentState=$r.Changed;this.langiumDocuments=e.workspace.LangiumDocuments;this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory;this.textDocuments=e.workspace.TextDocuments;this.indexManager=e.workspace.IndexManager;this.serviceRegistry=e.ServiceRegistry}async build(e,t={},n=ar.CancellationToken.None){var r,i;for(const s of e){const e=s.uri.toString();if(s.state===$r.Validated){if(typeof t.validation==="boolean"&&t.validation){s.state=$r.IndexedReferences;s.diagnostics=undefined;this.buildState.delete(e)}else if(typeof t.validation==="object"){const n=this.buildState.get(e);const a=(r=n===null||n===void 0?void 0:n.result)===null||r===void 0?void 0:r.validationChecks;if(a){const r=(i=t.validation.categories)!==null&&i!==void 0?i:Qr.all;const o=r.filter((e=>!a.includes(e)));if(o.length>0){this.buildState.set(e,{completed:false,options:{validation:Object.assign(Object.assign({},t.validation),{categories:o})},result:n.result});s.state=$r.IndexedReferences}}}}else{this.buildState.delete(e)}}this.currentState=$r.Changed;await this.emitUpdate(e.map((e=>e.uri)),[]);await this.buildDocuments(e,t,n)}async update(e,t,n=ar.CancellationToken.None){this.currentState=$r.Changed;for(const s of t){this.langiumDocuments.deleteDocument(s);this.buildState.delete(s.toString());this.indexManager.remove(s)}for(const s of e){const e=this.langiumDocuments.invalidateDocument(s);if(!e){const e=this.langiumDocumentFactory.fromModel({$type:"INVALID"},s);e.state=$r.Changed;this.langiumDocuments.addDocument(e)}this.buildState.delete(s.toString())}const r=(0,Dn.Td)(e).concat(t).map((e=>e.toString())).toSet();this.langiumDocuments.all.filter((e=>!r.has(e.uri.toString())&&this.shouldRelink(e,r))).forEach((e=>{const t=this.serviceRegistry.getServices(e.uri).references.Linker;t.unlink(e);e.state=Math.min(e.state,$r.ComputedScopes);e.diagnostics=undefined}));await this.emitUpdate(e,t);await mr(n);const i=this.sortDocuments(this.langiumDocuments.all.filter((e=>{var t;return e.state<$r.Linked||!((t=this.buildState.get(e.uri.toString()))===null||t===void 0?void 0:t.completed)})).toArray());await this.buildDocuments(i,this.updateBuildOptions,n)}async emitUpdate(e,t){await Promise.all(this.updateListeners.map((n=>n(e,t))))}sortDocuments(e){let t=0;let n=e.length-1;while(t<n){while(t<e.length&&this.hasTextDocument(e[t])){t++}while(n>=0&&!this.hasTextDocument(e[n])){n--}if(t<n){[e[t],e[n]]=[e[n],e[t]]}}return e}hasTextDocument(e){var t;return Boolean((t=this.textDocuments)===null||t===void 0?void 0:t.get(e.uri))}shouldRelink(e,t){if(e.references.some((e=>e.error!==undefined))){return true}return this.indexManager.isAffected(e,t)}onUpdate(e){this.updateListeners.push(e);return ui.create((()=>{const t=this.updateListeners.indexOf(e);if(t>=0){this.updateListeners.splice(t,1)}}))}async buildDocuments(e,t,n){this.prepareBuild(e,t);await this.runCancelable(e,$r.Parsed,n,(e=>this.langiumDocumentFactory.update(e,n)));await this.runCancelable(e,$r.IndexedContent,n,(e=>this.indexManager.updateContent(e,n)));await this.runCancelable(e,$r.ComputedScopes,n,(async e=>{const t=this.serviceRegistry.getServices(e.uri).references.ScopeComputation;e.precomputedScopes=await t.computeLocalScopes(e,n)}));await this.runCancelable(e,$r.Linked,n,(e=>{const t=this.serviceRegistry.getServices(e.uri).references.Linker;return t.link(e,n)}));await this.runCancelable(e,$r.IndexedReferences,n,(e=>this.indexManager.updateReferences(e,n)));const r=e.filter((e=>this.shouldValidate(e)));await this.runCancelable(r,$r.Validated,n,(e=>this.validate(e,n)));for(const i of e){const e=this.buildState.get(i.uri.toString());if(e){e.completed=true}}}prepareBuild(e,t){for(const n of e){const e=n.uri.toString();const r=this.buildState.get(e);if(!r||r.completed){this.buildState.set(e,{completed:false,options:t,result:r===null||r===void 0?void 0:r.result})}}}async runCancelable(e,t,n,r){const i=e.filter((e=>e.state<t));for(const a of i){await mr(n);await r(a);a.state=t;await this.notifyDocumentPhase(a,t,n)}const s=e.filter((e=>e.state===t));await this.notifyBuildPhase(s,t,n);this.currentState=t}onBuildPhase(e,t){this.buildPhaseListeners.add(e,t);return ui.create((()=>{this.buildPhaseListeners.delete(e,t)}))}onDocumentPhase(e,t){this.documentPhaseListeners.add(e,t);return ui.create((()=>{this.documentPhaseListeners.delete(e,t)}))}waitUntil(e,t,n){let r=undefined;if(t&&"path"in t){r=t}else{n=t}n!==null&&n!==void 0?n:n=ar.CancellationToken.None;if(r){const t=this.langiumDocuments.getDocument(r);if(t&&t.state>e){return Promise.resolve(r)}}if(this.currentState>=e){return Promise.resolve(undefined)}else if(n.isCancellationRequested){return Promise.reject(hr)}return new Promise(((t,i)=>{const s=this.onBuildPhase(e,(()=>{s.dispose();a.dispose();if(r){const e=this.langiumDocuments.getDocument(r);t(e===null||e===void 0?void 0:e.uri)}else{t(undefined)}}));const a=n.onCancellationRequested((()=>{s.dispose();a.dispose();i(hr)}))}))}async notifyDocumentPhase(e,t,n){const r=this.documentPhaseListeners.get(t);const i=r.slice();for(const a of i){try{await a(e,n)}catch(s){if(!pr(s)){throw s}}}}async notifyBuildPhase(e,t,n){if(e.length===0){return}const r=this.buildPhaseListeners.get(t);const i=r.slice();for(const s of i){await mr(n);await s(e,n)}}shouldValidate(e){return Boolean(this.getBuildOptions(e).validation)}async validate(e,t){var n,r;const i=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator;const s=this.getBuildOptions(e).validation;const a=typeof s==="object"?s:undefined;const o=await i.validateDocument(e,a,t);if(e.diagnostics){e.diagnostics.push(...o)}else{e.diagnostics=o}const c=this.buildState.get(e.uri.toString());if(c){(n=c.result)!==null&&n!==void 0?n:c.result={};const e=(r=a===null||a===void 0?void 0:a.categories)!==null&&r!==void 0?r:Qr.all;if(c.result.validationChecks){c.result.validationChecks.push(...e)}else{c.result.validationChecks=[...e]}}}getBuildOptions(e){var t,n;return(n=(t=this.buildState.get(e.uri.toString()))===null||t===void 0?void 0:t.options)!==null&&n!==void 0?n:{}}}class di{constructor(e){this.symbolIndex=new Map;this.symbolByTypeIndex=new Kr;this.referenceIndex=new Map;this.documents=e.workspace.LangiumDocuments;this.serviceRegistry=e.ServiceRegistry;this.astReflection=e.AstReflection}findAllReferences(e,t){const n=(0,Ge.YE)(e).uri;const r=[];this.referenceIndex.forEach((e=>{e.forEach((e=>{if(br.equals(e.targetUri,n)&&e.targetPath===t){r.push(e)}}))}));return(0,Dn.Td)(r)}allElements(e,t){let n=(0,Dn.Td)(this.symbolIndex.keys());if(t){n=n.filter((e=>!t||t.has(e)))}return n.map((t=>this.getFileDescriptions(t,e))).flat()}getFileDescriptions(e,t){var n;if(!t){return(n=this.symbolIndex.get(e))!==null&&n!==void 0?n:[]}const r=this.symbolByTypeIndex.get(e,t,(()=>{var n;const r=(n=this.symbolIndex.get(e))!==null&&n!==void 0?n:[];return r.filter((e=>this.astReflection.isSubtype(e.type,t)))}));return r}remove(e){const t=e.toString();this.symbolIndex.delete(t);this.symbolByTypeIndex.clear(t);this.referenceIndex.delete(t)}async updateContent(e,t=ar.CancellationToken.None){const n=this.serviceRegistry.getServices(e.uri);const r=await n.references.ScopeComputation.computeExports(e,t);const i=e.uri.toString();this.symbolIndex.set(i,r);this.symbolByTypeIndex.clear(i)}async updateReferences(e,t=ar.CancellationToken.None){const n=this.serviceRegistry.getServices(e.uri);const r=await n.workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),r)}isAffected(e,t){const n=this.referenceIndex.get(e.uri.toString());if(!n){return false}return n.some((e=>!e.local&&t.has(e.targetUri.toString())))}}class fi{constructor(e){this.initialBuildOptions={};this._ready=new gr;this.serviceRegistry=e.ServiceRegistry;this.langiumDocuments=e.workspace.LangiumDocuments;this.documentBuilder=e.workspace.DocumentBuilder;this.fileSystemProvider=e.workspace.FileSystemProvider;this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var t;this.folders=(t=e.workspaceFolders)!==null&&t!==void 0?t:undefined}initialized(e){return this.mutex.write((e=>{var t;return this.initializeWorkspace((t=this.folders)!==null&&t!==void 0?t:[],e)}))}async initializeWorkspace(e,t=ar.CancellationToken.None){const n=await this.performStartup(e);await mr(t);await this.documentBuilder.build(n,this.initialBuildOptions,t)}async performStartup(e){const t=this.serviceRegistry.all.flatMap((e=>e.LanguageMetaData.fileExtensions));const n=[];const r=e=>{n.push(e);if(!this.langiumDocuments.hasDocument(e.uri)){this.langiumDocuments.addDocument(e)}};await this.loadAdditionalDocuments(e,r);await Promise.all(e.map((e=>[e,this.getRootFolder(e)])).map((async e=>this.traverseFolder(...e,t,r))));this._ready.resolve();return n}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return xr.r.parse(e.uri)}async traverseFolder(e,t,n,r){const i=await this.fileSystemProvider.readDirectory(t);await Promise.all(i.map((async t=>{if(this.includeEntry(e,t,n)){if(t.isDirectory){await this.traverseFolder(e,t.uri,n,r)}else if(t.isFile){const e=await this.langiumDocuments.getOrCreateDocument(t.uri);r(e)}}})))}includeEntry(e,t,n){const r=br.basename(t.uri);if(r.startsWith(".")){return false}if(t.isDirectory){return r!=="node_modules"&&r!=="out"}else if(t.isFile){const e=br.extname(t.uri);return n.includes(e)}return false}}class hi{buildUnexpectedCharactersMessage(e,t,n,r,i){return c.PW.buildUnexpectedCharactersMessage(e,t,n,r,i)}buildUnableToPopLexerModeMessage(e){return c.PW.buildUnableToPopLexerModeMessage(e)}}const pi={mode:"full"};class mi{constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider;this.tokenBuilder=e.parser.TokenBuilder;const t=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);const n=vi(t)?Object.values(t):t;const r=e.LanguageMetaData.mode==="production";this.chevrotainLexer=new c.JG(n,{positionTracking:"full",skipValidations:r,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,t=pi){var n,r,i;const s=this.chevrotainLexer.tokenize(e);return{tokens:s.tokens,errors:s.errors,hidden:(n=s.groups.hidden)!==null&&n!==void 0?n:[],report:(i=(r=this.tokenBuilder).flushLexingReport)===null||i===void 0?void 0:i.call(r,e)}}toTokenTypeDictionary(e){if(vi(e))return e;const t=yi(e)?Object.values(e.modes).flat():e;const n={};t.forEach((e=>n[e.name]=e));return n}}function gi(e){return Array.isArray(e)&&(e.length===0||"name"in e[0])}function yi(e){return e&&"modes"in e&&"defaultMode"in e}function vi(e){return!gi(e)&&!yi(e)}function Ai(e,t,n){let r;let i;if(typeof e==="string"){i=t;r=n}else{i=e.range.start;r=t}if(!i){i=We.create(0,0)}const s=Ri(e);const a=Di(r);const o=xi({lines:s,position:i,options:a});return Ni({index:0,tokens:o,position:i})}function Ti(e,t){const n=Di(t);const r=Ri(e);if(r.length===0){return false}const i=r[0];const s=r[r.length-1];const a=n.start;const o=n.end;return Boolean(a===null||a===void 0?void 0:a.exec(i))&&Boolean(o===null||o===void 0?void 0:o.exec(s))}function Ri(e){let t="";if(typeof e==="string"){t=e}else{t=e.text}const n=t.split(s.TH);return n}const Ei=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy;const ki=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function xi(e){var t,n,r;const i=[];let s=e.position.line;let a=e.position.character;for(let o=0;o<e.lines.length;o++){const c=o===0;const u=o===e.lines.length-1;let l=e.lines[o];let d=0;if(c&&e.options.start){const n=(t=e.options.start)===null||t===void 0?void 0:t.exec(l);if(n){d=n.index+n[0].length}}else{const t=(n=e.options.line)===null||n===void 0?void 0:n.exec(l);if(t){d=t.index+t[0].length}}if(u){const t=(r=e.options.end)===null||r===void 0?void 0:r.exec(l);if(t){l=l.substring(0,t.index)}}l=l.substring(0,Ci(l));const f=Si(l,d);if(f>=l.length){if(i.length>0){const e=We.create(s,a);i.push({type:"break",content:"",range:He.create(e,e)})}}else{Ei.lastIndex=d;const e=Ei.exec(l);if(e){const t=e[0];const n=e[1];const r=We.create(s,a+d);const o=We.create(s,a+d+t.length);i.push({type:"tag",content:n,range:He.create(r,o)});d+=t.length;d=Si(l,d)}if(d<l.length){const e=l.substring(d);const t=Array.from(e.matchAll(ki));i.push(...$i(t,e,s,a+d))}}s++;a=0}if(i.length>0&&i[i.length-1].type==="break"){return i.slice(0,-1)}return i}function $i(e,t,n,r){const i=[];if(e.length===0){const e=We.create(n,r);const s=We.create(n,r+t.length);i.push({type:"text",content:t,range:He.create(e,s)})}else{let s=0;for(const o of e){const e=o.index;const a=t.substring(s,e);if(a.length>0){i.push({type:"text",content:t.substring(s,e),range:He.create(We.create(n,s+r),We.create(n,e+r))})}let c=a.length+1;const u=o[1];i.push({type:"inline-tag",content:u,range:He.create(We.create(n,s+c+r),We.create(n,s+c+u.length+r))});c+=u.length;if(o.length===4){c+=o[2].length;const e=o[3];i.push({type:"text",content:e,range:He.create(We.create(n,s+c+r),We.create(n,s+c+e.length+r))})}else{i.push({type:"text",content:"",range:He.create(We.create(n,s+c+r),We.create(n,s+c+r))})}s=e+o[0].length}const a=t.substring(s);if(a.length>0){i.push({type:"text",content:a,range:He.create(We.create(n,s+r),We.create(n,s+r+a.length))})}}return i}const wi=/\S/;const Ii=/\s*$/;function Si(e,t){const n=e.substring(t).match(wi);if(n){return t+n.index}else{return e.length}}function Ci(e){const t=e.match(Ii);if(t&&typeof t.index==="number"){return t.index}return undefined}function Ni(e){var t,n,r,i;const s=We.create(e.position.line,e.position.character);if(e.tokens.length===0){return new Fi([],He.create(s,s))}const a=[];while(e.index<e.tokens.length){const t=Li(e,a[a.length-1]);if(t){a.push(t)}}const o=(n=(t=a[0])===null||t===void 0?void 0:t.range.start)!==null&&n!==void 0?n:s;const c=(i=(r=a[a.length-1])===null||r===void 0?void 0:r.range.end)!==null&&i!==void 0?i:s;return new Fi(a,He.create(o,c))}function Li(e,t){const n=e.tokens[e.index];if(n.type==="tag"){return Pi(e,false)}else if(n.type==="text"||n.type==="inline-tag"){return _i(e)}else{bi(n,t);e.index++;return undefined}}function bi(e,t){if(t){const n=new Vi("",e.range);if("inlines"in t){t.inlines.push(n)}else{t.content.inlines.push(n)}}}function _i(e){let t=e.tokens[e.index];const n=t;let r=t;const i=[];while(t&&t.type!=="break"&&t.type!=="tag"){i.push(Oi(e));r=t;t=e.tokens[e.index]}return new ji(i,He.create(n.range.start,r.range.end))}function Oi(e){const t=e.tokens[e.index];if(t.type==="inline-tag"){return Pi(e,true)}else{return Mi(e)}}function Pi(e,t){const n=e.tokens[e.index++];const r=n.content.substring(1);const i=e.tokens[e.index];if((i===null||i===void 0?void 0:i.type)==="text"){if(t){const i=Mi(e);return new Gi(r,new ji([i],i.range),t,He.create(n.range.start,i.range.end))}else{const i=_i(e);return new Gi(r,i,t,He.create(n.range.start,i.range.end))}}else{const e=n.range;return new Gi(r,new ji([],e),t,e)}}function Mi(e){const t=e.tokens[e.index++];return new Vi(t.content,t.range)}function Di(e){if(!e){return Di({start:"/**",end:"*/",line:"*"})}const{start:t,end:n,line:r}=e;return{start:Ui(t,true),end:Ui(n,false),line:Ui(r,true)}}function Ui(e,t){if(typeof e==="string"||typeof e==="object"){const n=typeof e==="string"?(0,s.Nt)(e):e.source;if(t){return new RegExp(`^\\s*${n}`)}else{return new RegExp(`\\s*${n}\\s*$`)}}else{return e}}class Fi{constructor(e,t){this.elements=e;this.range=t}getTag(e){return this.getAllTags().find((t=>t.name===e))}getTags(e){return this.getAllTags().filter((t=>t.name===e))}getAllTags(){return this.elements.filter((e=>"name"in e))}toString(){let e="";for(const t of this.elements){if(e.length===0){e=t.toString()}else{const n=t.toString();e+=Wi(e)+n}}return e.trim()}toMarkdown(e){let t="";for(const n of this.elements){if(t.length===0){t=n.toMarkdown(e)}else{const r=n.toMarkdown(e);t+=Wi(t)+r}}return t.trim()}}class Gi{constructor(e,t,n,r){this.name=e;this.content=t;this.inline=n;this.range=r}toString(){let e=`@${this.name}`;const t=this.content.toString();if(this.content.inlines.length===1){e=`${e} ${t}`}else if(this.content.inlines.length>1){e=`${e}\n${t}`}if(this.inline){return`{${e}}`}else{return e}}toMarkdown(e){var t,n;return(n=(t=e===null||e===void 0?void 0:e.renderTag)===null||t===void 0?void 0:t.call(e,this))!==null&&n!==void 0?n:this.toMarkdownDefault(e)}toMarkdownDefault(e){const t=this.content.toMarkdown(e);if(this.inline){const n=Bi(this.name,t,e!==null&&e!==void 0?e:{});if(typeof n==="string"){return n}}let n="";if((e===null||e===void 0?void 0:e.tag)==="italic"||(e===null||e===void 0?void 0:e.tag)===undefined){n="*"}else if((e===null||e===void 0?void 0:e.tag)==="bold"){n="**"}else if((e===null||e===void 0?void 0:e.tag)==="bold-italic"){n="***"}let r=`${n}@${this.name}${n}`;if(this.content.inlines.length===1){r=`${r} — ${t}`}else if(this.content.inlines.length>1){r=`${r}\n${t}`}if(this.inline){return`{${r}}`}else{return r}}}function Bi(e,t,n){var r,i;if(e==="linkplain"||e==="linkcode"||e==="link"){const s=t.indexOf(" ");let a=t;if(s>0){const e=Si(t,s);a=t.substring(e);t=t.substring(0,s)}if(e==="linkcode"||e==="link"&&n.link==="code"){a=`\`${a}\``}const o=(i=(r=n.renderLink)===null||r===void 0?void 0:r.call(n,t,a))!==null&&i!==void 0?i:Ki(t,a);return o}return undefined}function Ki(e,t){try{xr.r.parse(e,true);return`[${t}](${e})`}catch(n){return e}}class ji{constructor(e,t){this.inlines=e;this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){const n=this.inlines[t];const r=this.inlines[t+1];e+=n.toString();if(r&&r.range.start.line>n.range.start.line){e+="\n"}}return e}toMarkdown(e){let t="";for(let n=0;n<this.inlines.length;n++){const r=this.inlines[n];const i=this.inlines[n+1];t+=r.toMarkdown(e);if(i&&i.range.start.line>r.range.start.line){t+="\n"}}return t}}class Vi{constructor(e,t){this.text=e;this.range=t}toString(){return this.text}toMarkdown(){return this.text}}function Wi(e){if(e.endsWith("\n")){return"\n"}else{return"\n\n"}}class Hi{constructor(e){this.indexManager=e.shared.workspace.IndexManager;this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){const t=this.commentProvider.getComment(e);if(t&&Ti(t)){const n=Ai(t);return n.toMarkdown({renderLink:(t,n)=>this.documentationLinkRenderer(e,t,n),renderTag:t=>this.documentationTagRenderer(e,t)})}return undefined}documentationLinkRenderer(e,t,n){var r;const i=(r=this.findNameInPrecomputedScopes(e,t))!==null&&r!==void 0?r:this.findNameInGlobalScope(e,t);if(i&&i.nameSegment){const e=i.nameSegment.range.start.line+1;const t=i.nameSegment.range.start.character+1;const r=i.documentUri.with({fragment:`L${e},${t}`});return`[${n}](${r.toString()})`}else{return undefined}}documentationTagRenderer(e,t){return undefined}findNameInPrecomputedScopes(e,t){const n=(0,Ge.YE)(e);const r=n.precomputedScopes;if(!r){return undefined}let i=e;do{const e=r.get(i);const n=e.find((e=>e.name===t));if(n){return n}i=i.$container}while(i);return undefined}findNameInGlobalScope(e,t){const n=this.indexManager.allElements().find((e=>e.name===t));return n}}class zi{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;if(Hr(e)){return e.$comment}return(t=(0,r.v)(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||t===void 0?void 0:t.text}}class Yi{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,t){return Promise.resolve(this.syncParser.parse(e))}}class qi{constructor(e){this.threadCount=8;this.terminationDelay=200;this.workerPool=[];this.queue=[];this.hydrator=e.serializer.Hydrator}initializeWorkers(){while(this.workerPool.length<this.threadCount){const e=this.createWorker();e.onReady((()=>{if(this.queue.length>0){const t=this.queue.shift();if(t){e.lock();t.resolve(e)}}}));this.workerPool.push(e)}}async parse(e,t){const n=await this.acquireParserWorker(t);const r=new Deferred;let i;const s=t.onCancellationRequested((()=>{i=setTimeout((()=>{this.terminateWorker(n)}),this.terminationDelay)}));n.parse(e).then((e=>{const t=this.hydrator.hydrate(e);r.resolve(t)})).catch((e=>{r.reject(e)})).finally((()=>{s.dispose();clearTimeout(i)}));return r.promise}terminateWorker(e){e.terminate();const t=this.workerPool.indexOf(e);if(t>=0){this.workerPool.splice(t,1)}}async acquireParserWorker(e){this.initializeWorkers();for(const n of this.workerPool){if(n.ready){n.lock();return n}}const t=new Deferred;e.onCancellationRequested((()=>{const e=this.queue.indexOf(t);if(e>=0){this.queue.splice(e,1)}t.reject(OperationCancelled)}));this.queue.push(t);return t.promise}}class Xi{get ready(){return this._ready}get onReady(){return this.onReadyEmitter.event}constructor(e,t,n,r){this.onReadyEmitter=new Emitter;this.deferred=new Deferred;this._ready=true;this._parsing=false;this.sendMessage=e;this._terminate=r;t((e=>{const t=e;this.deferred.resolve(t);this.unlock()}));n((e=>{this.deferred.reject(e);this.unlock()}))}terminate(){this.deferred.reject(OperationCancelled);this._terminate()}lock(){this._ready=false}unlock(){this._parsing=false;this._ready=true;this.onReadyEmitter.fire()}parse(e){if(this._parsing){throw new Error("Parser worker is busy")}this._parsing=true;this.deferred=new Deferred;this.sendMessage(e);return this.deferred.promise}}class Qi{constructor(){this.previousTokenSource=new ar.CancellationTokenSource;this.writeQueue=[];this.readQueue=[];this.done=true}write(e){this.cancelWrite();const t=dr();this.previousTokenSource=t;return this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,n=ar.CancellationToken.None){const r=new gr;const i={action:t,deferred:r,cancellationToken:n};e.push(i);this.performNextOperation();return r.promise}async performNextOperation(){if(!this.done){return}const e=[];if(this.writeQueue.length>0){e.push(this.writeQueue.shift())}else if(this.readQueue.length>0){e.push(...this.readQueue.splice(0,this.readQueue.length))}else{return}this.done=false;await Promise.all(e.map((async({action:e,deferred:t,cancellationToken:n})=>{try{const r=await Promise.resolve().then((()=>e(n)));t.resolve(r)}catch(r){if(pr(r)){t.resolve(undefined)}else{t.reject(r)}}})));this.done=true;this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}}class Zi{constructor(e){this.grammarElementIdMap=new Pr;this.tokenTypeIdMap=new Pr;this.grammar=e.Grammar;this.lexer=e.parser.Lexer;this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):undefined,parserErrors:e.parserErrors.map((e=>Object.assign(Object.assign({},e),{message:e.message}))),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){const t=new Map;const n=new Map;for(const r of(0,Ge.jm)(e)){t.set(r,{})}if(e.$cstNode){for(const t of(0,r.NS)(e.$cstNode)){n.set(t,{})}}return{astNodes:t,cstNodes:n}}dehydrateAstNode(e,t){const n=t.astNodes.get(e);n.$type=e.$type;n.$containerIndex=e.$containerIndex;n.$containerProperty=e.$containerProperty;if(e.$cstNode!==undefined){n.$cstNode=this.dehydrateCstNode(e.$cstNode,t)}for(const[r,i]of Object.entries(e)){if(r.startsWith("$")){continue}if(Array.isArray(i)){const e=[];n[r]=e;for(const n of i){if((0,or.ng)(n)){e.push(this.dehydrateAstNode(n,t))}else if((0,or.A_)(n)){e.push(this.dehydrateReference(n,t))}else{e.push(n)}}}else if((0,or.ng)(i)){n[r]=this.dehydrateAstNode(i,t)}else if((0,or.A_)(i)){n[r]=this.dehydrateReference(i,t)}else if(i!==undefined){n[r]=i}}return n}dehydrateReference(e,t){const n={};n.$refText=e.$refText;if(e.$refNode){n.$refNode=t.cstNodes.get(e.$refNode)}return n}dehydrateCstNode(e,t){const n=t.cstNodes.get(e);if((0,or.br)(e)){n.fullText=e.fullText}else{n.grammarSource=this.getGrammarElementId(e.grammarSource)}n.hidden=e.hidden;n.astNode=t.astNodes.get(e.astNode);if((0,or.mD)(e)){n.content=e.content.map((e=>this.dehydrateCstNode(e,t)))}else if((0,or.FC)(e)){n.tokenType=e.tokenType.name;n.offset=e.offset;n.length=e.length;n.startLine=e.range.start.line;n.startColumn=e.range.start.character;n.endLine=e.range.end.line;n.endColumn=e.range.end.character}return n}hydrate(e){const t=e.value;const n=this.createHydrationContext(t);if("$cstNode"in t){this.hydrateCstNode(t.$cstNode,n)}return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,n)}}createHydrationContext(e){const t=new Map;const n=new Map;for(const r of(0,Ge.jm)(e)){t.set(r,{})}let i;if(e.$cstNode){for(const t of(0,r.NS)(e.$cstNode)){let e;if("fullText"in t){e=new xn(t.fullText);i=e}else if("content"in t){e=new En}else if("tokenType"in t){e=this.hydrateCstLeafNode(t)}if(e){n.set(t,e);e.root=i}}}return{astNodes:t,cstNodes:n}}hydrateAstNode(e,t){const n=t.astNodes.get(e);n.$type=e.$type;n.$containerIndex=e.$containerIndex;n.$containerProperty=e.$containerProperty;if(e.$cstNode){n.$cstNode=t.cstNodes.get(e.$cstNode)}for(const[r,i]of Object.entries(e)){if(r.startsWith("$")){continue}if(Array.isArray(i)){const e=[];n[r]=e;for(const s of i){if((0,or.ng)(s)){e.push(this.setParent(this.hydrateAstNode(s,t),n))}else if((0,or.A_)(s)){e.push(this.hydrateReference(s,n,r,t))}else{e.push(s)}}}else if((0,or.ng)(i)){n[r]=this.setParent(this.hydrateAstNode(i,t),n)}else if((0,or.A_)(i)){n[r]=this.hydrateReference(i,n,r,t)}else if(i!==undefined){n[r]=i}}return n}setParent(e,t){e.$container=t;return e}hydrateReference(e,t,n,r){return this.linker.buildReference(t,n,r.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,n=0){const r=t.cstNodes.get(e);if(typeof e.grammarSource==="number"){r.grammarSource=this.getGrammarElement(e.grammarSource)}r.astNode=t.astNodes.get(e.astNode);if((0,or.mD)(r)){for(const i of e.content){const e=this.hydrateCstNode(i,t,n++);r.content.push(e)}}return r}hydrateCstLeafNode(e){const t=this.getTokenType(e.tokenType);const n=e.offset;const r=e.length;const i=e.startLine;const s=e.startColumn;const a=e.endLine;const o=e.endColumn;const c=e.hidden;const u=new Rn(n,r,{start:{line:i,character:s},end:{line:a,character:o}},t,c);return u}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(!e){return undefined}if(this.grammarElementIdMap.size===0){this.createGrammarElementIdMap()}return this.grammarElementIdMap.get(e)}getGrammarElement(e){if(this.grammarElementIdMap.size===0){this.createGrammarElementIdMap()}const t=this.grammarElementIdMap.getKey(e);return t}createGrammarElementIdMap(){let e=0;for(const t of(0,Ge.jm)(this.grammar)){if((0,a.r1)(t)){this.grammarElementIdMap.set(t,e++)}}}}function Ji(e){return{documentation:{CommentProvider:e=>new zi(e),DocumentationProvider:e=>new Hi(e)},parser:{AsyncParser:e=>new Yi(e),GrammarConfig:e=>o(e),LangiumParser:e=>nr(e),CompletionParser:e=>tr(e),ValueConverter:()=>new sr.d,TokenBuilder:()=>new ir.Q,Lexer:e=>new mi(e),ParserErrorMessageProvider:()=>new bn,LexerErrorMessageProvider:()=>new hi},workspace:{AstNodeLocator:()=>new ai,AstNodeDescriptionProvider:e=>new ii(e),ReferenceDescriptionProvider:e=>new si(e)},references:{Linker:e=>new Cr(e),NameProvider:()=>new Lr,ScopeProvider:e=>new Wr(e),ScopeComputation:e=>new Mr(e),References:e=>new _r(e)},serializer:{Hydrator:e=>new Zi(e),JsonSerializer:e=>new Yr(e)},validation:{DocumentValidator:e=>new Jr(e),ValidationRegistry:e=>new Zr(e)},shared:()=>e.shared}}function es(e){return{ServiceRegistry:e=>new qr(e),workspace:{LangiumDocuments:e=>new Ir(e),LangiumDocumentFactory:e=>new wr(e),DocumentBuilder:e=>new li(e),IndexManager:e=>new di(e),WorkspaceManager:e=>new fi(e),FileSystemProvider:t=>e.fileSystemProvider(t),WorkspaceLock:()=>new Qi,ConfigurationProvider:e=>new ci(e)}}}},41281:(e,t,n)=>{"use strict";n.d(t,{WQ:()=>i});var r;(function(e){e.merge=(e,t)=>l(l({},e),t)})(r||(r={}));function i(e,t,n,r,i,s,a,c,u){const d=[e,t,n,r,i,s,a,c,u].reduce(l,{});return o(d)}const s=Symbol("isProxy");function a(e){if(e&&e[s]){for(const t of Object.values(e)){a(t)}}return e}function o(e,t){const n=new Proxy({},{deleteProperty:()=>false,set:()=>{throw new Error("Cannot set property on injected service container")},get:(r,i)=>{if(i===s){return true}else{return u(r,i,e,t||n)}},getOwnPropertyDescriptor:(r,i)=>(u(r,i,e,t||n),Object.getOwnPropertyDescriptor(r,i)),has:(t,n)=>n in e,ownKeys:()=>[...Object.getOwnPropertyNames(e)]});return n}const c=Symbol();function u(e,t,n,r){if(t in e){if(e[t]instanceof Error){throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:e[t]})}if(e[t]===c){throw new Error('Cycle detected. Please make "'+String(t)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies')}return e[t]}else if(t in n){const s=n[t];e[t]=c;try{e[t]=typeof s==="function"?s(r):o(s,r)}catch(i){e[t]=i instanceof Error?i:undefined;throw i}return e[t]}else{return undefined}}function l(e,t){if(t){for(const[n,r]of Object.entries(t)){if(r!==undefined){const t=e[n];if(t!==null&&r!==null&&typeof t==="object"&&typeof r==="object"){e[n]=l(t,r)}else{e[n]=r}}}}return e}},85684:(e,t,n)=>{"use strict";n.d(t,{$g:()=>be,Bg:()=>ve,Ct:()=>G,Cz:()=>x,D8:()=>ee,FO:()=>Ee,Fy:()=>Oe,GL:()=>Se,IZ:()=>xe,Mz:()=>Ke,O4:()=>Me,QX:()=>We,RP:()=>S,S2:()=>M,SP:()=>O,TF:()=>H,Tu:()=>w,Xj:()=>ae,_c:()=>Te,cY:()=>Ge,fG:()=>Z,jp:()=>pe,lF:()=>Ue,r1:()=>v,rE:()=>ie,s7:()=>Y,vd:()=>Ne,ve:()=>fe,wb:()=>we,wh:()=>ge,z2:()=>Ve});var r=n(64032);const i={ID:/\^?[_a-zA-Z][\w_]*/,STRING:/"(\\.|[^"\\])*"|'(\\.|[^'\\])*'/,NUMBER:/NaN|-?((\d*\.\d+|\d+)([Ee][+-]?\d+)?|Infinity)/,RegexLiteral:/\/(?![*+?])(?:[^\r\n\[/\\]|\\.|\[(?:[^\r\n\]\\]|\\.)*\])+\/[a-z]*/,WS:/\s+/,ML_COMMENT:/\/\*[\s\S]*?\*\//,SL_COMMENT:/\/\/[^\n\r]*/};const s="AbstractRule";function a(e){return He.isInstance(e,s)}const o="AbstractType";function c(e){return He.isInstance(e,o)}const u="Condition";function l(e){return He.isInstance(e,u)}function d(e){return f(e)||e==="current"||e==="entry"||e==="extends"||e==="false"||e==="fragment"||e==="grammar"||e==="hidden"||e==="import"||e==="interface"||e==="returns"||e==="terminal"||e==="true"||e==="type"||e==="infer"||e==="infers"||e==="with"||typeof e==="string"&&/\^?[_a-zA-Z][\w_]*/.test(e)}function f(e){return e==="string"||e==="number"||e==="boolean"||e==="Date"||e==="bigint"}const h="TypeDefinition";function p(e){return He.isInstance(e,h)}const m="ValueLiteral";function g(e){return He.isInstance(e,m)}const y="AbstractElement";function v(e){return He.isInstance(e,y)}const A="ArrayLiteral";function T(e){return He.isInstance(e,A)}const R="ArrayType";function E(e){return He.isInstance(e,R)}const k="BooleanLiteral";function x(e){return He.isInstance(e,k)}const $="Conjunction";function w(e){return He.isInstance(e,$)}const I="Disjunction";function S(e){return He.isInstance(e,I)}const C="Grammar";function N(e){return He.isInstance(e,C)}const L="GrammarImport";function b(e){return He.isInstance(e,L)}const _="InferredType";function O(e){return He.isInstance(e,_)}const P="Interface";function M(e){return He.isInstance(e,P)}const D="NamedArgument";function U(e){return He.isInstance(e,D)}const F="Negation";function G(e){return He.isInstance(e,F)}const B="NumberLiteral";function K(e){return He.isInstance(e,B)}const j="Parameter";function V(e){return He.isInstance(e,j)}const W="ParameterReference";function H(e){return He.isInstance(e,W)}const z="ParserRule";function Y(e){return He.isInstance(e,z)}const q="ReferenceType";function X(e){return He.isInstance(e,q)}const Q="ReturnType";function Z(e){return He.isInstance(e,Q)}const J="SimpleType";function ee(e){return He.isInstance(e,J)}const te="StringLiteral";function ne(e){return He.isInstance(e,te)}const re="TerminalRule";function ie(e){return He.isInstance(e,re)}const se="Type";function ae(e){return He.isInstance(e,se)}const oe="TypeAttribute";function ce(e){return He.isInstance(e,oe)}const ue="UnionType";function le(e){return He.isInstance(e,ue)}const de="Action";function fe(e){return He.isInstance(e,de)}const he="Alternatives";function pe(e){return He.isInstance(e,he)}const me="Assignment";function ge(e){return He.isInstance(e,me)}const ye="CharacterRange";function ve(e){return He.isInstance(e,ye)}const Ae="CrossReference";function Te(e){return He.isInstance(e,Ae)}const Re="EndOfFile";function Ee(e){return He.isInstance(e,Re)}const ke="Group";function xe(e){return He.isInstance(e,ke)}const $e="Keyword";function we(e){return He.isInstance(e,$e)}const Ie="NegatedToken";function Se(e){return He.isInstance(e,Ie)}const Ce="RegexToken";function Ne(e){return He.isInstance(e,Ce)}const Le="RuleCall";function be(e){return He.isInstance(e,Le)}const _e="TerminalAlternatives";function Oe(e){return He.isInstance(e,_e)}const Pe="TerminalGroup";function Me(e){return He.isInstance(e,Pe)}const De="TerminalRuleCall";function Ue(e){return He.isInstance(e,De)}const Fe="UnorderedGroup";function Ge(e){return He.isInstance(e,Fe)}const Be="UntilToken";function Ke(e){return He.isInstance(e,Be)}const je="Wildcard";function Ve(e){return He.isInstance(e,je)}class We extends r.kD{getAllTypes(){return[y,s,o,de,he,A,R,me,k,ye,u,$,Ae,I,Re,C,L,ke,_,P,$e,D,Ie,F,B,j,W,z,q,Ce,Q,Le,J,te,_e,Pe,re,De,se,oe,h,ue,Fe,Be,m,je]}computeIsSubtype(e,t){switch(e){case de:case he:case me:case ye:case Ae:case Re:case ke:case $e:case Ie:case Ce:case Le:case _e:case Pe:case De:case Fe:case Be:case je:{return this.isSubtype(y,t)}case A:case B:case te:{return this.isSubtype(m,t)}case R:case q:case J:case ue:{return this.isSubtype(h,t)}case k:{return this.isSubtype(u,t)||this.isSubtype(m,t)}case $:case I:case F:case W:{return this.isSubtype(u,t)}case _:case P:case se:{return this.isSubtype(o,t)}case z:{return this.isSubtype(s,t)||this.isSubtype(o,t)}case re:{return this.isSubtype(s,t)}default:{return false}}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":{return o}case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":{return s}case"Grammar:usedGrammars":{return C}case"NamedArgument:parameter":case"ParameterReference:parameter":{return j}case"TerminalRuleCall:rule":{return re}default:{throw new Error(`${t} is not a valid reference id.`)}}}getTypeMetaData(e){switch(e){case y:{return{name:y,properties:[{name:"cardinality"},{name:"lookahead"}]}}case A:{return{name:A,properties:[{name:"elements",defaultValue:[]}]}}case R:{return{name:R,properties:[{name:"elementType"}]}}case k:{return{name:k,properties:[{name:"true",defaultValue:false}]}}case $:{return{name:$,properties:[{name:"left"},{name:"right"}]}}case I:{return{name:I,properties:[{name:"left"},{name:"right"}]}}case C:{return{name:C,properties:[{name:"definesHiddenTokens",defaultValue:false},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:false},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]}}case L:{return{name:L,properties:[{name:"path"}]}}case _:{return{name:_,properties:[{name:"name"}]}}case P:{return{name:P,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]}}case D:{return{name:D,properties:[{name:"calledByName",defaultValue:false},{name:"parameter"},{name:"value"}]}}case F:{return{name:F,properties:[{name:"value"}]}}case B:{return{name:B,properties:[{name:"value"}]}}case j:{return{name:j,properties:[{name:"name"}]}}case W:{return{name:W,properties:[{name:"parameter"}]}}case z:{return{name:z,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:false},{name:"definition"},{name:"entry",defaultValue:false},{name:"fragment",defaultValue:false},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:false}]}}case q:{return{name:q,properties:[{name:"referenceType"}]}}case Q:{return{name:Q,properties:[{name:"name"}]}}case J:{return{name:J,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]}}case te:{return{name:te,properties:[{name:"value"}]}}case re:{return{name:re,properties:[{name:"definition"},{name:"fragment",defaultValue:false},{name:"hidden",defaultValue:false},{name:"name"},{name:"type"}]}}case se:{return{name:se,properties:[{name:"name"},{name:"type"}]}}case oe:{return{name:oe,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:false},{name:"name"},{name:"type"}]}}case ue:{return{name:ue,properties:[{name:"types",defaultValue:[]}]}}case de:{return{name:de,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]}}case he:{return{name:he,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]}}case me:{return{name:me,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]}}case ye:{return{name:ye,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]}}case Ae:{return{name:Ae,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:false},{name:"lookahead"},{name:"terminal"},{name:"type"}]}}case Re:{return{name:Re,properties:[{name:"cardinality"},{name:"lookahead"}]}}case ke:{return{name:ke,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]}}case $e:{return{name:$e,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]}}case Ie:{return{name:Ie,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]}}case Ce:{return{name:Ce,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]}}case Le:{return{name:Le,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]}}case _e:{return{name:_e,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]}}case Pe:{return{name:Pe,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]}}case De:{return{name:De,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]}}case Fe:{return{name:Fe,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]}}case Be:{return{name:Be,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]}}case je:{return{name:je,properties:[{name:"cardinality"},{name:"lookahead"}]}}default:{return{name:e,properties:[]}}}}}const He=new We},25355:(e,t,n)=>{"use strict";n.d(t,{Q:()=>u});var r=n(50450);var i=n(85684);var s=n(63752);var a=n(70977);var o=n(65811);var c=n(64386);class u{constructor(){this.diagnostics=[]}buildTokens(e,t){const n=(0,c.Td)((0,a.YV)(e,false));const r=this.buildTerminalTokens(n);const i=this.buildKeywordTokens(n,r,t);r.forEach((e=>{const t=e.PATTERN;if(typeof t==="object"&&t&&"test"in t&&(0,o.Yv)(t)){i.unshift(e)}else{i.push(e)}}));return i}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){const e=[...this.diagnostics];this.diagnostics=[];return e}buildTerminalTokens(e){return e.filter(i.rE).filter((e=>!e.fragment)).map((e=>this.buildTerminalToken(e))).toArray()}buildTerminalToken(e){const t=(0,a.S)(e);const n=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t;const i={name:e.name,PATTERN:n};if(typeof n==="function"){i.LINE_BREAKS=true}if(e.hidden){i.GROUP=(0,o.Yv)(t)?r.JG.SKIPPED:"hidden"}return i}requiresCustomPattern(e){if(e.flags.includes("u")||e.flags.includes("s")){return true}else if(e.source.includes("?<=")||e.source.includes("?<!")){return true}else{return false}}regexPatternFunction(e){const t=new RegExp(e,e.flags+"y");return(e,n)=>{t.lastIndex=n;const r=t.exec(e);return r}}buildKeywordTokens(e,t,n){return e.filter(i.s7).flatMap((e=>(0,s.Uo)(e).filter(i.wb))).distinct((e=>e.value)).toArray().sort(((e,t)=>t.value.length-e.value.length)).map((e=>this.buildKeywordToken(e,t,Boolean(n===null||n===void 0?void 0:n.caseInsensitive))))}buildKeywordToken(e,t,n){const r=this.buildKeywordPattern(e,n);const i={name:e.value,PATTERN:r,LONGER_ALT:this.findLongerAlt(e,t)};if(typeof r==="function"){i.LINE_BREAKS=true}return i}buildKeywordPattern(e,t){return t?new RegExp((0,o.Ao)(e.value)):e.value}findLongerAlt(e,t){return t.reduce(((t,n)=>{const r=n===null||n===void 0?void 0:n.PATTERN;if((r===null||r===void 0?void 0:r.source)&&(0,o.PC)("^"+r.source+"$",e.value)){t.push(n)}return t}),[])}}},14480:(e,t,n)=>{"use strict";n.d(t,{d:()=>s});var r=n(85684);var i=n(70977);class s{convert(e,t){let n=t.grammarSource;if((0,r._c)(n)){n=(0,i.g4)(n)}if((0,r.$g)(n)){const r=n.rule.ref;if(!r){throw new Error("This cst node was not parsed by a rule.")}return this.runConverter(r,e,t)}return e}runConverter(e,t,n){var r;switch(e.name.toUpperCase()){case"INT":return a.convertInt(t);case"STRING":return a.convertString(t);case"ID":return a.convertID(t)}switch((r=(0,i.P3)(e))===null||r===void 0?void 0:r.toLowerCase()){case"number":return a.convertNumber(t);case"boolean":return a.convertBoolean(t);case"bigint":return a.convertBigint(t);case"date":return a.convertDate(t);default:return t}}}var a;(function(e){function t(e){let t="";for(let r=1;r<e.length-1;r++){const i=e.charAt(r);if(i==="\\"){const i=e.charAt(++r);t+=n(i)}else{t+=i}}return t}e.convertString=t;function n(e){switch(e){case"b":return"\b";case"f":return"\f";case"n":return"\n";case"r":return"\r";case"t":return"\t";case"v":return"\v";case"0":return"\0";default:return e}}function r(e){if(e.charAt(0)==="^"){return e.substring(1)}else{return e}}e.convertID=r;function i(e){return parseInt(e)}e.convertInt=i;function s(e){return BigInt(e)}e.convertBigint=s;function a(e){return new Date(e)}e.convertDate=a;function o(e){return Number(e)}e.convertNumber=o;function c(e){return e.toLowerCase()==="true"}e.convertBoolean=c})(a||(a={}))},64032:(e,t,n)=>{"use strict";n.d(t,{A_:()=>i,FC:()=>u,Nr:()=>s,Zl:()=>a,br:()=>l,kD:()=>o,mD:()=>c,ng:()=>r});function r(e){return typeof e==="object"&&e!==null&&typeof e.$type==="string"}function i(e){return typeof e==="object"&&e!==null&&typeof e.$refText==="string"}function s(e){return typeof e==="object"&&e!==null&&typeof e.name==="string"&&typeof e.type==="string"&&typeof e.path==="string"}function a(e){return typeof e==="object"&&e!==null&&r(e.container)&&i(e.reference)&&typeof e.message==="string"}class o{constructor(){this.subtypes={};this.allSubtypes={}}isInstance(e,t){return r(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t){return true}let n=this.subtypes[e];if(!n){n=this.subtypes[e]={}}const r=n[t];if(r!==undefined){return r}else{const r=this.computeIsSubtype(e,t);n[t]=r;return r}}getAllSubTypes(e){const t=this.allSubtypes[e];if(t){return t}else{const t=this.getAllTypes();const n=[];for(const r of t){if(this.isSubtype(r,e)){n.push(r)}}this.allSubtypes[e]=n;return n}}}function c(e){return typeof e==="object"&&e!==null&&Array.isArray(e.content)}function u(e){return typeof e==="object"&&e!==null&&typeof e.tokenType==="object"}function l(e){return c(e)&&typeof e.fullText==="string"}},63752:(e,t,n)=>{"use strict";n.d(t,{DM:()=>m,OP:()=>y,SD:()=>a,Uo:()=>f,VN:()=>d,XG:()=>o,YE:()=>u,cQ:()=>l,jm:()=>h});var r=n(64032);var i=n(64386);var s=n(5730);function a(e){for(const[t,n]of Object.entries(e)){if(!t.startsWith("$")){if(Array.isArray(n)){n.forEach(((n,i)=>{if((0,r.ng)(n)){n.$container=e;n.$containerProperty=t;n.$containerIndex=i}}))}else if((0,r.ng)(n)){n.$container=e;n.$containerProperty=t}}}}function o(e,t){let n=e;while(n){if(t(n)){return n}n=n.$container}return undefined}function c(e,t){let n=e;while(n){if(t(n)){return true}n=n.$container}return false}function u(e){const t=l(e);const n=t.$document;if(!n){throw new Error("AST node has no document.")}return n}function l(e){while(e.$container){e=e.$container}return e}function d(e,t){if(!e){throw new Error("Node must be an AstNode.")}const n=t===null||t===void 0?void 0:t.range;return new i.fq((()=>({keys:Object.keys(e),keyIndex:0,arrayIndex:0})),(t=>{while(t.keyIndex<t.keys.length){const i=t.keys[t.keyIndex];if(!i.startsWith("$")){const s=e[i];if((0,r.ng)(s)){t.keyIndex++;if(p(s,n)){return{done:false,value:s}}}else if(Array.isArray(s)){while(t.arrayIndex<s.length){const e=t.arrayIndex++;const i=s[e];if((0,r.ng)(i)&&p(i,n)){return{done:false,value:i}}}t.arrayIndex=0}}t.keyIndex++}return i.Rf}))}function f(e,t){if(!e){throw new Error("Root node must be an AstNode.")}return new i.Vj(e,(e=>d(e,t)))}function h(e,t){if(!e){throw new Error("Root node must be an AstNode.")}else if((t===null||t===void 0?void 0:t.range)&&!p(e,t.range)){return new i.Vj(e,(()=>[]))}return new i.Vj(e,(e=>d(e,t)),{includeRoot:true})}function p(e,t){var n;if(!t){return true}const r=(n=e.$cstNode)===null||n===void 0?void 0:n.range;if(!r){return false}return(0,s.r4)(r,t)}function m(e){return new i.fq((()=>({keys:Object.keys(e),keyIndex:0,arrayIndex:0})),(t=>{while(t.keyIndex<t.keys.length){const n=t.keys[t.keyIndex];if(!n.startsWith("$")){const i=e[n];if((0,r.A_)(i)){t.keyIndex++;return{done:false,value:{reference:i,container:e,property:n}}}else if(Array.isArray(i)){while(t.arrayIndex<i.length){const s=t.arrayIndex++;const a=i[s];if((0,r.A_)(a)){return{done:false,value:{reference:a,container:e,property:n,index:s}}}}t.arrayIndex=0}}t.keyIndex++}return i.Rf}))}function g(e,t=u(e).parseResult.value){const n=[];h(t).forEach((t=>{m(t).forEach((t=>{if(t.reference.ref===e){n.push(t.reference)}}))}));return stream(n)}function y(e,t){const n=e.getTypeMetaData(t.$type);const r=t;for(const i of n.properties){if(i.defaultValue!==undefined&&r[i.name]===undefined){r[i.name]=v(i.defaultValue)}}}function v(e){if(Array.isArray(e)){return[...e.map(v)]}else{return e}}function A(e,t){const n={$type:e.$type};for(const[r,i]of Object.entries(e)){if(!r.startsWith("$")){if(isAstNode(i)){n[r]=A(i,t)}else if(isReference(i)){n[r]=t(n,r,i.$refNode,i.$refText)}else if(Array.isArray(i)){const e=[];for(const s of i){if(isAstNode(s)){e.push(A(s,t))}else if(isReference(s)){e.push(t(n,r,s.$refNode,s.$refText))}else{e.push(s)}}n[r]=e}else{n[r]=i}}}a(n);return n}},5730:(e,t,n)=>{"use strict";n.d(t,{El:()=>h,NS:()=>s,SX:()=>u,pO:()=>o,r4:()=>f,v:()=>m,wf:()=>c});var r=n(64032);var i=n(64386);function s(e){return new i.Vj(e,(e=>{if((0,r.mD)(e)){return e.content}else{return[]}}),{includeRoot:true})}function a(e){return s(e).filter(isLeafCstNode)}function o(e,t){while(e.container){e=e.container;if(e===t){return true}}return false}function c(e){return{start:{character:e.startColumn-1,line:e.startLine-1},end:{character:e.endColumn,line:e.endLine-1}}}function u(e){if(!e){return undefined}const{offset:t,end:n,range:r}=e;return{range:r,offset:t,end:n,length:n-t}}var l;(function(e){e[e["Before"]=0]="Before";e[e["After"]=1]="After";e[e["OverlapFront"]=2]="OverlapFront";e[e["OverlapBack"]=3]="OverlapBack";e[e["Inside"]=4]="Inside";e[e["Outside"]=5]="Outside"})(l||(l={}));function d(e,t){if(e.end.line<t.start.line||e.end.line===t.start.line&&e.end.character<=t.start.character){return l.Before}else if(e.start.line>t.end.line||e.start.line===t.end.line&&e.start.character>=t.end.character){return l.After}const n=e.start.line>t.start.line||e.start.line===t.start.line&&e.start.character>=t.start.character;const r=e.end.line<t.end.line||e.end.line===t.end.line&&e.end.character<=t.end.character;if(n&&r){return l.Inside}else if(n){return l.OverlapBack}else if(r){return l.OverlapFront}else{return l.Outside}}function f(e,t){const n=d(e,t);return n>l.After}const h=/^[\w\p{L}]$/u;function p(e,t,n=h){if(e){if(t>0){const r=t-e.offset;const i=e.text.charAt(r);if(!n.test(i)){t--}}return y(e,t)}return undefined}function m(e,t){if(e){const n=T(e,true);if(n&&g(n,t)){return n}if((0,r.br)(e)){const n=e.content.findIndex((e=>!e.hidden));for(let r=n-1;r>=0;r--){const n=e.content[r];if(g(n,t)){return n}}}}return undefined}function g(e,t){return(0,r.FC)(e)&&t.includes(e.tokenType.name)}function y(e,t){if(isLeafCstNode(e)){return e}else if(isCompositeCstNode(e)){const n=A(e,t,false);if(n){return y(n,t)}}return undefined}function v(e,t){if(isLeafCstNode(e)){return e}else if(isCompositeCstNode(e)){const n=A(e,t,true);if(n){return v(n,t)}}return undefined}function A(e,t,n){let r=0;let i=e.content.length-1;let s=undefined;while(r<=i){const a=Math.floor((r+i)/2);const o=e.content[a];if(o.offset<=t&&o.end>t){return o}if(o.end<=t){s=n?o:undefined;r=a+1}else{i=a-1}}return s}function T(e,t=true){while(e.container){const n=e.container;let r=n.content.indexOf(e);while(r>0){r--;const e=n.content[r];if(t||!e.hidden){return e}}e=n}return undefined}function R(e,t=true){while(e.container){const n=e.container;let r=n.content.indexOf(e);const i=n.content.length-1;while(r<i){r++;const e=n.content[r];if(t||!e.hidden){return e}}e=n}return undefined}function E(e){if(e.range.start.character===0){return e}const t=e.range.start.line;let n=e;let r;while(e.container){const i=e.container;const s=r!==null&&r!==void 0?r:i.content.indexOf(e);if(s===0){e=i;r=undefined}else{r=s-1;e=i.content[r]}if(e.range.start.line!==t){break}n=e}return n}function k(e,t){const n=x(e,t);if(!n){return[]}return n.parent.content.slice(n.a+1,n.b)}function x(e,t){const n=$(e);const r=$(t);let i;for(let s=0;s<n.length&&s<r.length;s++){const e=n[s];const t=r[s];if(e.parent===t.parent){i={parent:e.parent,a:e.index,b:t.index}}else{break}}return i}function $(e){const t=[];while(e.container){const n=e.container;const r=n.content.indexOf(e);t.push({parent:n,index:r});e=n}return t.reverse()}},73101:(e,t,n)=>{"use strict";n.d(t,{W:()=>r,d:()=>i});class r extends Error{constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}}function i(e){throw new Error("Error! The input value was not handled.")}},70977:(e,t,n)=>{"use strict";n.d(t,{Bd:()=>m,P3:()=>M,PV:()=>b,Rp:()=>R,S:()=>D,SS:()=>A,U5:()=>E,Uz:()=>_,Xq:()=>S,YV:()=>d,eb:()=>p,g4:()=>h,qO:()=>g});var r=n(73101);var i=n(85684);var s=n(64032);var a=n(63752);var o=n(5730);var c=n(65811);function u(e){return e.rules.find((e=>i.s7(e)&&e.entry))}function l(e){return e.rules.filter((e=>i.rE(e)&&e.hidden))}function d(e,t){const n=new Set;const r=u(e);if(!r){return new Set(e.rules)}const s=[r].concat(l(e));for(const i of s){f(i,n,t)}const a=new Set;for(const o of e.rules){if(n.has(o.name)||i.rE(o)&&o.hidden){a.add(o)}}return a}function f(e,t,n){t.add(e.name);(0,a.Uo)(e).forEach((e=>{if(i.$g(e)||n&&i.lF(e)){const r=e.rule.ref;if(r&&!t.has(r.name)){f(r,t,n)}}}))}function h(e){if(e.terminal){return e.terminal}else if(e.type.ref){const t=E(e.type.ref);return t===null||t===void 0?void 0:t.terminal}return undefined}function p(e){return e.hidden&&!(0,c.Yv)(D(e))}function m(e,t){if(!e||!t){return[]}return y(e,t,e.astNode,true)}function g(e,t,n){if(!e||!t){return undefined}const r=y(e,t,e.astNode,true);if(r.length===0){return undefined}if(n!==undefined){n=Math.max(0,Math.min(n,r.length-1))}else{n=0}return r[n]}function y(e,t,n,r){if(!r){const n=(0,a.XG)(e.grammarSource,i.wh);if(n&&n.feature===t){return[e]}}if((0,s.mD)(e)&&e.astNode===n){return e.content.flatMap((e=>y(e,t,n,false)))}return[]}function v(e,t){if(!e){return[]}return T(e,t,e===null||e===void 0?void 0:e.astNode)}function A(e,t,n){if(!e){return undefined}const r=T(e,t,e===null||e===void 0?void 0:e.astNode);if(r.length===0){return undefined}if(n!==undefined){n=Math.max(0,Math.min(n,r.length-1))}else{n=0}return r[n]}function T(e,t,n){if(e.astNode!==n){return[]}if(i.wb(e.grammarSource)&&e.grammarSource.value===t){return[e]}const r=(0,o.NS)(e).iterator();let s;const a=[];do{s=r.next();if(!s.done){const e=s.value;if(e.astNode===n){if(i.wb(e.grammarSource)&&e.grammarSource.value===t){a.push(e)}}else{r.prune()}}}while(!s.done);return a}function R(e){var t;const n=e.astNode;while(n===((t=e.container)===null||t===void 0?void 0:t.astNode)){const t=(0,a.XG)(e.grammarSource,i.wh);if(t){return t}e=e.container}return undefined}function E(e){let t=e;if(i.SP(t)){if(i.ve(t.$container)){t=t.$container.$container}else if(i.s7(t.$container)){t=t.$container}else{(0,r.d)(t.$container)}}return k(e,t,new Map)}function k(e,t,n){var r;function s(t,r){let s=undefined;const o=(0,a.XG)(t,i.wh);if(!o){s=k(r,r,n)}n.set(e,s);return s}if(n.has(e)){return n.get(e)}n.set(e,undefined);for(const o of(0,a.Uo)(t)){if(i.wh(o)&&o.feature.toLowerCase()==="name"){n.set(e,o);return o}else if(i.$g(o)&&i.s7(o.rule.ref)){return s(o,o.rule.ref)}else if(i.D8(o)&&((r=o.typeRef)===null||r===void 0?void 0:r.ref)){return s(o,o.typeRef.ref)}}return undefined}function x(e){const t=e.$container;if(ast.isGroup(t)){const n=t.elements;const r=n.indexOf(e);for(let e=r-1;e>=0;e--){const t=n[e];if(ast.isAction(t)){return t}else{const t=streamAllContents(n[e]).find(ast.isAction);if(t){return t}}}}if(ast.isAbstractElement(t)){return x(t)}else{return undefined}}function $(e,t){return e==="?"||e==="*"||ast.isGroup(t)&&Boolean(t.guardCondition)}function w(e){return e==="*"||e==="+"}function I(e){return e==="+="}function S(e){return C(e,new Set)}function C(e,t){if(t.has(e)){return true}else{t.add(e)}for(const n of(0,a.Uo)(e)){if(i.$g(n)){if(!n.rule.ref){return false}if(i.s7(n.rule.ref)&&!C(n.rule.ref,t)){return false}}else if(i.wh(n)){return false}else if(i.ve(n)){return false}}return Boolean(e.definition)}function N(e){return L(e.type,new Set)}function L(e,t){if(t.has(e)){return true}else{t.add(e)}if(ast.isArrayType(e)){return false}else if(ast.isReferenceType(e)){return false}else if(ast.isUnionType(e)){return e.types.every((e=>L(e,t)))}else if(ast.isSimpleType(e)){if(e.primitiveType!==undefined){return true}else if(e.stringType!==undefined){return true}else if(e.typeRef!==undefined){const n=e.typeRef.ref;if(ast.isType(n)){return L(n.type,t)}else{return false}}else{return false}}else{return false}}function b(e){if(e.inferredType){return e.inferredType.name}else if(e.dataType){return e.dataType}else if(e.returnType){const t=e.returnType.ref;if(t){if(i.s7(t)){return t.name}else if(i.S2(t)||i.Xj(t)){return t.name}}}return undefined}function _(e){var t;if(i.s7(e)){return S(e)?e.name:(t=b(e))!==null&&t!==void 0?t:e.name}else if(i.S2(e)||i.Xj(e)||i.fG(e)){return e.name}else if(i.ve(e)){const t=O(e);if(t){return t}}else if(i.SP(e)){return e.name}throw new Error("Cannot get name of Unknown Type")}function O(e){var t;if(e.inferredType){return e.inferredType.name}else if((t=e.type)===null||t===void 0?void 0:t.ref){return _(e.type.ref)}return undefined}function P(e){var t,n,r;if(ast.isTerminalRule(e)){return(n=(t=e.type)===null||t===void 0?void 0:t.name)!==null&&n!==void 0?n:"string"}else{return S(e)?e.name:(r=b(e))!==null&&r!==void 0?r:e.name}}function M(e){var t,n,r;if(i.rE(e)){return(n=(t=e.type)===null||t===void 0?void 0:t.name)!==null&&n!==void 0?n:"string"}else{return(r=b(e))!==null&&r!==void 0?r:e.name}}function D(e){const t={s:false,i:false,u:false};const n=F(e.definition,t);const r=Object.entries(t).filter((([,e])=>e)).map((([e])=>e)).join("");return new RegExp(n,r)}const U=/[\s\S]/.source;function F(e,t){if(i.Fy(e)){return G(e)}else if(i.O4(e)){return B(e)}else if(i.Bg(e)){return V(e)}else if(i.lF(e)){const t=e.rule.ref;if(!t){throw new Error("Missing rule reference.")}return H(F(t.definition),{cardinality:e.cardinality,lookahead:e.lookahead})}else if(i.GL(e)){return j(e)}else if(i.Mz(e)){return K(e)}else if(i.vd(e)){const n=e.regex.lastIndexOf("/");const r=e.regex.substring(1,n);const i=e.regex.substring(n+1);if(t){t.i=i.includes("i");t.s=i.includes("s");t.u=i.includes("u")}return H(r,{cardinality:e.cardinality,lookahead:e.lookahead,wrap:false})}else if(i.z2(e)){return H(U,{cardinality:e.cardinality,lookahead:e.lookahead})}else{throw new Error(`Invalid terminal element: ${e===null||e===void 0?void 0:e.$type}`)}}function G(e){return H(e.elements.map((e=>F(e))).join("|"),{cardinality:e.cardinality,lookahead:e.lookahead})}function B(e){return H(e.elements.map((e=>F(e))).join(""),{cardinality:e.cardinality,lookahead:e.lookahead})}function K(e){return H(`${U}*?${F(e.terminal)}`,{cardinality:e.cardinality,lookahead:e.lookahead})}function j(e){return H(`(?!${F(e.terminal)})${U}*?`,{cardinality:e.cardinality,lookahead:e.lookahead})}function V(e){if(e.right){return H(`[${W(e.left)}-${W(e.right)}]`,{cardinality:e.cardinality,lookahead:e.lookahead,wrap:false})}return H(W(e.left),{cardinality:e.cardinality,lookahead:e.lookahead,wrap:false})}function W(e){return(0,c.Nt)(e.value)}function H(e,t){var n;if(t.wrap!==false||t.lookahead){e=`(${(n=t.lookahead)!==null&&n!==void 0?n:""}${e})`}if(t.cardinality){return`${e}${t.cardinality}`}return e}},65811:(e,t,n)=>{"use strict";n.d(t,{Ao:()=>h,Nt:()=>f,PC:()=>p,TH:()=>i,Yv:()=>d,lU:()=>u});var r=n(83173);const i=/\r?\n/gm;const s=new r.H;class a extends r.z{constructor(){super(...arguments);this.isStarting=true;this.endRegexpStack=[];this.multiline=false}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=false;this.regex=e;this.startRegexp="";this.isStarting=true;this.endRegexpStack=[]}visitGroup(e){if(e.quantifier){this.isStarting=false;this.endRegexpStack=[]}}visitCharacter(e){const t=String.fromCharCode(e.value);if(!this.multiline&&t==="\n"){this.multiline=true}if(e.quantifier){this.isStarting=false;this.endRegexpStack=[]}else{const e=f(t);this.endRegexpStack.push(e);if(this.isStarting){this.startRegexp+=e}}}visitSet(e){if(!this.multiline){const t=this.regex.substring(e.loc.begin,e.loc.end);const n=new RegExp(t);this.multiline=Boolean("\n".match(n))}if(e.quantifier){this.isStarting=false;this.endRegexpStack=[]}else{const t=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(t);if(this.isStarting){this.startRegexp+=t}}}visitChildren(e){if(e.type==="Group"){const t=e;if(t.quantifier){return}}super.visitChildren(e)}}const o=new a;function c(e){try{if(typeof e!=="string"){e=e.source}e=`/${e}/`;const t=s.pattern(e);const n=[];for(const r of t.value.value){o.reset(e);o.visit(r);n.push({start:o.startRegexp,end:o.endRegex})}return n}catch(t){return[]}}function u(e){try{if(typeof e==="string"){e=new RegExp(e)}e=e.toString();o.reset(e);o.visit(s.pattern(e));return o.multiline}catch(t){return false}}const l=("\f\n\r\t\v           "+"   \u2028\u2029  　\ufeff").split("");function d(e){const t=typeof e==="string"?new RegExp(e):e;return l.some((e=>t.test(e)))}function f(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function h(e){return Array.prototype.map.call(e,(e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:f(e))).join("")}function p(e,t){const n=m(e);const r=t.match(n);return!!r&&r[0].length>0}function m(e){if(typeof e==="string"){e=new RegExp(e)}const t=e,n=e.source;let r=0;function i(){let e="",s;function a(t){e+=n.substr(r,t);r+=t}function o(t){e+="(?:"+n.substr(r,t)+"|$)";r+=t}while(r<n.length){switch(n[r]){case"\\":switch(n[r+1]){case"c":o(3);break;case"x":o(4);break;case"u":if(t.unicode){if(n[r+2]==="{"){o(n.indexOf("}",r)-r+1)}else{o(6)}}else{o(2)}break;case"p":case"P":if(t.unicode){o(n.indexOf("}",r)-r+1)}else{o(2)}break;case"k":o(n.indexOf(">",r)-r+1);break;default:o(2);break}break;case"[":s=/\[(?:\\.|.)*?\]/g;s.lastIndex=r;s=s.exec(n)||[];o(s[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":a(1);break;case"{":s=/\{\d+,?\d*\}/g;s.lastIndex=r;s=s.exec(n);if(s){a(s[0].length)}else{o(1)}break;case"(":if(n[r+1]==="?"){switch(n[r+2]){case":":e+="(?:";r+=3;e+=i()+"|$)";break;case"=":e+="(?=";r+=3;e+=i()+")";break;case"!":s=r;r+=3;i();e+=n.substr(s,r-s);break;case"<":switch(n[r+3]){case"=":case"!":s=r;r+=4;i();e+=n.substr(s,r-s);break;default:a(n.indexOf(">",r)-r+1);e+=i()+"|$)";break}break}}else{a(1);e+=i()+"|$)"}break;case")":++r;return e;default:o(1);break}}return e}return new RegExp(i(),e.flags)}},64386:(e,t,n)=>{"use strict";n.d(t,{B5:()=>a,Rf:()=>o,Td:()=>c,Vj:()=>u,fq:()=>r,iD:()=>l});class r{constructor(e,t){this.startFn=e;this.nextFn=t}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){const e=this.iterator();return Boolean(e.next().done)}count(){const e=this.iterator();let t=0;let n=e.next();while(!n.done){t++;n=e.next()}return t}toArray(){const e=[];const t=this.iterator();let n;do{n=t.next();if(n.value!==undefined){e.push(n.value)}}while(!n.done);return e}toSet(){return new Set(this)}toMap(e,t){const n=this.map((n=>[e?e(n):n,t?t(n):n]));return new Map(n)}toString(){return this.join()}concat(e){return new r((()=>({first:this.startFn(),firstDone:false,iterator:e[Symbol.iterator]()})),(e=>{let t;if(!e.firstDone){do{t=this.nextFn(e.first);if(!t.done){return t}}while(!t.done);e.firstDone=true}do{t=e.iterator.next();if(!t.done){return t}}while(!t.done);return o}))}join(e=","){const t=this.iterator();let n="";let r;let s=false;do{r=t.next();if(!r.done){if(s){n+=e}n+=i(r.value)}s=true}while(!r.done);return n}indexOf(e,t=0){const n=this.iterator();let r=0;let i=n.next();while(!i.done){if(r>=t&&i.value===e){return r}i=n.next();r++}return-1}every(e){const t=this.iterator();let n=t.next();while(!n.done){if(!e(n.value)){return false}n=t.next()}return true}some(e){const t=this.iterator();let n=t.next();while(!n.done){if(e(n.value)){return true}n=t.next()}return false}forEach(e){const t=this.iterator();let n=0;let r=t.next();while(!r.done){e(r.value,n);r=t.next();n++}}map(e){return new r(this.startFn,(t=>{const{done:n,value:r}=this.nextFn(t);if(n){return o}else{return{done:false,value:e(r)}}}))}filter(e){return new r(this.startFn,(t=>{let n;do{n=this.nextFn(t);if(!n.done&&e(n.value)){return n}}while(!n.done);return o}))}nonNullable(){return this.filter((e=>e!==undefined&&e!==null))}reduce(e,t){const n=this.iterator();let r=t;let i=n.next();while(!i.done){if(r===undefined){r=i.value}else{r=e(r,i.value)}i=n.next()}return r}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,n){const r=e.next();if(r.done){return n}const i=this.recursiveReduce(e,t,n);if(i===undefined){return r.value}return t(i,r.value)}find(e){const t=this.iterator();let n=t.next();while(!n.done){if(e(n.value)){return n.value}n=t.next()}return undefined}findIndex(e){const t=this.iterator();let n=0;let r=t.next();while(!r.done){if(e(r.value)){return n}r=t.next();n++}return-1}includes(e){const t=this.iterator();let n=t.next();while(!n.done){if(n.value===e){return true}n=t.next()}return false}flatMap(e){return new r((()=>({this:this.startFn()})),(t=>{do{if(t.iterator){const e=t.iterator.next();if(e.done){t.iterator=undefined}else{return e}}const{done:n,value:r}=this.nextFn(t.this);if(!n){const n=e(r);if(s(n)){t.iterator=n[Symbol.iterator]()}else{return{done:false,value:n}}}}while(t.iterator);return o}))}flat(e){if(e===undefined){e=1}if(e<=0){return this}const t=e>1?this.flat(e-1):this;return new r((()=>({this:t.startFn()})),(e=>{do{if(e.iterator){const t=e.iterator.next();if(t.done){e.iterator=undefined}else{return t}}const{done:n,value:r}=t.nextFn(e.this);if(!n){if(s(r)){e.iterator=r[Symbol.iterator]()}else{return{done:false,value:r}}}}while(e.iterator);return o}))}head(){const e=this.iterator();const t=e.next();if(t.done){return undefined}return t.value}tail(e=1){return new r((()=>{const t=this.startFn();for(let n=0;n<e;n++){const e=this.nextFn(t);if(e.done){return t}}return t}),this.nextFn)}limit(e){return new r((()=>({size:0,state:this.startFn()})),(t=>{t.size++;if(t.size>e){return o}return this.nextFn(t.state)}))}distinct(e){return new r((()=>({set:new Set,internalState:this.startFn()})),(t=>{let n;do{n=this.nextFn(t.internalState);if(!n.done){const r=e?e(n.value):n.value;if(!t.set.has(r)){t.set.add(r);return n}}}while(!n.done);return o}))}exclude(e,t){const n=new Set;for(const r of e){const e=t?t(r):r;n.add(e)}return this.filter((e=>{const r=t?t(e):e;return!n.has(r)}))}}function i(e){if(typeof e==="string"){return e}if(typeof e==="undefined"){return"undefined"}if(typeof e.toString==="function"){return e.toString()}return Object.prototype.toString.call(e)}function s(e){return!!e&&typeof e[Symbol.iterator]==="function"}const a=new r((()=>undefined),(()=>o));const o=Object.freeze({done:true,value:undefined});function c(...e){if(e.length===1){const t=e[0];if(t instanceof r){return t}if(s(t)){return new r((()=>t[Symbol.iterator]()),(e=>e.next()))}if(typeof t.length==="number"){return new r((()=>({index:0})),(e=>{if(e.index<t.length){return{done:false,value:t[e.index++]}}else{return o}}))}}if(e.length>1){return new r((()=>({collIndex:0,arrIndex:0})),(t=>{do{if(t.iterator){const e=t.iterator.next();if(!e.done){return e}t.iterator=undefined}if(t.array){if(t.arrIndex<t.array.length){return{done:false,value:t.array[t.arrIndex++]}}t.array=undefined;t.arrIndex=0}if(t.collIndex<e.length){const n=e[t.collIndex++];if(s(n)){t.iterator=n[Symbol.iterator]()}else if(n&&typeof n.length==="number"){t.array=n}}}while(t.iterator||t.array||t.collIndex<e.length);return o}))}return a}class u extends r{constructor(e,t,n){super((()=>({iterators:(n===null||n===void 0?void 0:n.includeRoot)?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:false})),(e=>{if(e.pruned){e.iterators.pop();e.pruned=false}while(e.iterators.length>0){const n=e.iterators[e.iterators.length-1];const r=n.next();if(r.done){e.iterators.pop()}else{e.iterators.push(t(r.value)[Symbol.iterator]());return r}}return o}))}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),prune:()=>{e.state.pruned=true},[Symbol.iterator]:()=>e};return e}}var l;(function(e){function t(e){return e.reduce(((e,t)=>e+t),0)}e.sum=t;function n(e){return e.reduce(((e,t)=>e*t),0)}e.product=n;function r(e){return e.reduce(((e,t)=>Math.min(e,t)))}e.min=r;function i(e){return e.reduce(((e,t)=>Math.max(e,t)))}e.max=i})(l||(l={}))},6052:(e,t,n)=>{"use strict";n.d(t,{D:()=>i});class r{readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}}const i={fileSystemProvider:()=>new r}},95852:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(62579);function i(e,t,n){var i=-1,s=e.length;while(++i<s){var a=e[i],o=t(a);if(o!=null&&(c===undefined?o===o&&!(0,r.A)(o):n(o,c))){var c=o,u=a}}return u}const s=i},51135:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});function r(e,t){return e<t}const i=r},97457:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(15912);var i=n(21585);function s(e,t){var n=-1,s=(0,i.A)(e)?Array(e.length):[];(0,r.A)(e,(function(e,r,i){s[++n]=t(e,r,i)}));return s}const a=s},44835:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var r=n(22883);var i=n(16542);var s=n(65900);var a=n(78912);var o=n(85356);var c=n(43512);function u(e,t,n,r){if(!(0,o.A)(e)){return e}t=(0,s.A)(t,e);var u=-1,l=t.length,d=l-1,f=e;while(f!=null&&++u<l){var h=(0,c.A)(t[u]),p=n;if(h==="__proto__"||h==="constructor"||h==="prototype"){return e}if(u!=d){var m=f[h];p=r?r(m,h,f):undefined;if(p===undefined){p=(0,o.A)(m)?m:(0,a.A)(t[u+1])?[]:{}}}(0,i.A)(f,h,p);f=f[h]}return e}const l=u;function d(e,t,n){var i=-1,a=t.length,o={};while(++i<a){var c=t[i],u=(0,r.A)(e,c);if(n(u,c)){l(o,(0,s.A)(c,e),u)}}return o}const f=d},97134:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(59386);var i=4;function s(e){return(0,r.A)(e,i)}const a=s},38693:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(55881);var i=n(24461);var s=n(31943);var a=n(13839);var o=Object.prototype;var c=o.hasOwnProperty;var u=(0,r.A)((function(e,t){e=Object(e);var n=-1;var r=t.length;var u=r>2?t[2]:undefined;if(u&&(0,s.A)(t[0],t[1],u)){r=1}while(++n<r){var l=t[n];var d=(0,a.A)(l);var f=-1;var h=d.length;while(++f<h){var p=d[f];var m=e[p];if(m===undefined||(0,i.A)(m,o[p])&&!c.call(e,p)){e[p]=l[p]}}}return e}));const l=u},85075:(e,t,n)=>{"use strict";n.d(t,{A:()=>p});var r=n(1121);var i=n(21585);var s=n(37947);function a(e){return function(t,n,a){var o=Object(t);if(!(0,i.A)(t)){var c=(0,r.A)(n,3);t=(0,s.A)(t);n=function(e){return c(o[e],e,o)}}var u=e(t,n,a);return u>-1?o[c?t[u]:u]:undefined}}const o=a;var c=n(97314);var u=n(29914);var l=Math.max;function d(e,t,n){var i=e==null?0:e.length;if(!i){return-1}var s=n==null?0:(0,u.A)(n);if(s<0){s=l(i+s,0)}return(0,c.A)(e,(0,r.A)(t,3),s)}const f=d;var h=o(f);const p=h},57852:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(62040);var i=n(8937);function s(e,t){return(0,r.A)((0,i.A)(e,t),1)}const a=s},74033:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(62040);function i(e){var t=e==null?0:e.length;return t?(0,r.A)(e,1):[]}const s=i},2850:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var r=Object.prototype;var i=r.hasOwnProperty;function s(e,t){return e!=null&&i.call(e,t)}const a=s;var o=n(64491);function c(e,t){return e!=null&&(0,o.A)(e,t,a)}const u=c},86378:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(64128);var i=n(39990);var s=n(53315);var a="[object String]";function o(e){return typeof e=="string"||!(0,i.A)(e)&&(0,s.A)(e)&&(0,r.A)(e)==a}const c=o},80359:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});function r(e){var t=e==null?0:e.length;return t?e[t-1]:undefined}const i=r},8937:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(20900);var i=n(1121);var s=n(97457);var a=n(39990);function o(e,t){var n=(0,a.A)(e)?r.A:s.A;return n(e,(0,i.A)(t,3))}const c=o},963:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(95852);var i=n(51135);var s=n(63077);function a(e){return e&&e.length?(0,r.A)(e,s.A,i.A):undefined}const o=a},52712:(e,t,n)=>{"use strict";n.d(t,{A:()=>R});var r=/\s/;function i(e){var t=e.length;while(t--&&r.test(e.charAt(t))){}return t}const s=i;var a=/^\s+/;function o(e){return e?e.slice(0,s(e)+1).replace(a,""):e}const c=o;var u=n(85356);var l=n(62579);var d=0/0;var f=/^[-+]0x[0-9a-f]+$/i;var h=/^0b[01]+$/i;var p=/^0o[0-7]+$/i;var m=parseInt;function g(e){if(typeof e=="number"){return e}if((0,l.A)(e)){return d}if((0,u.A)(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=(0,u.A)(t)?t+"":t}if(typeof e!="string"){return e===0?e:+e}e=c(e);var n=h.test(e);return n||p.test(e)?m(e.slice(2),n?2:8):f.test(e)?d:+e}const y=g;var v=1/0,A=17976931348623157e292;function T(e){if(!e){return e===0?e:0}e=y(e);if(e===v||e===-v){var t=e<0?-1:1;return t*A}return e===e?e:0}const R=T},29914:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(52712);function i(e){var t=(0,r.A)(e),n=t%1;return t===t?n?t-n:t:0}const s=i},65606:e=>{var t=e.exports={};var n;var r;function i(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}(function(){try{if(typeof setTimeout==="function"){n=setTimeout}else{n=i}}catch(e){n=i}try{if(typeof clearTimeout==="function"){r=clearTimeout}else{r=s}}catch(e){r=s}})();function a(e){if(n===setTimeout){return setTimeout(e,0)}if((n===i||!n)&&setTimeout){n=setTimeout;return setTimeout(e,0)}try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}function o(e){if(r===clearTimeout){return clearTimeout(e)}if((r===s||!r)&&clearTimeout){r=clearTimeout;return clearTimeout(e)}try{return r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}var c=[];var u=false;var l;var d=-1;function f(){if(!u||!l){return}u=false;if(l.length){c=l.concat(c)}else{d=-1}if(c.length){h()}}function h(){if(u){return}var e=a(f);u=true;var t=c.length;while(t){l=c;c=[];while(++d<t){if(l){l[d].run()}}d=-1;t=c.length}l=null;u=false;o(e)}t.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var n=1;n<arguments.length;n++){t[n-1]=arguments[n]}}c.push(new p(e,t));if(c.length===1&&!u){a(h)}};function p(e,t){this.fun=e;this.array=t}p.prototype.run=function(){this.fun.apply(null,this.array)};t.title="browser";t.browser=true;t.env={};t.argv=[];t.version="";t.versions={};function m(){}t.on=m;t.addListener=m;t.once=m;t.off=m;t.removeListener=m;t.removeAllListeners=m;t.emit=m;t.prependListener=m;t.prependOnceListener=m;t.listeners=function(e){return[]};t.binding=function(e){throw new Error("process.binding is not supported")};t.cwd=function(){return"/"};t.chdir=function(e){throw new Error("process.chdir is not supported")};t.umask=function(){return 0}},59850:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.CancellationTokenSource=t.CancellationToken=void 0;const r=n(69590);const i=n(78585);const s=n(62676);var a;(function(e){e.None=Object.freeze({isCancellationRequested:false,onCancellationRequested:s.Event.None});e.Cancelled=Object.freeze({isCancellationRequested:true,onCancellationRequested:s.Event.None});function t(t){const n=t;return n&&(n===e.None||n===e.Cancelled||i.boolean(n.isCancellationRequested)&&!!n.onCancellationRequested)}e.is=t})(a||(t.CancellationToken=a={}));const o=Object.freeze((function(e,t){const n=(0,r.default)().timer.setTimeout(e.bind(t),0);return{dispose(){n.dispose()}}}));class c{constructor(){this._isCancelled=false}cancel(){if(!this._isCancelled){this._isCancelled=true;if(this._emitter){this._emitter.fire(undefined);this.dispose()}}}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){if(this._isCancelled){return o}if(!this._emitter){this._emitter=new s.Emitter}return this._emitter.event}dispose(){if(this._emitter){this._emitter.dispose();this._emitter=undefined}}}class u{get token(){if(!this._token){this._token=new c}return this._token}cancel(){if(!this._token){this._token=a.Cancelled}else{this._token.cancel()}}dispose(){if(!this._token){this._token=a.None}else if(this._token instanceof c){this._token.dispose()}}}t.CancellationTokenSource=u},62676:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.Emitter=t.Event=void 0;const r=n(69590);var i;(function(e){const t={dispose(){}};e.None=function(){return t}})(i||(t.Event=i={}));class s{add(e,t=null,n){if(!this._callbacks){this._callbacks=[];this._contexts=[]}this._callbacks.push(e);this._contexts.push(t);if(Array.isArray(n)){n.push({dispose:()=>this.remove(e,t)})}}remove(e,t=null){if(!this._callbacks){return}let n=false;for(let r=0,i=this._callbacks.length;r<i;r++){if(this._callbacks[r]===e){if(this._contexts[r]===t){this._callbacks.splice(r,1);this._contexts.splice(r,1);return}else{n=true}}}if(n){throw new Error("When adding a listener with a context, you should remove it with the same context")}}invoke(...e){if(!this._callbacks){return[]}const t=[],n=this._callbacks.slice(0),i=this._contexts.slice(0);for(let a=0,o=n.length;a<o;a++){try{t.push(n[a].apply(i[a],e))}catch(s){(0,r.default)().console.error(s)}}return t}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=undefined;this._contexts=undefined}}class a{constructor(e){this._options=e}get event(){if(!this._event){this._event=(e,t,n)=>{if(!this._callbacks){this._callbacks=new s}if(this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()){this._options.onFirstListenerAdd(this)}this._callbacks.add(e,t);const r={dispose:()=>{if(!this._callbacks){return}this._callbacks.remove(e,t);r.dispose=a._noop;if(this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()){this._options.onLastListenerRemove(this)}}};if(Array.isArray(n)){n.push(r)}return r}}return this._event}fire(e){if(this._callbacks){this._callbacks.invoke.call(this._callbacks,e)}}dispose(){if(this._callbacks){this._callbacks.dispose();this._callbacks=undefined}}}t.Emitter=a;a._noop=function(){}},78585:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.stringArray=t.array=t.func=t.error=t.number=t.string=t.boolean=void 0;function n(e){return e===true||e===false}t.boolean=n;function r(e){return typeof e==="string"||e instanceof String}t.string=r;function i(e){return typeof e==="number"||e instanceof Number}t.number=i;function s(e){return e instanceof Error}t.error=s;function a(e){return typeof e==="function"}t.func=a;function o(e){return Array.isArray(e)}t.array=o;function c(e){return o(e)&&e.every((e=>r(e)))}t.stringArray=c},69590:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});let n;function r(){if(n===undefined){throw new Error(`No runtime abstraction layer installed`)}return n}(function(e){function t(e){if(e===undefined){throw new Error(`No runtime abstraction layer provided`)}n=e}e.install=t})(r||(r={}));t["default"]=r},14247:(e,t,n)=>{"use strict";n.d(t,{A:()=>a,r:()=>s});var r=n(65606);var i;(()=>{"use strict";var e={470:e=>{function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,r="",i=0,s=-1,a=0,o=0;o<=e.length;++o){if(o<e.length)n=e.charCodeAt(o);else{if(47===n)break;n=47}if(47===n){if(s===o-1||1===a);else if(s!==o-1&&2===a){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var c=r.lastIndexOf("/");if(c!==r.length-1){-1===c?(r="",i=0):i=(r=r.slice(0,c)).length-1-r.lastIndexOf("/"),s=o,a=0;continue}}else if(2===r.length||1===r.length){r="",i=0,s=o,a=0;continue}t&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+e.slice(s+1,o):r=e.slice(s+1,o),i=o-s-1;s=o,a=0}else 46===n&&-1!==a?++a:a=-1}return r}var i={resolve:function(){for(var e,i="",s=!1,a=arguments.length-1;a>=-1&&!s;a--){var o;a>=0?o=arguments[a]:(void 0===e&&(e=r.cwd()),o=e),t(o),0!==o.length&&(i=o+"/"+i,s=47===o.charCodeAt(0))}return i=n(i,!s),s?i.length>0?"/"+i:"/":i.length>0?i:"."},normalize:function(e){if(t(e),0===e.length)return".";var r=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!r)).length||r||(e="."),e.length>0&&i&&(e+="/"),r?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var r=arguments[n];t(r),r.length>0&&(void 0===e?e=r:e+="/"+r)}return void 0===e?".":i.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=i.resolve(e))===(n=i.resolve(n)))return"";for(var r=1;r<e.length&&47===e.charCodeAt(r);++r);for(var s=e.length,a=s-r,o=1;o<n.length&&47===n.charCodeAt(o);++o);for(var c=n.length-o,u=a<c?a:c,l=-1,d=0;d<=u;++d){if(d===u){if(c>u){if(47===n.charCodeAt(o+d))return n.slice(o+d+1);if(0===d)return n.slice(o+d)}else a>u&&(47===e.charCodeAt(r+d)?l=d:0===d&&(l=0));break}var f=e.charCodeAt(r+d);if(f!==n.charCodeAt(o+d))break;47===f&&(l=d)}var h="";for(d=r+l+1;d<=s;++d)d!==s&&47!==e.charCodeAt(d)||(0===h.length?h+="..":h+="/..");return h.length>0?h+n.slice(o+l):(o+=l,47===n.charCodeAt(o)&&++o,n.slice(o))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),r=47===n,i=-1,s=!0,a=e.length-1;a>=1;--a)if(47===(n=e.charCodeAt(a))){if(!s){i=a;break}}else s=!1;return-1===i?r?"/":".":r&&1===i?"//":e.slice(0,i)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var r,i=0,s=-1,a=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var o=n.length-1,c=-1;for(r=e.length-1;r>=0;--r){var u=e.charCodeAt(r);if(47===u){if(!a){i=r+1;break}}else-1===c&&(a=!1,c=r+1),o>=0&&(u===n.charCodeAt(o)?-1==--o&&(s=r):(o=-1,s=c))}return i===s?s=c:-1===s&&(s=e.length),e.slice(i,s)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!a){i=r+1;break}}else-1===s&&(a=!1,s=r+1);return-1===s?"":e.slice(i,s)},extname:function(e){t(e);for(var n=-1,r=0,i=-1,s=!0,a=0,o=e.length-1;o>=0;--o){var c=e.charCodeAt(o);if(47!==c)-1===i&&(s=!1,i=o+1),46===c?-1===n?n=o:1!==a&&(a=1):-1!==n&&(a=-1);else if(!s){r=o+1;break}}return-1===n||-1===i||0===a||1===a&&n===i-1&&n===r+1?"":e.slice(n,i)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+"/"+r:r}(0,e)},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var r,i=e.charCodeAt(0),s=47===i;s?(n.root="/",r=1):r=0;for(var a=-1,o=0,c=-1,u=!0,l=e.length-1,d=0;l>=r;--l)if(47!==(i=e.charCodeAt(l)))-1===c&&(u=!1,c=l+1),46===i?-1===a?a=l:1!==d&&(d=1):-1!==a&&(d=-1);else if(!u){o=l+1;break}return-1===a||-1===c||0===d||1===d&&a===c-1&&a===o+1?-1!==c&&(n.base=n.name=0===o&&s?e.slice(1,c):e.slice(o,c)):(0===o&&s?(n.name=e.slice(1,a),n.base=e.slice(1,c)):(n.name=e.slice(o,a),n.base=e.slice(o,c)),n.ext=e.slice(a,c)),o>0?n.dir=e.slice(0,o-1):s&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};i.posix=i,e.exports=i}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};(()=>{let e;if(n.r(s),n.d(s,{URI:()=>d,Utils:()=>$}),"object"==typeof r)e="win32"===r.platform;else if("object"==typeof navigator){let t=navigator.userAgent;e=t.indexOf("Windows")>=0}const t=/^\w[\w\d+.-]*$/,i=/^\//,a=/^\/\//;function o(e,n){if(!e.scheme&&n)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!t.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!i.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(a.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}const c="",u="/",l=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class d{static isUri(e){return e instanceof d||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}scheme;authority;path;query;fragment;constructor(e,t,n,r,i,s=!1){"object"==typeof e?(this.scheme=e.scheme||c,this.authority=e.authority||c,this.path=e.path||c,this.query=e.query||c,this.fragment=e.fragment||c):(this.scheme=function(e,t){return e||t?e:"file"}(e,s),this.authority=t||c,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==u&&(t=u+t):t=u}return t}(this.scheme,n||c),this.query=r||c,this.fragment=i||c,o(this,s))}get fsPath(){return y(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:i,fragment:s}=e;return void 0===t?t=this.scheme:null===t&&(t=c),void 0===n?n=this.authority:null===n&&(n=c),void 0===r?r=this.path:null===r&&(r=c),void 0===i?i=this.query:null===i&&(i=c),void 0===s?s=this.fragment:null===s&&(s=c),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&s===this.fragment?this:new h(t,n,r,i,s)}static parse(e,t=!1){const n=l.exec(e);return n?new h(n[2]||c,R(n[4]||c),R(n[5]||c),R(n[7]||c),R(n[9]||c),t):new h(c,c,c,c,c)}static file(t){let n=c;if(e&&(t=t.replace(/\\/g,u)),t[0]===u&&t[1]===u){const e=t.indexOf(u,2);-1===e?(n=t.substring(2),t=u):(n=t.substring(2,e),t=t.substring(e)||u)}return new h("file",n,t,c,c)}static from(e){const t=new h(e.scheme,e.authority,e.path,e.query,e.fragment);return o(t,!0),t}toString(e=!1){return v(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof d)return e;{const t=new h(e);return t._formatted=e.external,t._fsPath=e._sep===f?e.fsPath:null,t}}return e}}const f=e?1:void 0;class h extends d{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=y(this,!1)),this._fsPath}toString(e=!1){return e?v(this,!0):(this._formatted||(this._formatted=v(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=f),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const p={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function m(e,t,n){let r,i=-1;for(let s=0;s<e.length;s++){const a=e.charCodeAt(s);if(a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||45===a||46===a||95===a||126===a||t&&47===a||n&&91===a||n&&93===a||n&&58===a)-1!==i&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),void 0!==r&&(r+=e.charAt(s));else{void 0===r&&(r=e.substr(0,s));const t=p[a];void 0!==t?(-1!==i&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),r+=t):-1===i&&(i=s)}}return-1!==i&&(r+=encodeURIComponent(e.substring(i))),void 0!==r?r:e}function g(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=p[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function y(t,n){let r;return r=t.authority&&t.path.length>1&&"file"===t.scheme?`//${t.authority}${t.path}`:47===t.path.charCodeAt(0)&&(t.path.charCodeAt(1)>=65&&t.path.charCodeAt(1)<=90||t.path.charCodeAt(1)>=97&&t.path.charCodeAt(1)<=122)&&58===t.path.charCodeAt(2)?n?t.path.substr(1):t.path[1].toLowerCase()+t.path.substr(2):t.path,e&&(r=r.replace(/\//g,"\\")),r}function v(e,t){const n=t?g:m;let r="",{scheme:i,authority:s,path:a,query:o,fragment:c}=e;if(i&&(r+=i,r+=":"),(s||"file"===i)&&(r+=u,r+=u),s){let e=s.indexOf("@");if(-1!==e){const t=s.substr(0,e);s=s.substr(e+1),e=t.lastIndexOf(":"),-1===e?r+=n(t,!1,!1):(r+=n(t.substr(0,e),!1,!1),r+=":",r+=n(t.substr(e+1),!1,!0)),r+="@"}s=s.toLowerCase(),e=s.lastIndexOf(":"),-1===e?r+=n(s,!1,!0):(r+=n(s.substr(0,e),!1,!0),r+=s.substr(e))}if(a){if(a.length>=3&&47===a.charCodeAt(0)&&58===a.charCodeAt(2)){const e=a.charCodeAt(1);e>=65&&e<=90&&(a=`/${String.fromCharCode(e+32)}:${a.substr(3)}`)}else if(a.length>=2&&58===a.charCodeAt(1)){const e=a.charCodeAt(0);e>=65&&e<=90&&(a=`${String.fromCharCode(e+32)}:${a.substr(2)}`)}r+=n(a,!0,!1)}return o&&(r+="?",r+=n(o,!1,!1)),c&&(r+="#",r+=t?c:m(c,!1,!1)),r}function A(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+A(e.substr(3)):e}}const T=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function R(e){return e.match(T)?e.replace(T,(e=>A(e))):e}var E=n(470);const k=E.posix||E,x="/";var $;!function(e){e.joinPath=function(e,...t){return e.with({path:k.join(e.path,...t)})},e.resolvePath=function(e,...t){let n=e.path,r=!1;n[0]!==x&&(n=x+n,r=!0);let i=k.resolve(n,...t);return r&&i[0]===x&&!e.authority&&(i=i.substring(1)),e.with({path:i})},e.dirname=function(e){if(0===e.path.length||e.path===x)return e;let t=k.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)&&(t=""),e.with({path:t})},e.basename=function(e){return k.basename(e.path)},e.extname=function(e){return k.extname(e.path)}}($||($={}))})(),i=s})();const{URI:s,Utils:a}=i}}]);