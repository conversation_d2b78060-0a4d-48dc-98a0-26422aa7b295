{"version": 3, "file": "2816.03541f3103bf4c09e591.js?v=03541f3103bf4c09e591", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAmD;;AAE5C;AACP,EAAE,6BAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,GAAG;AACH;;AAEO;AACP,EAAE,mCAAe;AACjB;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;;AC9BiC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,yBAAW;;AAE1C,kBAAkB,8BAAU;AACnC,cAAc,0BAAM;AACpB,UAAU,0CAA0C;;AAEpD;AACA,EAAE,gBAAgB;;AAElB;;AAEA;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACrCgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,6BAAe;;AAE9C,sBAAsB,8BAAU;AACvC,cAAc,0BAAM;AACpB,UAAU,0DAA0D;;AAEpE;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACvCgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,8BAAgB;;AAE/C,uBAAuB,8BAAU;AACxC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC5F8E;AAMhE;AACkC;AACjD,wCAA0B,YAAY,sBAAQ;;AAEvC,eAAe,8BAAU;AAChC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACxD8E;AAMhE;AACkC;AACjD,wCAA0B,YAAY,sBAAQ;;AAEvC,eAAe,8BAAU;AAChC,cAAc,0BAAM;AACpB,UAAU,kEAAkE;AAC5E;;AAEA;;AAEA;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACxC6E;AAM/D;AACkC;AACjD,wCAA0B,YAAY,qBAAO;;AAEtC,cAAc,8BAAU;AAC/B,cAAc,0BAAM;AACpB,UAAU,qDAAqD;;AAE/D;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACjCgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,0BAAY;;AAE3C,mBAAmB,8BAAU;AACpC,cAAc,0BAAM;AACpB,UAAU,8BAA8B;;AAExC;;AAEA;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACjCgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,8BAAgB;;AAE/C,uBAAuB,8BAAU;AACxC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACzD8E;AAMhE;AACkC;AACjD,wCAA0B,YAAY,sBAAQ;;AAEvC,eAAe,8BAAU;AAChC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC9D4E;AAM9D;AACkC;AACjD,wCAA0B,YAAY,oBAAM;;AAErC,aAAa,8BAAU;AAC9B,cAAc,0BAAM;AACpB,UAAU,8BAA8B;;AAExC;;AAEA;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC9BgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,wBAAU;;AAEzC,iBAAiB,8BAAU;AAClC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB;AACA;AACA;AACA;AACA;;AAEA,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACzDgC;AACsC;AACJ;AACnE,wCAA0B,YAAY,wBAAU;;AAEzC,iBAAiB,8BAAU;AAClC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACjEgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,yBAAW;;AAE1C,kBAAkB,8BAAU;AACnC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACpDgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,4BAAc;;AAE7C,qBAAqB,8BAAU;AACtC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB;AACA;AACA;AACA;AACA;;AAEA,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACrDgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,2BAAa;;AAE5C,oBAAoB,8BAAU;AACrC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB;AACA;AACA;AACA;AACA,wBAAwB,oBAAoB;AAC5C;AACA;;AAEA,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC9DgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,wBAAU;;AAEzC,iBAAiB,8BAAU;AAClC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC9D8E;AAMhE;AACoD;AACnE,wCAA0B,YAAY,sBAAQ;;AAEvC,eAAe,8BAAU;AAChC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;AACrB;AACA;AACA;AACA;AACA,GAAG;;AAEH,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACtDgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,0BAAY;;AAE3C,mBAAmB,8BAAU;AACpC,cAAc,0BAAM;AACpB,UAAU,2DAA2D;;AAErE;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC1C+E;AAMjE;AACkC;AACjD,wCAA0B,YAAY,uBAAS;;AAExC,gBAAgB,8BAAU;AACjC,cAAc,0BAAM;AACpB,UAAU,iDAAiD;;AAE3D;;AAEA;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACnC+E;AAMjE;AACoD;AACnE,wCAA0B,YAAY,uBAAS;;AAExC,gBAAgB,8BAAU;AACjC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AClDgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,wBAAU;;AAEzC,iBAAiB,8BAAU;AAClC,cAAc,0BAAM;AACpB,UAAU,iEAAiE;AAC3E;;AAEA;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB;AACA;AACA;AACA,6BAA6B,6BAA6B;AAC1D;AACA;AACA;AACA;;AAEA,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACtD4E;AAM9D;AACkC;AACjD,wCAA0B,YAAY,oBAAM;;AAErC,aAAa,8BAAU;AAC9B,cAAc,0BAAM;AACpB,UAAU,8BAA8B;;AAExC;;AAEA;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC9BgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,2BAAa;;AAE5C,oBAAoB,8BAAU;AACrC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACzE8E;AACR;AACtB;AACjD,wCAA0B,YAAY,sBAAQ;;AAEvC,eAAe,8BAAU;AAChC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC5CgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,4BAAc;;AAE7C,qBAAqB,8BAAU;AACtC,cAAc,0BAAM;AACpB,UAAU,uDAAuD;;AAEjE;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACrCgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,wBAAU;;AAEzC,iBAAiB,8BAAU;AAClC,cAAc,0BAAM;AACpB,UAAU,uDAAuD;;AAEjE;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACxC6E;AAM/D;AACoD;AACnE,wCAA0B,YAAY,qBAAO;;AAEtC,cAAc,8BAAU;AAC/B,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AChDgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,0BAAY;;AAE3C,mBAAmB,8BAAU;AACpC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACtD8E;AAMhE;AACoD;AACnE,wCAA0B,YAAY,sBAAQ;;AAEvC,eAAe,8BAAU;AAChC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AClE8E;AAMhE;AACoD;AACnE,wCAA0B,YAAY,sBAAQ;;AAEvC,eAAe,8BAAU;AAChC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACnEgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,wBAAU;;AAEzC,iBAAiB,8BAAU;AAClC,cAAc,0BAAM;AACpB,UAAU,6DAA6D;;AAEvE;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACxC8E;AAMhE;AACoD;AACnE,wCAA0B,YAAY,sBAAQ;;AAEvC,eAAe,8BAAU;AAChC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC1DgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,2BAAa;;AAE5C,oBAAoB,8BAAU;AACrC,cAAc,0BAAM;AACpB,UAAU,4DAA4D;;AAEtE;;AAEA;AACA,EAAE,uCAAmB;;AAErB;AACA;AACA;AACA;AACA;;AAEA,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC7C8E;AAMhE;AACoD;AACnE,wCAA0B,YAAY,sBAAQ;;AAEvC,eAAe,8BAAU;AAChC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACjD2E;AAM7D;AACkC;AACjD,wCAA0B,YAAY,mBAAK;;AAEpC,YAAY,8BAAU;AAC7B,cAAc,0BAAM;AACpB,UAAU,wCAAwC;;AAElD;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB;AACA;AACA;AACA;AACA;;AAEA,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACrCgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,wBAAU;;AAEzC,iBAAiB,8BAAU;AAClC,cAAc,0BAAM;AACpB,UAAU,8BAA8B;;AAExC;;AAEA;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACpC4E;AAM9D;AACoD;AACnE,wCAA0B,YAAY,oBAAM;;AAErC,aAAa,8BAAU;AAC9B,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC5CgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,wBAAU;;AAEzC,iBAAiB,8BAAU;AAClC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACtEgC;AAMlB;AACoD;AACnE,wCAA0B,YAAY,yBAAW;;AAE1C,kBAAkB,8BAAU;AACnC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACvE+E;AAMjE;AACkC;AACjD,wCAA0B,YAAY,uBAAS;;AAExC,gBAAgB,8BAAU;AACjC,cAAc,0BAAM;AACpB,UAAU,8BAA8B;;AAExC;;AAEA;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACjC+E;AAMjE;AACoD;AACnE,wCAA0B,YAAY,uBAAS;;AAExC,gBAAgB,8BAAU;AACjC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACpDgC;AACsC;AACJ;AACnE,wCAA0B,YAAY,wBAAU;;AAEzC,iBAAiB,8BAAU;AAClC,cAAc,0BAAM;AACpB,UAAU,4DAA4D;;AAEtE;AACA,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;;AAElB;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB;AACA;AACA;AACA;AACA;;AAEA,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACzCgC;AAMlB;AACkC;;AAEjD,wCAA0B,YAAY,wBAAU;;AAEzC,iBAAiB,8BAAU;AAClC,cAAc,0BAAM;AACpB,UAAU,qEAAqE;AAC/E;;AAEA,EAAE,mCAAe;AACjB;AACA;AACA;AACA,GAAG;;AAEH;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC9C8E;AAMhE;AACkC;AACjD,wCAA0B,YAAY,sBAAQ;;AAEvC,eAAe,8BAAU;AAChC,cAAc,0BAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;AACf;AACA;AACA;AACA;AACA,EAAE,aAAa;AACf;AACA;AACA;AACA;AACA,EAAE,aAAa;AACf;AACA;AACA;AACA;AACA,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;AC3FgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,0BAAY;;AAE3C,mBAAmB,8BAAU;AACpC,cAAc,0BAAM;AACpB,UAAU,wDAAwD;;AAElE;AACA,EAAE,aAAa;AACf;AACA;AACA;AACA;;AAEA;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACtCgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,gCAAkB;;AAEjD,yBAAyB,8BAAU;AAC1C,cAAc,0BAAM;AACpB,UAAU,uDAAuD;;AAEjE;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACnCgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,0BAAY;;AAE3C,mBAAmB,8BAAU;AACpC,cAAc,0BAAM;AACpB,UAAU,8BAA8B;;AAExC;;AAEA;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACjCgC;AAMlB;AACkC;AACjD,wCAA0B,YAAY,8BAAgB;;AAE/C,uBAAuB,8BAAU;AACxC,cAAc,0BAAM;AACpB,UAAU,uDAAuD;;AAEjE;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,uCAAmB;;AAErB,SAAS,sCAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;AACA;AACA,CAAC;;;ACtC8B;AACI;AACC;AACR;AACA;AACD;AACK;AACI;AACR;AACF;AACI;AACA;AACC;AACG;AACD;AACH;AACF;AACI;AACH;AACA;AACC;AACJ;AACO;AACL;AACM;AACJ;AACH;AACK;AACJ;AACA;AACE;AACF;AACK;AACL;AACH;AACK;AACJ;AACI;AACC;AACF;AACA;AACC;AACA;AACF;AACI;AACM;AACN;AACI", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/react-utils.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Accordion.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/AccordionItem.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/AnchoredRegion.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Anchor.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Avatar.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Badge.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Breadcrumb.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/BreadcrumbItem.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Button.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Card.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Checkbox.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Combobox.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/DateField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/DataGridCell.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/DataGridRow.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/DataGrid.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Dialog.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Disclosure.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Divider.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Listbox.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/MenuItem.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Menu.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/NumberField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Option.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/ProgressRing.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Progress.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Radio.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/RadioGroup.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Search.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Select.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Skeleton.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Slider.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/SliderLabel.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Switch.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Tab.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/TabPanel.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Tabs.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/TextArea.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/TextField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Toolbar.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Tooltip.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/TreeItem.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/TreeView.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/Picker.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/PickerMenu.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/PickerMenuOption.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/PickerList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/PickerListItem.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/react-components/lib/index.js"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react';\n\nexport function useProperties(targetElement, propName, value) {\n  useEffect(() => {\n    if (\n      value !== undefined &&\n      targetElement.current &&\n      targetElement.current[propName] !== value\n    ) {\n      // add try catch to avoid errors when setting read-only properties\n      try {\n        targetElement.current[propName] = value;\n      } catch (e) {\n        console.warn(e);\n      }\n    }\n  }, [value, targetElement.current]);\n}\n\nexport function useEventListener(targetElement, eventName, eventHandler) {\n  useLayoutEffect(() => {\n    if (eventHandler !== undefined) {\n      targetElement?.current?.addEventListener(eventName, eventHandler);\n    }\n\n    return () => {\n      if (eventHandler?.cancel) {\n        eventHandler.cancel();\n      }\n\n      targetElement?.current?.removeEventListener(eventName, eventHandler);\n    };\n  }, [eventName, eventHandler, targetElement.current]);\n}\n", "import {\n  jpAccordion,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpAccordion());\n\nexport const Accordion = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, expandMode, ...filteredProps } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-accordion',\n    {\n      ref,\n      ...filteredProps,\n      'expand-mode': props.expandMode || props['expand-mode'],\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpAccordionItem,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpAccordionItem());\n\nexport const AccordionItem = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, headingLevel, id, expanded, ...filteredProps } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'expanded', props.expanded);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-accordion-item',\n    {\n      ref,\n      ...filteredProps,\n      'heading-level': props.headingLevel || props['heading-level'],\n      id: props.id,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpAnchoredRegion,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpAnchoredRegion());\n\nexport const AnchoredRegion = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    horizontalViewportLock,\n    horizontalInset,\n    verticalViewportLock,\n    verticalInset,\n    fixedPlacement,\n    anchor,\n    viewport,\n    horizontalPositioningMode,\n    horizontalDefaultPosition,\n    horizontalThreshold,\n    horizontalScaling,\n    verticalPositioningMode,\n    verticalDefaultPosition,\n    verticalThreshold,\n    verticalScaling,\n    autoUpdateMode,\n    anchorElement,\n    viewportElement,\n    verticalPosition,\n    horizontalPosition,\n    update,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'loaded', props.onLoaded);\n  useEventListener(ref, 'positionchange', props.onPositionchange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'anchorElement', props.anchorElement);\n  useProperties(ref, 'viewportElement', props.viewportElement);\n  useProperties(ref, 'verticalPosition', props.verticalPosition);\n  useProperties(ref, 'horizontalPosition', props.horizontalPosition);\n  useProperties(ref, 'update', props.update);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-anchored-region',\n    {\n      ref,\n      ...filteredProps,\n      anchor: props.anchor,\n      viewport: props.viewport,\n      'horizontal-positioning-mode':\n        props.horizontalPositioningMode || props['horizontal-positioning-mode'],\n      'horizontal-default-position':\n        props.horizontalDefaultPosition || props['horizontal-default-position'],\n      'horizontal-threshold':\n        props.horizontalThreshold || props['horizontal-threshold'],\n      'horizontal-scaling':\n        props.horizontalScaling || props['horizontal-scaling'],\n      'vertical-positioning-mode':\n        props.verticalPositioningMode || props['vertical-positioning-mode'],\n      'vertical-default-position':\n        props.verticalDefaultPosition || props['vertical-default-position'],\n      'vertical-threshold':\n        props.verticalThreshold || props['vertical-threshold'],\n      'vertical-scaling': props.verticalScaling || props['vertical-scaling'],\n      'auto-update-mode': props.autoUpdateMode || props['auto-update-mode'],\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      'horizontal-viewport-lock': props.horizontalViewportLock ? '' : undefined,\n      'horizontal-inset': props.horizontalInset ? '' : undefined,\n      'vertical-viewport-lock': props.verticalViewportLock ? '' : undefined,\n      'vertical-inset': props.verticalInset ? '' : undefined,\n      'fixed-placement': props.fixedPlacement ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpAnchor, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpAnchor());\n\nexport const Anchor = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    appearance,\n    download,\n    href,\n    hreflang,\n    ping,\n    referrerpolicy,\n    rel,\n    target,\n    type,\n    control,\n    ...filteredProps\n  } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'control', props.control);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-anchor',\n    {\n      ref,\n      ...filteredProps,\n      appearance: props.appearance,\n      download: props.download,\n      href: props.href,\n      hreflang: props.hreflang,\n      ping: props.ping,\n      referrerpolicy: props.referrerpolicy,\n      rel: props.rel,\n      target: props.target,\n      type: props.type,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpAvatar, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpAvatar());\n\nexport const Avatar = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, src, alt, fill, color, link, shape, ...filteredProps } =\n    props;\n\n  /** Properties - run whenever a property has changed */\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-avatar',\n    {\n      ref,\n      ...filteredProps,\n      src: props.src,\n      alt: props.alt,\n      fill: props.fill,\n      color: props.color,\n      link: props.link,\n      shape: props.shape,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpBadge, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpBadge());\n\nexport const Badge = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, fill, color, circular, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'circular', props.circular);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-badge',\n    {\n      ref,\n      ...filteredProps,\n      fill: props.fill,\n      color: props.color,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpBreadcrumb,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpBreadcrumb());\n\nexport const Breadcrumb = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-breadcrumb',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpBreadcrumbItem,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpBreadcrumbItem());\n\nexport const BreadcrumbItem = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    download,\n    href,\n    hreflang,\n    ping,\n    referrerpolicy,\n    rel,\n    target,\n    type,\n    control,\n    ...filteredProps\n  } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'control', props.control);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-breadcrumb-item',\n    {\n      ref,\n      ...filteredProps,\n      download: props.download,\n      href: props.href,\n      hreflang: props.hreflang,\n      ping: props.ping,\n      referrerpolicy: props.referrerpolicy,\n      rel: props.rel,\n      target: props.target,\n      type: props.type,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpButton, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpButton());\n\nexport const Button = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    minimal,\n    appearance,\n    form,\n    formaction,\n    formenctype,\n    formmethod,\n    formtarget,\n    type,\n    autofocus,\n    formnovalidate,\n    defaultSlottedContent,\n    disabled,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'autofocus', props.autofocus);\n  useProperties(ref, 'formnovalidate', props.formnovalidate);\n  useProperties(ref, 'defaultSlottedContent', props.defaultSlottedContent);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-button',\n    {\n      ref,\n      ...filteredProps,\n      appearance: props.appearance,\n      form: props.form,\n      formaction: props.formaction,\n      formenctype: props.formenctype,\n      formmethod: props.formmethod,\n      formtarget: props.formtarget,\n      type: props.type,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      minimal: props.minimal ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpCard, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpCard());\n\nexport const Card = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-card',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpCheckbox,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpCheckbox());\n\nexport const Checkbox = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    readonly,\n    readOnly,\n    indeterminate,\n    checked,\n    disabled,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'readOnly', props.readOnly);\n  useProperties(ref, 'indeterminate', props.indeterminate);\n  useProperties(ref, 'checked', props.checked);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  // Add web component internal classes on top of `className`\n  let allClasses = className ?? '';\n  if (ref.current?.indeterminate) {\n    allClasses += ' indeterminate';\n  }\n\n  return React.createElement(\n    'jp-checkbox',\n    {\n      ref,\n      ...filteredProps,\n      class: allClasses.trim(),\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      readonly: props.readonly ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpCombobox,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpCombobox());\n\nexport const Combobox = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    autowidth,\n    minimal,\n    open,\n    autocomplete,\n    placeholder,\n    position,\n    autoWidth,\n    filteredOptions,\n    options,\n    value,\n    length,\n    disabled,\n    selectedIndex,\n    selectedOptions,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'input', props.onInput);\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'autoWidth', props.autoWidth);\n  useProperties(ref, 'filteredOptions', props.filteredOptions);\n  useProperties(ref, 'options', props.options);\n  useProperties(ref, 'value', props.value);\n  useProperties(ref, 'length', props.length);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'selectedIndex', props.selectedIndex);\n  useProperties(ref, 'selectedOptions', props.selectedOptions);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-combobox',\n    {\n      ref,\n      ...filteredProps,\n      autocomplete: props.autocomplete,\n      placeholder: props.placeholder,\n      position: props.position,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      autowidth: props.autowidth ? '' : undefined,\n      minimal: props.minimal ? '' : undefined,\n      open: props.open ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpDateField,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpDateField());\n\nexport const DateField = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    autofocus,\n    step,\n    max,\n    min,\n    disabled,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'input', props.onInput);\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'autofocus', props.autofocus);\n  useProperties(ref, 'step', props.step);\n  useProperties(ref, 'max', props.max);\n  useProperties(ref, 'min', props.min);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-date-field',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpDataGridCell,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpDataGridCell());\n\nexport const DataGridCell = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    cellType,\n    gridColumn,\n    rowData,\n    columnDefinition,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'cell-focused', props.onCellFocused);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'rowData', props.rowData);\n  useProperties(ref, 'columnDefinition', props.columnDefinition);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  // Add web component internal classes on top of `className`\n  let allClasses = className ?? '';\n  if (ref.current?.cellType === 'columnheader') {\n    allClasses += ' column-header';\n  }\n\n  return React.createElement(\n    'jp-data-grid-cell',\n    {\n      ref,\n      ...filteredProps,\n      'cell-type': props.cellType || props['cell-type'],\n      'grid-column': props.gridColumn || props['grid-column'],\n      class: allClasses.trim(),\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpDataGridRow,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpDataGridRow());\n\nexport const DataGridRow = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    gridTemplateColumns,\n    rowType,\n    rowData,\n    columnDefinitions,\n    cellItemTemplate,\n    headerCellItemTemplate,\n    rowIndex,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'row-focused', props.onRowFocused);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'rowData', props.rowData);\n  useProperties(ref, 'columnDefinitions', props.columnDefinitions);\n  useProperties(ref, 'cellItemTemplate', props.cellItemTemplate);\n  useProperties(ref, 'headerCellItemTemplate', props.headerCellItemTemplate);\n  useProperties(ref, 'rowIndex', props.rowIndex);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  // Add web component internal classes on top of `className`\n  let allClasses = className ?? '';\n  if (ref.current) {\n    if (ref.current.rowType !== 'default') {\n      allClasses += ` ${ref.current.rowType}`;\n    }\n  }\n\n  return React.createElement(\n    'jp-data-grid-row',\n    {\n      ref,\n      ...filteredProps,\n      'grid-template-columns':\n        props.gridTemplateColumns || props['grid-template-columns'],\n      'row-type': props.rowType || props['row-type'],\n      class: allClasses.trim(),\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpDataGrid,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpDataGrid());\n\nexport const DataGrid = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    noTabbing,\n    generateHeader,\n    gridTemplateColumns,\n    rowsData,\n    columnDefinitions,\n    rowItemTemplate,\n    cellItemTemplate,\n    headerCellItemTemplate,\n    focusRowIndex,\n    focusColumnIndex,\n    rowElementTag,\n    ...filteredProps\n  } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'rowsData', props.rowsData);\n  useProperties(ref, 'columnDefinitions', props.columnDefinitions);\n  useProperties(ref, 'rowItemTemplate', props.rowItemTemplate);\n  useProperties(ref, 'cellItemTemplate', props.cellItemTemplate);\n  useProperties(ref, 'headerCellItemTemplate', props.headerCellItemTemplate);\n  useProperties(ref, 'focusRowIndex', props.focusRowIndex);\n  useProperties(ref, 'focusColumnIndex', props.focusColumnIndex);\n  useProperties(ref, 'rowElementTag', props.rowElementTag);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-data-grid',\n    {\n      ref,\n      ...filteredProps,\n      'generate-header': props.generateHeader || props['generate-header'],\n      'grid-template-columns':\n        props.gridTemplateColumns || props['grid-template-columns'],\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      'no-tabbing': props.noTabbing ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpDialog, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpDialog());\n\nexport const Dialog = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    trapFocus,\n    ariaDescribedby,\n    ariaLabelledby,\n    ariaLabel,\n    modal,\n    hidden,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'cancel', props.onCancel);\n  useEventListener(ref, 'close', props.onClose);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'modal', props.modal);\n  useProperties(ref, 'hidden', props.hidden);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ({\n    show: () => ref.current.show(),\n    hide: () => ref.current.hide(),\n    compose: (this_, elementDefinition) =>\n      ref.current.compose(this_, elementDefinition)\n  }));\n\n  return React.createElement(\n    'jp-dialog',\n    {\n      ref,\n      ...filteredProps,\n      'aria-describedby': props.ariaDescribedby || props['aria-describedby'],\n      'aria-labelledby': props.ariaLabelledby || props['aria-labelledby'],\n      'aria-label': props.ariaLabel || props['aria-label'],\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      'trap-focus': props.trapFocus ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpDisclosure,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpDisclosure());\n\nexport const Disclosure = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, appearance, title, expanded, ...filteredProps } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'toggle', props.onToggle);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'expanded', props.expanded);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-disclosure',\n    {\n      ref,\n      ...filteredProps,\n      appearance: props.appearance,\n      title: props.title,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpDivider, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpDivider());\n\nexport const Divider = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, role, orientation, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-divider',\n    {\n      ref,\n      ...filteredProps,\n      role: props.role,\n      orientation: props.orientation,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpListbox, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpListbox());\n\nexport const Listbox = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    multiple,\n    size,\n    length,\n    options,\n    disabled,\n    selectedIndex,\n    selectedOptions,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'multiple', props.multiple);\n  useProperties(ref, 'size', props.size);\n  useProperties(ref, 'length', props.length);\n  useProperties(ref, 'options', props.options);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'selectedIndex', props.selectedIndex);\n  useProperties(ref, 'selectedOptions', props.selectedOptions);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-listbox',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpMenuItem,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpMenuItem());\n\nexport const MenuItem = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, role, disabled, expanded, checked, ...filteredProps } =\n    props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'expanded-change', props.onExpand);\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'expanded', props.expanded);\n  useProperties(ref, 'checked', props.checked);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  // Add web component internal classes on top of `className`\n  let allClasses = className ?? '';\n  if (ref.current) {\n    allClasses += ` indent-${ref.current.startColumnCount}`;\n    if (ref.current.expanded) {\n      allClasses += ' expanded';\n    }\n  }\n\n  return React.createElement(\n    'jp-menu-item',\n    {\n      ref,\n      ...filteredProps,\n      role: props.role,\n      class: allClasses.trim(),\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpMenu, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpMenu());\n\nexport const Menu = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-menu',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpNumberField,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpNumberField());\n\nexport const NumberField = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    readonly,\n    hideStep,\n    appearance,\n    placeholder,\n    list,\n    readOnly,\n    autofocus,\n    maxlength,\n    minlength,\n    size,\n    step,\n    max,\n    min,\n    disabled,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'input', props.onInput);\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'readOnly', props.readOnly);\n  useProperties(ref, 'autofocus', props.autofocus);\n  useProperties(ref, 'maxlength', props.maxlength);\n  useProperties(ref, 'minlength', props.minlength);\n  useProperties(ref, 'size', props.size);\n  useProperties(ref, 'step', props.step);\n  useProperties(ref, 'max', props.max);\n  useProperties(ref, 'min', props.min);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-number-field',\n    {\n      ref,\n      ...filteredProps,\n      appearance: props.appearance,\n      placeholder: props.placeholder,\n      list: props.list,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      readonly: props.readonly ? '' : undefined,\n      'hide-step': props.hideStep ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpOption, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpOption());\n\nexport const Option = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    selected,\n    value,\n    checked,\n    content,\n    defaultSelected,\n    disabled,\n    selectedAttribute,\n    dirtyValue,\n    ...filteredProps\n  } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'checked', props.checked);\n  useProperties(ref, 'content', props.content);\n  useProperties(ref, 'defaultSelected', props.defaultSelected);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'selectedAttribute', props.selectedAttribute);\n  useProperties(ref, 'dirtyValue', props.dirtyValue);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-option',\n    {\n      ref,\n      ...filteredProps,\n      value: props.value,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      selected: props.selected ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpProgressRing,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpProgressRing());\n\nexport const ProgressRing = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, value, min, max, paused, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'value', props.value);\n  useProperties(ref, 'min', props.min);\n  useProperties(ref, 'max', props.max);\n  useProperties(ref, 'paused', props.paused);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-progress-ring',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpProgress,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpProgress());\n\nexport const Progress = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, value, min, max, paused, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'value', props.value);\n  useProperties(ref, 'min', props.min);\n  useProperties(ref, 'max', props.max);\n  useProperties(ref, 'paused', props.paused);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-progress',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpRadio, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpRadio());\n\nexport const Radio = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    readonly,\n    readOnly,\n    name,\n    checked,\n    disabled,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'readOnly', props.readOnly);\n  useProperties(ref, 'name', props.name);\n  useProperties(ref, 'checked', props.checked);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-radio',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      readonly: props.readonly ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpRadioGroup,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpRadioGroup());\n\nexport const RadioGroup = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    readonly,\n    disabled,\n    name,\n    value,\n    orientation,\n    readOnly,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'readOnly', props.readOnly);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-radio-group',\n    {\n      ref,\n      ...filteredProps,\n      name: props.name,\n      value: props.value,\n      orientation: props.orientation,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      readonly: props.readonly ? '' : undefined,\n      disabled: props.disabled ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpSearch, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpSearch());\n\nexport const Search = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    readonly,\n    appearance,\n    placeholder,\n    list,\n    pattern,\n    readOnly,\n    autofocus,\n    maxlength,\n    minlength,\n    size,\n    spellcheck,\n    disabled,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'input', props.onInput);\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'readOnly', props.readOnly);\n  useProperties(ref, 'autofocus', props.autofocus);\n  useProperties(ref, 'maxlength', props.maxlength);\n  useProperties(ref, 'minlength', props.minlength);\n  useProperties(ref, 'size', props.size);\n  useProperties(ref, 'spellcheck', props.spellcheck);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-search',\n    {\n      ref,\n      ...filteredProps,\n      appearance: props.appearance,\n      placeholder: props.placeholder,\n      list: props.list,\n      pattern: props.pattern,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      readonly: props.readonly ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpSelect, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpSelect());\n\nexport const Select = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    autowidth,\n    minimal,\n    open,\n    position,\n    autoWidth,\n    value,\n    displayValue,\n    multiple,\n    size,\n    length,\n    options,\n    disabled,\n    selectedIndex,\n    selectedOptions,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'input', props.onInput);\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'autoWidth', props.autoWidth);\n  useProperties(ref, 'value', props.value);\n  useProperties(ref, 'displayValue', props.displayValue);\n  useProperties(ref, 'multiple', props.multiple);\n  useProperties(ref, 'size', props.size);\n  useProperties(ref, 'length', props.length);\n  useProperties(ref, 'options', props.options);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'selectedIndex', props.selectedIndex);\n  useProperties(ref, 'selectedOptions', props.selectedOptions);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-select',\n    {\n      ref,\n      ...filteredProps,\n      position: props.position,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      autowidth: props.autowidth ? '' : undefined,\n      minimal: props.minimal ? '' : undefined,\n      open: props.open ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpSkeleton,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpSkeleton());\n\nexport const Skeleton = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, fill, shape, pattern, shimmer, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'shimmer', props.shimmer);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-skeleton',\n    {\n      ref,\n      ...filteredProps,\n      fill: props.fill,\n      shape: props.shape,\n      pattern: props.pattern,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpSlider, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpSlider());\n\nexport const Slider = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    readonly,\n    orientation,\n    mode,\n    readOnly,\n    valueAsNumber,\n    valueTextFormatter,\n    min,\n    max,\n    step,\n    disabled,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'readOnly', props.readOnly);\n  useProperties(ref, 'valueAsNumber', props.valueAsNumber);\n  useProperties(ref, 'valueTextFormatter', props.valueTextFormatter);\n  useProperties(ref, 'min', props.min);\n  useProperties(ref, 'max', props.max);\n  useProperties(ref, 'step', props.step);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-slider',\n    {\n      ref,\n      ...filteredProps,\n      orientation: props.orientation,\n      mode: props.mode,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      readonly: props.readonly ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpSliderLabel,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpSliderLabel());\n\nexport const SliderLabel = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, hideMark, disabled, position, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  // Add web component internal classes on top of `className`\n  let allClasses = className ?? '';\n  if (ref.current?.disabled) {\n    allClasses += ' disabled';\n  }\n\n  return React.createElement(\n    'jp-slider-label',\n    {\n      ref,\n      ...filteredProps,\n      position: props.position,\n      class: allClasses.trim(),\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      'hide-mark': props.hideMark ? '' : undefined,\n      disabled: props.disabled ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpSwitch, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpSwitch());\n\nexport const Switch = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    readonly,\n    readOnly,\n    checked,\n    disabled,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'readOnly', props.readOnly);\n  useProperties(ref, 'checked', props.checked);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-switch',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      readonly: props.readonly ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpTab, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpTab());\n\nexport const Tab = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, disabled, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'disabled', props.disabled);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  // Add web component internal classes on top of `className`\n  let allClasses = className ?? '';\n  if (ref.current?.classList.contains('vertical')) {\n    allClasses += ' vertical';\n  }\n\n  return React.createElement(\n    'jp-tab',\n    {\n      ref,\n      ...filteredProps,\n      class: allClasses.trim(),\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpTabPanel,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpTabPanel());\n\nexport const TabPanel = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-tab-panel',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpTabs, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpTabs());\n\nexport const Tabs = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    orientation,\n    activeid,\n    activeindicator,\n    activetab,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'activeindicator', props.activeindicator);\n  useProperties(ref, 'activetab', props.activetab);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-tabs',\n    {\n      ref,\n      ...filteredProps,\n      orientation: props.orientation,\n      activeid: props.activeid,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpTextArea,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpTextArea());\n\nexport const TextArea = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    appearance,\n    resize,\n    form,\n    list,\n    name,\n    placeholder,\n    readOnly,\n    autofocus,\n    maxlength,\n    minlength,\n    cols,\n    rows,\n    spellcheck,\n    disabled,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'select', props.onSelect);\n  useEventListener(ref, 'change', props.onChange);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'readOnly', props.readOnly);\n  useProperties(ref, 'autofocus', props.autofocus);\n  useProperties(ref, 'maxlength', props.maxlength);\n  useProperties(ref, 'minlength', props.minlength);\n  useProperties(ref, 'cols', props.cols);\n  useProperties(ref, 'rows', props.rows);\n  useProperties(ref, 'spellcheck', props.spellcheck);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-text-area',\n    {\n      ref,\n      ...filteredProps,\n      appearance: props.appearance,\n      resize: props.resize,\n      form: props.form,\n      list: props.list,\n      name: props.name,\n      placeholder: props.placeholder,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpTextField,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpTextField());\n\nexport const TextField = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    readonly,\n    appearance,\n    placeholder,\n    type,\n    list,\n    pattern,\n    readOnly,\n    autofocus,\n    maxlength,\n    minlength,\n    size,\n    spellcheck,\n    disabled,\n    required,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'change', props.onChange);\n  useEventListener(ref, 'input', props.onInput);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'readOnly', props.readOnly);\n  useProperties(ref, 'autofocus', props.autofocus);\n  useProperties(ref, 'maxlength', props.maxlength);\n  useProperties(ref, 'minlength', props.minlength);\n  useProperties(ref, 'size', props.size);\n  useProperties(ref, 'spellcheck', props.spellcheck);\n  useProperties(ref, 'disabled', props.disabled);\n  useProperties(ref, 'required', props.required);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-text-field',\n    {\n      ref,\n      ...filteredProps,\n      appearance: props.appearance,\n      placeholder: props.placeholder,\n      type: props.type,\n      list: props.list,\n      pattern: props.pattern,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      readonly: props.readonly ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpToolbar, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpToolbar());\n\nexport const Toolbar = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-toolbar',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpTooltip, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpTooltip());\n\nexport const Tooltip = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    horizontalViewportLock,\n    verticalViewportLock,\n    anchor,\n    delay,\n    position,\n    autoUpdateMode,\n    visible,\n    anchorElement,\n    ...filteredProps\n  } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'dismiss', props.onDismiss);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'visible', props.visible);\n  useProperties(ref, 'anchorElement', props.anchorElement);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-tooltip',\n    {\n      ref,\n      ...filteredProps,\n      anchor: props.anchor,\n      delay: props.delay,\n      position: props.position,\n      'auto-update-mode': props.autoUpdateMode || props['auto-update-mode'],\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      'horizontal-viewport-lock': props.horizontalViewportLock ? '' : undefined,\n      'vertical-viewport-lock': props.verticalViewportLock ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpTreeItem,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { useEventListener, useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpTreeItem());\n\nexport const TreeItem = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, expanded, selected, disabled, ...filteredProps } = props;\n\n  /** Event listeners - run once */\n  useEventListener(ref, 'expanded-change', props.onExpand);\n  useEventListener(ref, 'selected-change', props.onSelect);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'expanded', props.expanded);\n  useProperties(ref, 'selected', props.selected);\n  useProperties(ref, 'disabled', props.disabled);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  // Add web component internal classes on top of `className`\n  let allClasses = className ?? '';\n  if (ref.current?.nested) {\n    allClasses += ' nested';\n  }\n\n  return React.createElement(\n    'jp-tree-item',\n    {\n      ref,\n      ...filteredProps,\n      class: allClasses.trim(),\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpTreeView,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useLayoutEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\n\nprovideJupyterDesignSystem().register(jpTreeView());\n\nexport const TreeView = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, renderCollapsedNodes, currentSelected, ...filteredProps } =\n    props;\n\n  useLayoutEffect(() => {\n    // Fix using private API to force refresh of nested flag on\n    // first level of tree items.\n    ref.current?.setItems();\n  }, [ref.current]);\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'currentSelected', props.currentSelected);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-tree-view',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      'render-collapsed-nodes': props.renderCollapsedNodes ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import { jpPicker, provideJupyterDesignSystem } from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpPicker());\n\nexport const Picker = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const {\n    className,\n    filterSelected,\n    filterQuery,\n    selection,\n    options,\n    maxSelected,\n    noSuggestionsText,\n    suggestionsAvailableText,\n    loadingText,\n    label,\n    labelledby,\n    placeholder,\n    menuPlacement,\n    showLoading,\n    listItemTemplate,\n    defaultListItemTemplate,\n    menuOptionTemplate,\n    defaultMenuOptionTemplate,\n    listItemContentsTemplate,\n    menuOptionContentsTemplate,\n    optionsList,\n    query,\n    itemsPlaceholderElement,\n    ...filteredProps\n  } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'showLoading', props.showLoading);\n  useProperties(ref, 'listItemTemplate', props.listItemTemplate);\n  useProperties(ref, 'defaultListItemTemplate', props.defaultListItemTemplate);\n  useProperties(ref, 'menuOptionTemplate', props.menuOptionTemplate);\n  useProperties(\n    ref,\n    'defaultMenuOptionTemplate',\n    props.defaultMenuOptionTemplate\n  );\n  useProperties(\n    ref,\n    'listItemContentsTemplate',\n    props.listItemContentsTemplate\n  );\n  useProperties(\n    ref,\n    'menuOptionContentsTemplate',\n    props.menuOptionContentsTemplate\n  );\n  useProperties(ref, 'optionsList', props.optionsList);\n  useProperties(ref, 'query', props.query);\n  useProperties(ref, 'itemsPlaceholderElement', props.itemsPlaceholderElement);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-draft-picker',\n    {\n      ref,\n      ...filteredProps,\n      selection: props.selection,\n      options: props.options,\n      'max-selected': props.maxSelected || props['max-selected'],\n      'no-suggestions-text':\n        props.noSuggestionsText || props['no-suggestions-text'],\n      'suggestions-available-text':\n        props.suggestionsAvailableText || props['suggestions-available-text'],\n      'loading-text': props.loadingText || props['loading-text'],\n      label: props.label,\n      labelledby: props.labelledby,\n      placeholder: props.placeholder,\n      'menu-placement': props.menuPlacement || props['menu-placement'],\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      'filter-selected': props.filterSelected ? '' : undefined,\n      'filter-query': props.filterQuery ? '' : undefined,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpPickerMenu,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpPickerMenu());\n\nexport const PickerMenu = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, suggestionsAvailableText, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(\n    ref,\n    'suggestionsAvailableText',\n    props.suggestionsAvailableText\n  );\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-draft-picker-menu',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpPickerMenuOption,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpPickerMenuOption());\n\nexport const PickerMenuOption = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, value, contentsTemplate, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'contentsTemplate', props.contentsTemplate);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-draft-picker-menu-option',\n    {\n      ref,\n      ...filteredProps,\n      value: props.value,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpPickerList,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpPickerList());\n\nexport const PickerList = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-draft-picker-list',\n    {\n      ref,\n      ...filteredProps,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "import {\n  jpPickerListItem,\n  provideJupyterDesignSystem\n} from '@jupyter/web-components';\nimport React, {\n  forwardRef,\n  useEffect,\n  useImperativeHandle,\n  useRef\n} from 'react';\nimport { useProperties } from './react-utils.js';\nprovideJupyterDesignSystem().register(jpPickerListItem());\n\nexport const PickerListItem = forwardRef((props, forwardedRef) => {\n  const ref = useRef(null);\n  const { className, value, contentsTemplate, ...filteredProps } = props;\n\n  /** Properties - run whenever a property has changed */\n  useProperties(ref, 'contentsTemplate', props.contentsTemplate);\n\n  /** Methods - uses `useImperativeHandle` hook to pass ref to component */\n  useImperativeHandle(forwardedRef, () => ref.current, [ref.current]);\n\n  return React.createElement(\n    'jp-draft-picker-list-item',\n    {\n      ref,\n      ...filteredProps,\n      value: props.value,\n      class: props.className,\n      exportparts: props.exportparts,\n      for: props.htmlFor,\n      part: props.part,\n      tabindex: props.tabIndex,\n      style: { ...props.style }\n    },\n    props.children\n  );\n});\n", "export * from './Accordion.js';\nexport * from './AccordionItem.js';\nexport * from './AnchoredRegion.js';\nexport * from './Anchor.js';\nexport * from './Avatar.js';\nexport * from './Badge.js';\nexport * from './Breadcrumb.js';\nexport * from './BreadcrumbItem.js';\nexport * from './Button.js';\nexport * from './Card.js';\nexport * from './Checkbox.js';\nexport * from './Combobox.js';\nexport * from './DateField.js';\nexport * from './DataGridCell.js';\nexport * from './DataGridRow.js';\nexport * from './DataGrid.js';\nexport * from './Dialog.js';\nexport * from './Disclosure.js';\nexport * from './Divider.js';\nexport * from './Listbox.js';\nexport * from './MenuItem.js';\nexport * from './Menu.js';\nexport * from './NumberField.js';\nexport * from './Option.js';\nexport * from './ProgressRing.js';\nexport * from './Progress.js';\nexport * from './Radio.js';\nexport * from './RadioGroup.js';\nexport * from './Search.js';\nexport * from './Select.js';\nexport * from './Skeleton.js';\nexport * from './Slider.js';\nexport * from './SliderLabel.js';\nexport * from './Switch.js';\nexport * from './Tab.js';\nexport * from './TabPanel.js';\nexport * from './Tabs.js';\nexport * from './TextArea.js';\nexport * from './TextField.js';\nexport * from './Toolbar.js';\nexport * from './Tooltip.js';\nexport * from './TreeItem.js';\nexport * from './TreeView.js';\nexport * from './Picker.js';\nexport * from './PickerMenu.js';\nexport * from './PickerMenuOption.js';\nexport * from './PickerList.js';\nexport * from './PickerListItem.js';\n"], "names": [], "sourceRoot": ""}