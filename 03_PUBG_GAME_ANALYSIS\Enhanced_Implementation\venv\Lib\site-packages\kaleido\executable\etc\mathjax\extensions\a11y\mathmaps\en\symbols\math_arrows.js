[{"locale": "en"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow", "short": "left arrow"}, "mathspeak": {"default": "left-arrow", "sbrief": "L arrow"}}, "key": "2190"}, {"category": "Sm", "mappings": {"default": {"default": "upwards arrow", "short": "up arrow"}, "mathspeak": {"default": "up-arrow", "sbrief": "U arrow"}}, "key": "2191"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow", "short": "right arrow"}, "mathspeak": {"default": "right-arrow", "sbrief": "R arrow"}}, "key": "2192"}, {"category": "Sm", "mappings": {"default": {"default": "downwards arrow", "short": "down arrow"}, "mathspeak": {"default": "down-arrow", "sbrief": "D arrow"}}, "key": "2193"}, {"category": "Sm", "mappings": {"default": {"default": "left right arrow"}, "mathspeak": {"default": "left-right-arrow", "sbrief": "L R arrow"}}, "key": "2194"}, {"category": "So", "mappings": {"default": {"default": "up down arrow"}, "mathspeak": {"default": "up down-arrow", "sbrief": "U D arrow"}}, "key": "2195"}, {"category": "So", "mappings": {"default": {"default": "north west arrow", "short": "up left arrow"}, "mathspeak": {"default": "up left-arrow", "sbrief": "U L arrow"}}, "key": "2196"}, {"category": "So", "mappings": {"default": {"default": "north east arrow", "short": "up right arrow"}, "mathspeak": {"default": "up right-arrow", "sbrief": "U R arrow"}}, "key": "2197"}, {"category": "So", "mappings": {"default": {"default": "south east arrow", "short": "down right arrow"}, "mathspeak": {"default": "down right-arrow", "sbrief": "D R arrow"}}, "key": "2198"}, {"category": "So", "mappings": {"default": {"default": "south west arrow", "short": "down left arrow"}, "mathspeak": {"default": "down left-arrow", "sbrief": "D L arrow"}}, "key": "2199"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow with stroke", "short": "left arrow with stroke"}, "mathspeak": {"default": "left-arrow with stroke", "sbrief": "L arrow with stroke"}}, "key": "219A"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow with stroke", "short": "right arrow with stroke"}, "mathspeak": {"default": "right-arrow with stroke", "sbrief": "R arrow with stroke"}}, "key": "219B"}, {"category": "So", "mappings": {"default": {"default": "leftwards wave arrow", "short": "left wave arrow"}, "mathspeak": {"sbrief": "L wave arrow"}}, "key": "219C"}, {"category": "So", "mappings": {"default": {"default": "rightwards wave arrow", "short": "right wave arrow"}, "mathspeak": {"sbrief": "R wave arrow"}}, "key": "219D"}, {"category": "So", "mappings": {"default": {"default": "leftwards two headed arrow", "alternative": "left two headed arrow", "short": "two headed left arrow"}, "mathspeak": {"default": "two headed left-arrow", "sbrief": "two headed L arrow"}}, "key": "219E"}, {"category": "So", "mappings": {"default": {"default": "upwards two headed arrow", "alternative": "up two headed arrow", "short": "two headed up arrow"}, "mathspeak": {"default": "two headed up-arrow", "sbrief": "two headed U arrow"}}, "key": "219F"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards two headed arrow", "alternative": "right two headed arrow", "short": "two headed right arrow"}, "mathspeak": {"default": "two headed right-arrow", "sbrief": "two headed R arrow"}}, "key": "21A0"}, {"category": "So", "mappings": {"default": {"default": "downwards two headed arrow", "alternative": "down two headed arrow", "short": "two headed down arrow"}, "mathspeak": {"default": "two headed down-arrow", "sbrief": "two headed D arrow"}}, "key": "21A1"}, {"category": "So", "mappings": {"default": {"default": "leftwards arrow with tail", "short": "left arrow with tail"}, "mathspeak": {"default": "left-arrow with tail", "sbrief": "L arrow with tail"}}, "key": "21A2"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow with tail", "short": "right arrow with tail"}, "mathspeak": {"default": "right-arrow with tail", "sbrief": "R arrow with tail"}}, "key": "21A3"}, {"category": "So", "mappings": {"default": {"default": "leftwards arrow from bar", "short": "left arrow from bar"}, "mathspeak": {"default": "left-arrow from bar", "sbrief": "L arrow from bar"}}, "key": "21A4"}, {"category": "So", "mappings": {"default": {"default": "upwards arrow from bar", "short": "up arrow from bar"}, "mathspeak": {"default": "up-arrow from bar", "sbrief": "U arrow from bar"}}, "key": "21A5"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow from bar", "short": "right arrow from bar"}, "mathspeak": {"default": "right-arrow from bar", "sbrief": "R arrow from bar"}}, "key": "21A6"}, {"category": "So", "mappings": {"default": {"default": "downwards arrow from bar", "short": "down arrow from bar"}, "mathspeak": {"default": "down-arrow from bar", "sbrief": "D arrow from bar"}}, "key": "21A7"}, {"category": "So", "mappings": {"default": {"default": "up down arrow with base"}, "mathspeak": {"default": "up down-arrow with base", "sbrief": "U D arrow with base"}}, "key": "21A8"}, {"category": "So", "mappings": {"default": {"default": "leftwards arrow with hook", "short": "left arrow with hook"}, "mathspeak": {"default": "left-arrow with hook", "sbrief": "L arrow with hook"}}, "key": "21A9"}, {"category": "So", "mappings": {"default": {"default": "rightwards arrow with hook", "short": "right arrow with hook"}, "mathspeak": {"default": "right-arrow with hook", "sbrief": "R arrow with hook"}}, "key": "21AA"}, {"category": "So", "mappings": {"default": {"default": "leftwards arrow with loop", "short": "left arrow with loop"}, "mathspeak": {"default": "left-arrow with loop", "sbrief": "L arrow with loop"}}, "key": "21AB"}, {"category": "So", "mappings": {"default": {"default": "rightwards arrow with loop", "short": "right arrow with loop"}, "mathspeak": {"default": "right-arrow with loop", "sbrief": "R arrow with loop"}}, "key": "21AC"}, {"category": "So", "mappings": {"default": {"default": "left right wave arrow"}, "mathspeak": {"sbrief": "L R wave arrow"}}, "key": "21AD"}, {"category": "Sm", "mappings": {"default": {"default": "left right arrow with stroke"}, "mathspeak": {"default": "left-right-arrow with stroke", "sbrief": "L R arrow with stroke"}}, "key": "21AE"}, {"category": "So", "mappings": {"default": {"default": "downwards zigzag arrow", "short": "down zigzag arrow"}, "mathspeak": {"sbrief": "d zigzag arrow"}}, "key": "21AF"}, {"category": "So", "mappings": {"default": {"default": "upwards arrow with tip leftwards", "short": "up arrow with tip left"}, "mathspeak": {"default": "up-arrow with tip left", "sbrief": "U arrow with tip left"}}, "key": "21B0"}, {"category": "So", "mappings": {"default": {"default": "upwards arrow with tip rightwards", "short": "up arrow with tip right"}, "mathspeak": {"default": "up-arrow with tip right", "sbrief": "U arrow with tip right"}}, "key": "21B1"}, {"category": "So", "mappings": {"default": {"default": "downwards arrow with tip leftwards", "short": "down arrow with tip left"}, "mathspeak": {"default": "down-arrow with tip left", "sbrief": "D arrow with tip left"}}, "key": "21B2"}, {"category": "So", "mappings": {"default": {"default": "downwards arrow with tip rightwards", "short": "down arrow with tip right"}, "mathspeak": {"default": "down-arrow with tip right", "sbrief": "D arrow with tip right"}}, "key": "21B3"}, {"category": "So", "mappings": {"default": {"default": "rightwards arrow with corner downwards", "short": "right arrow with corner down"}, "mathspeak": {"default": "right-arrow with corner down", "sbrief": "R arrow with corner down"}}, "key": "21B4"}, {"category": "So", "mappings": {"default": {"default": "downwards arrow with corner leftwards", "short": "down arrow with corner left"}, "mathspeak": {"default": "down-arrow with corner left", "sbrief": "D arrow with corner left"}}, "key": "21B5"}, {"category": "So", "mappings": {"default": {"default": "anticlockwise top semicircle arrow"}}, "key": "21B6"}, {"category": "So", "mappings": {"default": {"default": "clockwise top semicircle arrow"}}, "key": "21B7"}, {"category": "So", "mappings": {"default": {"default": "north west arrow to long bar", "short": "up left arrow to long bar"}, "mathspeak": {"default": "up left-arrow to long bar", "sbrief": "U L arrow to long bar"}}, "key": "21B8"}, {"category": "So", "mappings": {"default": {"default": "leftwards arrow to bar over rightwards arrow to bar", "short": "left arrow to bar over right arrow to bar"}, "mathspeak": {"default": "left-arrow to bar over right-arrow to bar", "sbrief": "L arrow to bar over R arrow to bar"}}, "key": "21B9"}, {"category": "So", "mappings": {"default": {"default": "anticlockwise open circle arrow"}}, "key": "21BA"}, {"category": "So", "mappings": {"default": {"default": "clockwise open circle arrow"}}, "key": "21BB"}, {"category": "So", "mappings": {"default": {"default": "rightwards arrow over leftwards arrow", "short": "right arrow over left arrow"}, "mathspeak": {"default": "right-arrow over left-arrow", "sbrief": "R arrow over L arrow"}}, "key": "21C4"}, {"category": "So", "mappings": {"default": {"default": "upwards arrow leftwards of downwards arrow", "short": "up arrow left of down arrow"}, "mathspeak": {"default": "up-arrow left of down-arrow", "sbrief": "U arrow L of D arrow"}}, "key": "21C5"}, {"category": "So", "mappings": {"default": {"default": "leftwards arrow over rightwards arrow", "short": "left arrow over right arrow"}, "mathspeak": {"default": "left-arrow over right-arrow", "sbrief": "L arrow over R arrow"}}, "key": "21C6"}, {"category": "So", "mappings": {"default": {"default": "leftwards paired arrows", "short": "left paired arrows"}, "mathspeak": {"sbrief": "L paired arrows"}}, "key": "21C7"}, {"category": "So", "mappings": {"default": {"default": "upwards paired arrows", "short": "up paired arrows"}, "mathspeak": {"sbrief": "U paired arrows"}}, "key": "21C8"}, {"category": "So", "mappings": {"default": {"default": "rightwards paired arrows", "short": "right paired arrows"}, "mathspeak": {"sbrief": "R paired arrows"}}, "key": "21C9"}, {"category": "So", "mappings": {"default": {"default": "downwards paired arrows", "short": "down paired arrows"}, "mathspeak": {"sbrief": "D paired arrows"}}, "key": "21CA"}, {"category": "So", "mappings": {"default": {"default": "leftwards double arrow with stroke", "short": "left double arrow with stroke"}, "mathspeak": {"sbrief": "L double arrow with stroke"}}, "key": "21CD"}, {"category": "Sm", "mappings": {"default": {"default": "left right double arrow with stroke"}, "mathspeak": {"sbrief": "L R double arrow with stroke"}}, "key": "21CE"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards double arrow with stroke", "short": "right double arrow with stroke"}, "mathspeak": {"sbrief": "R double arrow with stroke"}}, "key": "21CF"}, {"category": "So", "mappings": {"default": {"default": "leftwards double arrow", "short": "left double arrow"}, "mathspeak": {"sbrief": "L double arrow"}}, "key": "21D0"}, {"category": "So", "mappings": {"default": {"default": "upwards double arrow", "short": "up double arrow"}, "mathspeak": {"sbrief": "U double arrow"}}, "key": "21D1"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards double arrow", "short": "right double arrow"}, "mathspeak": {"sbrief": "R double arrow"}}, "key": "21D2"}, {"category": "So", "mappings": {"default": {"default": "downwards double arrow", "short": "down double arrow"}, "mathspeak": {"sbrief": "d double arrow"}}, "key": "21D3"}, {"category": "Sm", "mappings": {"default": {"default": "left right double arrow"}, "mathspeak": {"sbrief": "L R double arrow"}}, "key": "21D4"}, {"category": "So", "mappings": {"default": {"default": "up down double arrow"}, "mathspeak": {"sbrief": "U d double arrow"}}, "key": "21D5"}, {"category": "So", "mappings": {"default": {"default": "north west double arrow", "short": "up left double arrow"}, "mathspeak": {"sbrief": "U L double arrow"}}, "key": "21D6"}, {"category": "So", "mappings": {"default": {"default": "north east double arrow", "short": "up right double arrow"}, "mathspeak": {"sbrief": "U R double arrow"}}, "key": "21D7"}, {"category": "So", "mappings": {"default": {"default": "south east double arrow", "short": "down right double arrow"}, "mathspeak": {"sbrief": "d R double arrow"}}, "key": "21D8"}, {"category": "So", "mappings": {"default": {"default": "south west double arrow", "short": "down left double arrow"}, "mathspeak": {"sbrief": "d L double arrow"}}, "key": "21D9"}, {"category": "So", "mappings": {"default": {"default": "leftwards triple arrow", "short": "left triple arrow"}, "mathspeak": {"sbrief": "L triple arrow"}}, "key": "21DA"}, {"category": "So", "mappings": {"default": {"default": "rightwards triple arrow", "short": "right triple arrow"}, "mathspeak": {"sbrief": "r triple arrow"}}, "key": "21DB"}, {"category": "So", "mappings": {"default": {"default": "leftwards squiggle arrow", "short": "left squiggle arrow"}, "mathspeak": {"sbrief": "L squiggle arrow"}}, "key": "21DC"}, {"category": "So", "mappings": {"default": {"default": "rightwards squiggle arrow", "short": "right squiggle arrow"}, "mathspeak": {"sbrief": "r squiggle arrow"}}, "key": "21DD"}, {"category": "So", "mappings": {"default": {"default": "upwards arrow with double stroke", "short": "up arrow with double stroke"}, "mathspeak": {"default": "up-arrow with double stroke", "sbrief": "U arrow with double stroke"}}, "key": "21DE"}, {"category": "So", "mappings": {"default": {"default": "downwards arrow with double stroke", "short": "down arrow with double stroke"}, "mathspeak": {"default": "down-arrow with double stroke", "sbrief": "D arrow with double stroke"}}, "key": "21DF"}, {"category": "So", "mappings": {"default": {"default": "leftwards dasheD arrow", "short": "left dasheD arrow"}, "mathspeak": {"sbrief": "L dasheD arrow"}}, "key": "21E0"}, {"category": "So", "mappings": {"default": {"default": "upwards dasheD arrow", "short": "up dasheD arrow"}, "mathspeak": {"sbrief": "U dasheD arrow"}}, "key": "21E1"}, {"category": "So", "mappings": {"default": {"default": "rightwards dasheD arrow", "short": "right dasheD arrow"}, "mathspeak": {"sbrief": "r dasheD arrow"}}, "key": "21E2"}, {"category": "So", "mappings": {"default": {"default": "downwards dasheD arrow", "short": "down dasheD arrow"}, "mathspeak": {"sbrief": "d dasheD arrow"}}, "key": "21E3"}, {"category": "So", "mappings": {"default": {"default": "leftwards arrow to bar", "short": "left arrow to bar"}, "mathspeak": {"default": "left-arrow to bar", "sbrief": "L arrow to bar"}}, "key": "21E4"}, {"category": "So", "mappings": {"default": {"default": "rightwards arrow to bar", "short": "right arrow to bar"}, "mathspeak": {"default": "right-arrow to bar", "sbrief": "R arrow to bar"}}, "key": "21E5"}, {"category": "So", "mappings": {"default": {"default": "leftwards white arrow", "short": "white left arrow"}, "mathspeak": {"default": "white left-arrow", "sbrief": "white L arrow"}}, "key": "21E6"}, {"category": "So", "mappings": {"default": {"default": "upwards white arrow", "short": "white up arrow"}, "mathspeak": {"default": "white up-arrow", "sbrief": "white U arrow"}}, "key": "21E7"}, {"category": "So", "mappings": {"default": {"default": "rightwards white arrow", "short": "white right arrow"}, "mathspeak": {"default": "white right-arrow", "sbrief": "white R arrow"}}, "key": "21E8"}, {"category": "So", "mappings": {"default": {"default": "downwards white arrow", "short": "white down arrow"}, "mathspeak": {"default": "white down-arrow", "sbrief": "white D arrow"}}, "key": "21E9"}, {"category": "So", "mappings": {"default": {"default": "upwards white arrow from bar", "short": "white up arrow from bar"}, "mathspeak": {"default": "white up-arrow from bar", "sbrief": "white U arrow from bar"}}, "key": "21EA"}, {"category": "So", "mappings": {"default": {"default": "upwards white arrow on pedestal", "alternative": "up white arrow on pedestal", "short": "white up arrow on pedestal"}, "mathspeak": {"default": "white up-arrow on pedestal", "sbrief": "white U arrow on pedestal"}}, "key": "21EB"}, {"category": "So", "mappings": {"default": {"default": "upwards white arrow on pedestal with horizontal bar", "alternative": "up white arrow on pedestal with horizontal bar", "short": "white up arrow on pedestal with horizontal bar"}, "mathspeak": {"default": "white up-arrow on pedestal with horizontal bar", "sbrief": "white U arrow on pedestal with horizontal bar"}}, "key": "21EC"}, {"category": "So", "mappings": {"default": {"default": "upwards white arrow on pedestal with vertical bar", "alternative": "up white arrow on pedestal with vertical bar", "short": "white up arrow on pedestal with vertical bar"}, "mathspeak": {"default": "white up-arrow on pedestal with vertical bar", "sbrief": "white U arrow on pedestal with vertical bar"}}, "key": "21ED"}, {"category": "So", "mappings": {"default": {"default": "upwards white double arrow", "alternative": "up white double arrow", "short": "white double up arrow"}, "mathspeak": {"default": "white double up-arrow", "sbrief": "white double U arrow"}}, "key": "21EE"}, {"category": "So", "mappings": {"default": {"default": "upwards white double arrow on pedestal", "alternative": "up white double arrow on pedestal", "short": "white double up arrow on pedestal"}, "mathspeak": {"default": "white double up-arrow on pedestal", "sbrief": "white double U arrow on pedestal"}}, "key": "21EF"}, {"category": "So", "mappings": {"default": {"default": "rightwards white arrow from wall", "alternative": "right white arrow from wall", "short": "white right arrow from wall"}, "mathspeak": {"default": "white right-arrow from wall", "sbrief": "white R arrow from wall"}}, "key": "21F0"}, {"category": "So", "mappings": {"default": {"default": "north west arrow to corner"}}, "key": "21F1"}, {"category": "So", "mappings": {"default": {"default": "south east arrow to corner"}}, "key": "21F2"}, {"category": "So", "mappings": {"default": {"default": "up down white arrow"}, "mathspeak": {"sbrief": "U d white arrow"}}, "key": "21F3"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with small circle"}, "mathspeak": {"default": "right-arrow with small circle", "sbrief": "R arrow with small circle"}}, "key": "21F4"}, {"category": "Sm", "mappings": {"default": {"default": "down arrow leftwards of upwards arrow", "short": "down arrow left of up arrow"}, "mathspeak": {"default": "down-arrow left of up-arrow", "sbrief": "D arrow l of U arrow"}}, "key": "21F5"}, {"category": "Sm", "mappings": {"default": {"default": "three rightwards arrows", "short": "three right arrows"}, "mathspeak": {"default": "three right-arrows", "sbrief": "three R arrows"}}, "key": "21F6"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow with vertical stroke", "short": "left arrow with vertical stroke"}, "mathspeak": {"default": "left-arrow with vertical stroke", "sbrief": "L arrow with vertical stroke"}}, "key": "21F7"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow with vertical stroke", "short": "right arrow with vertical stroke"}, "mathspeak": {"default": "right-arrow with vertical stroke", "sbrief": "R arrow with vertical stroke"}}, "key": "21F8"}, {"category": "Sm", "mappings": {"default": {"default": "left right arrow with vertical stroke"}, "mathspeak": {"default": "left-right-arrow with vertical stroke", "sbrief": "L R arrow with vertical stroke"}}, "key": "21F9"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow with double vertical stroke", "short": "left arrow with double vertical stroke"}, "mathspeak": {"default": "left-arrow with double vertical stroke", "sbrief": "L arrow with double vertical stroke"}}, "key": "21FA"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow with double vertical stroke", "short": "right arrow with double vertical stroke"}, "mathspeak": {"default": "right-arrow with double vertical stroke", "sbrief": "R arrow with double vertical stroke"}}, "key": "21FB"}, {"category": "Sm", "mappings": {"default": {"default": "left right arrow with double vertical stroke"}, "mathspeak": {"default": "left-right-arrow with double vertical stroke", "sbrief": "L R arrow with double vertical stroke"}}, "key": "21FC"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards open headed arrow", "short": "left open headed arrow"}, "mathspeak": {"sbrief": "l open headed arrow"}}, "key": "21FD"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards open headed arrow", "short": "right open headed arrow"}, "mathspeak": {"sbrief": "r open headed arrow"}}, "key": "21FE"}, {"category": "Sm", "mappings": {"default": {"default": "left right open headed arrow"}, "mathspeak": {"sbrief": "L R open headed arrow"}}, "key": "21FF"}, {"category": "So", "mappings": {"default": {"default": "electric arrow"}}, "key": "2301"}, {"category": "So", "mappings": {"default": {"default": "up arrowhead"}, "mathspeak": {"default": "up-arrowhead", "sbrief": "U arrowhead"}}, "key": "2303"}, {"category": "So", "mappings": {"default": {"default": "down arrowhead"}, "mathspeak": {"default": "down-arrowhead", "sbrief": "D arrowhead"}}, "key": "2304"}, {"category": "So", "mappings": {"default": {"default": "up arrowhead between two horizontal bars", "alternative": "enter key"}, "mathspeak": {"default": "up-arrowhead between two horizontal bars", "sbrief": "U arrowhead between two horizontal bars"}}, "key": "2324"}, {"category": "So", "mappings": {"default": {"default": "broken circle with northwest arrow"}}, "key": "238B"}, {"category": "So", "mappings": {"default": {"default": "heavy wide headed rightwards arrow", "short": "heavy wide headed right arrow"}, "mathspeak": {"default": "heavy wide headed right-arrow", "sbrief": "heavy wide headed R arrow"}}, "key": "2794"}, {"category": "So", "mappings": {"default": {"default": "heavy south east arrow", "short": "heavy down right arrow"}, "mathspeak": {"default": "heavy down right-arrow", "sbrief": "heavy d R arrow"}}, "key": "2798"}, {"category": "So", "mappings": {"default": {"default": "heavy rightwards arrow", "short": "heavy right arrow"}, "mathspeak": {"default": "heavy right-arrow", "sbrief": "heavy R arrow"}}, "key": "2799"}, {"category": "So", "mappings": {"default": {"default": "heavy north east arrow", "short": "heavy up right arrow"}, "mathspeak": {"default": "heavy up right-arrow", "sbrief": "heavy U R arrow"}}, "key": "279A"}, {"category": "So", "mappings": {"default": {"default": "drafting point rightwards arrow", "short": "drafting point right arrow"}, "mathspeak": {"default": "drafting point right-arrow", "sbrief": "drafting point R arrow"}}, "key": "279B"}, {"category": "So", "mappings": {"default": {"default": "heavy round tipped rightwards arrow", "short": "heavy round tipped right arrow"}, "mathspeak": {"default": "heavy round tipped right-arrow", "sbrief": "heavy round tipped R arrow"}}, "key": "279C"}, {"category": "So", "mappings": {"default": {"default": "triangle headed rightwards arrow", "short": "triangle headed right arrow"}, "mathspeak": {"default": "triangle headed right-arrow", "sbrief": "triangle headed R arrow"}}, "key": "279D"}, {"category": "So", "mappings": {"default": {"default": "heavy triangle headed rightwards arrow", "short": "heavy triangle headed right arrow"}, "mathspeak": {"default": "heavy triangle headed right-arrow", "sbrief": "heavy triangle headed R arrow"}}, "key": "279E"}, {"category": "So", "mappings": {"default": {"default": "dashed triangle headed rightwards arrow", "short": "dashed triangle headed right arrow"}, "mathspeak": {"default": "dashed triangle headed right-arrow", "sbrief": "dashed triangle headed R arrow"}}, "key": "279F"}, {"category": "So", "mappings": {"default": {"default": "heavy dashed triangle headed rightwards arrow", "short": "heavy dashed triangle headed right arrow"}, "mathspeak": {"default": "heavy dashed triangle headed right-arrow", "sbrief": "heavy dashed triangle headed R arrow"}}, "key": "27A0"}, {"category": "So", "mappings": {"default": {"default": "black rightwards arrow", "short": "black right arrow"}, "mathspeak": {"default": "black right-arrow", "sbrief": "black R arrow"}}, "key": "27A1"}, {"category": "So", "mappings": {"default": {"default": "three d top lighted rightwards arrowhead", "short": "three d top lighted right arrowhead"}, "mathspeak": {"default": "three d top lighted right-arrowhead", "sbrief": "three d top lighted R arrowhead"}}, "key": "27A2"}, {"category": "So", "mappings": {"default": {"default": "three d bottom lighted rightwards arrowhead", "short": "three d bottom lighted right arrowhead"}, "mathspeak": {"default": "three d bottom lighted right-arrowhead", "sbrief": "three d bottom lighted R arrowhead"}}, "key": "27A3"}, {"category": "So", "mappings": {"default": {"default": "black rightwards arrowhead", "short": "black right arrowhead"}, "mathspeak": {"default": "black right-arrowhead", "sbrief": "black R arrowhead"}}, "key": "27A4"}, {"category": "So", "mappings": {"default": {"default": "heavy black curved downwards and rightwards arrow", "short": "heavy black curved down and right arrow"}, "mathspeak": {"default": "heavy black curved down and right-arrow", "sbrief": "heavy black curved d and R arrow"}}, "key": "27A5"}, {"category": "So", "mappings": {"default": {"default": "heavy black curved upwards and rightwards arrow", "short": "heavy black curved up and right arrow"}, "mathspeak": {"default": "heavy black curved up and right-arrow", "sbrief": "heavy black curved U and R arrow"}}, "key": "27A6"}, {"category": "So", "mappings": {"default": {"default": "squat black rightwards arrow", "short": "squat black right arrow"}, "mathspeak": {"default": "squat black right-arrow", "sbrief": "squat black R arrow"}}, "key": "27A7"}, {"category": "So", "mappings": {"default": {"default": "heavy concave pointed black rightwards arrow", "short": "heavy concave pointed black right arrow"}, "mathspeak": {"default": "heavy concave pointed black right-arrow", "sbrief": "heavy concave pointed black R arrow"}}, "key": "27A8"}, {"category": "So", "mappings": {"default": {"default": "right shaded white rightwards arrow", "short": "right shaded white right arrow"}, "mathspeak": {"default": "right shaded white right-arrow", "sbrief": "right shaded white R arrow"}}, "key": "27A9"}, {"category": "So", "mappings": {"default": {"default": "left shaded white rightwards arrow", "short": "left shaded white right arrow"}, "mathspeak": {"default": "left shaded white right-arrow", "sbrief": "left shaded white R arrow"}}, "key": "27AA"}, {"category": "So", "mappings": {"default": {"default": "back tilted shadowed white rightwards arrow", "short": "back tilted shadowed white right arrow"}, "mathspeak": {"default": "back tilted shadowed white right-arrow", "sbrief": "back tilted shadowed white R arrow"}}, "key": "27AB"}, {"category": "So", "mappings": {"default": {"default": "front tilted shadowed white rightwards arrow", "short": "front tilted shadowed white right arrow"}, "mathspeak": {"default": "front tilted shadowed white right-arrow", "sbrief": "front tilted shadowed white R arrow"}}, "key": "27AC"}, {"category": "So", "mappings": {"default": {"default": "heavy lower right shadowed white rightwards arrow", "short": "heavy lower right shadowed white right arrow"}, "mathspeak": {"default": "heavy lower right shadowed white right-arrow", "sbrief": "heavy lower right shadowed white R arrow"}}, "key": "27AD"}, {"category": "So", "mappings": {"default": {"default": "heavy upper right shadowed white rightwards arrow", "short": "heavy upper right shadowed white right arrow"}, "mathspeak": {"default": "heavy upper right shadowed white right-arrow", "sbrief": "heavy upper right shadowed white R arrow"}}, "key": "27AE"}, {"category": "So", "mappings": {"default": {"default": "notched lower right shadowed white rightwards arrow", "short": "notched lower right shadowed white right arrow"}, "mathspeak": {"default": "notched lower right shadowed white right-arrow", "sbrief": "notched lower right shadowed white R arrow"}}, "key": "27AF"}, {"category": "So", "mappings": {"default": {"default": "notched upper right shadowed white rightwards arrow", "short": "notched upper right shadowed white right arrow"}, "mathspeak": {"default": "notched upper right shadowed white right-arrow", "sbrief": "notched upper right shadowed white R arrow"}}, "key": "27B1"}, {"category": "So", "mappings": {"default": {"default": "circled heavy white rightwards arrow", "short": "circled heavy white right arrow"}, "mathspeak": {"default": "circled heavy white right-arrow", "sbrief": "circled heavy white R arrow"}}, "key": "27B2"}, {"category": "So", "mappings": {"default": {"default": "white feathered rightwards arrow", "short": "white feathered right arrow"}, "mathspeak": {"default": "white feathered right-arrow", "sbrief": "white feathered R arrow"}}, "key": "27B3"}, {"category": "So", "mappings": {"default": {"default": "black feathered south east arrow", "short": "black feathered down right arrow"}, "mathspeak": {"default": "black feathered down right-arrow", "sbrief": "black feathered d R arrow"}}, "key": "27B4"}, {"category": "So", "mappings": {"default": {"default": "black feathered rightwards arrow", "short": "black feathered right arrow"}, "mathspeak": {"default": "black feathered right-arrow", "sbrief": "black feathered R arrow"}}, "key": "27B5"}, {"category": "So", "mappings": {"default": {"default": "black feathered north east arrow", "short": "black feathered up right arrow"}, "mathspeak": {"default": "black feathered up right-arrow", "sbrief": "black feathered U R arrow"}}, "key": "27B6"}, {"category": "So", "mappings": {"default": {"default": "heavy black feathered south east arrow", "short": "heavy black feathered down right arrow"}, "mathspeak": {"default": "heavy black feathered down right-arrow", "sbrief": "heavy black feathered d R arrow"}}, "key": "27B7"}, {"category": "So", "mappings": {"default": {"default": "heavy black feathered rightwards arrow", "short": "heavy black feathered right arrow"}, "mathspeak": {"default": "heavy black feathered right-arrow", "sbrief": "heavy black feathered R arrow"}}, "key": "27B8"}, {"category": "So", "mappings": {"default": {"default": "heavy black feathered north east arrow", "short": "heavy black feathered up right arrow"}, "mathspeak": {"default": "heavy black feathered up right-arrow", "sbrief": "heavy black feathered U R arrow"}}, "key": "27B9"}, {"category": "So", "mappings": {"default": {"default": "teardrop barbed rightwards arrow", "short": "teardrop barbed right arrow"}, "mathspeak": {"default": "teardrop barbed right-arrow", "sbrief": "teardrop barbed R arrow"}}, "key": "27BA"}, {"category": "So", "mappings": {"default": {"default": "heavy teardrop shanked rightwards arrow", "short": "heavy teardrop shanked right arrow"}, "mathspeak": {"default": "heavy teardrop shanked right-arrow", "sbrief": "heavy teardrop shanked R arrow"}}, "key": "27BB"}, {"category": "So", "mappings": {"default": {"default": "wedge tailed rightwards arrow", "short": "wedge tailed right arrow"}, "mathspeak": {"default": "wedge tailed right-arrow", "sbrief": "wedge tailed R arrow"}}, "key": "27BC"}, {"category": "So", "mappings": {"default": {"default": "heavy wedge tailed rightwards arrow", "short": "heavy wedge tailed right arrow"}, "mathspeak": {"default": "heavy wedge tailed right-arrow", "sbrief": "heavy wedge tailed R arrow"}}, "key": "27BD"}, {"category": "So", "mappings": {"default": {"default": "open outlined rightwards arrow", "short": "open outlined right arrow"}, "mathspeak": {"default": "open outlined right-arrow", "sbrief": "open outlined R arrow"}}, "key": "27BE"}, {"category": "Sm", "mappings": {"default": {"default": "upwards quadruple arrow", "short": "up quadruple arrow"}, "mathspeak": {"sbrief": "U quadruple arrow"}}, "key": "27F0"}, {"category": "Sm", "mappings": {"default": {"default": "downwards quadruple arrow", "short": "down quadruple arrow"}, "mathspeak": {"sbrief": "d quadrule arrow"}}, "key": "27F1"}, {"category": "Sm", "mappings": {"default": {"default": "anticlockwise gapped circle arrow"}}, "key": "27F2"}, {"category": "Sm", "mappings": {"default": {"default": "clockwise gapped circle arrow"}}, "key": "27F3"}, {"category": "Sm", "mappings": {"default": {"default": "right arrow with circled plus"}, "mathspeak": {"default": "right-arrow with circled plus", "sbrief": "R arrow with circled plus"}}, "key": "27F4"}, {"category": "Sm", "mappings": {"default": {"default": "long leftwards arrow", "short": "long left arrow"}, "mathspeak": {"default": "long left-arrow", "sbrief": "long L arrow"}}, "key": "27F5"}, {"category": "Sm", "mappings": {"default": {"default": "long rightwards arrow", "short": "long right arrow"}, "mathspeak": {"default": "long right-arrow", "sbrief": "long R arrow"}}, "key": "27F6"}, {"category": "Sm", "mappings": {"default": {"default": "long left right arrow"}, "mathspeak": {"default": "long left-right-arrow", "sbrief": "long L R arrow"}}, "key": "27F7"}, {"category": "Sm", "mappings": {"default": {"default": "long leftwards double arrow", "short": "long left double arrow"}, "mathspeak": {"sbrief": "long l double arrow"}}, "key": "27F8"}, {"category": "Sm", "mappings": {"default": {"default": "long rightwards double arrow", "short": "long right double arrow"}, "mathspeak": {"sbrief": "long R double arrow"}}, "key": "27F9"}, {"category": "Sm", "mappings": {"default": {"default": "long left right double arrow"}, "mathspeak": {"sbrief": "long L R double arrow"}}, "key": "27FA"}, {"category": "Sm", "mappings": {"default": {"default": "long leftwards arrow from bar", "short": "long left arrow from bar"}, "mathspeak": {"default": "long left-arrow from bar", "sbrief": "long L arrow from bar"}}, "key": "27FB"}, {"category": "Sm", "mappings": {"default": {"default": "long rightwards arrow from bar", "short": "long right arrow from bar"}, "mathspeak": {"default": "long right-arrow from bar", "sbrief": "long R arrow from bar"}}, "key": "27FC"}, {"category": "Sm", "mappings": {"default": {"default": "long leftwards double arrow from bar", "short": "long left double arrow from bar"}, "mathspeak": {"sbrief": "long l double arrow from bar"}}, "key": "27FD"}, {"category": "Sm", "mappings": {"default": {"default": "long rightwards double arrow from bar", "short": "long right double arrow from bar"}, "mathspeak": {"sbrief": "long R double arrow from bar"}}, "key": "27FE"}, {"category": "Sm", "mappings": {"default": {"default": "long rightwards squiggle arrow", "short": "long right squiggle arrow"}, "mathspeak": {"sbrief": "long r squiggle arrow"}}, "key": "27FF"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards two headed arrow with vertical stroke", "alternative": "right two headed arrow with vertical stroke", "short": "two headed right arrow with vertical stroke"}, "mathspeak": {"default": "two headed right-arrow with vertical stroke", "sbrief": "two headed R arrow with vertical stroke"}}, "key": "2900"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards two headed arrow with double vertical stroke", "alternative": "right two headed arrow with double vertical stroke", "short": "two headed right arrow with double vertical stroke"}, "mathspeak": {"default": "two headed right-arrow with double vertical stroke", "sbrief": "two headed R arrow with double vertical stroke"}}, "key": "2901"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards double arrow with vertical stroke", "alternative": "left double arrow with vertical stroke", "short": "double left arrow with vertical stroke"}, "mathspeak": {"default": "double left-arrow with vertical stroke", "sbrief": "double L arrow with vertical stroke"}}, "key": "2902"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards double arrow with vertical stroke", "alternative": "right double arrow with vertical stroke", "short": "double right arrow with vertical stroke"}, "mathspeak": {"default": "double right-arrow with vertical stroke", "sbrief": "double R arrow with vertical stroke"}}, "key": "2903"}, {"category": "Sm", "mappings": {"default": {"default": "left right double arrow with vertical stroke", "short": "double left right arrow with vertical stroke"}, "mathspeak": {"default": "double left-right-arrow with vertical stroke", "sbrief": "double L R arrow with vertical stroke"}}, "key": "2904"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards two headed arrow from bar", "alternative": "right two headed arrow from bar", "short": "two headed right arrow from bar"}, "mathspeak": {"default": "two headed right-arrow from bar", "sbrief": "two headed R arrow from bar"}}, "key": "2905"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards double arrow from bar", "alternative": "left double arrow from bar", "short": "double left arrow from bar"}, "mathspeak": {"default": "double left-arrow from bar", "sbrief": "double L arrow from bar"}}, "key": "2906"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards double arrow from bar", "alternative": "right double arrow from bar", "short": "double right arrow from bar"}, "mathspeak": {"default": "double right-arrow from bar", "sbrief": "double R arrow from bar"}}, "key": "2907"}, {"category": "Sm", "mappings": {"default": {"default": "downwards arrow with horizontal stroke", "alternative": "down arrow with horizontal stroke", "short": "arrow down with horizontal stroke"}}, "key": "2908"}, {"category": "Sm", "mappings": {"default": {"default": "upwards arrow with horizontal stroke", "short": "up arrow with horizontal stroke"}, "mathspeak": {"default": "up-arrow with horizontal stroke", "sbrief": "U arrow with horizontal stroke"}}, "key": "2909"}, {"category": "Sm", "mappings": {"default": {"default": "upwards triple arrow", "short": "up triple arrow"}, "mathspeak": {"sbrief": "U triple arrow"}}, "key": "290A"}, {"category": "Sm", "mappings": {"default": {"default": "downwards triple arrow", "short": "down triple arrow"}, "mathspeak": {"sbrief": "d triple arrow"}}, "key": "290B"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards double dash arrow", "short": "left double dash arrow"}, "mathspeak": {"sbrief": "l double dash arrow"}}, "key": "290C"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards double dash arrow", "short": "right double dash arrow"}, "mathspeak": {"sbrief": "R double dash arrow"}}, "key": "290D"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards triple dash arrow", "short": "left triple dash arrow"}, "mathspeak": {"sbrief": "l triple dash arrow"}}, "key": "290E"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards triple dash arrow", "short": "right triple dash arrow"}, "mathspeak": {"sbrief": "r triple dash arrow"}}, "key": "290F"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards two headed triple dash arrow", "short": "right two headed triple dash arrow"}, "mathspeak": {"sbrief": "r two headed triple dash arrow"}}, "key": "2910"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow with dotted stem", "short": "right arrow with dotted stem"}, "mathspeak": {"default": "right-arrow with dotted stem", "sbrief": "R arrow with dotted stem"}}, "key": "2911"}, {"category": "Sm", "mappings": {"default": {"default": "upwards arrow to bar", "short": "up arrow to bar"}, "mathspeak": {"default": "up-arrow to bar", "sbrief": "U arrow to bar"}}, "key": "2912"}, {"category": "Sm", "mappings": {"default": {"default": "downwards arrow to bar", "short": "down arrow to bar"}, "mathspeak": {"default": "down-arrow to bar", "sbrief": "D arrow to bar"}}, "key": "2913"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow with tail with vertical stroke", "short": "right arrow with tail with vertical stroke"}, "mathspeak": {"default": "right-arrow with tail with vertical stroke", "sbrief": "R arrow with tail with vertical stroke"}}, "key": "2914"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow with tail with double vertical stroke", "short": "right arrow with tail with double vertical stroke"}, "mathspeak": {"default": "right-arrow with tail with double vertical stroke", "sbrief": "R arrow with tail with double vertical stroke"}}, "key": "2915"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards two headed arrow with tail", "short": "right two headed arrow with tail"}, "mathspeak": {"sbrief": "r two headed arrow with tail"}}, "key": "2916"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards two headed arrow with tail with vertical stroke", "short": "right two headed arrow with tail with vertical stroke"}, "mathspeak": {"sbrief": "r two headed arrow with tail with vertical stroke"}}, "key": "2917"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards two headed arrow with tail with double vertical stroke", "short": "right two headed arrow with tail with double vertical stroke"}, "mathspeak": {"sbrief": "r two headed arrow with tail with double vertical stroke"}}, "key": "2918"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow tail", "short": "left arrow tail"}, "mathspeak": {"default": "left-arrow tail", "sbrief": "L arrow tail"}}, "key": "2919"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow tail", "short": "right arrow tail"}, "mathspeak": {"default": "right-arrow tail", "sbrief": "R arrow tail"}}, "key": "291A"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards double arrow tail", "short": "left double arrow tail"}, "mathspeak": {"sbrief": "l double arrow tail"}}, "key": "291B"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards double arrow tail", "short": "right double arrow tail"}, "mathspeak": {"sbrief": "R double arrow tail"}}, "key": "291C"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow to black diamond", "short": "left arrow to black diamond"}, "mathspeak": {"default": "left-arrow to black diamond", "sbrief": "L arrow to black diamond"}}, "key": "291D"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow to black diamond", "short": "right arrow to black diamond"}, "mathspeak": {"default": "right-arrow to black diamond", "sbrief": "R arrow to black diamond"}}, "key": "291E"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow from bar to black diamond", "short": "left arrow from bar to black diamond"}, "mathspeak": {"default": "left-arrow from bar to black diamond", "sbrief": "L arrow from bar to black diamond"}}, "key": "291F"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow from bar to black diamond", "short": "right arrow from bar to black diamond"}, "mathspeak": {"default": "right-arrow from bar to black diamond", "sbrief": "R arrow from bar to black diamond"}}, "key": "2920"}, {"category": "Sm", "mappings": {"default": {"default": "north west and south east arrow"}}, "key": "2921"}, {"category": "Sm", "mappings": {"default": {"default": "north east and south west arrow"}}, "key": "2922"}, {"category": "Sm", "mappings": {"default": {"default": "north west arrow with hook"}}, "key": "2923"}, {"category": "Sm", "mappings": {"default": {"default": "north east arrow with hook"}}, "key": "2924"}, {"category": "Sm", "mappings": {"default": {"default": "south east arrow with hook"}}, "key": "2925"}, {"category": "Sm", "mappings": {"default": {"default": "south west arrow with hook"}}, "key": "2926"}, {"category": "Sm", "mappings": {"default": {"default": "north west arrow and north east arrow"}}, "key": "2927"}, {"category": "Sm", "mappings": {"default": {"default": "north east arrow and south east arrow"}}, "key": "2928"}, {"category": "Sm", "mappings": {"default": {"default": "south east arrow and south west arrow"}}, "key": "2929"}, {"category": "Sm", "mappings": {"default": {"default": "south west arrow and north west arrow"}}, "key": "292A"}, {"category": "Sm", "mappings": {"default": {"default": "south east arrow crossing north east arrow"}}, "key": "292D"}, {"category": "Sm", "mappings": {"default": {"default": "north east arrow crossing south east arrow"}}, "key": "292E"}, {"category": "Sm", "mappings": {"default": {"default": "falling diagonal crossing north east arrow"}}, "key": "292F"}, {"category": "Sm", "mappings": {"default": {"default": "rising diagonal crossing south east arrow"}}, "key": "2930"}, {"category": "Sm", "mappings": {"default": {"default": "north east arrow crossing north west arrow"}}, "key": "2931"}, {"category": "Sm", "mappings": {"default": {"default": "north west arrow crossing north east arrow"}}, "key": "2932"}, {"category": "Sm", "mappings": {"default": {"default": "wave arrow pointing directly right"}}, "key": "2933"}, {"category": "Sm", "mappings": {"default": {"default": "arrow pointing rightwards then curving upwards", "short": "arrow pointing right then curving up"}}, "key": "2934"}, {"category": "Sm", "mappings": {"default": {"default": "arrow pointing rightwards then curving downwards", "short": "arrow pointing right then curving down"}}, "key": "2935"}, {"category": "Sm", "mappings": {"default": {"default": "arrow pointing downwards then curving leftwards", "short": "arrow pointing down then curving left"}}, "key": "2936"}, {"category": "Sm", "mappings": {"default": {"default": "arrow pointing downwards then curving rightwards", "short": "arrow pointing down then curving right"}}, "key": "2937"}, {"category": "Sm", "mappings": {"default": {"default": "right side arc clockwise arrow"}, "mathspeak": {"sbrief": "r side arc clockwise arrow"}}, "key": "2938"}, {"category": "Sm", "mappings": {"default": {"default": "left side arc anticlockwise arrow"}, "mathspeak": {"sbrief": "l side arc anticlockwise arrow"}}, "key": "2939"}, {"category": "Sm", "mappings": {"default": {"default": "top arc anticlockwise arrow"}}, "key": "293A"}, {"category": "Sm", "mappings": {"default": {"default": "bottom arc anticlockwise arrow"}}, "key": "293B"}, {"category": "Sm", "mappings": {"default": {"default": "top arc clockwise arrow with minus"}}, "key": "293C"}, {"category": "Sm", "mappings": {"default": {"default": "top arc anticlockwise arrow with plus"}}, "key": "293D"}, {"category": "Sm", "mappings": {"default": {"default": "lower right semicircular clockwise arrow", "short": "down right semicircular clockwise arrow"}, "mathspeak": {"sbrief": "d r semicircular clockwise arrow"}}, "key": "293E"}, {"category": "Sm", "mappings": {"default": {"default": "lower left semicircular anticlockwise arrow", "short": "down left semicircular anticlockwise arrow"}, "mathspeak": {"sbrief": "d l semicircular anticlockwise arrow"}}, "key": "293F"}, {"category": "Sm", "mappings": {"default": {"default": "anticlockwise closed circle arrow"}}, "key": "2940"}, {"category": "Sm", "mappings": {"default": {"default": "clockwise closed circle arrow"}}, "key": "2941"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow above short leftwards arrow", "short": "right arrow above short left arrow"}, "mathspeak": {"default": "right-arrow above short left-arrow", "sbrief": "R arrow above short L arrow"}}, "key": "2942"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow above short rightwards arrow", "short": "left arrow above short right arrow"}, "mathspeak": {"default": "left-arrow above short right-arrow", "sbrief": "L arrow above short R arrow"}}, "key": "2943"}, {"category": "Sm", "mappings": {"default": {"default": "short rightwards arrow above leftwards arrow", "short": "short right arrow above left arrow"}, "mathspeak": {"default": "short right-arrow above left-arrow", "sbrief": "short R arrow above L arrow"}}, "key": "2944"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow with plus below", "short": "right arrow with plus below"}, "mathspeak": {"default": "right-arrow with plus below", "sbrief": "R arrow with plus below"}}, "key": "2945"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow with plus below", "short": "left arrow with plus below"}, "mathspeak": {"default": "left-arrow with plus below", "sbrief": "L arrow with plus below"}}, "key": "2946"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow through x", "short": "right arrow through x"}, "mathspeak": {"default": "right-arrow through x", "sbrief": "R arrow through x"}}, "key": "2947"}, {"category": "Sm", "mappings": {"default": {"default": "left right arrow through small circle"}, "mathspeak": {"default": "left-right-arrow through small circle", "sbrief": "L R arrow through small circle"}}, "key": "2948"}, {"category": "Sm", "mappings": {"default": {"default": "upwards two headed arrow from small circle", "short": "up two headed arrow from small circle"}, "mathspeak": {"sbrief": "U two headed arrow from small circle"}}, "key": "2949"}, {"category": "Sm", "mappings": {"default": {"default": "right double arrow with rounded head"}, "mathspeak": {"sbrief": "R double arrow with rounded head"}}, "key": "2970"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign above rightwards arrow", "short": "equals sign above right arrow"}, "mathspeak": {"default": "equals sign above right-arrow", "sbrief": "equals sign above R arrow"}}, "key": "2971"}, {"category": "Sm", "mappings": {"default": {"default": "tilde operator above rightwards arrow", "short": "tilde operator above right arrow"}, "mathspeak": {"default": "tilde operator above right-arrow", "sbrief": "tilde operator above R arrow"}}, "key": "2972"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow above tilde operator", "short": "left arrow above tilde operator"}, "mathspeak": {"default": "left-arrow above tilde operator", "sbrief": "L arrow above tilde operator"}}, "key": "2973"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow above tilde operator", "short": "right arrow above tilde operator"}, "mathspeak": {"default": "right-arrow above tilde operator", "sbrief": "R arrow above tilde operator"}}, "key": "2974"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow above almost equals", "short": "right arrow above almost equals"}, "mathspeak": {"default": "right-arrow above almost equals", "sbrief": "R arrow above almost equals"}}, "key": "2975"}, {"category": "Sm", "mappings": {"default": {"default": "less than above leftwards arrow", "short": "less than above left arrow"}, "mathspeak": {"default": "less than above left-arrow", "sbrief": "less than above L arrow"}}, "key": "2976"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow through less than", "short": "left arrow through less than"}, "mathspeak": {"default": "left-arrow through less than", "sbrief": "L arrow through less than"}}, "key": "2977"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above rightwards arrow", "short": "greater than above right arrow"}, "mathspeak": {"default": "greater than above right-arrow", "sbrief": "greater than above R arrow"}}, "key": "2978"}, {"category": "Sm", "mappings": {"default": {"default": "subset above rightwards arrow", "short": "subset above right arrow"}, "mathspeak": {"default": "subset above right-arrow", "sbrief": "subset above R arrow"}}, "key": "2979"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow through subset", "short": "left arrow through subset"}, "mathspeak": {"default": "left-arrow through subset", "sbrief": "L arrow through subset"}}, "key": "297A"}, {"category": "Sm", "mappings": {"default": {"default": "superset above leftwards arrow", "short": "superset above left arrow"}, "mathspeak": {"default": "superset above left-arrow", "sbrief": "suerset above L arrow"}}, "key": "297B"}, {"category": "Sm", "mappings": {"default": {"default": "empty set with right arrow above"}, "mathspeak": {"default": "empty set with right-arrow above", "sbrief": "empty set with R arrow above"}}, "key": "29B3"}, {"category": "Sm", "mappings": {"default": {"default": "empty set with left arrow above"}, "mathspeak": {"default": "empty set with left-arrow above", "sbrief": "empty set with L arrow above"}}, "key": "29B4"}, {"category": "Sm", "mappings": {"default": {"default": "up arrow through circle"}, "mathspeak": {"default": "up-arrow through circle", "sbrief": "U arrow through circle"}}, "key": "29BD"}, {"category": "Sm", "mappings": {"default": {"default": "black diamond with down arrow"}, "mathspeak": {"default": "black diamond with down-arrow", "sbrief": "black diamond with D arrow"}}, "key": "29EA"}, {"category": "Sm", "mappings": {"default": {"default": "white circle with down arrow"}, "mathspeak": {"default": "white circle with down-arrow", "sbrief": "white circle with D arrow"}}, "key": "29EC"}, {"category": "Sm", "mappings": {"default": {"default": "black circle with down arrow"}, "mathspeak": {"default": "black circle with down-arrow", "sbrief": "black circle with D arrow"}}, "key": "29ED"}, {"category": "Sm", "mappings": {"default": {"default": "integral with leftwards arrow with hook", "short": "integral with left arrow with hook"}, "mathspeak": {"default": "integral with left-arrow with hook", "sbrief": "integral with L arrow with hook"}}, "key": "2A17"}, {"category": "So", "mappings": {"default": {"default": "north east white arrow"}}, "key": "2B00"}, {"category": "So", "mappings": {"default": {"default": "north west white arrow"}}, "key": "2B01"}, {"category": "So", "mappings": {"default": {"default": "south east white arrow"}}, "key": "2B02"}, {"category": "So", "mappings": {"default": {"default": "south west white arrow"}}, "key": "2B03"}, {"category": "So", "mappings": {"default": {"default": "left right white arrow"}, "mathspeak": {"sbrief": "L R white arrow"}}, "key": "2B04"}, {"category": "So", "mappings": {"default": {"default": "leftwards black arrow", "short": "left black arrow"}, "mathspeak": {"sbrief": "L black arrow"}}, "key": "2B05"}, {"category": "So", "mappings": {"default": {"default": "upwards black arrow", "short": "up black arrow"}, "mathspeak": {"sbrief": "U black arrow"}}, "key": "2B06"}, {"category": "So", "mappings": {"default": {"default": "downwards black arrow", "short": "down black arrow"}, "mathspeak": {"sbrief": "D black arrow"}}, "key": "2B07"}, {"category": "So", "mappings": {"default": {"default": "north east black arrow"}}, "key": "2B08"}, {"category": "So", "mappings": {"default": {"default": "north west black arrow"}}, "key": "2B09"}, {"category": "So", "mappings": {"default": {"default": "south east black arrow"}}, "key": "2B0A"}, {"category": "So", "mappings": {"default": {"default": "south west black arrow"}}, "key": "2B0B"}, {"category": "So", "mappings": {"default": {"default": "left right black arrow"}, "mathspeak": {"sbrief": "L R black arrow"}}, "key": "2B0C"}, {"category": "So", "mappings": {"default": {"default": "up down black arrow"}, "mathspeak": {"sbrief": "U D black arrow"}}, "key": "2B0D"}, {"category": "So", "mappings": {"default": {"default": "rightwards arrow with tip downwards", "short": "right arrow with tip down"}, "mathspeak": {"default": "right-arrow with tip down", "sbrief": "R arrow with tip down"}}, "key": "2B0E"}, {"category": "So", "mappings": {"default": {"default": "rightwards arrow with tip upwards", "short": "right arrow with tip up"}, "mathspeak": {"default": "right-arrow with tip up", "sbrief": "R arrow with tip up"}}, "key": "2B0F"}, {"category": "So", "mappings": {"default": {"default": "leftwards arrow with tip downwards", "short": "left arrow with tip down"}, "mathspeak": {"default": "left-arrow with tip down", "sbrief": "L arrow with tip down"}}, "key": "2B10"}, {"category": "So", "mappings": {"default": {"default": "leftwards arrow with tip upwards", "short": "left arrow with tip up"}, "mathspeak": {"default": "left-arrow with tip up", "sbrief": "L arrow with tip up"}}, "key": "2B11"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with small circle"}, "mathspeak": {"default": "left-arrow with small circle", "sbrief": "L arrow with small circle"}}, "key": "2B30"}, {"category": "Sm", "mappings": {"default": {"default": "three leftwards arrows", "short": "three left arrows"}, "mathspeak": {"default": "three left-arrows", "sbrief": "three L arrows"}}, "key": "2B31"}, {"category": "Sm", "mappings": {"default": {"default": "left arrow with circled plus"}, "mathspeak": {"default": "left-arrow with circled plus", "sbrief": "L arrow with circled plus"}}, "key": "2B32"}, {"category": "Sm", "mappings": {"default": {"default": "long leftwards squiggle arrow", "short": "long left squiggle arrow"}, "mathspeak": {"sbrief": "long l squiggle arrow"}}, "key": "2B33"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards two headed arrow with vertical stroke", "short": "left two headed arrow with vertical stroke"}, "mathspeak": {"sbrief": "l two headed arrow with vertical stroke"}}, "key": "2B34"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards two headed arrow with double vertical stroke", "short": "left two headed arrow with double vertical stroke"}, "mathspeak": {"sbrief": "l two headed arrow with double vertical stroke"}}, "key": "2B35"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards two headed arrow from bar", "short": "left two headed arrow from bar"}, "mathspeak": {"sbrief": "l two headed arrow from bar"}}, "key": "2B36"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards two headed triple dash arrow", "short": "left two headed triple dash arrow"}, "mathspeak": {"sbrief": "l two headed triple dash arrow"}}, "key": "2B37"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow with dotted stem", "short": "left arrow with dotted stem"}, "mathspeak": {"default": "left-arrow with dotted stem", "sbrief": "L arrow with dotted stem"}}, "key": "2B38"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow with tail with vertical stroke", "short": "left arrow with tail with vertical stroke"}, "mathspeak": {"default": "left-arrow with tail with vertical stroke", "sbrief": "L arrow with tail with vertical stroke"}}, "key": "2B39"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow with tail with double vertical stroke", "short": "left arrow with tail with double vertical stroke"}, "mathspeak": {"default": "left-arrow with tail with double vertical stroke", "sbrief": "L arrow with tail with double vertical stroke"}}, "key": "2B3A"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards two headed arrow with tail", "short": "left two headed arrow with tail"}, "mathspeak": {"sbrief": "l two headed arrow with tail"}}, "key": "2B3B"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards two headed arrow with tail with vertical stroke", "short": "left two headed arrow with tail with vertical stroke"}, "mathspeak": {"sbrief": "l two headed arrow with tail with vertical stroke"}}, "key": "2B3C"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards two headed arrow with tail with double vertical stroke", "short": "left two headed arrow with tail with double vertical stroke"}, "mathspeak": {"sbrief": "l two headed arrow with tail with double vertical stroke"}}, "key": "2B3D"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow through x", "short": "left arrow through x"}, "mathspeak": {"default": "left-arrow through x", "sbrief": "L arrow through x"}}, "key": "2B3E"}, {"category": "Sm", "mappings": {"default": {"default": "wave arrow pointing directly left"}}, "key": "2B3F"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign above leftwards arrow", "short": "equals sign above left arrow"}, "mathspeak": {"default": "equals sign above left-arrow", "sbrief": "equals sign above L arrow"}}, "key": "2B40"}, {"category": "Sm", "mappings": {"default": {"default": "reverse tilde operator above leftwards arrow", "short": "reverse tilde operator above left arrow"}, "mathspeak": {"default": "reverse tilde operator above left-arrow", "sbrief": "reverse tilde operator above L arrow"}}, "key": "2B41"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow above reverse almost equals", "short": "left arrow above reverse almost equals"}, "mathspeak": {"default": "left-arrow above reverse almost equals", "sbrief": "L arrow above reverse almost equals"}}, "key": "2B42"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow through greater than", "short": "right arrow through greater than"}, "mathspeak": {"default": "right-arrow through greater than", "sbrief": "R arrow through greater than"}}, "key": "2B43"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow through superset", "short": "right arrow through superset"}, "mathspeak": {"default": "right-arrow through superset", "sbrief": "R arrow through superset"}}, "key": "2B44"}, {"category": "So", "mappings": {"default": {"default": "leftwards quadruple arrow", "short": "left quadruple arrow"}, "mathspeak": {"sbrief": "l quadrule arrow"}}, "key": "2B45"}, {"category": "So", "mappings": {"default": {"default": "rightwards quadruple arrow", "short": "right quadruple arrow"}, "mathspeak": {"sbrief": "r quadrule arrow"}}, "key": "2B46"}, {"category": "Sm", "mappings": {"default": {"default": "reverse tilde operator above rightwards arrow", "short": "reverse tilde operator above right arrow"}, "mathspeak": {"default": "reverse tilde operator above right-arrow", "sbrief": "reverse tilde operator above R arrow"}}, "key": "2B47"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow above reverse almost equals", "short": "right arrow above reverse almost equals"}, "mathspeak": {"default": "right-arrow above reverse almost equals", "sbrief": "R arrow above reverse almost equals"}}, "key": "2B48"}, {"category": "Sm", "mappings": {"default": {"default": "tilde operator above leftwards arrow", "short": "tilde operator above left arrow"}, "mathspeak": {"default": "tilde operator above left-arrow", "sbrief": "tilde operator above L arrow"}}, "key": "2B49"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow above almost equals", "short": "left arrow above almost equals"}, "mathspeak": {"default": "left-arrow above almost equals", "sbrief": "L arrow above almost equals"}}, "key": "2B4A"}, {"category": "Sm", "mappings": {"default": {"default": "leftwards arrow above reverse tilde operator", "short": "left arrow above reverse tilde operator"}, "mathspeak": {"default": "left-arrow above reverse tilde operator", "sbrief": "L arrow above reverse tilde operator"}}, "key": "2B4B"}, {"category": "Sm", "mappings": {"default": {"default": "rightwards arrow above reverse tilde operator", "short": "right arrow above reverse tilde operator"}, "mathspeak": {"default": "right-arrow above reverse tilde operator", "sbrief": "R arrow above reverse tilde operator"}}, "key": "2B4C"}, {"category": "Sm", "mappings": {"default": {"default": "halfwidth leftwards arrow", "short": "halfwidth left arrow"}, "mathspeak": {"default": "halfwidth left-arrow", "sbrief": "halfwidth L arrow"}}, "key": "FFE9"}, {"category": "Sm", "mappings": {"default": {"default": "halfwidth upwards arrow", "short": "halfwidth up arrow"}, "mathspeak": {"default": "halfwidth up-arrow", "sbrief": "halfwidth U arrow"}}, "key": "FFEA"}, {"category": "Sm", "mappings": {"default": {"default": "halfwidth rightwards arrow", "short": "halfwidth right arrow"}, "mathspeak": {"default": "halfwidth right-arrow", "sbrief": "halfwidth R arrow"}}, "key": "FFEB"}, {"category": "Sm", "mappings": {"default": {"default": "halfwidth downwards arrow", "short": "halfwidth down arrow"}, "mathspeak": {"default": "halfwidth down-arrow", "sbrief": "halfwidth D arrow"}}, "key": "FFEC"}]