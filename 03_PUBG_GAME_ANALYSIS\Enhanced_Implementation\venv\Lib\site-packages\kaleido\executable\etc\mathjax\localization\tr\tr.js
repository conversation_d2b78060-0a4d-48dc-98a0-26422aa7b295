/*************************************************************
 *
 *  MathJax/localization/tr/tr.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("tr",null,{
  menuTitle: "T\u00FCrk\u00E7e",
  version: "2.7.5",
  isLoaded: true,
  domains: {
    "_": {
        version: "2.7.5",
        isLoaded: true,
        strings: {
          MathProcessingError: "Matematik i\u015Fleme hatas\u0131",
          MathError: "Matematik hatas\u0131",
          LoadFile: "%1 Y\u00FCkleniyor",
          Loading: "Y\u00FCkleniyor",
          LoadFailed: "Dosya y\u00FCklenemedi: %1",
          ProcessMath: "Matematik i\u015Fleme: %%%1",
          MathJaxNotSupported: "Taray\u0131c\u0131n\u0131z MathJax'i desteklemiyor"
        }
    },
    "FontWarnings": {},
    "HTML-CSS": {},
    "HelpDialog": {},
    "MathML": {},
    "MathMenu": {},
    "TeX": {}
  },
  plural: function (n) {
      return 1; // other
    },
  number: function (n) {
      return n;
    }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/tr/tr.js");
