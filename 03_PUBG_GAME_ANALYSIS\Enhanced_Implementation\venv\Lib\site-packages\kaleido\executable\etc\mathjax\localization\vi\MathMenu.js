/*************************************************************
 *
 *  MathJax/localization/vi/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("vi","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "Xem To\u00E1n D\u01B0\u1EDBi d\u1EA1ng",
          MathMLcode: "M\u00E3 ngu\u1ED3n MathML",
          OriginalMathML: "MathML G\u1ED1c",
          TeXCommands: "L\u1EC7nh TeX",
          AsciiMathInput: "\u0110\u1EA7u v\u00E0o AsciiMathML",
          Original: "H\u00ECnh th\u1EE9c G\u1ED1c",
          ErrorMessage: "Th\u00F4ng b\u00E1o L\u1ED7i",
          Annotation: "Ch\u00FA th\u00EDch",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "MathML N\u1ED9i dung",
          OpenMath: "OpenMath",
          texHints: "Xem g\u1EE3i \u00FD TeX trong MathML",
          Settings: "T\u00F9y ch\u1ECDn To\u00E1n",
          ZoomTrigger: "H\u00E0nh \u0111\u1ED9ng Ph\u00F3ng to",
          Hover: "R\u00EA chu\u1ED9t",
          Click: "Nh\u1EA5n chu\u1ED9t",
          DoubleClick: "Nh\u1EA5n \u0111\u00FAp chu\u1ED9t",
          NoZoom: "Kh\u00F4ng ph\u00F3ng to",
          TriggerRequires: "Ch\u1EC9 khi B\u1EA5m gi\u1EEF:",
          Option: "Option",
          Alt: "Alt",
          Command: "Command",
          Control: "Control",
          Shift: "Shift",
          ZoomFactor: "T\u1EF7 l\u1EC7 Ph\u00F3ng to",
          Renderer: "Ki\u1EC3u K\u1EBFt xu\u1EA5t To\u00E1n",
          MPHandles: "Cho ph\u00E9p MathPlayer X\u1EED l\u00FD:",
          MenuEvents: "S\u1EF1 ki\u1EC7n Tr\u00ECnh \u0111\u01A1n",
          MouseEvents: "S\u1EF1 ki\u1EC7n Chu\u1ED9t",
          MenuAndMouse: "S\u1EF1 ki\u1EC7n Chu\u1ED9t v\u00E0 Tr\u00ECnh \u0111\u01A1n",
          FontPrefs: "T\u00F9y ch\u1ECDn Ph\u00F4ng ch\u1EEF",
          ForHTMLCSS: "Cho HTML+CSS:",
          Auto: "T\u1EF1 \u0111\u1ED9ng",
          TeXLocal: "TeX (tr\u00EAn m\u00E1y)",
          TeXWeb: "TeX (tr\u00EAn Web)",
          TeXImage: "TeX (h\u00ECnh \u1EA3nh)",
          STIXLocal: "STIX (tr\u00EAn m\u00E1y)",
          STIXWeb: "STIX (tr\u00EAn Web)",
          AsanaMathWeb: "Asana Math (tr\u00EAn Web)",
          GyrePagellaWeb: "Gyre Pagella (tr\u00EAn Web)",
          GyreTermesWeb: "Gyre Termes (tr\u00EAn Web)",
          LatinModernWeb: "Latinh Modern (tr\u00EAn Web)",
          NeoEulerWeb: "Neo Euler (tr\u00EAn Web)",
          ContextMenu: "Tr\u00ECnh \u0111\u01A1n Ng\u1EEF c\u1EA3nh",
          Browser: "Tr\u00ECnh duy\u1EC7t",
          Scale: "Ph\u00F3ng to T\u1EA5t c\u1EA3 To\u00E1n\u2026",
          Discoverable: "T\u00F4 s\u00E1ng khi R\u00EA chu\u1ED9t",
          Locale: "Ng\u00F4n ng\u1EEF",
          LoadLocale: "T\u1EA3i t\u1EEB URL\u2026",
          About: "Gi\u1EDBi thi\u1EC7u v\u1EC1 MathJax",
          Help: "Tr\u1EE3 gi\u00FAp MathJax",
          localTeXfonts: "d\u00F9ng c\u00E1c ph\u00F4ng ch\u1EEF TeX tr\u00EAn m\u00E1y",
          webTeXfonts: "d\u00F9ng ph\u00F4ng ch\u1EEF TeX tr\u00EAn Web",
          imagefonts: "d\u00F9ng c\u00E1c ph\u00F4ng ch\u1EEF h\u00ECnh \u1EA3nh",
          localSTIXfonts: "d\u00F9ng c\u00E1c ph\u00F4ng ch\u1EEF STIX tr\u00EAn m\u00E1y",
          webSVGfonts: "d\u00F9ng c\u00E1c ph\u00F4ng ch\u1EEF SVG tr\u00EAn Web",
          genericfonts: "d\u00F9ng c\u00E1c ph\u00F4ng ch\u1EEF Unicode chung",
          wofforotffonts: "Ph\u00F4ng ch\u1EEF WOFF ho\u1EB7c OTF",
          eotffonts: "Ph\u00F4ng ch\u1EEF EOT",
          svgfonts: "Ph\u00F4ng ch\u1EEF SVG",
          WebkitNativeMMLWarning: "Tr\u00ECnh duy\u1EC7t c\u1EE7a b\u1EA1n h\u00ECnh nh\u01B0 kh\u00F4ng h\u1ED7 tr\u1EE3 MathML l\u00E0 m\u1ED9t ng\u00F4n ng\u1EEF g\u1ED1c, n\u00EAn k\u00EDch ho\u1EA1t ch\u1EBF \u0111\u1ED9 k\u1EBFt xu\u1EA5t MathML c\u00F3 th\u1EC3 l\u00E0m cho kh\u00F4ng \u0111\u1ECDc \u0111\u01B0\u1EE3c c\u00E1c to\u00E1n tr\u00EAn trang.",
          MSIENativeMMLWarning: "Internet Explorer c\u1EA7n ph\u1EA7n b\u1ED5 sung MathPlayer \u0111\u1EC3 x\u1EED l\u00FD \u0111\u1EA7u ra MathML.",
          OperaNativeMMLWarning: "Opera kh\u00F4ng h\u1ED7 tr\u1EE3 MathML \u0111\u1EA7y \u0111\u1EE7, n\u00EAn k\u00EDch ho\u1EA1t ch\u1EBF \u0111\u1ED9 k\u1EBFt xu\u1EA5t MathML c\u00F3 th\u1EC3 l\u00E0m cho m\u1ED9t s\u1ED1 bi\u1EC3u th\u1EE9c hi\u1EC3n th\u1ECB m\u1ED9t c\u00E1ch kh\u00F4ng ch\u00EDnh x\u00E1c.",
          SafariNativeMMLWarning: "Tr\u00ECnh duy\u1EC7t c\u1EE7a b\u1EA1n h\u1ED7 tr\u1EE3 MathML g\u1ED1c nh\u01B0ng kh\u00F4ng h\u1ED7 tr\u1EE3 t\u1EA5t c\u1EA3 nh\u1EEFng t\u00EDnh n\u0103ng m\u00E0 MathJax s\u1EED d\u1EE5ng, n\u00EAn m\u1ED9t s\u1ED1 bi\u1EC3u th\u1EE9c c\u00F3 th\u1EC3 hi\u1EC3n th\u1ECB kh\u00F4ng ch\u00EDnh x\u00E1c.",
          FirefoxNativeMMLWarning: "Tr\u00ECnh duy\u1EC7t c\u1EE7a b\u1EA1n h\u1ED7 tr\u1EE3 MathML g\u1ED1c nh\u01B0ng kh\u00F4ng h\u1ED7 tr\u1EE3 t\u1EA5t c\u1EA3 nh\u1EEFng t\u00EDnh n\u0103ng m\u00E0 MathJax s\u1EED d\u1EE5ng, n\u00EAn m\u1ED9t s\u1ED1 bi\u1EC3u th\u1EE9c c\u00F3 th\u1EC3 hi\u1EC3n th\u1ECB kh\u00F4ng ch\u00EDnh x\u00E1c.",
          MSIESVGWarning: "SVG kh\u00F4ng \u0111\u01B0\u1EE3c h\u1ED7 tr\u1EE3 trong Internet Explorer tr\u01B0\u1EDBc IE9 ho\u1EB7c khi gi\u1EA3 l\u1EADp IE8 tr\u1EDF xu\u1ED1ng. Vi\u1EC7c k\u00EDch ho\u1EA1t ch\u1EBF \u0111\u1ED9 hi\u1EC3n th\u1ECB SVG s\u1EBD l\u00E0m cho c\u00E1c to\u00E1n hi\u1EC3n th\u1ECB kh\u00F4ng ch\u00EDnh x\u00E1c.",
          LoadURL: "T\u1EA3i d\u1EEF li\u1EC7u bi\u00EAn d\u1ECBch t\u1EEB URL n\u00E0y:",
          BadURL: "URL ph\u1EA3i d\u1EABn \u0111\u1EBFn m\u1ED9t t\u1EADp tin JavaScript \u0111\u1ECBnh r\u00F5 d\u1EEF li\u1EC7u bi\u00EAn d\u1ECBch MathJax. C\u00E1c t\u00EAn t\u1EADp tin JavaScript ph\u1EA3i c\u00F3 \u201C.js\u201D \u0111\u1EB1ng sau.",
          BadData: "Th\u1EA5t b\u1EA1i khi t\u1EA3i d\u1EEF li\u1EC7u bi\u00EAn d\u1ECBch t\u1EEB %1",
          SwitchAnyway: "C\u1EE9 k\u00EDch ho\u1EA1t ch\u1EBF \u0111\u1ED9 k\u1EBFt xu\u1EA5t n\u00E0y?\n\n(B\u1EA5m OK \u0111\u1EC3 k\u00EDch ho\u1EA1t, ho\u1EB7c Cancel \u0111\u1EC3 ti\u1EBFp t\u1EE5c s\u1EED d\u1EE5ng ch\u1EBF \u0111\u1ED9 k\u1EBFt xu\u1EA5t hi\u1EC7n t\u1EA1i.)",
          ScaleMath: "Ph\u00F3ng to t\u1EA5t c\u1EA3 to\u00E1n (so v\u1EDBi v\u0103n b\u1EA3n n\u1EB1m chung quanh) b\u1EB1ng t\u1EF7 l\u1EC7:",
          NonZeroScale: "T\u1EF7 l\u1EC7 ph\u1EA3i kh\u00F4ng 0",
          PercentScale: "T\u1EF7 l\u1EC7 ph\u1EA3i l\u00E0 s\u1ED1 ph\u1EA7n tr\u0103m (th\u00ED d\u1EE5 120%%)",
          IE8warning: "Tr\u00ECnh \u0111\u01A1n MathJax v\u00E0 t\u00EDnh n\u0103ng ph\u00F3ng to s\u1EBD kh\u00F4ng c\u00F2n ho\u1EA1t \u0111\u1ED9ng. B\u1EA1n v\u1EABn c\u00F3 th\u1EC3 b\u1EA5m gi\u1EEF Alt v\u00E0 nh\u1EA5n chu\u1ED9t v\u00E0o m\u1ED9t bi\u1EC3u th\u1EE9c \u0111\u1EC3 m\u1EDF tr\u00ECnh \u0111\u01A1n MathJax thay th\u1EBF.\n\nB\u1EA1n c\u00F3 ch\u1EAFc ch\u1EAFn mu\u1ED1n thay \u0111\u1ED5i t\u00F9y ch\u1ECDn MathPlayer?",
          IE9warning: "Tr\u00ECnh \u0111\u01A1n ng\u1EEF c\u1EA3nh MathJax s\u1EBD b\u1ECB v\u00F4 hi\u1EC7u, nh\u01B0ng b\u1EA1n c\u00F3 th\u1EC3 b\u1EA5m gi\u1EEF Alt v\u00E0 nh\u1EA5n chu\u1ED9t v\u00E0o m\u1ED9t bi\u1EC3u th\u1EE9c \u0111\u1EC3 m\u1EDF tr\u00ECnh \u0111\u01A1n MathJax thay th\u1EBF.",
          NoOriginalForm: "H\u00ECnh th\u1EE9c g\u1ED1c kh\u00F4ng c\u00F3 s\u1EB5n",
          Close: "\u0110\u00F3ng",
          EqSource: "M\u00E3 ngu\u1ED3n C\u00F4ng th\u1EE9c MathJax",
          CloseAboutDialog: "\u0110\u00F3ng h\u1ED9p tho\u1EA1i gi\u1EDBi thi\u1EC7u v\u1EC1 MathJax",
          FastPreview: "Xem tr\u01B0\u1EDBc nhanh",
          AssistiveMML: "MathML tr\u1EE3 n\u0103ng",
          InTabOrder: "Bao g\u1ED3m trong th\u1EE9 t\u1EF1 Tab"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/vi/MathMenu.js");
