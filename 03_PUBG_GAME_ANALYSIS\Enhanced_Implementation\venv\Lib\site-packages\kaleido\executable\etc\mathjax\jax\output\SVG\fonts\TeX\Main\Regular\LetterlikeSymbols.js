/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Main/Regular/LetterlikeSymbols.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Main'],
  {
    // stix-/hbar - <PERSON><PERSON>'s over 2pi
    0x210F: [695,13,540,42,562,'182 599Q182 611 174 615T133 619Q118 619 114 621T109 630Q109 636 114 656T122 681Q125 685 202 688Q272 695 286 695Q304 695 304 684Q304 682 295 644T282 597Q282 592 360 592H399Q430 592 445 587T460 563Q460 552 451 541L442 535H266L251 468Q247 453 243 436T236 409T233 399Q233 395 244 404Q295 441 357 441Q405 441 445 417T485 333Q485 284 449 178T412 58T426 44Q447 44 466 68Q485 87 500 130L509 152H531H543Q562 152 562 144Q562 128 546 93T494 23T415 -13Q385 -13 359 3T322 44Q318 52 318 77Q318 99 352 196T386 337Q386 386 346 386Q318 386 286 370Q267 361 245 338T211 292Q207 287 193 235T162 113T138 21Q128 7 122 4Q105 -12 83 -12Q66 -12 54 -2T42 26L166 530Q166 534 161 534T129 535Q127 535 122 535T112 534Q74 534 74 562Q74 570 77 576T84 585T96 589T109 591T124 592T138 592L182 595V599'],

    // BLACK-LETTER CAPITAL I
    0x2111: [705,10,722,55,693,'55 507Q55 589 116 647T260 705Q395 705 526 541Q542 522 549 517T567 512Q595 512 621 521T647 550Q647 553 647 555T650 558T653 560T657 561T661 561T665 561T670 561Q681 561 685 561T691 558T693 548Q693 515 657 495T565 475Q518 475 481 495T418 543T371 599T320 647T259 667Q194 667 148 622T102 508Q102 468 119 436T164 385T220 357T273 347Q282 347 284 344T287 329Q287 317 285 314T272 310Q193 310 124 364T55 507ZM420 312Q420 367 464 399T564 431Q613 431 651 406T693 336Q693 325 689 323T667 320Q654 320 651 322T647 335Q645 360 622 376T566 393H563Q557 393 551 391T537 381T523 356T517 312Q517 287 535 265T574 229T613 190T631 132Q628 74 586 37T487 -9Q478 -10 417 -10H387Q344 -10 310 4T215 69Q130 142 71 146Q59 146 57 149T55 163Q55 177 58 180T75 183H108Q177 183 207 170T306 93Q346 56 368 40T420 13Q474 25 503 60T533 136Q533 160 516 182T477 219T438 257T420 304V312'],

    // SCRIPT SMALL L
    0x2113: [706,20,417,7,397,'345 104T349 104T361 95T369 80T352 59Q268 -20 206 -20Q170 -20 146 3T113 53T99 104L94 129Q94 130 79 116T48 86T28 70Q22 70 15 79T7 94Q7 98 12 103T58 147L91 179V185Q91 186 91 191T92 200Q92 282 128 400T223 612T336 705Q397 705 397 636V627Q397 453 194 233Q185 223 180 218T174 211T171 208T165 201L163 186Q159 142 159 123Q159 17 208 17Q228 17 253 30T293 56T335 94Q345 104 349 104ZM360 634Q360 655 354 661T336 668Q328 668 322 666T302 645T272 592Q252 547 229 467T192 330L179 273Q179 272 186 280T204 300T221 322Q327 453 355 590Q360 612 360 634'],

    // SCRIPT CAPITAL P
    0x2118: [453,216,636,67,625,'300 74Q300 133 338 133Q350 133 356 126T363 109Q363 88 340 76Q340 71 342 62T358 39T393 26Q435 26 474 67T532 182T551 290Q551 325 535 349T484 373Q430 373 378 348T291 289T228 218T187 157T174 130Q254 30 265 10Q276 -15 276 -41Q276 -101 235 -158T142 -216Q112 -216 90 -195T67 -118Q67 -40 104 64L110 81Q81 118 81 174Q81 268 134 360T247 453Q252 453 255 451T258 447L259 445Q259 432 253 420Q251 416 242 416Q209 411 176 341T142 203Q142 193 143 184T146 170T149 165L158 180Q215 280 303 345T485 410Q548 410 586 368T625 255Q625 157 553 74T389 -10H383Q349 -10 325 14Q302 37 300 74ZM105 -123Q105 -134 106 -141T110 -158T122 -173T145 -178Q155 -178 160 -176Q184 -163 199 -132T214 -73Q214 -69 214 -66T213 -59T212 -53T209 -47T205 -41T199 -33T193 -25T184 -14T174 -1L165 10Q156 22 148 32L139 43Q138 43 130 15T113 -54T105 -123'],

    // BLACK-LETTER CAPITAL R
    0x211C: [716,22,722,40,715,'300 614L306 620Q311 626 316 631T332 646T356 663T386 679T425 695T473 707T531 715Q534 715 543 715T557 716Q570 716 572 714Q574 713 644 580T715 444Q715 441 713 439Q712 438 677 419T602 379T549 354L550 348Q550 337 555 238T561 128Q561 122 560 115T559 101Q559 63 591 25L599 18L631 51Q665 85 671 85Q674 85 687 78T702 68Q704 63 702 59Q702 58 659 20T613 -21Q612 -22 598 -22Q556 -22 526 -8T484 27T466 66T461 101Q461 110 462 116T463 129Q463 135 458 232T452 331V338H343V280Q342 195 333 157Q316 64 267 12Q233 -22 193 -22Q155 -22 122 2T72 74Q72 76 70 76T67 74T60 74T48 82Q40 91 40 95Q40 100 42 102T57 109V113Q57 118 66 127T81 137Q88 137 93 123Q105 127 108 126Q111 124 118 117T127 107Q127 101 123 98T113 93T107 90Q107 86 115 71T143 37T189 15H192Q230 15 239 96Q244 135 244 334Q244 510 242 542Q236 584 233 596Q223 630 205 649T166 668Q136 668 118 642T100 584Q100 567 110 554T137 522T166 486Q194 446 194 401V389Q189 243 114 243Q91 243 77 260T59 294T55 322Q55 331 59 333T75 336T91 334T95 322Q95 280 113 280Q134 280 140 305T147 375V391Q147 417 139 435T101 487Q56 540 56 572V580Q56 630 86 667T169 704Q214 704 247 676T300 614ZM324 562Q326 555 330 539T336 515T340 484T343 427V384H424L505 385Q537 396 584 422L609 435Q610 435 594 465T550 550Q536 575 520 605T496 650L488 664L476 662Q348 633 324 562'],

    // ALEF SYMBOL
    0x2135: [694,0,611,55,555,'55 613Q55 643 61 663T74 688T85 694Q94 694 94 681Q98 632 134 588L412 285Q416 311 430 397T447 509V519L438 526Q407 554 398 571T388 617T394 664T407 688T418 694Q425 694 427 684Q429 675 454 635T488 586Q490 584 496 579T513 563T537 540Q555 516 555 487Q555 460 549 441T537 416T528 409Q519 409 517 415T513 435T503 463Q492 481 490 481Q454 264 454 246Q454 237 479 212T529 152T555 79Q555 32 538 9Q531 1 524 1Q516 1 516 13Q512 62 476 106Q468 115 337 258T195 412L193 406Q191 401 189 394T183 377T176 352T171 322T167 284T165 240Q165 224 166 220Q171 199 211 152T252 70Q252 45 235 29T203 8T175 1Q170 0 115 0H79Q60 0 58 3T55 20Q55 31 58 34Q60 37 76 37Q112 39 126 46T140 70Q140 96 112 148T83 236Q83 281 102 334T140 419T159 452Q55 556 55 613']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Regular/LetterlikeSymbols.js");
