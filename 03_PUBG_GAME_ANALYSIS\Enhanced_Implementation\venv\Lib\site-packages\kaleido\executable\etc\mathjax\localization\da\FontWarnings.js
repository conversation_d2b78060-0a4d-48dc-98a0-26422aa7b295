/*************************************************************
 *
 *  MathJax/localization/da/FontWarnings.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("da","FontWarnings",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          webFont: "MathJax bruger web-baserede skrifttyper til at vise det matematiske p\u00E5 denne side.  <PERSON><PERSON> tager tid at hente, s\u00E5 siden ville blive hurtigere, hvis du installerede matematiske skrifttyper direkte i dit systems skrifttypemappe.",
          imageFonts: "MathJax bruger sine billedeskrifttyper snarere end lokale eller web-baserede skrifttyper. Dette vil g\u00F8re visningen langsommere end s\u00E6dvanligt, og matematik kan ikke udskrives med printerens fulde opl\u00F8sning.",
          noFonts: "MathJax er ikke i stand til at finde en skrifttype, som kan bruges til at vise matematikken, og billedeskrifttyper er ikke tilg\u00E6ngelige, s\u00E5 der faldes tilbage p\u00E5 generiske Unicode-tegn i h\u00E5b om, at din browser vil v\u00E6re i stand til at vise dem. Nogle tegn kan m\u00E5ske ikke vises korrekt, eller muligvis slet ikke.",
          webFonts: "De fleste moderne browsere giver mulighed for at hente skrifttyper over nettet. At opdatere til en nyere version af din browser (eller skifte browser) kunne forbedre kvaliteten af matematik p\u00E5 denne side.",
          fonts: "MathJax kan bruge enten [STIX fonts](%1) eller [MathJax TeX fonts](%2). Hent og installer en af disse skrifttyper for at forbedre din MathJax oplevelse.",
          STIXPage: "Denne side er designet til at bruge [STIX fonts](%1). Hent og install\u00E9r disse skrifttyper for at forbedre oplevelsen med MathJax.",
          TeXPage: "Denne side er designet til at bruge [MathJax TeX fonts](%1).  Hent og install\u00E9r disse skrifttyper for at forbedre oplevelsen med MathJax."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/da/FontWarnings.js");
