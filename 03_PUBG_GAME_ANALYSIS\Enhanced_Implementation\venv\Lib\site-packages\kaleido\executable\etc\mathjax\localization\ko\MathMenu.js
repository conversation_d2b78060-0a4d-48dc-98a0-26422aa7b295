/*************************************************************
 *
 *  MathJax/localization/ko/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ko","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "\uC218\uC2DD \uBCF4\uAE30",
          MathMLcode: "MathML \uCF54\uB4DC",
          OriginalMathML: "\uC6D0\uB798 MathML",
          TeXCommands: "TeX \uBA85\uB839",
          AsciiMathInput: "AsciiMathML \uC785\uB825",
          Original: "\uC6D0\uB798 \uC591\uC2DD",
          ErrorMessage: "\uC624\uB958 \uBA54\uC2DC\uC9C0",
          Annotation: "\uC8FC\uC11D",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "Content MathML",
          OpenMath: "OpenMath",
          texHints: "MathML\uC5D0 TeX \uD78C\uD2B8 \uBCF4\uC774\uAE30",
          Settings: "\uC218\uC2DD \uC124\uC815",
          ZoomTrigger: "\uD2B8\uB9AC\uAC70 \uD655\uB300",
          Hover: "\uAC00\uB9AC\uD0A4\uAE30",
          Click: "\uD074\uB9AD",
          DoubleClick: "\uB354\uBE14 \uD074\uB9AD",
          NoZoom: "\uD655\uB300 \uC5C6\uC74C",
          TriggerRequires: "\uD544\uC694\uD55C \uD2B8\uB9AC\uAC70:",
          Option: "\uC120\uD0DD \uC0AC\uD56D",
          Alt: "Alt",
          Command: "\uCEE4\uB9E8\uB4DC",
          Control: "\uCEE8\uD2B8\uB864",
          Shift: "\uC2DC\uD504\uD2B8",
          ZoomFactor: "\uD655\uB300 \uBC30\uC728",
          Renderer: "\uC218\uC2DD \uB80C\uB354\uB7EC",
          MPHandles: "MathPlayer\uC5D0 \uCC98\uB9AC\uD560 \uC774\uBCA4\uD2B8:",
          MenuEvents: "\uBA54\uB274 \uC774\uBCA4\uD2B8",
          MouseEvents: "\uB9C8\uC6B0\uC2A4 \uC774\uBCA4\uD2B8",
          MenuAndMouse: "\uB9C8\uC6B0\uC2A4\uC640 \uBA54\uB274 \uC774\uBCA4\uD2B8",
          FontPrefs: "\uAE00\uAF34 \uD658\uACBD \uC124\uC815",
          ForHTMLCSS: "HTML-CSS:",
          Auto: "\uC790\uB3D9",
          TeXLocal: "TeX (\uB85C\uCEEC)",
          TeXWeb: "TeX (\uC6F9)",
          TeXImage: "TeX (\uADF8\uB9BC)",
          STIXLocal: "STIX (\uB85C\uCEEC)",
          ContextMenu: "\uCEE8\uD14D\uC2A4\uD2B8 \uBA54\uB274",
          Browser: "\uD0D0\uC0C9\uAE30",
          Scale: "\uBAA8\uB4E0 \uC218\uC2DD \uBC30\uC728 ...",
          Discoverable: "\uAC00\uB9AC\uD0A4\uBA74 \uAC15\uC870",
          Locale: "\uC5B8\uC5B4",
          LoadLocale: "URL\uC5D0\uC11C \uC5F4\uAE30 ...",
          About: "MathJax \uC815\uBCF4",
          Help: "MathJax \uB3C4\uC6C0\uB9D0",
          localTeXfonts: "\uB85C\uCEEC TeX \uAE00\uAF34 \uC0AC\uC6A9",
          webTeXfonts: "\uC6F9 TeX \uAE00\uAF34 \uC0AC\uC6A9",
          imagefonts: "\uADF8\uB9BC \uAE00\uAF34 \uC0AC\uC6A9",
          localSTIXfonts: "\uB85C\uCEEC STIX \uAE00\uAF34 \uC0AC\uC6A9",
          webSVGfonts: "\uC6F9 SVG \uAE00\uAF34 \uC0AC\uC6A9",
          genericfonts: "\uC77C\uBC18 \uC720\uB2C8\uCF54\uB4DC \uAE00\uAF34 \uC0AC\uC6A9",
          wofforotffonts: "woff \uB610\uB294 otf \uAE00\uAF34",
          eotffonts: "eot \uAE00\uAF34",
          svgfonts: "svg \uAE00\uAF34",
          WebkitNativeMMLWarning: "\uC0AC\uC6A9\uD558\uB294 \uBE0C\uB77C\uC6B0\uC800\uAC00 \uAE30\uBCF8\uC801\uC73C\uB85C MathML\uC744 \uC9C0\uC6D0\uD558\uC9C0 \uC54A\uB294 \uAC83 \uAC19\uAE30 \uB54C\uBB38\uC5D0 MathML \uCD9C\uB825\uC73C\uB85C \uC804\uD658\uD558\uBA74 \uBB38\uC11C\uC5D0 \uC788\uB294 \uC218\uC2DD\uC744 \uC77D\uC744 \uC218 \uC5C6\uAC8C \uB420 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
          MSIENativeMMLWarning: "Internet Explorer\uB294 MathML \uCD9C\uB825\uC744 \uCC98\uB9AC\uD558\uAE30 \uC704\uD574 MathPlayer \uD50C\uB7EC\uADF8\uC778\uC774 \uD544\uC694\uD569\uB2C8\uB2E4.",
          OperaNativeMMLWarning: "\uC624\uD398\uB77C\uC758 MathML \uC9C0\uC6D0\uC740 \uC81C\uD55C\uC801\uC774\uBBC0\uB85C MathML \uCD9C\uB825\uC73C\uB85C \uC804\uD658\uD558\uBA74 \uC77C\uBD80 \uD45C\uD604\uC774 \uBD88\uC644\uC804\uD558\uAC8C \uB80C\uB354\uB420 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
          SafariNativeMMLWarning: "\uC0AC\uC6A9\uD558\uB294 \uBE0C\uB77C\uC6B0\uC800\uC758 \uAE30\uBCF8 MathML\uC740 MathJax\uC5D0\uC11C \uC0AC\uC6A9\uD558\uB294 \uBAA8\uB4E0 \uAE30\uB2A5\uC744 \uAD6C\uD604\uD558\uC9C0 \uC54A\uAE30 \uB54C\uBB38\uC5D0 \uC77C\uBD80 \uD45C\uD604\uC774 \uC81C\uB300\uB85C \uB80C\uB354\uB418\uC9C0 \uC54A\uC744 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
          FirefoxNativeMMLWarning: "\uC0AC\uC6A9\uD558\uB294 \uBE0C\uB77C\uC6B0\uC800\uC758 \uAE30\uBCF8 MathML\uC740 MathJax\uC5D0\uC11C \uC0AC\uC6A9\uD558\uB294 \uBAA8\uB4E0 \uAE30\uB2A5\uC744 \uAD6C\uD604\uD558\uC9C0 \uC54A\uAE30 \uB54C\uBB38\uC5D0 \uC77C\uBD80 \uD45C\uD604\uC774 \uC81C\uB300\uB85C \uB80C\uB354\uB418\uC9C0 \uC54A\uC744 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
          LoadURL: "\uC774 URL\uC5D0\uC11C \uBC88\uC5ED \uB370\uC774\uD130 \uC5F4\uAE30:",
          BadData: "%1\uC5D0\uC11C \uBC88\uC5ED \uB370\uC774\uD130\uB97C \uC5F4 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4",
          NonZeroScale: "\uBC30\uC728\uC740 0\uC774 \uC544\uB2C8\uC5B4\uC57C \uD569\uB2C8\uB2E4",
          PercentScale: "\uBC30\uC728\uC740 \uBC31\uBD84\uC728\uC774\uC5B4\uC57C \uD569\uB2C8\uB2E4 (\uC608, 120%%)",
          IE9warning: "MathJax \uCEE8\uD14D\uC2A4\uD2B8 \uBA54\uB274\uAC00 \uBE44\uD65C\uC131\uD654\uB418\uC9C0\uB9CC, \uB300\uC2E0 MathJax \uBA54\uB274\uB97C \uC5BB\uC73C\uB824\uBA74 Alt-\uD074\uB9AD\uC744 \uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
          NoOriginalForm: "\uC6D0\uB798 \uC591\uC2DD\uC744 \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4",
          Close: "\uB2EB\uAE30",
          EqSource: "MathJax \uC218\uC2DD \uC790\uB8CC",
          MSIESVGWarning: "SVG\uB294 Internet Explorer 9 \uC774\uD558\uB098 IE8 \uC774\uD558\uB97C \uC5D0\uBBAC\uB808\uC774\uD2B8\uB97C \uD560 \uB54C \uAD6C\uD604\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4. SVG \uCD9C\uB825\uC73C\uB85C \uC804\uD658\uD558\uB294 \uAC83\uC740 \uC218\uC2DD\uC774 \uC798 \uD45C\uC2DC\uB418\uC9C0 \uC54A\uAC8C \uB9CC\uB4E4 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
          STIXWeb: "STIX (\uC6F9)",
          AsanaMathWeb: "Asana Math (\uC6F9)",
          GyrePagellaWeb: "Gyre Pagella (\uC6F9)",
          GyreTermesWeb: "Gyre Termes (\uC6F9)",
          LatinModernWeb: "Latin Modern (\uC6F9)",
          NeoEulerWeb: "Neo Euler (\uC6F9)",
          SwitchAnyway: "\uC5B4\uCA0C\uB4E0 \uB80C\uB354\uB7EC\uB97C \uC804\uD658\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?\n\n(\uC804\uD658\uD558\uB824\uBA74 \uD655\uC778\uC744, \uD604\uC7AC \uB80C\uB354\uB7EC\uB85C \uACC4\uC18D\uD558\uB824\uBA74 \uCDE8\uC18C\uB97C \uB204\uB974\uC138\uC694)",
          CloseAboutDialog: "MathJax \uC815\uBCF4 \uB300\uD654 \uC0C1\uC790 \uB2EB\uAE30",
          FastPreview: "\uACE0\uC18D \uBBF8\uB9AC \uBCF4\uAE30"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ko/MathMenu.js");
