/*************************************************************
 *
 *  MathJax/localization/fr/FontWarnings.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("fr","FontWarnings",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          webFont: "MathJax utilise les polices web pour afficher les expressions math\u00E9matiques sur cette page. Celles-ci mettent du temps \u00E0 \u00EAtre t\u00E9l\u00E9charg\u00E9es et la page serait affich\u00E9e plus rapidement si vous installiez les polices math\u00E9matiques directement dans le dossier des polices de votre syst\u00E8me.",
          imageFonts: "MathJax utilise des images de caract\u00E8res plut\u00F4t que les polices web ou locales. Ceci rend le rendu plus lent que la normale et les expressions math\u00E9matiques peuvent ne pas s'imprimer \u00E0 la r\u00E9solution maximale de votre imprimante",
          noFonts: "MathJax est incapable de localiser une police \u00E0 utiliser pour afficher ses math\u00E9matiques, et les polices image ne sont pas disponibles, donc il doit se rabattre sur les caract\u00E8res unicode en esp\u00E9rant que votre navigateur pourra les afficher. Certains caract\u00E8res peuvent ne pas s\u2019afficher correctement, voire m\u00EAme pas du tout.",
          webFonts: "La plupart des navigateurs modernes permettent de t\u00E9l\u00E9charger des polices depuis le web. Mettre \u00E0 jour votre navigateur (ou changer de navigateur) pourrait am\u00E9liorer la qualit\u00E9 des math\u00E9matiques sur cette page.",
          fonts: "MathJax peut utiliser soit les [polices STIX](%1) soit les [polices TeX MathJax](%2). T\u00E9l\u00E9chargez et installez une de ces polices pour am\u00E9liorer votre exp\u00E9rience avec MathJax.",
          STIXPage: "Cette page est con\u00E7ue pour utiliser les [polices STIX](%1). T\u00E9l\u00E9chargez et installez ces polices pour am\u00E9liorer votre exp\u00E9rience avec MathJax.",
          TeXPage: "Cette page est con\u00E7ue pour utiliser les [polices TeX MathJax](%1). T\u00E9l\u00E9chargez et installez ces polices pour am\u00E9liorer votre exp\u00E9rience avec MathJax."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/fr/FontWarnings.js");
