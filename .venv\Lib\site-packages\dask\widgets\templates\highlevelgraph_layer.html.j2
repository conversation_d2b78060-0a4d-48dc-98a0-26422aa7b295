<div style="">
    <svg width="24" height="24" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" style="position: absolute;">
        {% if materialized %}
        <circle cx="16" cy="16" r="14" fill="#8F8F8F" style="stroke: var(--jp-ui-font-color2, #1D1D1D);" stroke-width="2"/>
        {% else %}
        <circle cx="16" cy="16" r="14" style="stroke: var(--jp-ui-font-color2, #1D1D1D); fill: var(--jp-layout-color1, #F2F2F2);" stroke-width="2" />
        {% endif %}
    </svg>

    <details style="margin-left: 32px;">
        <summary style="margin-bottom: 10px; margin-top: 10px;">
            <h4 style="display: inline;">Layer{{ layer_index }}: {{ shortname }}</h4>
        </summary>
        <p style="color: var(--jp-ui-font-color2, #5D5851); margin: -0.25em 0px 0px 0px;">
            {{ highlevelgraph_key }}
        </p>

        <table>
        <tr>
            <td>
                <table>
                {% for key, val in info.items() %}
                    <tr>
                        <th style="text-align: left; width: 150px;">{{ key }}</th>
                        <td style="text-align: left;">{{ val }}</td>
                    </tr>
                {% endfor %}
                {% for dep in dependencies %}
                    {% if loop.index > 1 %}
                        <tr>
                            <th style="text-align: left; width: 150px;"></th>
                            <td style="text-align: left;">{{ dep }}</td>
                        </tr>
                    {% else %}
                        <tr>
                            <th style="text-align: left; width: 150px;"> depends on </th>
                            <td style="text-align: left;">{{ dep }}</td>
                        </tr>
                    {% endif %}
                {% endfor %}
                </table>
            </td>
            <td>
                {{ svg_repr }}
            </td>
        </tr>
        </table>

    </details>
</div>
