/*************************************************************
 *
 *  MathJax/localization/ar/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ar","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "\u062A\u062D\u0645\u064A\u0644 \u062E\u0637 \u0639\u0644\u0649 \u0634\u0628\u0643\u0629 \u0627\u0644\u0625\u0646\u062A\u0631\u0646\u062A %1",
          CantLoadWebFont: "\u0644\u0627 \u064A\u0645\u0643\u0646 \u062A\u062D\u0645\u064A\u0644 \u062E\u0637 \u0639\u0644\u0649 \u0634\u0628\u0643\u0629 \u0627\u0644\u0625\u0646\u062A\u0631\u0646\u062A %1",
          FirefoxCantLoadWebFont: "\u0641\u0627\u064A\u0631\u0641\u0648\u0643\u0633 \u0644\u0627 \u064A\u0645\u0643\u0646\u0647 \u062A\u062D\u0645\u064A\u0644 \u0627\u0644\u062E\u0637\u0648\u0637 \u0639\u0644\u0649 \u0634\u0628\u0643\u0629 \u0627\u0644\u0625\u0646\u062A\u0631\u0646\u062A \u0645\u0646 \u0645\u0636\u064A\u0641 \u0628\u0639\u064A\u062F",
          CantFindFontUsing: "\u0644\u0627 \u064A\u0645\u0643\u0646 \u0627\u0644\u0639\u062B\u0648\u0631 \u0639\u0644\u0649 \u062E\u0637 \u0635\u0627\u0644\u062D \u0628\u0627\u0633\u062A\u062E\u062F\u0627\u0645 %1",
          WebFontsNotAvailable: "\u062E\u0637\u0648\u0637 \u0627\u0644\u0648\u064A\u0628 \u063A\u064A\u0631 \u0645\u062A\u0648\u0641\u0631\u0629. \u0627\u0633\u062A\u062E\u062F\u0627\u0645 \u062E\u0637\u0648\u0637 \u0627\u0644\u0635\u0648\u0631\u0629 \u0628\u062F\u0644\u0627 \u0645\u0646 \u0630\u0644\u0643"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ar/HTML-CSS.js");
