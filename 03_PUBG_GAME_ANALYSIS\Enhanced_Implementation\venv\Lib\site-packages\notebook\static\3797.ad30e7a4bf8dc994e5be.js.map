{"version": 3, "file": "3797.ad30e7a4bf8dc994e5be.js?v=ad30e7a4bf8dc994e5be", "mappings": ";;;;;;;;;;;;;;;;;;;AAAyC;;AAEzC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC,yCAAyC,OAAO;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,mDAAQ;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,oBAAoB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,6BAA6B;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,mDAAQ;AAC9D;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,QAAQ;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,mCAAmC;AACzC,MAAM,yCAAyC;AAC/C,MAAM,2CAA2C;AACjD,MAAM,uCAAuC;AAC7C,MAAM,yCAAyC;AAC/C,MAAM,mCAAmC;AACzC,MAAM,mCAAmC;AACzC,MAAM,iCAAiC;AACvC,MAAM,6CAA6C;AACnD,MAAM,2CAA2C;AACjD,MAAM,yCAAyC;AAC/C,MAAM,yCAAyC;AAC/C,MAAM,uCAAuC;AAC7C,MAAM,uCAAuC;AAC7C,MAAM,kFAAkF;AACxF,MAAM,mDAAmD;AACzD,MAAM,yEAAyE;AAC/E,MAAM,mFAAmF;AACzF,MAAM,kEAAkE;AACxE,MAAM,mFAAmF;AACzF,MAAM,2CAA2C;AACjD,MAAM,6CAA6C;AACnD,MAAM,6CAA6C;AACnD,MAAM,6CAA6C;AACnD,MAAM,mDAAmD;AACzD,MAAM,2CAA2C;AACjD,MAAM,yCAAyC;AAC/C,MAAM,mCAAmC;AACzC,MAAM,yCAAyC;AAC/C,MAAM;AACN;;AAE8G", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@lezer/highlight/dist/index.js"], "sourcesContent": ["import { NodeProp } from '@lezer/common';\n\nlet nextTagID = 0;\n/**\nHighlighting tags are markers that denote a highlighting category.\nThey are [associated](#highlight.styleTags) with parts of a syntax\ntree by a language mode, and then mapped to an actual CSS style by\na [highlighter](#highlight.Highlighter).\n\nBecause syntax tree node types and highlight styles have to be\nable to talk the same language, CodeMirror uses a mostly _closed_\n[vocabulary](#highlight.tags) of syntax tags (as opposed to\ntraditional open string-based systems, which make it hard for\nhighlighting themes to cover all the tokens produced by the\nvarious languages).\n\nIt _is_ possible to [define](#highlight.Tag^define) your own\nhighlighting tags for system-internal use (where you control both\nthe language package and the highlighter), but such tags will not\nbe picked up by regular highlighters (though you can derive them\nfrom standard tags to allow highlighters to fall back to those).\n*/\nclass Tag {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The set of this tag and all its parent tags, starting with\n    this one itself and sorted in order of decreasing specificity.\n    */\n    set, \n    /**\n    The base unmodified tag that this one is based on, if it's\n    modified @internal\n    */\n    base, \n    /**\n    The modifiers applied to this.base @internal\n    */\n    modified) {\n        this.set = set;\n        this.base = base;\n        this.modified = modified;\n        /**\n        @internal\n        */\n        this.id = nextTagID++;\n    }\n    /**\n    Define a new tag. If `parent` is given, the tag is treated as a\n    sub-tag of that parent, and\n    [highlighters](#highlight.tagHighlighter) that don't mention\n    this tag will try to fall back to the parent tag (or grandparent\n    tag, etc).\n    */\n    static define(parent) {\n        if (parent === null || parent === void 0 ? void 0 : parent.base)\n            throw new Error(\"Can not derive from a modified tag\");\n        let tag = new Tag([], null, []);\n        tag.set.push(tag);\n        if (parent)\n            for (let t of parent.set)\n                tag.set.push(t);\n        return tag;\n    }\n    /**\n    Define a tag _modifier_, which is a function that, given a tag,\n    will return a tag that is a subtag of the original. Applying the\n    same modifier to a twice tag will return the same value (`m1(t1)\n    == m1(t1)`) and applying multiple modifiers will, regardless or\n    order, produce the same tag (`m1(m2(t1)) == m2(m1(t1))`).\n    \n    When multiple modifiers are applied to a given base tag, each\n    smaller set of modifiers is registered as a parent, so that for\n    example `m1(m2(m3(t1)))` is a subtype of `m1(m2(t1))`,\n    `m1(m3(t1)`, and so on.\n    */\n    static defineModifier() {\n        let mod = new Modifier;\n        return (tag) => {\n            if (tag.modified.indexOf(mod) > -1)\n                return tag;\n            return Modifier.get(tag.base || tag, tag.modified.concat(mod).sort((a, b) => a.id - b.id));\n        };\n    }\n}\nlet nextModifierID = 0;\nclass Modifier {\n    constructor() {\n        this.instances = [];\n        this.id = nextModifierID++;\n    }\n    static get(base, mods) {\n        if (!mods.length)\n            return base;\n        let exists = mods[0].instances.find(t => t.base == base && sameArray(mods, t.modified));\n        if (exists)\n            return exists;\n        let set = [], tag = new Tag(set, base, mods);\n        for (let m of mods)\n            m.instances.push(tag);\n        let configs = powerSet(mods);\n        for (let parent of base.set)\n            if (!parent.modified.length)\n                for (let config of configs)\n                    set.push(Modifier.get(parent, config));\n        return tag;\n    }\n}\nfunction sameArray(a, b) {\n    return a.length == b.length && a.every((x, i) => x == b[i]);\n}\nfunction powerSet(array) {\n    let sets = [[]];\n    for (let i = 0; i < array.length; i++) {\n        for (let j = 0, e = sets.length; j < e; j++) {\n            sets.push(sets[j].concat(array[i]));\n        }\n    }\n    return sets.sort((a, b) => b.length - a.length);\n}\n/**\nThis function is used to add a set of tags to a language syntax\nvia [`NodeSet.extend`](#common.NodeSet.extend) or\n[`LRParser.configure`](#lr.LRParser.configure).\n\nThe argument object maps node selectors to [highlighting\ntags](#highlight.Tag) or arrays of tags.\n\nNode selectors may hold one or more (space-separated) node paths.\nSuch a path can be a [node name](#common.NodeType.name), or\nmultiple node names (or `*` wildcards) separated by slash\ncharacters, as in `\"Block/Declaration/VariableName\"`. Such a path\nmatches the final node but only if its direct parent nodes are the\nother nodes mentioned. A `*` in such a path matches any parent,\nbut only a single level—wildcards that match multiple parents\naren't supported, both for efficiency reasons and because Lezer\ntrees make it rather hard to reason about what they would match.)\n\nA path can be ended with `/...` to indicate that the tag assigned\nto the node should also apply to all child nodes, even if they\nmatch their own style (by default, only the innermost style is\nused).\n\nWhen a path ends in `!`, as in `Attribute!`, no further matching\nhappens for the node's child nodes, and the entire node gets the\ngiven style.\n\nIn this notation, node names that contain `/`, `!`, `*`, or `...`\nmust be quoted as JSON strings.\n\nFor example:\n\n```javascript\nparser.withProps(\n  styleTags({\n    // Style Number and BigNumber nodes\n    \"Number BigNumber\": tags.number,\n    // Style Escape nodes whose parent is String\n    \"String/Escape\": tags.escape,\n    // Style anything inside Attributes nodes\n    \"Attributes!\": tags.meta,\n    // Add a style to all content inside Italic nodes\n    \"Italic/...\": tags.emphasis,\n    // Style InvalidString nodes as both `string` and `invalid`\n    \"InvalidString\": [tags.string, tags.invalid],\n    // Style the node named \"/\" as punctuation\n    '\"/\"': tags.punctuation\n  })\n)\n```\n*/\nfunction styleTags(spec) {\n    let byName = Object.create(null);\n    for (let prop in spec) {\n        let tags = spec[prop];\n        if (!Array.isArray(tags))\n            tags = [tags];\n        for (let part of prop.split(\" \"))\n            if (part) {\n                let pieces = [], mode = 2 /* Mode.Normal */, rest = part;\n                for (let pos = 0;;) {\n                    if (rest == \"...\" && pos > 0 && pos + 3 == part.length) {\n                        mode = 1 /* Mode.Inherit */;\n                        break;\n                    }\n                    let m = /^\"(?:[^\"\\\\]|\\\\.)*?\"|[^\\/!]+/.exec(rest);\n                    if (!m)\n                        throw new RangeError(\"Invalid path: \" + part);\n                    pieces.push(m[0] == \"*\" ? \"\" : m[0][0] == '\"' ? JSON.parse(m[0]) : m[0]);\n                    pos += m[0].length;\n                    if (pos == part.length)\n                        break;\n                    let next = part[pos++];\n                    if (pos == part.length && next == \"!\") {\n                        mode = 0 /* Mode.Opaque */;\n                        break;\n                    }\n                    if (next != \"/\")\n                        throw new RangeError(\"Invalid path: \" + part);\n                    rest = part.slice(pos);\n                }\n                let last = pieces.length - 1, inner = pieces[last];\n                if (!inner)\n                    throw new RangeError(\"Invalid path: \" + part);\n                let rule = new Rule(tags, mode, last > 0 ? pieces.slice(0, last) : null);\n                byName[inner] = rule.sort(byName[inner]);\n            }\n    }\n    return ruleNodeProp.add(byName);\n}\nconst ruleNodeProp = new NodeProp();\nclass Rule {\n    constructor(tags, mode, context, next) {\n        this.tags = tags;\n        this.mode = mode;\n        this.context = context;\n        this.next = next;\n    }\n    get opaque() { return this.mode == 0 /* Mode.Opaque */; }\n    get inherit() { return this.mode == 1 /* Mode.Inherit */; }\n    sort(other) {\n        if (!other || other.depth < this.depth) {\n            this.next = other;\n            return this;\n        }\n        other.next = this.sort(other.next);\n        return other;\n    }\n    get depth() { return this.context ? this.context.length : 0; }\n}\nRule.empty = new Rule([], 2 /* Mode.Normal */, null);\n/**\nDefine a [highlighter](#highlight.Highlighter) from an array of\ntag/class pairs. Classes associated with more specific tags will\ntake precedence.\n*/\nfunction tagHighlighter(tags, options) {\n    let map = Object.create(null);\n    for (let style of tags) {\n        if (!Array.isArray(style.tag))\n            map[style.tag.id] = style.class;\n        else\n            for (let tag of style.tag)\n                map[tag.id] = style.class;\n    }\n    let { scope, all = null } = options || {};\n    return {\n        style: (tags) => {\n            let cls = all;\n            for (let tag of tags) {\n                for (let sub of tag.set) {\n                    let tagClass = map[sub.id];\n                    if (tagClass) {\n                        cls = cls ? cls + \" \" + tagClass : tagClass;\n                        break;\n                    }\n                }\n            }\n            return cls;\n        },\n        scope\n    };\n}\nfunction highlightTags(highlighters, tags) {\n    let result = null;\n    for (let highlighter of highlighters) {\n        let value = highlighter.style(tags);\n        if (value)\n            result = result ? result + \" \" + value : value;\n    }\n    return result;\n}\n/**\nHighlight the given [tree](#common.Tree) with the given\n[highlighter](#highlight.Highlighter). Often, the higher-level\n[`highlightCode`](#highlight.highlightCode) function is easier to\nuse.\n*/\nfunction highlightTree(tree, highlighter, \n/**\nAssign styling to a region of the text. Will be called, in order\nof position, for any ranges where more than zero classes apply.\n`classes` is a space separated string of CSS classes.\n*/\nputStyle, \n/**\nThe start of the range to highlight.\n*/\nfrom = 0, \n/**\nThe end of the range.\n*/\nto = tree.length) {\n    let builder = new HighlightBuilder(from, Array.isArray(highlighter) ? highlighter : [highlighter], putStyle);\n    builder.highlightRange(tree.cursor(), from, to, \"\", builder.highlighters);\n    builder.flush(to);\n}\n/**\nHighlight the given tree with the given highlighter, calling\n`putText` for every piece of text, either with a set of classes or\nwith the empty string when unstyled, and `putBreak` for every line\nbreak.\n*/\nfunction highlightCode(code, tree, highlighter, putText, putBreak, from = 0, to = code.length) {\n    let pos = from;\n    function writeTo(p, classes) {\n        if (p <= pos)\n            return;\n        for (let text = code.slice(pos, p), i = 0;;) {\n            let nextBreak = text.indexOf(\"\\n\", i);\n            let upto = nextBreak < 0 ? text.length : nextBreak;\n            if (upto > i)\n                putText(text.slice(i, upto), classes);\n            if (nextBreak < 0)\n                break;\n            putBreak();\n            i = nextBreak + 1;\n        }\n        pos = p;\n    }\n    highlightTree(tree, highlighter, (from, to, classes) => {\n        writeTo(from, \"\");\n        writeTo(to, classes);\n    }, from, to);\n    writeTo(to, \"\");\n}\nclass HighlightBuilder {\n    constructor(at, highlighters, span) {\n        this.at = at;\n        this.highlighters = highlighters;\n        this.span = span;\n        this.class = \"\";\n    }\n    startSpan(at, cls) {\n        if (cls != this.class) {\n            this.flush(at);\n            if (at > this.at)\n                this.at = at;\n            this.class = cls;\n        }\n    }\n    flush(to) {\n        if (to > this.at && this.class)\n            this.span(this.at, to, this.class);\n    }\n    highlightRange(cursor, from, to, inheritedClass, highlighters) {\n        let { type, from: start, to: end } = cursor;\n        if (start >= to || end <= from)\n            return;\n        if (type.isTop)\n            highlighters = this.highlighters.filter(h => !h.scope || h.scope(type));\n        let cls = inheritedClass;\n        let rule = getStyleTags(cursor) || Rule.empty;\n        let tagCls = highlightTags(highlighters, rule.tags);\n        if (tagCls) {\n            if (cls)\n                cls += \" \";\n            cls += tagCls;\n            if (rule.mode == 1 /* Mode.Inherit */)\n                inheritedClass += (inheritedClass ? \" \" : \"\") + tagCls;\n        }\n        this.startSpan(Math.max(from, start), cls);\n        if (rule.opaque)\n            return;\n        let mounted = cursor.tree && cursor.tree.prop(NodeProp.mounted);\n        if (mounted && mounted.overlay) {\n            let inner = cursor.node.enter(mounted.overlay[0].from + start, 1);\n            let innerHighlighters = this.highlighters.filter(h => !h.scope || h.scope(mounted.tree.type));\n            let hasChild = cursor.firstChild();\n            for (let i = 0, pos = start;; i++) {\n                let next = i < mounted.overlay.length ? mounted.overlay[i] : null;\n                let nextPos = next ? next.from + start : end;\n                let rangeFrom = Math.max(from, pos), rangeTo = Math.min(to, nextPos);\n                if (rangeFrom < rangeTo && hasChild) {\n                    while (cursor.from < rangeTo) {\n                        this.highlightRange(cursor, rangeFrom, rangeTo, inheritedClass, highlighters);\n                        this.startSpan(Math.min(rangeTo, cursor.to), cls);\n                        if (cursor.to >= nextPos || !cursor.nextSibling())\n                            break;\n                    }\n                }\n                if (!next || nextPos > to)\n                    break;\n                pos = next.to + start;\n                if (pos > from) {\n                    this.highlightRange(inner.cursor(), Math.max(from, next.from + start), Math.min(to, pos), \"\", innerHighlighters);\n                    this.startSpan(Math.min(to, pos), cls);\n                }\n            }\n            if (hasChild)\n                cursor.parent();\n        }\n        else if (cursor.firstChild()) {\n            if (mounted)\n                inheritedClass = \"\";\n            do {\n                if (cursor.to <= from)\n                    continue;\n                if (cursor.from >= to)\n                    break;\n                this.highlightRange(cursor, from, to, inheritedClass, highlighters);\n                this.startSpan(Math.min(to, cursor.to), cls);\n            } while (cursor.nextSibling());\n            cursor.parent();\n        }\n    }\n}\n/**\nMatch a syntax node's [highlight rules](#highlight.styleTags). If\nthere's a match, return its set of tags, and whether it is\nopaque (uses a `!`) or applies to all child nodes (`/...`).\n*/\nfunction getStyleTags(node) {\n    let rule = node.type.prop(ruleNodeProp);\n    while (rule && rule.context && !node.matchContext(rule.context))\n        rule = rule.next;\n    return rule || null;\n}\nconst t = Tag.define;\nconst comment = t(), name = t(), typeName = t(name), propertyName = t(name), literal = t(), string = t(literal), number = t(literal), content = t(), heading = t(content), keyword = t(), operator = t(), punctuation = t(), bracket = t(punctuation), meta = t();\n/**\nThe default set of highlighting [tags](#highlight.Tag).\n\nThis collection is heavily biased towards programming languages,\nand necessarily incomplete. A full ontology of syntactic\nconstructs would fill a stack of books, and be impractical to\nwrite themes for. So try to make do with this set. If all else\nfails, [open an\nissue](https://github.com/codemirror/codemirror.next) to propose a\nnew tag, or [define](#highlight.Tag^define) a local custom tag for\nyour use case.\n\nNote that it is not obligatory to always attach the most specific\ntag possible to an element—if your grammar can't easily\ndistinguish a certain type of element (such as a local variable),\nit is okay to style it as its more general variant (a variable).\n\nFor tags that extend some parent tag, the documentation links to\nthe parent.\n*/\nconst tags = {\n    /**\n    A comment.\n    */\n    comment,\n    /**\n    A line [comment](#highlight.tags.comment).\n    */\n    lineComment: t(comment),\n    /**\n    A block [comment](#highlight.tags.comment).\n    */\n    blockComment: t(comment),\n    /**\n    A documentation [comment](#highlight.tags.comment).\n    */\n    docComment: t(comment),\n    /**\n    Any kind of identifier.\n    */\n    name,\n    /**\n    The [name](#highlight.tags.name) of a variable.\n    */\n    variableName: t(name),\n    /**\n    A type [name](#highlight.tags.name).\n    */\n    typeName: typeName,\n    /**\n    A tag name (subtag of [`typeName`](#highlight.tags.typeName)).\n    */\n    tagName: t(typeName),\n    /**\n    A property or field [name](#highlight.tags.name).\n    */\n    propertyName: propertyName,\n    /**\n    An attribute name (subtag of [`propertyName`](#highlight.tags.propertyName)).\n    */\n    attributeName: t(propertyName),\n    /**\n    The [name](#highlight.tags.name) of a class.\n    */\n    className: t(name),\n    /**\n    A label [name](#highlight.tags.name).\n    */\n    labelName: t(name),\n    /**\n    A namespace [name](#highlight.tags.name).\n    */\n    namespace: t(name),\n    /**\n    The [name](#highlight.tags.name) of a macro.\n    */\n    macroName: t(name),\n    /**\n    A literal value.\n    */\n    literal,\n    /**\n    A string [literal](#highlight.tags.literal).\n    */\n    string,\n    /**\n    A documentation [string](#highlight.tags.string).\n    */\n    docString: t(string),\n    /**\n    A character literal (subtag of [string](#highlight.tags.string)).\n    */\n    character: t(string),\n    /**\n    An attribute value (subtag of [string](#highlight.tags.string)).\n    */\n    attributeValue: t(string),\n    /**\n    A number [literal](#highlight.tags.literal).\n    */\n    number,\n    /**\n    An integer [number](#highlight.tags.number) literal.\n    */\n    integer: t(number),\n    /**\n    A floating-point [number](#highlight.tags.number) literal.\n    */\n    float: t(number),\n    /**\n    A boolean [literal](#highlight.tags.literal).\n    */\n    bool: t(literal),\n    /**\n    Regular expression [literal](#highlight.tags.literal).\n    */\n    regexp: t(literal),\n    /**\n    An escape [literal](#highlight.tags.literal), for example a\n    backslash escape in a string.\n    */\n    escape: t(literal),\n    /**\n    A color [literal](#highlight.tags.literal).\n    */\n    color: t(literal),\n    /**\n    A URL [literal](#highlight.tags.literal).\n    */\n    url: t(literal),\n    /**\n    A language keyword.\n    */\n    keyword,\n    /**\n    The [keyword](#highlight.tags.keyword) for the self or this\n    object.\n    */\n    self: t(keyword),\n    /**\n    The [keyword](#highlight.tags.keyword) for null.\n    */\n    null: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) denoting some atomic value.\n    */\n    atom: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) that represents a unit.\n    */\n    unit: t(keyword),\n    /**\n    A modifier [keyword](#highlight.tags.keyword).\n    */\n    modifier: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) that acts as an operator.\n    */\n    operatorKeyword: t(keyword),\n    /**\n    A control-flow related [keyword](#highlight.tags.keyword).\n    */\n    controlKeyword: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) that defines something.\n    */\n    definitionKeyword: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) related to defining or\n    interfacing with modules.\n    */\n    moduleKeyword: t(keyword),\n    /**\n    An operator.\n    */\n    operator,\n    /**\n    An [operator](#highlight.tags.operator) that dereferences something.\n    */\n    derefOperator: t(operator),\n    /**\n    Arithmetic-related [operator](#highlight.tags.operator).\n    */\n    arithmeticOperator: t(operator),\n    /**\n    Logical [operator](#highlight.tags.operator).\n    */\n    logicOperator: t(operator),\n    /**\n    Bit [operator](#highlight.tags.operator).\n    */\n    bitwiseOperator: t(operator),\n    /**\n    Comparison [operator](#highlight.tags.operator).\n    */\n    compareOperator: t(operator),\n    /**\n    [Operator](#highlight.tags.operator) that updates its operand.\n    */\n    updateOperator: t(operator),\n    /**\n    [Operator](#highlight.tags.operator) that defines something.\n    */\n    definitionOperator: t(operator),\n    /**\n    Type-related [operator](#highlight.tags.operator).\n    */\n    typeOperator: t(operator),\n    /**\n    Control-flow [operator](#highlight.tags.operator).\n    */\n    controlOperator: t(operator),\n    /**\n    Program or markup punctuation.\n    */\n    punctuation,\n    /**\n    [Punctuation](#highlight.tags.punctuation) that separates\n    things.\n    */\n    separator: t(punctuation),\n    /**\n    Bracket-style [punctuation](#highlight.tags.punctuation).\n    */\n    bracket,\n    /**\n    Angle [brackets](#highlight.tags.bracket) (usually `<` and `>`\n    tokens).\n    */\n    angleBracket: t(bracket),\n    /**\n    Square [brackets](#highlight.tags.bracket) (usually `[` and `]`\n    tokens).\n    */\n    squareBracket: t(bracket),\n    /**\n    Parentheses (usually `(` and `)` tokens). Subtag of\n    [bracket](#highlight.tags.bracket).\n    */\n    paren: t(bracket),\n    /**\n    Braces (usually `{` and `}` tokens). Subtag of\n    [bracket](#highlight.tags.bracket).\n    */\n    brace: t(bracket),\n    /**\n    Content, for example plain text in XML or markup documents.\n    */\n    content,\n    /**\n    [Content](#highlight.tags.content) that represents a heading.\n    */\n    heading,\n    /**\n    A level 1 [heading](#highlight.tags.heading).\n    */\n    heading1: t(heading),\n    /**\n    A level 2 [heading](#highlight.tags.heading).\n    */\n    heading2: t(heading),\n    /**\n    A level 3 [heading](#highlight.tags.heading).\n    */\n    heading3: t(heading),\n    /**\n    A level 4 [heading](#highlight.tags.heading).\n    */\n    heading4: t(heading),\n    /**\n    A level 5 [heading](#highlight.tags.heading).\n    */\n    heading5: t(heading),\n    /**\n    A level 6 [heading](#highlight.tags.heading).\n    */\n    heading6: t(heading),\n    /**\n    A prose separator (such as a horizontal rule).\n    */\n    contentSeparator: t(content),\n    /**\n    [Content](#highlight.tags.content) that represents a list.\n    */\n    list: t(content),\n    /**\n    [Content](#highlight.tags.content) that represents a quote.\n    */\n    quote: t(content),\n    /**\n    [Content](#highlight.tags.content) that is emphasized.\n    */\n    emphasis: t(content),\n    /**\n    [Content](#highlight.tags.content) that is styled strong.\n    */\n    strong: t(content),\n    /**\n    [Content](#highlight.tags.content) that is part of a link.\n    */\n    link: t(content),\n    /**\n    [Content](#highlight.tags.content) that is styled as code or\n    monospace.\n    */\n    monospace: t(content),\n    /**\n    [Content](#highlight.tags.content) that has a strike-through\n    style.\n    */\n    strikethrough: t(content),\n    /**\n    Inserted text in a change-tracking format.\n    */\n    inserted: t(),\n    /**\n    Deleted text.\n    */\n    deleted: t(),\n    /**\n    Changed text.\n    */\n    changed: t(),\n    /**\n    An invalid or unsyntactic element.\n    */\n    invalid: t(),\n    /**\n    Metadata or meta-instruction.\n    */\n    meta,\n    /**\n    [Metadata](#highlight.tags.meta) that applies to the entire\n    document.\n    */\n    documentMeta: t(meta),\n    /**\n    [Metadata](#highlight.tags.meta) that annotates or adds\n    attributes to a given syntactic element.\n    */\n    annotation: t(meta),\n    /**\n    Processing instruction or preprocessor directive. Subtag of\n    [meta](#highlight.tags.meta).\n    */\n    processingInstruction: t(meta),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that indicates that a\n    given element is being defined. Expected to be used with the\n    various [name](#highlight.tags.name) tags.\n    */\n    definition: Tag.defineModifier(),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that indicates that\n    something is constant. Mostly expected to be used with\n    [variable names](#highlight.tags.variableName).\n    */\n    constant: Tag.defineModifier(),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) used to indicate that\n    a [variable](#highlight.tags.variableName) or [property\n    name](#highlight.tags.propertyName) is being called or defined\n    as a function.\n    */\n    function: Tag.defineModifier(),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that can be applied to\n    [names](#highlight.tags.name) to indicate that they belong to\n    the language's standard environment.\n    */\n    standard: Tag.defineModifier(),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that indicates a given\n    [names](#highlight.tags.name) is local to some scope.\n    */\n    local: Tag.defineModifier(),\n    /**\n    A generic variant [modifier](#highlight.Tag^defineModifier) that\n    can be used to tag language-specific alternative variants of\n    some common tag. It is recommended for themes to define special\n    forms of at least the [string](#highlight.tags.string) and\n    [variable name](#highlight.tags.variableName) tags, since those\n    come up a lot.\n    */\n    special: Tag.defineModifier()\n};\n/**\nThis is a highlighter that adds stable, predictable classes to\ntokens, for styling with external CSS.\n\nThe following tags are mapped to their name prefixed with `\"tok-\"`\n(for example `\"tok-comment\"`):\n\n* [`link`](#highlight.tags.link)\n* [`heading`](#highlight.tags.heading)\n* [`emphasis`](#highlight.tags.emphasis)\n* [`strong`](#highlight.tags.strong)\n* [`keyword`](#highlight.tags.keyword)\n* [`atom`](#highlight.tags.atom)\n* [`bool`](#highlight.tags.bool)\n* [`url`](#highlight.tags.url)\n* [`labelName`](#highlight.tags.labelName)\n* [`inserted`](#highlight.tags.inserted)\n* [`deleted`](#highlight.tags.deleted)\n* [`literal`](#highlight.tags.literal)\n* [`string`](#highlight.tags.string)\n* [`number`](#highlight.tags.number)\n* [`variableName`](#highlight.tags.variableName)\n* [`typeName`](#highlight.tags.typeName)\n* [`namespace`](#highlight.tags.namespace)\n* [`className`](#highlight.tags.className)\n* [`macroName`](#highlight.tags.macroName)\n* [`propertyName`](#highlight.tags.propertyName)\n* [`operator`](#highlight.tags.operator)\n* [`comment`](#highlight.tags.comment)\n* [`meta`](#highlight.tags.meta)\n* [`punctuation`](#highlight.tags.punctuation)\n* [`invalid`](#highlight.tags.invalid)\n\nIn addition, these mappings are provided:\n\n* [`regexp`](#highlight.tags.regexp),\n  [`escape`](#highlight.tags.escape), and\n  [`special`](#highlight.tags.special)[`(string)`](#highlight.tags.string)\n  are mapped to `\"tok-string2\"`\n* [`special`](#highlight.tags.special)[`(variableName)`](#highlight.tags.variableName)\n  to `\"tok-variableName2\"`\n* [`local`](#highlight.tags.local)[`(variableName)`](#highlight.tags.variableName)\n  to `\"tok-variableName tok-local\"`\n* [`definition`](#highlight.tags.definition)[`(variableName)`](#highlight.tags.variableName)\n  to `\"tok-variableName tok-definition\"`\n* [`definition`](#highlight.tags.definition)[`(propertyName)`](#highlight.tags.propertyName)\n  to `\"tok-propertyName tok-definition\"`\n*/\nconst classHighlighter = tagHighlighter([\n    { tag: tags.link, class: \"tok-link\" },\n    { tag: tags.heading, class: \"tok-heading\" },\n    { tag: tags.emphasis, class: \"tok-emphasis\" },\n    { tag: tags.strong, class: \"tok-strong\" },\n    { tag: tags.keyword, class: \"tok-keyword\" },\n    { tag: tags.atom, class: \"tok-atom\" },\n    { tag: tags.bool, class: \"tok-bool\" },\n    { tag: tags.url, class: \"tok-url\" },\n    { tag: tags.labelName, class: \"tok-labelName\" },\n    { tag: tags.inserted, class: \"tok-inserted\" },\n    { tag: tags.deleted, class: \"tok-deleted\" },\n    { tag: tags.literal, class: \"tok-literal\" },\n    { tag: tags.string, class: \"tok-string\" },\n    { tag: tags.number, class: \"tok-number\" },\n    { tag: [tags.regexp, tags.escape, tags.special(tags.string)], class: \"tok-string2\" },\n    { tag: tags.variableName, class: \"tok-variableName\" },\n    { tag: tags.local(tags.variableName), class: \"tok-variableName tok-local\" },\n    { tag: tags.definition(tags.variableName), class: \"tok-variableName tok-definition\" },\n    { tag: tags.special(tags.variableName), class: \"tok-variableName2\" },\n    { tag: tags.definition(tags.propertyName), class: \"tok-propertyName tok-definition\" },\n    { tag: tags.typeName, class: \"tok-typeName\" },\n    { tag: tags.namespace, class: \"tok-namespace\" },\n    { tag: tags.className, class: \"tok-className\" },\n    { tag: tags.macroName, class: \"tok-macroName\" },\n    { tag: tags.propertyName, class: \"tok-propertyName\" },\n    { tag: tags.operator, class: \"tok-operator\" },\n    { tag: tags.comment, class: \"tok-comment\" },\n    { tag: tags.meta, class: \"tok-meta\" },\n    { tag: tags.invalid, class: \"tok-invalid\" },\n    { tag: tags.punctuation, class: \"tok-punctuation\" }\n]);\n\nexport { Tag, classHighlighter, getStyleTags, highlightCode, highlightTree, styleTags, tagHighlighter, tags };\n"], "names": [], "sourceRoot": ""}