{"version": 3, "file": "9380.127327c2f419baab61f8.js?v=127327c2f419baab61f8", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAC+D;AACd;AACK;AACQ;AAC/B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,gCAAgC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gEAAW;AAC1B,eAAe,2DAAS,EAAE,iEAAe;AACzC;AACA;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA,+BAA+B,gDAAmB,CAAC,2CAAc;AACjE,oBAAoB,gDAAmB,WAAW,sCAAsC;AACxF,wBAAwB,gDAAmB,CAAC,wEAAW,UAAU,gCAAgC;AACjG;AACA;AACA;AACA;AACA,uCAAuC,gDAAmB;AAC1D,oBAAoB,gDAAmB,QAAQ,mIAAmI;AAClL,oBAAoB,gDAAmB,QAAQ,sIAAsI;AACrL;AACA;AACA,8BAA8B,gDAAmB,CAAC,2CAAc;AAChE,oBAAoB,gDAAmB,WAAW,uCAAuC;AACzF,oBAAoB,gDAAmB;AACvC,oBAAoB,gDAAmB,WAAW,+CAA+C;AACjG,mCAAmC,wDAAM;AACzC;AACA;AACA;AACA,wBAAwB,wDAAM;AAC9B;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA,8BAA8B,qCAAqC;AACnE;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,iEAAe,OAAO,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/help-extension/lib/index.js"], "sourcesContent": ["// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { Dialog, ICommandPalette } from '@jupyterlab/apputils';\nimport { IMainMenu } from '@jupyterlab/mainmenu';\nimport { ITranslator } from '@jupyterlab/translation';\nimport { jupyterIcon } from '@jupyter-notebook/ui-components';\nimport * as React from 'react';\n/**\n * A list of resources to show in the help menu.\n */\nconst RESOURCES = [\n    {\n        text: 'About Jupyter',\n        url: 'https://jupyter.org',\n    },\n    {\n        text: 'Markdown Reference',\n        url: 'https://commonmark.org/help/',\n    },\n    {\n        text: 'Documentation',\n        url: 'https://jupyter-notebook.readthedocs.io/en/stable/',\n    },\n];\n/**\n * The command IDs used by the help plugin.\n */\nvar CommandIDs;\n(function (CommandIDs) {\n    CommandIDs.open = 'help:open';\n    CommandIDs.about = 'help:about';\n})(CommandIDs || (CommandIDs = {}));\n/**\n * A plugin to open the about section with resources.\n */\nconst open = {\n    id: '@jupyter-notebook/help-extension:open',\n    autoStart: true,\n    description: 'A plugin to open the about section with resources',\n    activate: (app) => {\n        const { commands } = app;\n        commands.addCommand(CommandIDs.open, {\n            label: (args) => args['text'],\n            execute: (args) => {\n                const url = args['url'];\n                window.open(url);\n            },\n        });\n    },\n};\n/**\n * Plugin to add a command to show an About Jupyter Notebook and Markdown Reference.\n */\nconst about = {\n    id: '@jupyter-notebook/help-extension:about',\n    autoStart: true,\n    requires: [ITranslator],\n    optional: [IMainMenu, ICommandPalette],\n    description: 'Plugin to add a command to show an About Jupyter Notebook and Markdown Reference',\n    activate: (app, translator, menu, palette) => {\n        const { commands } = app;\n        const trans = translator.load('notebook');\n        const category = trans.__('Help');\n        commands.addCommand(CommandIDs.about, {\n            label: trans.__('About %1', app.name),\n            execute: () => {\n                const title = (React.createElement(React.Fragment, null,\n                    React.createElement(\"span\", { className: \"jp-AboutNotebook-header\" },\n                        React.createElement(jupyterIcon.react, { width: \"196px\", height: \"auto\" }))));\n                const notebookURL = 'https://github.com/jupyter/notebook';\n                const contributorURL = 'https://github.com/jupyter/notebook/pulse';\n                const aboutJupyter = trans.__('JUPYTER NOTEBOOK ON GITHUB');\n                const contributorList = trans.__('CONTRIBUTOR LIST');\n                const externalLinks = (React.createElement(\"span\", null,\n                    React.createElement(\"a\", { href: notebookURL, target: \"_blank\", rel: \"noopener noreferrer\", className: \"jp-Button-flat jp-AboutNotebook-about-externalLinks\" }, aboutJupyter),\n                    React.createElement(\"a\", { href: contributorURL, target: \"_blank\", rel: \"noopener noreferrer\", className: \"jp-Button-flat jp-AboutNotebook-about-externalLinks\" }, contributorList)));\n                const version = trans.__('Version: %1', app.version);\n                const copyright = trans.__('© 2021-2023 Jupyter Notebook Contributors');\n                const body = (React.createElement(React.Fragment, null,\n                    React.createElement(\"span\", { className: \"jp-AboutNotebook-version\" }, version),\n                    React.createElement(\"div\", null, externalLinks),\n                    React.createElement(\"span\", { className: \"jp-AboutNotebook-about-copyright\" }, copyright)));\n                const dialog = new Dialog({\n                    title,\n                    body,\n                    buttons: [\n                        Dialog.createButton({\n                            label: trans.__('Dismiss'),\n                            className: 'jp-AboutNotebook-about-button jp-mod-reject jp-mod-styled',\n                        }),\n                    ],\n                });\n                dialog.addClass('jp-AboutNotebook');\n                void dialog.launch();\n            },\n        });\n        if (palette) {\n            palette.addItem({ command: CommandIDs.about, category });\n        }\n        const resourcesGroup = RESOURCES.map((args) => ({\n            args,\n            command: CommandIDs.open,\n        }));\n        if (menu) {\n            menu.helpMenu.addGroup(resourcesGroup, 30);\n        }\n    },\n};\nconst plugins = [open, about];\nexport default plugins;\n"], "names": [], "sourceRoot": ""}