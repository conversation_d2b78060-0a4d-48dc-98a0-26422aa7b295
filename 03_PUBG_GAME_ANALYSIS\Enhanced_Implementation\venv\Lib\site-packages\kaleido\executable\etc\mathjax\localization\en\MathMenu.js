/*************************************************************
 *
 *  MathJax/localization/en/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("en","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "Show math as",
          MathMLcode: "MathML code",
          OriginalMathML: "Original MathML",
          TeXCommands: "TeX commands",
          AsciiMathInput: "AsciiMathML input",
          Original: "Original form",
          ErrorMessage: "Error message",
          Annotation: "Annotation",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "Content MathML",
          OpenMath: "OpenMath",
          texHints: "Show TeX hints in MathML",
          Settings: "Math settings",
          ZoomTrigger: "Zoom trigger",
          Hover: "Hover",
          Click: "Click",
          DoubleClick: "Double-click",
          NoZoom: "No zoom",
          TriggerRequires: "Trigger requires:",
          Option: "Option",
          Alt: "Alt",
          Command: "Command",
          Control: "Control",
          Shift: "Shift",
          ZoomFactor: "Zoom factor",
          Renderer: "Math renderer",
          MPHandles: "Let MathPlayer handle:",
          MenuEvents: "Menu events",
          MouseEvents: "Mouse events",
          MenuAndMouse: "Mouse and menu events",
          FontPrefs: "Font preferences",
          ForHTMLCSS: "For HTML-CSS:",
          Auto: "Auto",
          TeXLocal: "TeX (local)",
          TeXWeb: "TeX (web)",
          TeXImage: "TeX (image)",
          STIXLocal: "STIX (local)",
          STIXWeb: "STIX (web)",
          AsanaMathWeb: "Asana Math (web)",
          GyrePagellaWeb: "Gyre Pagella (web)",
          GyreTermesWeb: "Gyre Termes (web)",
          LatinModernWeb: "Latin Modern (web)",
          NeoEulerWeb: "Neo Euler (web)",
          ContextMenu: "Contextual menu",
          Browser: "Browser",
          Scale: "Scale all math ...",
          Discoverable: "Highlight on hover",
          Locale: "Language",
          LoadLocale: "Load from URL ...",
          About: "About MathJax",
          Help: "MathJax help",
          localTeXfonts: "using local TeX fonts",
          webTeXfonts: "using web TeX font",
          imagefonts: "using Image fonts",
          localSTIXfonts: "using local STIX fonts",
          webSVGfonts: "using web SVG fonts",
          genericfonts: "using generic Unicode fonts",
          wofforotffonts: "WOFF or OTF fonts",
          eotffonts: "EOT fonts",
          svgfonts: "SVG fonts",
          WebkitNativeMMLWarning: "Your browser does not seem to support MathML natively, so switching to MathML output may cause the mathematics on the page to become unreadable",
          MSIENativeMMLWarning: "Internet Explorer requires the MathPlayer plugin in order to process MathML output.",
          OperaNativeMMLWarning: "Opera's support for MathML is limited, so switching to MathML output may cause some expressions to render poorly.",
          SafariNativeMMLWarning: "Your browser's native MathML does not implement all the features used by MathJax, so some expressions may not render properly.",
          FirefoxNativeMMLWarning: "Your browser's native MathML does not implement all the features used by MathJax, so some expressions may not render properly.",
          MSIESVGWarning: "SVG is not implemented in Internet Explorer prior to IE9 or when it is emulating IE8 or below. Switching to SVG output will cause the mathematics to not display properly.",
          LoadURL: "Load translation data from this URL:",
          BadURL: "The URL should be for a JavaScript file that defines MathJax translation data. JavaScript file names should end with '.js'",
          BadData: "Failed to load translation data from %1",
          SwitchAnyway: "Switch the renderer anyway?\n\n(Press OK to switch, CANCEL to continue with the current renderer)",
          ScaleMath: "Scale all mathematics (compared to surrounding text) by",
          NonZeroScale: "The scale should not be zero",
          PercentScale: "The scale should be a percentage (for example 120%%)",
          IE8warning: "This will disable the MathJax menu and zoom features, but you can Alt-Click on an expression to obtain the MathJax menu instead.\n\nReally change the MathPlayer settings?",
          IE9warning: "The MathJax contextual menu will be disabled, but you can Alt-Click on an expression to obtain the MathJax menu instead.",
          NoOriginalForm: "No original form available",
          Close: "Close",
          EqSource: "MathJax Equation Source",
          CloseAboutDialog: "Close about MathJax dialog",
          FastPreview: "Fast Preview",
          AssistiveMML: "Assistive MathML",
          InTabOrder: "Include in Tab Order"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/en/MathMenu.js");
