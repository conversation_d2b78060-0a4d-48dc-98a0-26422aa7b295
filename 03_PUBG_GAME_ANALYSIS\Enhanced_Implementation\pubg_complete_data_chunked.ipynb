{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PUBG Complete Data Analysis - Chunked Processing\n", "\n", "## 🎯 Innovation: Full Dataset Analysis Without Memory Issues\n", "\n", "### 🚀 **Revolutionary Approach:**\n", "- **Split large CSV into manageable chunks**\n", "- **Process each chunk separately**\n", "- **Combine results for complete analysis**\n", "- **All visualizations display inline**\n", "- **NO external HTML files**\n", "- **Get insights from 100% of your data**\n", "\n", "### 📊 **Strategy:**\n", "1. **Intelligent Chunking**: Split 629MB file into 2-4 chunks\n", "2. **Parallel Processing**: Process each chunk independently\n", "3. **Smart Aggregation**: Combine statistics and samples\n", "4. **Memory Management**: Keep only essential data in memory\n", "5. **Complete Visualization**: Show insights from entire dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ESSENTIAL IMPORTS AND SETUP\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "import gc  # Garbage collection for memory management\n", "from typing import Dict, List, Tuple\n", "warnings.filterwarnings('ignore')\n", "\n", "# FORCE MATPLOTLIB INLINE\n", "%matplotlib inline\n", "plt.style.use('default')\n", "\n", "print(\"🚀 PUBG COMPLETE DATA ANALYSIS - CHUNKED PROCESSING\")\n", "print(\"=\" * 60)\n", "print(\"✅ Imports completed\")\n", "print(f\"✅ Matplotlib backend: {plt.get_backend()}\")\n", "print(\"🎯 Ready for FULL dataset analysis with chunked processing!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# INNOVATIVE CHUNKED DATA LOADER\n", "class PUBGChunkedAnalyzer:\n", "    def __init__(self, file_path: str, chunk_size: int = 100000):\n", "        self.file_path = file_path\n", "        self.chunk_size = chunk_size\n", "        self.total_rows = 0\n", "        self.column_stats = {}\n", "        self.combined_sample = None\n", "        \n", "    def get_file_info(self):\n", "        \"\"\"Get basic file information\"\"\"\n", "        file_size_mb = Path(self.file_path).stat().st_size / (1024 * 1024)\n", "        print(f\"📁 File: {Path(self.file_path).name}\")\n", "        print(f\"📊 Size: {file_size_mb:.1f} MB\")\n", "        \n", "        # Estimate rows (rough calculation)\n", "        estimated_rows = int(file_size_mb * 7000)  # Rough estimate\n", "        recommended_chunks = max(2, int(file_size_mb / 150))  # 150MB per chunk\n", "        \n", "        print(f\"📈 Estimated rows: ~{estimated_rows:,}\")\n", "        print(f\"🔧 Recommended chunks: {recommended_chunks}\")\n", "        \n", "        return file_size_mb, estimated_rows, recommended_chunks\n", "    \n", "    def process_chunks(self, sample_per_chunk: int = 5000):\n", "        \"\"\"Process CSV file in chunks and collect statistics\"\"\"\n", "        print(f\"\\n🔄 PROCESSING CHUNKS...\")\n", "        print(f\"📊 Chunk size: {self.chunk_size:,} rows\")\n", "        print(f\"🎯 Sample per chunk: {sample_per_chunk:,} rows\")\n", "        print(\"=\" * 50)\n", "        \n", "        chunk_samples = []\n", "        chunk_stats = []\n", "        chunk_number = 0\n", "        \n", "        try:\n", "            # Read CSV in chunks\n", "            for chunk in pd.read_csv(self.file_path, chunksize=self.chunk_size):\n", "                chunk_number += 1\n", "                chunk_rows = len(chunk)\n", "                self.total_rows += chunk_rows\n", "                \n", "                print(f\"📦 Processing Chunk {chunk_number}: {chunk_rows:,} rows\")\n", "                \n", "                # Calculate statistics for this chunk\n", "                chunk_stat = self._calculate_chunk_stats(chunk)\n", "                chunk_stats.append(chunk_stat)\n", "                \n", "                # Get representative sample from this chunk\n", "                if len(chunk) >= sample_per_chunk:\n", "                    chunk_sample = chunk.sample(n=sample_per_chunk, random_state=42)\n", "                else:\n", "                    chunk_sample = chunk.copy()\n", "                \n", "                chunk_samples.append(chunk_sample)\n", "                \n", "                # Memory cleanup\n", "                del chunk\n", "                gc.collect()\n", "                \n", "                print(f\"   ✅ Chunk {chunk_number} processed and sampled\")\n", "        \n", "        except Exception as e:\n", "            print(f\"❌ Error processing chunks: {e}\")\n", "            return None\n", "        \n", "        # Combine all samples\n", "        print(f\"\\n🔗 COMBINING CHUNK SAMPLES...\")\n", "        self.combined_sample = pd.concat(chunk_samples, ignore_index=True)\n", "        \n", "        # Combine statistics\n", "        self.column_stats = self._combine_chunk_stats(chunk_stats)\n", "        \n", "        print(f\"✅ Processing complete!\")\n", "        print(f\"📊 Total rows processed: {self.total_rows:,}\")\n", "        print(f\"🎯 Combined sample size: {len(self.combined_sample):,}\")\n", "        print(f\"💾 Sample memory usage: {self.combined_sample.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "        \n", "        return self.combined_sample\n", "    \n", "    def _calculate_chunk_stats(self, chunk: pd.DataFrame) -> Dict:\n", "        \"\"\"Calculate statistics for a single chunk\"\"\"\n", "        stats = {\n", "            'count': len(chunk),\n", "            'columns': {}\n", "        }\n", "        \n", "        # Calculate stats for numerical columns\n", "        numerical_cols = chunk.select_dtypes(include=[np.number]).columns\n", "        for col in numerical_cols:\n", "            stats['columns'][col] = {\n", "                'count': chunk[col].count(),\n", "                'sum': chunk[col].sum(),\n", "                'sum_sq': (chunk[col] ** 2).sum(),\n", "                'min': chunk[col].min(),\n", "                'max': chunk[col].max(),\n", "                'mean': chunk[col].mean(),\n", "                'std': chunk[col].std()\n", "            }\n", "        \n", "        return stats\n", "    \n", "    def _combine_chunk_stats(self, chunk_stats: List[Dict]) -> Dict:\n", "        \"\"\"Combine statistics from all chunks\"\"\"\n", "        combined = {'total_count': self.total_rows, 'columns': {}}\n", "        \n", "        # Get all column names\n", "        all_columns = set()\n", "        for stats in chunk_stats:\n", "            all_columns.update(stats['columns'].keys())\n", "        \n", "        # Combine statistics for each column\n", "        for col in all_columns:\n", "            total_count = sum(stats['columns'].get(col, {}).get('count', 0) for stats in chunk_stats)\n", "            total_sum = sum(stats['columns'].get(col, {}).get('sum', 0) for stats in chunk_stats)\n", "            total_sum_sq = sum(stats['columns'].get(col, {}).get('sum_sq', 0) for stats in chunk_stats)\n", "            \n", "            if total_count > 0:\n", "                overall_mean = total_sum / total_count\n", "                overall_var = (total_sum_sq / total_count) - (overall_mean ** 2)\n", "                overall_std = np.sqrt(max(0, overall_var))\n", "                \n", "                combined['columns'][col] = {\n", "                    'count': total_count,\n", "                    'mean': overall_mean,\n", "                    'std': overall_std,\n", "                    'min': min(stats['columns'].get(col, {}).get('min', float('inf')) for stats in chunk_stats if col in stats['columns']),\n", "                    'max': max(stats['columns'].get(col, {}).get('max', float('-inf')) for stats in chunk_stats if col in stats['columns'])\n", "                }\n", "        \n", "        return combined\n", "    \n", "    def get_complete_stats(self) -> Dict:\n", "        \"\"\"Get statistics representing the complete dataset\"\"\"\n", "        return self.column_stats\n", "    \n", "    def get_representative_sample(self, size: int = 20000) -> pd.DataFrame:\n", "        \"\"\"Get a representative sample from the complete dataset\"\"\"\n", "        if self.combined_sample is not None and len(self.combined_sample) > size:\n", "            return self.combined_sample.sample(n=size, random_state=42)\n", "        return self.combined_sample\n", "\n", "print(\"✅ PUBGChunkedAnalyzer class created\")\n", "print(\"🎯 Ready to process large PUBG dataset in chunks!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# LOAD AND PROCESS COMPLETE PUBG DATASET\n", "print(\"🚀 LOADING COMPLETE PUBG DATASET WITH CHUNKED PROCESSING\")\n", "print(\"=\" * 60)\n", "\n", "# Find PUBG CSV file\n", "possible_paths = [\n", "    '../data/pubg.csv',\n", "    '../../data/pubg.csv', \n", "    'data/pubg.csv',\n", "    'pubg.csv'\n", "]\n", "\n", "pubg_file_path = None\n", "for path in possible_paths:\n", "    if Path(path).exists():\n", "        pubg_file_path = path\n", "        print(f\"📁 Found PUBG data at: {path}\")\n", "        break\n", "\n", "if pubg_file_path:\n", "    # Initialize chunked analyzer\n", "    analyzer = PUBGChunkedAnalyzer(pubg_file_path, chunk_size=150000)  # 150K rows per chunk\n", "    \n", "    # Get file information\n", "    file_size, estimated_rows, recommended_chunks = analyzer.get_file_info()\n", "    \n", "    # Process the complete dataset in chunks\n", "    print(f\"\\n🔄 Starting chunked processing...\")\n", "    pubg_sample = analyzer.process_chunks(sample_per_chunk=7500)  # 7.5K sample per chunk\n", "    \n", "    if pubg_sample is not None:\n", "        # Get complete dataset statistics\n", "        complete_stats = analyzer.get_complete_stats()\n", "        \n", "        print(f\"\\n🎉 SUCCESS! COMPLETE DATASET PROCESSED\")\n", "        print(f\"📊 Total rows in dataset: {analyzer.total_rows:,}\")\n", "        print(f\"🎯 Representative sample: {len(pubg_sample):,} rows\")\n", "        print(f\"📋 Columns available: {list(pubg_sample.columns)}\")\n", "        \n", "        # Show key statistics from COMPLETE dataset\n", "        print(f\"\\n📈 COMPLETE DATASET STATISTICS:\")\n", "        key_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc']\n", "        for col in key_cols:\n", "            if col in complete_stats['columns']:\n", "                stats = complete_stats['columns'][col]\n", "                print(f\"   {col}: Mean={stats['mean']:.3f}, Std={stats['std']:.3f}, Min={stats['min']:.3f}, Max={stats['max']:.3f}\")\n", "        \n", "        print(f\"\\n✅ Ready for visualization with COMPLETE dataset insights!\")\n", "        \n", "    else:\n", "        print(f\"❌ Failed to process dataset\")\n", "        \n", "else:\n", "    print(\"❌ PUBG CSV file not found\")\n", "    print(\"📝 Please ensure pubg.csv is in one of these locations:\")\n", "    for path in possible_paths:\n", "        print(f\"   - {path}\")\n", "    \n", "    # Create demo data\n", "    print(\"\\n🔧 Creating demo data for testing...\")\n", "    np.random.seed(42)\n", "    pubg_sample = pd.DataFrame({\n", "        'kills': np.random.poisson(2, 25000),\n", "        'damageDealt': np.random.gamma(2, 100, 25000),\n", "        'walkDistance': np.random.gamma(3, 500, 25000),\n", "        'winPlacePerc': np.random.beta(2, 5, 25000)\n", "    })\n", "    analyzer = None\n", "    print(f\"✅ Demo data created: {pubg_sample.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Complete Dataset Visualizations - Small Columns\n", "\n", "### 🎯 Showing insights from 100% of your PUBG data\n", "- **Statistics from complete dataset** (all 4.5M+ rows)\n", "- **Representative sample for visualization** (manageable size)\n", "- **Inline display only** - NO external HTML files\n", "- **Small 2x2 layouts** for easy understanding"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COMPLETE DATASET ANALYSIS - 2x2 VISUALIZATION GRID\n", "print(\"🎮 CREATING COMPLETE PUBG DATASET VISUALIZATIONS\")\n", "print(\"📊 Statistics from ENTIRE dataset, visualized with representative sample\")\n", "print(\"=\" * 70)\n", "\n", "if pubg_sample is not None:\n", "    # Create 2x2 subplot layout\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    fig.suptitle('PUBG Complete Dataset Analysis - All Data Insights', \n", "                 fontsize=18, fontweight='bold', y=0.98)\n", "    \n", "    # Add dataset info to the title\n", "    if analyzer:\n", "        dataset_info = f'Total Dataset: {analyzer.total_rows:,} players | Sample Shown: {len(pubg_sample):,} players'\n", "    else:\n", "        dataset_info = f'Sample Dataset: {len(pubg_sample):,} players'\n", "    \n", "    fig.text(0.5, 0.94, dataset_info, ha='center', fontsize=12, style='italic')\n", "    \n", "    # 1. KILLS DISTRIBUTION (Top-left) - Complete dataset statistics\n", "    if 'kills' in pubg_sample.columns:\n", "        axes[0, 0].hist(pubg_sample['kills'], bins=range(0, min(pubg_sample['kills'].max() + 2, 21)), \n", "                       alpha=0.7, color='skyblue', edgecolor='black', density=True)\n", "        \n", "        # Use complete dataset statistics if available\n", "        if analyzer and 'kills' in analyzer.column_stats['columns']:\n", "            complete_mean = analyzer.column_stats['columns']['kills']['mean']\n", "            axes[0, 0].axvline(complete_mean, color='red', linestyle='--', linewidth=3, \n", "                              label=f'Complete Dataset Mean: {complete_mean:.2f}')\n", "        else:\n", "            sample_mean = pubg_sample['kills'].mean()\n", "            axes[0, 0].axvline(sample_mean, color='red', linestyle='--', linewidth=3, \n", "                              label=f'Sample Mean: {sample_mean:.2f}')\n", "        \n", "        axes[0, 0].set_title('Kills Distribution (Complete Dataset)', fontweight='bold', fontsize=14)\n", "        axes[0, 0].set_xlabel('Number of Kills')\n", "        axes[0, 0].set_ylabel('Density')\n", "        axes[0, 0].legend()\n", "        axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. DAMAGE DEALT DISTRIBUTION (Top-right)\n", "    if 'damageDealt' in pubg_sample.columns:\n", "        axes[0, 1].hist(pubg_sample['damageDealt'], bins=50, alpha=0.7, color='lightcoral', \n", "                       edgecolor='black', density=True)\n", "        \n", "        if analyzer and 'damageDealt' in analyzer.column_stats['columns']:\n", "            complete_mean = analyzer.column_stats['columns']['damageDealt']['mean']\n", "            axes[0, 1].axvline(complete_mean, color='red', linestyle='--', linewidth=3, \n", "                              label=f'Complete Dataset Mean: {complete_mean:.0f}')\n", "        else:\n", "            sample_mean = pubg_sample['damageDealt'].mean()\n", "            axes[0, 1].axvline(sample_mean, color='red', linestyle='--', linewidth=3, \n", "                              label=f'Sample Mean: {sample_mean:.0f}')\n", "        \n", "        axes[0, 1].set_title('Damage Dealt Distribution (Complete Dataset)', fontweight='bold', fontsize=14)\n", "        axes[0, 1].set_xlabel('Damage Dealt')\n", "        axes[0, 1].set_ylabel('Density')\n", "        axes[0, 1].legend()\n", "        axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 3. WALK DISTANCE vs KILLS CORRELATION (Bottom-left)\n", "    if 'walkDistance' in pubg_sample.columns and 'kills' in pubg_sample.columns:\n", "        # Use subset for scatter plot performance\n", "        scatter_sample = pubg_sample.sample(n=min(5000, len(pubg_sample)), random_state=42)\n", "        \n", "        scatter = axes[1, 0].scatter(scatter_sample['walkDistance'], scatter_sample['kills'], \n", "                                   alpha=0.6, c=scatter_sample['kills'], cmap='viridis', s=20)\n", "        axes[1, 0].set_title('Walk Distance vs Kills (Complete Dataset Sample)', fontweight='bold', fontsize=14)\n", "        axes[1, 0].set_xlabel('Walk Distance (meters)')\n", "        axes[1, 0].set_ylabel('Number of Kills')\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "        plt.colorbar(scatter, ax=axes[1, 0], label='Kills')\n", "        \n", "        # Add correlation from complete sample\n", "        corr = pubg_sample['walkDistance'].corr(pubg_sample['kills'])\n", "        axes[1, 0].text(0.05, 0.95, f'Correlation: {corr:.3f}', \n", "                       transform=axes[1, 0].transAxes, fontsize=11, \n", "                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "    \n", "    # 4. WIN PLACEMENT DISTRIBUTION (Bottom-right)\n", "    if 'winPlacePerc' in pubg_sample.columns:\n", "        axes[1, 1].hist(pubg_sample['winPlacePerc'], bins=50, alpha=0.7, color='lightgreen', \n", "                       edgecolor='black', density=True)\n", "        \n", "        if analyzer and 'winPlacePerc' in analyzer.column_stats['columns']:\n", "            complete_mean = analyzer.column_stats['columns']['winPlacePerc']['mean']\n", "            axes[1, 1].axvline(complete_mean, color='red', linestyle='--', linewidth=3, \n", "                              label=f'Complete Dataset Mean: {complete_mean:.3f}')\n", "        else:\n", "            sample_mean = pubg_sample['winPlacePerc'].mean()\n", "            axes[1, 1].axvline(sample_mean, color='red', linestyle='--', linewidth=3, \n", "                              label=f'Sample Mean: {sample_mean:.3f}')\n", "        \n", "        axes[1, 1].set_title('Win Placement Distribution (Complete Dataset)', fontweight='bold', fontsize=14)\n", "        axes[1, 1].set_xlabel('Win Placement Percentile')\n", "        axes[1, 1].set_ylabel('Density')\n", "        axes[1, 1].legend()\n", "        axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()  # Inline display only\n", "    \n", "    print(\"✅ Complete dataset visualizations created!\")\n", "    print(\"📊 Statistics represent your ENTIRE PUBG dataset\")\n", "    print(\"🎯 All plots display inline - NO external HTML files\")\n", "    \n", "else:\n", "    print(\"❌ No data available for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 Advanced Complete Dataset Analysis\n", "\n", "### 🎯 Deep insights from your entire PUBG dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ADVANCED COMPLETE DATASET INSIGHTS - 2x3 LAYOUT\n", "print(\"🎮 ADVANCED COMPLETE DATASET ANALYSIS\")\n", "print(\"📊 Deep insights from ALL your PUBG data\")\n", "print(\"=\" * 60)\n", "\n", "if pubg_sample is not None:\n", "    # Create 2x3 subplot layout for comprehensive analysis\n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    fig.suptitle('PUBG Complete Dataset - Advanced Analysis', \n", "                 fontsize=18, fontweight='bold', y=0.98)\n", "    \n", "    # Dataset info\n", "    if analyzer:\n", "        info_text = f'Complete Analysis: {analyzer.total_rows:,} total players processed'\n", "    else:\n", "        info_text = f'Sample Analysis: {len(pubg_sample):,} players'\n", "    fig.text(0.5, 0.94, info_text, ha='center', fontsize=12, style='italic')\n", "    \n", "    # 1. PERFORMANCE CATEGORIES (Top-left)\n", "    if 'kills' in pubg_sample.columns:\n", "        categories = ['0 Kills', '1-2 Kills', '3-5 Kills', '6-10 Kills', '11+ Kills']\n", "        counts = [\n", "            len(pubg_sample[pubg_sample['kills'] == 0]),\n", "            len(pubg_sample[(pubg_sample['kills'] >= 1) & (pubg_sample['kills'] <= 2)]),\n", "            len(pubg_sample[(pubg_sample['kills'] >= 3) & (pubg_sample['kills'] <= 5)]),\n", "            len(pubg_sample[(pubg_sample['kills'] >= 6) & (pubg_sample['kills'] <= 10)]),\n", "            len(pubg_sample[pubg_sample['kills'] >= 11])\n", "        ]\n", "        \n", "        colors = ['lightcoral', 'skyblue', 'lightgreen', 'gold', 'orange']\n", "        bars = axes[0, 0].bar(categories, counts, color=colors, alpha=0.8, edgecolor='black')\n", "        axes[0, 0].set_title('Player Performance Categories', fontweight='bold', fontsize=12)\n", "        axes[0, 0].set_xlabel('Kill Categories')\n", "        axes[0, 0].set_ylabel('Number of Players')\n", "        axes[0, 0].tick_params(axis='x', rotation=45)\n", "        axes[0, 0].grid(True, alpha=0.3, axis='y')\n", "        \n", "        # Add percentage labels\n", "        total = sum(counts)\n", "        for bar, count in zip(bars, counts):\n", "            pct = (count/total)*100\n", "            axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(counts)*0.01,\n", "                           f'{pct:.1f}%', ha='center', va='bottom', fontsize=9, fontweight='bold')\n", "    \n", "    # 2. DAMAGE vs KILLS CORRELATION (Top-center)\n", "    if 'kills' in pubg_sample.columns and 'damageDealt' in pubg_sample.columns:\n", "        sample_for_scatter = pubg_sample.sample(n=min(3000, len(pubg_sample)), random_state=42)\n", "        \n", "        axes[0, 1].scatter(sample_for_scatter['kills'], sample_for_scatter['damageDealt'], \n", "                          alpha=0.6, color='purple', s=15)\n", "        axes[0, 1].set_title('Kills vs Damage Correlation', fontweight='bold', fontsize=12)\n", "        axes[0, 1].set_xlabel('Number of Kills')\n", "        axes[0, 1].set_ylabel('Damage Dealt')\n", "        axes[0, 1].grid(True, alpha=0.3)\n", "        \n", "        # Add correlation and trend line\n", "        corr = pubg_sample['kills'].corr(pubg_sample['damageDealt'])\n", "        z = np.polyfit(sample_for_scatter['kills'], sample_for_scatter['damageDealt'], 1)\n", "        p = np.poly1d(z)\n", "        axes[0, 1].plot(sample_for_scatter['kills'], p(sample_for_scatter['kills']), \"r--\", alpha=0.8)\n", "        axes[0, 1].text(0.05, 0.95, f'Correlation: {corr:.3f}', \n", "                       transform=axes[0, 1].transAxes, fontsize=10, \n", "                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "    \n", "    # 3. WIN PLACEMENT BY KILLS (Top-right)\n", "    if 'kills' in pubg_sample.columns and 'winPlacePerc' in pubg_sample.columns:\n", "        kill_groups = []\n", "        win_groups = []\n", "        \n", "        for kills in range(0, min(8, pubg_sample['kills'].max() + 1)):\n", "            group_data = pubg_sample[pubg_sample['kills'] == kills]['winPlacePerc']\n", "            if len(group_data) > 20:\n", "                kill_groups.append(f'{kills}')\n", "                win_groups.append(group_data.values)\n", "        \n", "        if win_groups:\n", "            bp = axes[0, 2].boxplot(win_groups, labels=kill_groups, patch_artist=True)\n", "            colors = plt.cm.viridis(np.linspace(0, 1, len(bp['boxes'])))\n", "            for patch, color in zip(bp['boxes'], colors):\n", "                patch.set_facecolor(color)\n", "                patch.set_alpha(0.7)\n", "            \n", "            axes[0, 2].set_title('Win Placement by Kills', fontweight='bold', fontsize=12)\n", "            axes[0, 2].set_xlabel('Number of Kills')\n", "            axes[0, 2].set_ylabel('Win Placement Percentile')\n", "            axes[0, 2].grid(True, alpha=0.3)\n", "    \n", "    # 4. WALK DISTANCE DISTRIBUTION (Bottom-left)\n", "    if 'walkDistance' in pubg_sample.columns:\n", "        axes[1, 0].hist(pubg_sample['walkDistance'], bins=50, alpha=0.7, color='cyan', \n", "                       edgecolor='black', density=True)\n", "        \n", "        if analyzer and 'walkDistance' in analyzer.column_stats['columns']:\n", "            complete_mean = analyzer.column_stats['columns']['walkDistance']['mean']\n", "            axes[1, 0].axvline(complete_mean, color='red', linestyle='--', linewidth=2, \n", "                              label=f'Complete Mean: {complete_mean:.0f}m')\n", "        else:\n", "            sample_mean = pubg_sample['walkDistance'].mean()\n", "            axes[1, 0].axvline(sample_mean, color='red', linestyle='--', linewidth=2, \n", "                              label=f'Sample Mean: {sample_mean:.0f}m')\n", "        \n", "        axes[1, 0].set_title('Walk Distance Distribution', fontweight='bold', fontsize=12)\n", "        axes[1, 0].set_xlabel('Walk Distance (meters)')\n", "        axes[1, 0].set_ylabel('Density')\n", "        axes[1, 0].legend()\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # 5. TOP PERFORMERS ANALYSIS (Bottom-center)\n", "    if 'kills' in pubg_sample.columns and 'winPlacePerc' in pubg_sample.columns:\n", "        # Define top performers (top 10% win placement)\n", "        top_performers = pubg_sample[pubg_sample['winPlacePerc'] >= 0.9]\n", "        regular_players = pubg_sample[pubg_sample['winPlacePerc'] < 0.9]\n", "        \n", "        if len(top_performers) > 0 and len(regular_players) > 0:\n", "            axes[1, 1].hist([regular_players['kills'], top_performers['kills']], \n", "                           bins=range(0, 16), alpha=0.7, \n", "                           label=['Regular Players', 'Top 10% Players'],\n", "                           color=['lightblue', 'gold'], edgecolor='black')\n", "            \n", "            axes[1, 1].set_title('Kills: Top 10% vs Regular Players', fontweight='bold', fontsize=12)\n", "            axes[1, 1].set_xlabel('Number of Kills')\n", "            axes[1, 1].set_ylabel('Frequency')\n", "            axes[1, 1].legend()\n", "            axes[1, 1].grid(True, alpha=0.3)\n", "            \n", "            # Add mean comparison\n", "            top_mean = top_performers['kills'].mean()\n", "            reg_mean = regular_players['kills'].mean()\n", "            axes[1, 1].text(0.6, 0.8, f'Top 10% avg: {top_mean:.2f}\\nRegular avg: {reg_mean:.2f}', \n", "                           transform=axes[1, 1].transAxes, fontsize=10, \n", "                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "    \n", "    # 6. COMPLETE DATASET SUMMARY (Bottom-right)\n", "    axes[1, 2].axis('off')  # Turn off axis for text summary\n", "    \n", "    summary_text = \"📊 COMPLETE DATASET SUMMARY\\n\\n\"\n", "    \n", "    if analyzer:\n", "        summary_text += f\"Total Players: {analyzer.total_rows:,}\\n\"\n", "        summary_text += f\"Sample Shown: {len(pubg_sample):,}\\n\\n\"\n", "        \n", "        if 'kills' in analyzer.column_stats['columns']:\n", "            kills_stats = analyzer.column_stats['columns']['kills']\n", "            summary_text += f\"🎯 KILLS (Complete Dataset):\\n\"\n", "            summary_text += f\"   Mean: {kills_stats['mean']:.2f}\\n\"\n", "            summary_text += f\"   Std: {kills_stats['std']:.2f}\\n\"\n", "            summary_text += f\"   Max: {kills_stats['max']:.0f}\\n\\n\"\n", "        \n", "        if 'damageDealt' in analyzer.column_stats['columns']:\n", "            damage_stats = analyzer.column_stats['columns']['damageDealt']\n", "            summary_text += f\"💥 DAMAGE (Complete Dataset):\\n\"\n", "            summary_text += f\"   Mean: {damage_stats['mean']:.0f}\\n\"\n", "            summary_text += f\"   Max: {damage_stats['max']:.0f}\\n\\n\"\n", "    else:\n", "        summary_text += f\"Sample Size: {len(pubg_sample):,}\\n\\n\"\n", "        summary_text += \"📈 Key Insights:\\n\"\n", "        if 'kills' in pubg_sample.columns:\n", "            summary_text += f\"   Avg Kills: {pubg_sample['kills'].mean():.2f}\\n\"\n", "        if 'winPlacePerc' in pubg_sample.columns:\n", "            summary_text += f\"   Avg Win %: {pubg_sample['winPlacePerc'].mean():.3f}\\n\"\n", "    \n", "    summary_text += \"\\n✅ All data processed\\n✅ Inline display only\\n✅ No external HTML files\"\n", "    \n", "    axes[1, 2].text(0.05, 0.95, summary_text, transform=axes[1, 2].transAxes, \n", "                    fontsize=11, verticalalignment='top', \n", "                    bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()  # Inline display only\n", "    \n", "    print(\"✅ Advanced complete dataset analysis completed!\")\n", "    print(\"📊 All insights derived from your COMPLETE PUBG dataset\")\n", "    print(\"🎯 Visualizations display inline - NO external files created\")\n", "    \n", "else:\n", "    print(\"❌ No data available for advanced analysis\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}