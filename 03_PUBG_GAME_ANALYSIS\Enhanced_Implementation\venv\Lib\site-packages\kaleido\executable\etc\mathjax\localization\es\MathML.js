/*************************************************************
 *
 *  MathJax/localization/es/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("es","MathML",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          BadMglyph: "\u00ABmglyph\u00BB da\u00F1ado: %1",
          BadMglyphFont: "Tipo de letra da\u00F1ado: %1",
          MathPlayer: "MathJax no fue capaz de configurar MathPlayer.\n\nSi no est\u00E1 instalado MathPlayer, tienes que instalarlo primero.\nDe lo contrario, la configuraci\u00F3n de seguridad puede impedir que los controles de ActiveX\nfuncionen. Utiliza el elemento en opciones de Internet en el men\u00FA Herramientas y selecciona la ficha Seguridad, luego presiona el\nbot\u00F3n de nivel personalizado. Comprueba que la configuraci\u00F3n de\n\"Ejecutar controles ActiveX\" y \"comportamientos binarios y script\"\nest\u00E1n activados.\n\nActualmente ver\u00E1s los mensajes de error en lugar de la\ntipograf\u00EDa matem\u00E1tica",
          CantCreateXMLParser: "MathJax no puede crear un analizador de XML para MathML. Comprueba que la configuraci\u00F3n de seguridad \"controles de secuencia de comandos ActiveX marcados como seguros para scripts\" est\u00E1 habilitada (utiliza el elemento en opciones de Internet en el men\u00FA Herramientas y selecciona el panel de seguridad, luego presiona el bot\u00F3n de nivel personalizado para comprobarlo).\n\nLas ecuaciones MathML no podr\u00E1n ser procesadas por MathJax",
          UnknownNodeType: "Tipo de nodo desconocido: %1",
          UnexpectedTextNode: "Nodo de texto inesperado: %1",
          ErrorParsingMathML: "Error al analizar MathML",
          ParsingError: "Error al analizar MathML: %1",
          MathMLSingleElement: "MathML debe estar formado por un solo elemento",
          MathMLRootElement: "MathML debe estar formado por un elemento \u003Cmath\u003E, no %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/es/MathML.js");
