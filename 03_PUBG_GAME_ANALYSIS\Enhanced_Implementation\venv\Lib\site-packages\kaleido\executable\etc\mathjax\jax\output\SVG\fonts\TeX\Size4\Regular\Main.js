/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Size4/Regular/Main.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Size4'] = {
  directory: 'Size4/Regular',
  family: 'MathJax_Size4',
  id: 'MJSZ4',

    // SPACE
    0x20: [0,0,250,0,0,''],

    // LEFT PARENTHESIS
    0x28: [1750,1249,792,237,758,'758 -1237T758 -1240T752 -1249H736Q718 -1249 717 -1248Q711 -1245 672 -1199Q237 -706 237 251T672 1700Q697 1730 716 1749Q718 1750 735 1750H752Q758 1744 758 1741Q758 1737 740 1713T689 1644T619 1537T540 1380T463 1176Q348 802 348 251Q348 -242 441 -599T744 -1218Q758 -1237 758 -1240'],

    // RIGHT PARENTHESIS
    0x29: [1750,1250,792,33,554,'33 1741Q33 1750 51 1750H60H65Q73 1750 81 1743T119 1700Q554 1207 554 251Q554 -707 119 -1199Q76 -1250 66 -1250Q65 -1250 62 -1250T56 -1249Q55 -1249 53 -1249T49 -1250Q33 -1250 33 -1239Q33 -1236 50 -1214T98 -1150T163 -1052T238 -910T311 -727Q443 -335 443 251Q443 402 436 532T405 831T339 1142T224 1438T50 1716Q33 1737 33 1741'],

    // SOLIDUS
    0x2F: [1750,1249,1278,56,1221,'1166 1738Q1176 1750 1189 1750T1211 1742T1221 1721Q1221 1720 1221 1718T1220 1715Q1219 1708 666 238T111 -1237Q102 -1249 86 -1249Q74 -1249 65 -1240T56 -1220Q56 -1219 56 -1217T57 -1214Q58 -1207 611 263T1166 1738'],

    // LEFT SQUARE BRACKET
    0x5B: [1750,1249,583,269,577,'269 -1249V1750H577V1677H342V-1176H577V-1249H269'],

    // REVERSE SOLIDUS
    0x5C: [1750,1249,1278,56,1221,'56 1720Q56 1732 64 1741T85 1750Q104 1750 111 1738Q113 1734 666 264T1220 -1214Q1220 -1215 1220 -1217T1221 -1220Q1221 -1231 1212 -1240T1191 -1249Q1175 -1249 1166 -1237Q1164 -1233 611 237T57 1715Q57 1716 56 1718V1720'],

    // RIGHT SQUARE BRACKET
    0x5D: [1750,1249,583,5,313,'5 1677V1750H313V-1249H5V-1176H240V1677H5'],

    // LEFT CURLY BRACKET
    0x7B: [1750,1249,806,144,661,'661 -1243L655 -1249H622L604 -1240Q503 -1190 434 -1107T348 -909Q346 -897 346 -499L345 -98L343 -82Q335 3 287 87T157 223Q146 232 145 236Q144 240 144 250Q144 265 145 268T157 278Q242 333 288 417T343 583L345 600L346 1001Q346 1398 348 1410Q379 1622 600 1739L622 1750H655L661 1744V1727V1721Q661 1712 661 1710T657 1705T648 1700T630 1690T602 1668Q589 1659 574 1643T531 1593T484 1508T459 1398Q458 1389 458 1001Q458 614 457 605Q441 435 301 316Q254 277 202 251L250 222Q260 216 301 185Q443 66 457 -104Q458 -113 458 -501Q458 -888 459 -897Q463 -944 478 -988T509 -1060T548 -1114T580 -1149T602 -1167Q620 -1183 634 -1192T653 -1202T659 -1207T661 -1220V-1226V-1243'],

    // RIGHT CURLY BRACKET
    0x7D: [1750,1249,806,144,661,'144 1727Q144 1743 146 1746T162 1750H167H183L203 1740Q274 1705 325 1658T403 1562T440 1478T456 1410Q458 1398 458 1001Q459 661 459 624T465 558Q470 526 480 496T502 441T529 395T559 356T588 325T615 301T637 284T654 273L660 269V266Q660 263 660 259T661 250V239Q661 236 661 234T660 232T656 229T649 224Q577 179 528 105T465 -57Q460 -86 460 -123T458 -499V-661Q458 -857 457 -893T447 -955Q425 -1048 359 -1120T203 -1239L183 -1249H168Q150 -1249 147 -1246T144 -1226Q144 -1213 145 -1210T153 -1202Q169 -1193 186 -1181T232 -1140T282 -1081T322 -1000T345 -897Q346 -888 346 -501Q346 -113 347 -104Q359 58 503 184Q554 226 603 250Q504 299 430 393T347 605Q346 614 346 1002Q346 1389 345 1398Q338 1493 288 1573T153 1703Q146 1707 145 1710T144 1727'],

    // MODIFIER LETTER CIRCUMFLEX ACCENT
    0x2C6: [845,-561,1889,-14,1902,'5 561Q-4 561 -9 582T-14 618Q-14 623 -13 625Q-11 628 461 736T943 845Q945 845 1417 738T1896 628Q1902 628 1902 618Q1902 607 1897 584T1883 561Q1881 561 1412 654L945 750L476 654Q6 561 5 561'],

    // SMALL TILDE
    0x2DC: [823,-582,1889,0,1885,'1212 583Q1124 583 1048 603T923 647T799 691T635 711Q524 711 375 679T120 615L16 583Q14 584 12 587T9 592Q-2 650 2 659Q2 669 38 687Q54 696 146 723T309 767Q527 823 666 823Q759 823 837 803T964 759T1088 715T1252 695Q1363 695 1512 727T1764 791T1871 823Q1872 822 1874 819T1878 814Q1885 783 1885 753Q1885 748 1884 747Q1884 738 1849 719Q1836 712 1740 682T1484 617T1212 583'],

    // COMBINING CIRCUMFLEX ACCENT
    0x302: [845,-561,0,-1903,13,'-1884 561Q-1893 561 -1898 582T-1903 618Q-1903 623 -1902 625Q-1900 628 -1428 736T-946 845Q-944 845 -472 738T7 628Q13 628 13 618Q13 607 8 584T-6 561Q-8 561 -477 654L-944 750L-1413 654Q-1883 561 -1884 561'],

    // COMBINING TILDE
    0x303: [823,-582,0,-1889,-4,'-677 583Q-765 583 -841 603T-966 647T-1090 691T-1254 711Q-1365 711 -1514 679T-1768 615L-1873 583Q-1875 584 -1877 587T-1880 592Q-1891 650 -1887 659Q-1887 669 -1851 687Q-1835 696 -1743 723T-1580 767Q-1362 823 -1223 823Q-1130 823 -1052 803T-925 759T-801 715T-637 695Q-526 695 -377 727T-125 791T-18 823Q-17 822 -15 819T-11 814Q-4 782 -4 753Q-4 748 -5 747Q-5 738 -40 719Q-53 712 -149 682T-405 617T-677 583'],

    // SQUARE ROOT
    0x221A: [1750,1250,1000,111,1020,'983 1739Q988 1750 1001 1750Q1008 1750 1013 1745T1020 1733Q1020 1726 742 244T460 -1241Q458 -1250 439 -1250H436Q424 -1250 424 -1248L410 -1166Q395 -1083 367 -920T312 -601L201 44L137 -83L111 -57L187 96L264 247Q265 246 369 -357Q470 -958 473 -963L727 384Q979 1729 983 1739'],

    // LEFT CEILING
    0x2308: [1750,1249,639,269,633,'269 -1249V1750H633V1677H342V-1249H269'],

    // RIGHT CEILING
    0x2309: [1750,1249,639,5,369,'5 1677V1750H369V-1249H296V1677H5'],

    // LEFT FLOOR
    0x230A: [1750,1249,639,269,633,'269 -1249V1750H342V-1176H633V-1249H269'],

    // RIGHT FLOOR
    0x230B: [1750,1249,639,5,369,'296 -1176V1750H369V-1249H5V-1176H296'],

    // LEFT PARENTHESIS UPPER HOOK
    0x239B: [1155,655,875,291,843,'837 1154Q843 1148 843 1145Q843 1141 818 1106T753 1002T667 841T574 604T494 299Q417 -84 417 -609Q417 -641 416 -647T411 -654Q409 -655 366 -655Q299 -655 297 -654Q292 -652 292 -643T291 -583Q293 -400 304 -242T347 110T432 470T574 813T785 1136Q787 1139 790 1142T794 1147T796 1150T799 1152T802 1153T807 1154T813 1154H819H837'],

    // LEFT PARENTHESIS EXTENSION
    0x239C: [610,11,875,291,417,'413 -9Q412 -9 407 -9T388 -10T354 -10Q300 -10 297 -9Q294 -8 293 -5Q291 5 291 127V300Q291 602 292 605L296 609Q298 610 366 610Q382 610 392 610T407 610T412 609Q416 609 416 592T417 473V127Q417 -9 413 -9'],

    // LEFT PARENTHESIS LOWER HOOK
    0x239D: [1165,644,875,291,843,'843 -635Q843 -638 837 -644H820Q801 -644 800 -643Q792 -635 785 -626Q684 -503 605 -363T473 -75T385 216T330 518T302 809T291 1093Q291 1144 291 1153T296 1164Q298 1165 366 1165Q409 1165 411 1164Q415 1163 416 1157T417 1119Q417 529 517 109T833 -617Q843 -631 843 -635'],

    // RIGHT PARENTHESIS UPPER HOOK
    0x239E: [1154,655,875,31,583,'31 1143Q31 1154 49 1154H59Q72 1154 75 1152T89 1136Q190 1013 269 873T401 585T489 294T544 -8T572 -299T583 -583Q583 -634 583 -643T577 -654Q575 -655 508 -655Q465 -655 463 -654Q459 -653 458 -647T457 -609Q457 -58 371 340T100 1037Q87 1059 61 1098T31 1143'],

    // RIGHT PARENTHESIS EXTENSION
    0x239F: [610,11,875,457,583,'579 -9Q578 -9 573 -9T554 -10T520 -10Q466 -10 463 -9Q460 -8 459 -5Q457 5 457 127V300Q457 602 458 605L462 609Q464 610 532 610Q548 610 558 610T573 610T578 609Q582 609 582 592T583 473V127Q583 -9 579 -9'],

    // RIGHT PARENTHESIS LOWER HOOK
    0x23A0: [1165,644,875,31,583,'56 -644H50Q31 -644 31 -635Q31 -632 37 -622Q69 -579 100 -527Q286 -228 371 170T457 1119Q457 1161 462 1164Q464 1165 520 1165Q575 1165 577 1164Q582 1162 582 1153T583 1093Q581 910 570 752T527 400T442 40T300 -303T89 -626Q78 -640 75 -642T61 -644H56'],

    // LEFT SQUARE BRACKET UPPER CORNER
    0x23A1: [1154,645,667,319,666,'319 -645V1154H666V1070H403V-645H319'],

    // LEFT SQUARE BRACKET EXTENSION
    0x23A2: [602,0,667,319,403,'319 0V602H403V0H319'],

    // LEFT SQUARE BRACKET LOWER CORNER
    0x23A3: [1155,644,667,319,666,'319 -644V1155H403V-560H666V-644H319'],

    // RIGHT SQUARE BRACKET UPPER CORNER
    0x23A4: [1154,645,667,0,347,'0 1070V1154H347V-645H263V1070H0'],

    // RIGHT SQUARE BRACKET EXTENSION
    0x23A5: [602,0,667,263,347,'263 0V602H347V0H263'],

    // RIGHT SQUARE BRACKET LOWER CORNER
    0x23A6: [1155,644,667,0,347,'263 -560V1155H347V-644H0V-560H263'],

    // LEFT CURLY BRACKET UPPER HOOK
    0x23A7: [899,10,889,383,718,'712 899L718 893V876V865Q718 854 704 846Q627 793 577 710T510 525Q510 524 509 521Q505 493 504 349Q504 345 504 334Q504 277 504 240Q504 -2 503 -4Q502 -8 494 -9T444 -10Q392 -10 390 -9Q387 -8 386 -5Q384 5 384 230Q384 262 384 312T383 382Q383 481 392 535T434 656Q510 806 664 892L677 899H712'],

    // LEFT CURLY BRACKET MIDDLE PIECE
    0x23A8: [1160,660,889,170,504,'389 1159Q391 1160 455 1160Q496 1160 498 1159Q501 1158 502 1155Q504 1145 504 924Q504 691 503 682Q494 549 425 439T243 259L229 250L243 241Q349 175 421 66T503 -182Q504 -191 504 -424Q504 -600 504 -629T499 -659H498Q496 -660 444 -660T390 -659Q387 -658 386 -655Q384 -645 384 -425V-282Q384 -176 377 -116T342 10Q325 54 301 92T255 155T214 196T183 222T171 232Q170 233 170 250T171 268Q171 269 191 284T240 331T300 407T354 524T383 679Q384 691 384 925Q384 1152 385 1155L389 1159'],

    // LEFT CURLY BRACKET LOWER HOOK
    0x23A9: [10,899,889,384,718,'718 -893L712 -899H677L666 -893Q542 -825 468 -714T385 -476Q384 -466 384 -282Q384 3 385 5L389 9Q392 10 444 10Q486 10 494 9T503 4Q504 2 504 -239V-310V-366Q504 -470 508 -513T530 -609Q546 -657 569 -698T617 -767T661 -812T699 -843T717 -856T718 -876V-893'],

    // CURLY BRACKET EXTENSION
    0x23AA: [310,10,889,384,504,'384 150V266Q384 304 389 309Q391 310 455 310Q496 310 498 309Q502 308 503 298Q504 283 504 150Q504 32 504 12T499 -9H498Q496 -10 444 -10T390 -9Q386 -8 385 2Q384 17 384 150'],

    // RIGHT CURLY BRACKET UPPER HOOK
    0x23AB: [899,10,889,170,504,'170 875Q170 892 172 895T189 899H194H211L222 893Q345 826 420 715T503 476Q504 467 504 230Q504 51 504 21T499 -9H498Q496 -10 444 -10Q402 -10 394 -9T385 -4Q384 -2 384 240V311V366Q384 469 380 513T358 609Q342 657 319 698T271 767T227 812T189 843T171 856T170 875'],

    // RIGHT CURLY BRACKET MIDDLE PIECE
    0x23AC: [1160,660,889,384,718,'389 1159Q391 1160 455 1160Q496 1160 498 1159Q501 1158 502 1155Q504 1145 504 925V782Q504 676 511 616T546 490Q563 446 587 408T633 345T674 304T705 278T717 268Q718 267 718 250T717 232Q717 231 697 216T648 169T588 93T534 -24T505 -179Q504 -191 504 -425Q504 -600 504 -629T499 -659H498Q496 -660 444 -660T390 -659Q387 -658 386 -655Q384 -645 384 -424Q384 -191 385 -182Q394 -49 463 61T645 241L659 250L645 259Q539 325 467 434T385 682Q384 692 384 873Q384 1153 385 1155L389 1159'],

    // RIGHT CURLY BRACKET LOWER HOOK
    0x23AD: [10,899,889,170,505,'384 -239V-57Q384 4 389 9Q391 10 455 10Q496 10 498 9Q501 8 502 5Q504 -5 504 -230Q504 -261 504 -311T505 -381Q505 -486 492 -551T435 -691Q357 -820 222 -893L211 -899H195Q176 -899 173 -896T170 -874Q170 -858 171 -855T184 -846Q262 -793 312 -709T378 -525Q378 -524 379 -522Q383 -493 384 -351Q384 -345 384 -334Q384 -276 384 -239'],

    // RADICAL SYMBOL BOTTOM
    0x23B7: [935,885,1056,111,742,'742 -871Q740 -873 737 -876T733 -880T730 -882T724 -884T714 -885H702L222 569L180 484Q138 399 137 399Q131 404 124 412L111 425L265 736L702 -586V168L703 922Q713 935 722 935Q734 935 742 920V-871'],

    // MATHEMATICAL LEFT ANGLE BRACKET
    0x27E8: [1750,1248,806,140,703,'140 242V260L386 994Q633 1729 635 1732Q643 1745 657 1749Q658 1749 662 1749T668 1750Q682 1749 692 1740T702 1714V1705L214 251L703 -1204L702 -1213Q702 -1230 692 -1239T667 -1248H664Q647 -1248 635 -1231Q633 -1228 386 -493L140 242'],

    // MATHEMATICAL RIGHT ANGLE BRACKET
    0x27E9: [1750,1248,806,103,665,'103 1714Q103 1732 114 1741T137 1750Q157 1750 170 1732Q172 1729 419 994L665 260V242L419 -493Q172 -1228 170 -1231Q158 -1248 141 -1248H138Q123 -1248 113 -1239T103 -1213V-1204L591 251L103 1705V1714'],

    // stix-radical symbol vertical extender
    0xE000: [625,14,1056,702,742,'722 -14H720Q708 -14 702 0V306L703 612Q713 625 722 625Q734 625 742 610V0Q734 -14 724 -14H722'],

    // stix-radical symbol top corner piece
    0xE001: [605,14,1056,702,1076,'702 589Q706 601 718 605H1061Q1076 597 1076 585Q1076 572 1061 565H742V0Q734 -14 724 -14H722H720Q708 -14 702 0V589'],

    // stix-horizontal brace, down left piece
    0xE150: [120,213,450,-24,460,'-18 -213L-24 -207V-172L-16 -158Q75 2 260 84Q334 113 415 119Q418 119 427 119T440 120Q454 120 457 117T460 98V60V25Q460 7 457 4T441 0Q308 0 193 -55T25 -205Q21 -211 18 -212T-1 -213H-18'],

    // stix-horizontal brace, down right piece
    0xE151: [120,213,450,-10,474,'-10 60Q-10 104 -10 111T-5 118Q-1 120 10 120Q96 120 190 84Q375 2 466 -158L474 -172V-207L468 -213H451H447Q437 -213 434 -213T428 -209T423 -202T414 -187T396 -163Q331 -82 224 -41T9 0Q-4 0 -7 3T-10 25V60'],

    // stix-horizontal brace, upper left piece
    0xE152: [333,0,450,-24,460,'-24 327L-18 333H-1Q11 333 15 333T22 329T27 322T35 308T54 284Q115 203 225 162T441 120Q454 120 457 117T460 95V60V28Q460 8 457 4T442 0Q355 0 260 36Q75 118 -16 278L-24 292V327'],

    // stix-horizontal brace, upper right piece
    0xE153: [333,0,450,-10,474,'-10 60V95Q-10 113 -7 116T9 120Q151 120 250 171T396 284Q404 293 412 305T424 324T431 331Q433 333 451 333H468L474 327V292L466 278Q375 118 190 36Q95 0 8 0Q-5 0 -7 3T-10 24V60'],

    // stix-oblique open face capital letter A
    0xE154: [120,0,400,-10,410,'-10 0V120H410V0H-10']
};

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Size4/Regular/Main.js");
