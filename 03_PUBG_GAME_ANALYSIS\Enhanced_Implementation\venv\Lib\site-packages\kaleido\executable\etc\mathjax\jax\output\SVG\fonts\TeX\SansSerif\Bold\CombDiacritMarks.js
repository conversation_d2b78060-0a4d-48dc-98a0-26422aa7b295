/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/SansSerif/Bold/CombDiacritMarks.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_SansSerif-bold'],
  {
    // COMBINING GRAVE ACCENT
    0x300: [694,-537,0,-458,-218,'-458 682Q-458 690 -452 692T-426 694H-381H-314L-312 691Q-311 691 -305 682T-287 655T-263 622L-218 555V549Q-218 544 -224 538L-259 537Q-295 537 -296 538Q-298 539 -376 606T-456 676Q-458 680 -458 682'],

    // COMBINING ACUTE ACCENT
    0x301: [694,-537,0,-334,-93,'-290 537H-310Q-334 537 -334 549Q-334 553 -311 588T-264 656L-241 690Q-240 690 -239 691T-236 693Q-235 694 -167 694H-100Q-93 684 -93 681T-94 677Q-95 675 -173 607T-255 538Q-256 537 -290 537'],

    // COMBINING CIRCUMFLEX ACCENT
    0x302: [694,-537,0,-442,-109,'-442 550Q-442 554 -415 589T-360 658T-331 692Q-329 694 -275 694Q-222 694 -220 693Q-219 692 -169 629T-112 557Q-109 552 -109 549Q-109 545 -116 538L-151 537Q-187 537 -188 538Q-189 538 -232 575L-275 611Q-276 611 -319 575Q-362 538 -363 538Q-364 537 -400 537L-435 538Q-442 545 -442 550'],

    // COMBINING TILDE
    0x303: [694,-548,0,-458,-93,'-458 565Q-458 609 -428 651T-346 694Q-312 694 -286 679T-240 650T-207 635Q-194 635 -189 645T-181 672T-173 694H-100Q-93 684 -93 680Q-93 631 -123 590T-206 548Q-238 548 -265 563T-311 592T-344 607Q-362 607 -368 580T-378 549L-413 548H-430Q-448 548 -453 550T-458 565'],

    // COMBINING MACRON
    0x304: [660,-560,0,-474,-77,'-84 660Q-81 656 -77 653V567L-81 564L-84 561L-274 560H-383Q-469 560 -471 565L-472 566Q-474 569 -474 611L-473 653Q-469 659 -466 659Q-465 660 -274 660H-84'],

    // COMBINING BREVE
    0x306: [694,-552,0,-470,-80,'-123 694Q-80 694 -80 657Q-80 626 -99 601T-161 563Q-199 552 -275 552Q-352 552 -389 563Q-470 586 -470 655Q-470 667 -468 673Q-457 694 -435 694H-431Q-408 694 -396 685Q-387 676 -387 671Q-384 661 -275 661Q-167 661 -164 671Q-164 674 -163 677T-151 687T-123 694'],

    // COMBINING DOT ABOVE
    0x307: [695,-596,0,-356,-194,'-329 596Q-346 602 -351 611T-356 638V646Q-356 653 -356 654T-356 661T-355 668T-353 673T-351 679T-347 684T-341 689T-332 693T-274 695H-221Q-202 683 -198 676T-194 645Q-194 632 -195 625T-202 610T-221 596H-329'],

    // COMBINING DIAERESIS
    0x308: [696,-595,0,-459,-91,'-331 695Q-312 683 -308 676T-304 645Q-304 632 -304 626T-311 610T-331 596L-380 595H-408Q-448 595 -457 617Q-459 621 -459 645T-457 673Q-448 696 -409 696Q-405 696 -396 696T-380 695H-331ZM-247 644Q-247 658 -246 665T-239 680T-221 694Q-217 695 -169 695H-143Q-102 695 -93 672Q-91 664 -91 645V635Q-91 613 -106 602Q-113 597 -121 596T-171 595L-219 596Q-232 600 -238 608T-246 622T-247 644'],

    // COMBINING RING ABOVE
    0x30A: [694,-538,0,-365,-119,'-365 616Q-365 658 -331 676T-256 694Q-253 694 -247 694T-236 693Q-166 693 -139 666Q-119 644 -119 616T-139 565Q-166 538 -237 538H-242Q-365 538 -365 616ZM-181 616Q-181 641 -195 647T-242 654Q-258 654 -266 654T-284 650T-298 638T-303 616Q-303 592 -289 585T-242 577Q-209 577 -195 584T-181 616'],

    // COMBINING DOUBLE ACUTE ACCENT
    0x30B: [694,-537,0,-440,-94,'-426 686Q-424 694 -394 694H-350H-283Q-277 686 -277 682Q-277 673 -317 608T-361 538L-396 537H-420Q-432 537 -436 539T-440 548Q-440 560 -434 616Q-432 633 -430 650T-427 677L-426 686ZM-243 686Q-241 694 -211 694H-167H-100Q-94 686 -94 682Q-94 673 -134 608T-178 538L-213 537H-237Q-249 537 -253 539T-257 548Q-257 560 -251 616Q-249 633 -247 650T-244 677L-243 686'],

    // COMBINING CARON
    0x30C: [657,-500,0,-442,-109,'-442 645Q-442 657 -418 657H-398Q-393 657 -388 657T-379 657T-371 656T-365 656H-363L-319 620L-276 583Q-275 583 -232 619Q-189 656 -188 656Q-187 657 -151 657H-116Q-109 649 -109 645Q-109 642 -112 637Q-118 629 -168 566T-220 501Q-222 500 -275 500Q-329 500 -331 501Q-442 634 -442 645']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/SansSerif/Bold/CombDiacritMarks.js");
