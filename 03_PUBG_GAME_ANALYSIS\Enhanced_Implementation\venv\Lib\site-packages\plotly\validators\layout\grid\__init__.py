import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._yside import <PERSON><PERSON><PERSON>alida<PERSON>
    from ._ygap import <PERSON>gap<PERSON><PERSON>da<PERSON>
    from ._yaxes import Ya<PERSON><PERSON>alidator
    from ._xside import <PERSON><PERSON>Validator
    from ._xgap import <PERSON><PERSON>p<PERSON><PERSON><PERSON><PERSON>
    from ._xaxes import <PERSON>ax<PERSON>V<PERSON><PERSON><PERSON>
    from ._subplots import SubplotsValida<PERSON>
    from ._rows import RowsValida<PERSON>
    from ._roworder import RoworderValidator
    from ._pattern import Pat<PERSON>Valida<PERSON>
    from ._domain import DomainValidator
    from ._columns import ColumnsValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._yside.YsideValidator",
            "._ygap.YgapValidator",
            "._yaxes.YaxesValidator",
            "._xside.XsideValidator",
            "._xgap.XgapValidator",
            "._xaxes.XaxesValidator",
            "._subplots.SubplotsValidator",
            "._rows.RowsValidator",
            "._roworder.RoworderValidator",
            "._pattern.PatternValidator",
            "._domain.DomainValidator",
            "._columns.ColumnsValidator",
        ],
    )
