/*************************************************************
 *
 *  MathJax/localization/br/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("br","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "O karga\u00F1 ar font web  %1",
          CantLoadWebFont: "Ne c'haller ket karga\u00F1 ar font web %1",
          CantFindFontUsing: "Ne c'haller ket kavout ur font dereat e-touez %1",
          FirefoxCantLoadWebFont: "Ne c'hall ket Firefox karga\u00F1 ar fonto\u00F9 adalek un ostiz a-bell",
          WebFontsNotAvailable: "N'haller ket kaout ar Fonto\u00F9 web. Ar fonto\u00F9 skeudenn a vo implijet en o flas"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/br/HTML-CSS.js");
