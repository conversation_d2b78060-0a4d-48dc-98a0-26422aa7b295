/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Main/Bold/CombDiacritMarks.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Main-bold'],
  {
    // COMBINING GRAVE ACCENT
    0x300: [706,-503,0,-462,-237,'-461 634Q-461 663 -439 684T-392 706Q-384 706 -379 705T-367 700T-356 693T-343 681T-330 666T-313 645T-293 620Q-243 558 -238 553Q-237 552 -257 527L-276 503L-352 543Q-360 547 -373 553T-392 563T-408 571T-422 580T-434 587T-444 595T-451 603T-457 612T-460 622T-461 634'],

    // COMBINING ACUTE ACCENT
    0x301: [706,-503,0,-339,-115,'-184 706Q-156 706 -136 683T-115 634Q-115 608 -134 593T-209 550Q-219 545 -224 543L-300 503L-319 527Q-339 552 -338 553Q-333 558 -283 620Q-276 629 -266 641T-251 659T-239 673T-229 685T-221 693T-212 699T-204 703T-195 705T-184 706'],

    // COMBINING CIRCUMFLEX ACCENT
    0x302: [694,-520,0,-449,-126,'-368 632L-288 694Q-286 693 -207 632T-127 570T-144 545T-162 520Q-165 520 -225 559L-288 597L-351 559Q-411 520 -414 520Q-415 520 -432 544T-449 570T-368 632'],

    // COMBINING TILDE
    0x303: [694,-552,0,-479,-97,'-232 552Q-255 552 -297 575T-360 599Q-394 599 -429 564L-441 552L-460 569Q-464 572 -469 576T-476 584L-479 586Q-417 656 -410 663Q-376 694 -345 694Q-336 694 -331 693Q-313 689 -275 668T-216 647Q-182 647 -147 682L-135 694L-116 677Q-112 674 -107 670T-100 662L-97 660Q-159 590 -166 583Q-200 552 -232 552'],

    // COMBINING MACRON
    0x304: [607,-540,0,-495,-81,'-495 540V607H-81V540H-495'],

    // COMBINING BREVE
    0x306: [694,-500,0,-473,-103,'-288 500Q-367 500 -420 558T-473 689V694H-422V685Q-422 681 -421 674T-411 648T-389 615T-349 590T-288 578Q-228 578 -193 611T-154 685V694H-103V689Q-103 623 -153 562T-288 500'],

    // COMBINING DOT ABOVE
    0x307: [695,-525,0,-373,-203,'-373 610Q-373 647 -348 671T-292 695Q-251 695 -227 669T-203 610T-225 551T-288 525Q-327 525 -350 551T-373 610'],

    // COMBINING DIAERESIS
    0x308: [695,-535,0,-479,-97,'-479 615Q-479 650 -456 672T-397 695Q-361 693 -341 669T-320 615Q-320 583 -343 559T-399 535Q-428 535 -453 556T-479 615ZM-256 615Q-256 651 -232 673T-176 695Q-149 695 -123 675T-97 615Q-97 578 -121 557T-180 535Q-211 537 -233 559T-256 615'],

    // COMBINING RING ABOVE
    0x30A: [702,-535,0,-415,-161,'-415 618Q-415 653 -382 677T-296 702H-291Q-194 702 -168 647Q-161 634 -161 618Q-161 607 -165 596T-180 570T-220 546T-288 536T-355 545T-394 568T-410 594T-415 618ZM-223 618Q-223 645 -234 652T-274 659H-283Q-289 659 -297 659T-307 660Q-328 660 -339 653T-351 638T-353 619Q-353 591 -341 585T-288 578Q-260 578 -249 580T-230 590T-223 618'],

    // COMBINING DOUBLE ACUTE ACCENT
    0x30B: [714,-511,0,-442,-82,'-389 511L-442 543Q-442 544 -424 606T-404 674Q-390 705 -361 713Q-360 713 -356 713T-349 714Q-340 714 -330 712Q-273 690 -273 644Q-273 621 -290 604L-342 554L-389 511ZM-198 511L-251 543Q-251 544 -233 606T-213 674Q-199 705 -170 713Q-169 713 -165 713T-158 714Q-127 714 -105 693T-82 647Q-82 638 -84 631T-89 618T-99 604T-112 590T-130 574T-151 554L-198 511'],

    // COMBINING CARON
    0x30C: [660,-515,0,-445,-132,'-444 603Q-445 604 -439 618T-425 646T-417 659L-352 635L-288 611L-224 635L-159 659Q-158 660 -151 647T-138 619T-132 603Q-135 601 -211 558T-288 515T-365 558T-444 603'],

    // COMBINING LONG SOLIDUS OVERLAY
    0x338: [711,210,0,-734,-161,'-705 -210Q-715 -210 -724 -203T-734 -179Q-734 -171 -732 -166Q-730 -163 -474 266T-215 698Q-208 711 -190 711Q-180 711 -171 704T-161 681Q-161 672 -164 667Q-171 654 -425 228T-683 -201Q-692 -210 -705 -210']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/CombDiacritMarks.js");
