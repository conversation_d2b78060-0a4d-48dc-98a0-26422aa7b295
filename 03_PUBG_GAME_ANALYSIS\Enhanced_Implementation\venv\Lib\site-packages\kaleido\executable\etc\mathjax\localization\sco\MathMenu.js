/*************************************************************
 *
 *  MathJax/localization/sco/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("sco","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "Shaw maths aes",
          MathMLcode: "MathML code",
          OriginalMathML: "Oreeginal MathML",
          TeXCommands: "TeX commauns",
          AsciiMathInput: "AsciiMathML input",
          Original: "Oreeginal form",
          ErrorMessage: "Mistak message",
          Annotation: "Annotation",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "Content MathML",
          OpenMath: "OpenMath",
          texHints: "Shaw TeX hints in MathML",
          Settings: "Maths settins",
          ZoomTrigger: "Zuim trigger",
          Hover: "Hover",
          Click: "Clap",
          DoubleClick: "Dooble-clap",
          NoZoom: "Naw zuim",
          TriggerRequires: "Trigger needs:",
          Option: "Optie",
          Alt: "Alt",
          Command: "Commaun",
          Control: "Control",
          Shift: "Shift",
          ZoomFactor: "Zuim facter",
          Renderer: "Maths renderer",
          MPHandles: "Let MathPlayer haunle:",
          MenuEvents: "Menu events",
          MouseEvents: "Moose events",
          MenuAndMouse: "Moose n menu events",
          FontPrefs: "Font preferences",
          ForHTMLCSS: "Fer HTML-CSS:",
          Auto: "Aut\u00E6",
          TeXLocal: "TeX (local)",
          TeXWeb: "TeX (wab)",
          TeXImage: "TeX (eimage)",
          STIXLocal: "STIX (local)",
          STIXWeb: "STIX (wab)",
          AsanaMathWeb: "Asana Math (wab)",
          GyrePagellaWeb: "Gyre Pagella (wab)",
          GyreTermesWeb: "Gyre Termes (wab)",
          LatinModernWeb: "Latin Modern (wab)",
          NeoEulerWeb: "Neo Euler (wab)",
          ContextMenu: "Contextual menu",
          Browser: "Brouser",
          Scale: "Scale aw maths ...",
          Discoverable: "Heilicht oan hover",
          Locale: "Leid",
          LoadLocale: "Laid fae URL ...",
          About: "Aneat MathJax",
          Help: "MathJax heelp",
          localTeXfonts: "uisin local TeX fonts",
          webTeXfonts: "uisin wab TeX font",
          imagefonts: "uisin Eimage fonts",
          localSTIXfonts: "uisin local STIX fonts",
          webSVGfonts: "uisin wab SVG fonts",
          genericfonts: "uisin generic Unicode fonts",
          wofforotffonts: "WOFF or OTF fonts",
          eotffonts: "EOT fonts",
          svgfonts: "SVG fonts",
          WebkitNativeMMLWarning: "Yer brouser disna seem tae support MathML nateevelie, sae switchin tae MathML ootput micht cause the mathematics oan the page tae become onreadable",
          MSIENativeMMLWarning: "Internet Explorer needs the MathPlayer plug-in fer tae process MathML ootput.",
          OperaNativeMMLWarning: "Opera's support fer MathML is leemitit, sae switchin tae MathML ootput micht cause some expressions tae render puirlie.",
          SafariNativeMMLWarning: "Yer brouser's native MathML disna implement aw the features uised bi MathJax, sae some expressions michtna render properlie.",
          FirefoxNativeMMLWarning: "Yer brouser's native MathML disna implement aw the features uised bi MathJax, sae some expressions michtna render properlie.",
          MSIESVGWarning: "SVG isna implemented in Internet Explorer prior til IE9 or whan it's emulating IE8 or ablo. Switchin til SVG ootput will cause the mathematics tae no displey properlie.",
          LoadURL: "Laid owersetin data fae this URL:",
          BadURL: "The URL shid be fer ae JavaScript file that defines MathJax owersetin data. JavaScript file names shid end wi '.js'",
          BadData: "Failed tae laid owersetin data fae %1",
          SwitchAnyway: "Switch the renderer oniewas?\n\n(Press OK tae switch, CANCEL tae continue wi the current renderer)",
          ScaleMath: "Scale aw mathematics (compared til surroondin tex) bi",
          NonZeroScale: "The scale shidna be zero",
          PercentScale: "The scale shid be ae percentage (fer example 120%%)",
          IE8warning: "This will disable the MathJax menu n zuim features, but ye can Alt-Clap oan aen expression tae obtain the MathJax menu insteid.\n\nReallie want tae chynge the MathPlayer settins?",
          IE9warning: "The MathJax contextual menu will be disabled, but ye can Alt-Clap oan aen expression tae obtain the MathJax menu insteid.",
          NoOriginalForm: "Naw oreeginal form available",
          Close: "Claise",
          EqSource: "MathJax Equation Soorce"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/sco/MathMenu.js");
