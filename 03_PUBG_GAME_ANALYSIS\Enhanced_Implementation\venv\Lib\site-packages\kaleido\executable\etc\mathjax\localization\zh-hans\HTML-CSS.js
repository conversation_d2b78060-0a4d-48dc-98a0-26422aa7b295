/*************************************************************
 *
 *  MathJax/localization/zh-hans/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("zh-hans","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "\u52A0\u8F7Dweb\u5B57\u4F53%1",
          CantLoadWebFont: "Web\u5B57\u4F53%1\u65E0\u6CD5\u52A0\u8F7D",
          FirefoxCantLoadWebFont: "\u60A8\u5728\u4F7F\u7528\u706B\u72D0\u6D4F\u89C8\u5668\uFF0C\u5979\u4E0D\u652F\u6301\u4ECE\u8FDC\u7A0B\u4E3B\u673A\u4E0B\u8F7D\u4EFB\u4F55web\u5B57\u4F53",
          CantFindFontUsing: "\u65E0\u6CD5\u627E\u5230\u4F7F\u7528%1\u7684\u6709\u6548\u5B57\u4F53",
          WebFontsNotAvailable: "Web\u5B57\u4F53\u4E0D\u77E5\u4E3A\u4F55\u65E0\u6CD5\u4F7F\u7528\uFF1F\uFF01\u2014\u2014\u6211\u4EEC\u5C06\u4E3A\u9601\u4E0B\u8F6C\u6362\u4E3A\u56FE\u7247\u5B57\u4F53"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/zh-hans/HTML-CSS.js");
