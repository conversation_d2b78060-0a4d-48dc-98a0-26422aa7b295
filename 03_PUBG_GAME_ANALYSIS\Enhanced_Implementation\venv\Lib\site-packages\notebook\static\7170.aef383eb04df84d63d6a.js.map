{"version": 3, "file": "7170.aef383eb04df84d63d6a.js?v=aef383eb04df84d63d6a", "mappings": ";;;;;;;;;;AAAA;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,8CAA8C,MAAM;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ,oEAAoE;AACpE;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEO;AACP;AACA,0BAA0B,QAAQ,YAAY;AAC9C;AACA;AACA,GAAG;AACH;AACA;AACA,oBAAoB,uBAAuB,kBAAkB;AAC7D,oBAAoB;AACpB;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/shell.js"], "sourcesContent": ["var words = {};\nfunction define(style, dict) {\n  for(var i = 0; i < dict.length; i++) {\n    words[dict[i]] = style;\n  }\n};\n\nvar commonAtoms = [\"true\", \"false\"];\nvar commonKeywords = [\"if\", \"then\", \"do\", \"else\", \"elif\", \"while\", \"until\", \"for\", \"in\", \"esac\", \"fi\",\n                      \"fin\", \"fil\", \"done\", \"exit\", \"set\", \"unset\", \"export\", \"function\"];\nvar commonCommands = [\"ab\", \"awk\", \"bash\", \"beep\", \"cat\", \"cc\", \"cd\", \"chown\", \"chmod\", \"chroot\", \"clear\",\n                      \"cp\", \"curl\", \"cut\", \"diff\", \"echo\", \"find\", \"gawk\", \"gcc\", \"get\", \"git\", \"grep\", \"hg\", \"kill\", \"killall\",\n                      \"ln\", \"ls\", \"make\", \"mkdir\", \"openssl\", \"mv\", \"nc\", \"nl\", \"node\", \"npm\", \"ping\", \"ps\", \"restart\", \"rm\",\n                      \"rmdir\", \"sed\", \"service\", \"sh\", \"shopt\", \"shred\", \"source\", \"sort\", \"sleep\", \"ssh\", \"start\", \"stop\",\n                      \"su\", \"sudo\", \"svn\", \"tee\", \"telnet\", \"top\", \"touch\", \"vi\", \"vim\", \"wall\", \"wc\", \"wget\", \"who\", \"write\",\n                      \"yes\", \"zsh\"];\n\ndefine('atom', commonAtoms);\ndefine('keyword', commonKeywords);\ndefine('builtin', commonCommands);\n\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) return null;\n\n  var sol = stream.sol();\n  var ch = stream.next();\n\n  if (ch === '\\\\') {\n    stream.next();\n    return null;\n  }\n  if (ch === '\\'' || ch === '\"' || ch === '`') {\n    state.tokens.unshift(tokenString(ch, ch === \"`\" ? \"quote\" : \"string\"));\n    return tokenize(stream, state);\n  }\n  if (ch === '#') {\n    if (sol && stream.eat('!')) {\n      stream.skipToEnd();\n      return 'meta'; // 'comment'?\n    }\n    stream.skipToEnd();\n    return 'comment';\n  }\n  if (ch === '$') {\n    state.tokens.unshift(tokenDollar);\n    return tokenize(stream, state);\n  }\n  if (ch === '+' || ch === '=') {\n    return 'operator';\n  }\n  if (ch === '-') {\n    stream.eat('-');\n    stream.eatWhile(/\\w/);\n    return 'attribute';\n  }\n  if (ch == \"<\") {\n    if (stream.match(\"<<\")) return \"operator\"\n    var heredoc = stream.match(/^<-?\\s*(?:['\"]([^'\"]*)['\"]|([^'\"\\s]*))/)\n    if (heredoc) {\n      state.tokens.unshift(tokenHeredoc(heredoc[1] || heredoc[2]))\n      return 'string.special'\n    }\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/\\d/);\n    if(stream.eol() || !/\\w/.test(stream.peek())) {\n      return 'number';\n    }\n  }\n  stream.eatWhile(/[\\w-]/);\n  var cur = stream.current();\n  if (stream.peek() === '=' && /\\w+/.test(cur)) return 'def';\n  return words.hasOwnProperty(cur) ? words[cur] : null;\n}\n\nfunction tokenString(quote, style) {\n  var close = quote == \"(\" ? \")\" : quote == \"{\" ? \"}\" : quote\n  return function(stream, state) {\n    var next, escaped = false;\n    while ((next = stream.next()) != null) {\n      if (next === close && !escaped) {\n        state.tokens.shift();\n        break;\n      } else if (next === '$' && !escaped && quote !== \"'\" && stream.peek() != close) {\n        escaped = true;\n        stream.backUp(1);\n        state.tokens.unshift(tokenDollar);\n        break;\n      } else if (!escaped && quote !== close && next === quote) {\n        state.tokens.unshift(tokenString(quote, style))\n        return tokenize(stream, state)\n      } else if (!escaped && /['\"]/.test(next) && !/['\"]/.test(quote)) {\n        state.tokens.unshift(tokenStringStart(next, \"string\"));\n        stream.backUp(1);\n        break;\n      }\n      escaped = !escaped && next === '\\\\';\n    }\n    return style;\n  };\n};\n\nfunction tokenStringStart(quote, style) {\n  return function(stream, state) {\n    state.tokens[0] = tokenString(quote, style)\n    stream.next()\n    return tokenize(stream, state)\n  }\n}\n\nvar tokenDollar = function(stream, state) {\n  if (state.tokens.length > 1) stream.eat('$');\n  var ch = stream.next()\n  if (/['\"({]/.test(ch)) {\n    state.tokens[0] = tokenString(ch, ch == \"(\" ? \"quote\" : ch == \"{\" ? \"def\" : \"string\");\n    return tokenize(stream, state);\n  }\n  if (!/\\d/.test(ch)) stream.eatWhile(/\\w/);\n  state.tokens.shift();\n  return 'def';\n};\n\nfunction tokenHeredoc(delim) {\n  return function(stream, state) {\n    if (stream.sol() && stream.string == delim) state.tokens.shift()\n    stream.skipToEnd()\n    return \"string.special\"\n  }\n}\n\nfunction tokenize(stream, state) {\n  return (state.tokens[0] || tokenBase) (stream, state);\n};\n\nexport const shell = {\n  name: \"shell\",\n  startState: function() {return {tokens:[]};},\n  token: function(stream, state) {\n    return tokenize(stream, state);\n  },\n  languageData: {\n    autocomplete: commonAtoms.concat(commonKeywords, commonCommands),\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]},\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": [], "sourceRoot": ""}