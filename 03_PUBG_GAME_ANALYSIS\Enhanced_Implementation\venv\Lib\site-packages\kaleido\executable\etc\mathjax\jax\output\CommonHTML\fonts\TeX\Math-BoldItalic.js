/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/TeX/Math/BoldItalic/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

(function (CHTML) {

var font = 'MathJax_Math-BoldItalic';

CHTML.FONTDATA.FONTS[font] = {
  className: CHTML.FONTDATA.familyName(font),
  centerline: 255, ascent: 725, descent: 216,
  weight: 'bold',
  style: 'italic',
  skew: {
    0x41: 0.16,
    0x42: 0.0958,
    0x43: 0.0958,
    0x44: 0.0639,
    0x45: 0.0958,
    0x46: 0.0958,
    0x47: 0.0958,
    0x48: 0.0639,
    0x49: 0.128,
    0x4A: 0.192,
    0x4B: 0.0639,
    0x4C: 0.0319,
    0x4D: 0.0958,
    0x4E: 0.0958,
    0x4F: 0.0958,
    0x50: 0.0958,
    0x51: 0.0958,
    0x52: 0.0958,
    0x53: 0.0958,
    0x54: 0.0958,
    0x55: 0.0319,
    0x58: 0.0958,
    0x5A: 0.0958,
    0x63: 0.0639,
    0x64: 0.192,
    0x65: 0.0639,
    0x66: 0.192,
    0x67: 0.0319,
    0x68: -0.0319,
    0x6C: 0.0958,
    0x6F: 0.0639,
    0x70: 0.0958,
    0x71: 0.0958,
    0x72: 0.0639,
    0x73: 0.0639,
    0x74: 0.0958,
    0x75: 0.0319,
    0x76: 0.0319,
    0x77: 0.0958,
    0x78: 0.0319,
    0x79: 0.0639,
    0x7A: 0.0639,
    0x393: 0.0958,
    0x394: 0.192,
    0x398: 0.0958,
    0x39B: 0.192,
    0x39E: 0.0958,
    0x3A0: 0.0639,
    0x3A3: 0.0958,
    0x3A5: 0.0639,
    0x3A6: 0.0958,
    0x3A8: 0.0639,
    0x3A9: 0.0958,
    0x3B1: 0.0319,
    0x3B2: 0.0958,
    0x3B4: 0.0639,
    0x3B5: 0.0958,
    0x3B6: 0.0958,
    0x3B7: 0.0639,
    0x3B8: 0.0958,
    0x3B9: 0.0639,
    0x3BC: 0.0319,
    0x3BD: 0.0319,
    0x3BE: 0.128,
    0x3BF: 0.0639,
    0x3C1: 0.0958,
    0x3C2: 0.0958,
    0x3C4: 0.0319,
    0x3C5: 0.0319,
    0x3C6: 0.0958,
    0x3C7: 0.0639,
    0x3C8: 0.128,
    0x3D1: 0.0958,
    0x3D5: 0.0958,
    0x3F1: 0.0958,
    0x3F5: 0.0639
  },
  0x20: [0,0,250,0,0],               // SPACE
  0x2F: [711,210,894,160,733],       // SOLIDUS
  0x41: [711,0,869,45,839],          // LATIN CAPITAL LETTER A
  0x42: [686,0,866,43,853],          // LATIN CAPITAL LETTER B
  0x43: [703,17,817,55,855],         // LATIN CAPITAL LETTER C
  0x44: [686,0,938,43,914],          // LATIN CAPITAL LETTER D
  0x45: [680,0,810,43,825],          // LATIN CAPITAL LETTER E
  0x46: [680,0,689,43,809],          // LATIN CAPITAL LETTER F
  0x47: [703,16,887,56,854],         // LATIN CAPITAL LETTER G
  0x48: [686,0,982,43,1027],         // LATIN CAPITAL LETTER H
  0x49: [686,0,511,30,573],          // LATIN CAPITAL LETTER I
  0x4A: [686,17,631,42,694],         // LATIN CAPITAL LETTER J
  0x4B: [686,0,971,43,1003],         // LATIN CAPITAL LETTER K
  0x4C: [686,0,756,43,711],          // LATIN CAPITAL LETTER L
  0x4D: [686,0,1142,43,1219],        // LATIN CAPITAL LETTER M
  0x4E: [686,0,950,43,1027],         // LATIN CAPITAL LETTER N
  0x4F: [703,17,837,53,815],         // LATIN CAPITAL LETTER O
  0x50: [686,0,723,43,847],          // LATIN CAPITAL LETTER P
  0x51: [703,194,869,53,815],        // LATIN CAPITAL LETTER Q
  0x52: [686,17,872,43,881],         // LATIN CAPITAL LETTER R
  0x53: [703,17,693,63,714],         // LATIN CAPITAL LETTER S
  0x54: [675,0,637,22,772],          // LATIN CAPITAL LETTER T
  0x55: [686,16,800,63,877],         // LATIN CAPITAL LETTER U
  0x56: [686,16,678,62,886],         // LATIN CAPITAL LETTER V
  0x57: [686,17,1093,61,1207],       // LATIN CAPITAL LETTER W
  0x58: [686,0,947,38,953],          // LATIN CAPITAL LETTER X
  0x59: [686,0,675,40,876],          // LATIN CAPITAL LETTER Y
  0x5A: [686,0,773,68,805],          // LATIN CAPITAL LETTER Z
  0x61: [452,8,633,38,607],          // LATIN SMALL LETTER A
  0x62: [694,8,521,45,513],          // LATIN SMALL LETTER B
  0x63: [451,8,513,40,509],          // LATIN SMALL LETTER C
  0x64: [694,8,610,38,612],          // LATIN SMALL LETTER D
  0x65: [452,8,554,42,509],          // LATIN SMALL LETTER E
  0x66: [701,201,568,64,624],        // LATIN SMALL LETTER F
  0x67: [452,202,545,0,540],         // LATIN SMALL LETTER G
  0x68: [694,8,668,45,642],          // LATIN SMALL LETTER H
  0x69: [694,8,405,24,367],          // LATIN SMALL LETTER I
  0x6A: [694,202,471,-12,456],       // LATIN SMALL LETTER J
  0x6B: [694,8,604,45,578],          // LATIN SMALL LETTER K
  0x6C: [694,8,348,27,296],          // LATIN SMALL LETTER L
  0x6D: [452,8,1032,24,1006],        // LATIN SMALL LETTER M
  0x6E: [452,8,713,24,687],          // LATIN SMALL LETTER N
  0x6F: [452,8,585,39,576],          // LATIN SMALL LETTER O
  0x70: [452,194,601,-23,593],       // LATIN SMALL LETTER P
  0x71: [452,194,542,38,550],        // LATIN SMALL LETTER Q
  0x72: [452,8,529,24,500],          // LATIN SMALL LETTER R
  0x73: [451,8,531,57,476],          // LATIN SMALL LETTER S
  0x74: [643,7,415,21,387],          // LATIN SMALL LETTER T
  0x75: [452,8,681,24,655],          // LATIN SMALL LETTER U
  0x76: [453,8,567,24,540],          // LATIN SMALL LETTER V
  0x77: [453,8,831,24,796],          // LATIN SMALL LETTER W
  0x78: [452,8,659,43,599],          // LATIN SMALL LETTER X
  0x79: [452,202,590,24,587],        // LATIN SMALL LETTER Y
  0x7A: [452,8,555,34,539],          // LATIN SMALL LETTER Z
  0xA0: [0,0,250,0,0],               // NO-BREAK SPACE
  0x393: [680,0,657,43,777],         // GREEK CAPITAL LETTER GAMMA
  0x394: [711,0,958,59,904],         // GREEK CAPITAL LETTER DELTA
  0x398: [702,17,867,54,844],        // GREEK CAPITAL LETTER THETA
  0x39B: [711,0,806,44,776],         // GREEK CAPITAL LETTER LAMDA
  0x39E: [675,0,841,62,867],         // GREEK CAPITAL LETTER XI
  0x3A0: [680,0,982,43,1026],        // GREEK CAPITAL LETTER PI
  0x3A3: [686,0,885,69,902],         // GREEK CAPITAL LETTER SIGMA
  0x3A5: [703,0,671,32,802],         // GREEK CAPITAL LETTER UPSILON
  0x3A6: [686,0,767,29,737],         // GREEK CAPITAL LETTER PHI
  0x3A8: [686,0,714,22,790],         // GREEK CAPITAL LETTER PSI
  0x3A9: [703,0,879,93,886],         // GREEK CAPITAL LETTER OMEGA
  0x3B1: [452,8,761,39,712],         // GREEK SMALL LETTER ALPHA
  0x3B2: [701,194,660,28,637],       // GREEK SMALL LETTER BETA
  0x3B3: [451,211,590,5,617],        // GREEK SMALL LETTER GAMMA
  0x3B4: [725,8,522,39,513],         // GREEK SMALL LETTER DELTA
  0x3B5: [461,17,529,36,481],        // GREEK SMALL LETTER EPSILON
  0x3B6: [711,202,508,48,521],       // GREEK SMALL LETTER ZETA
  0x3B7: [452,211,600,24,600],       // GREEK SMALL LETTER ETA
  0x3B8: [702,8,562,40,554],         // GREEK SMALL LETTER THETA
  0x3B9: [452,8,412,38,386],         // GREEK SMALL LETTER IOTA
  0x3BA: [452,8,668,45,642],         // GREEK SMALL LETTER KAPPA
  0x3BB: [694,13,671,40,652],        // GREEK SMALL LETTER LAMDA
  0x3BC: [452,211,708,33,682],       // GREEK SMALL LETTER MU
  0x3BD: [452,2,577,38,608],         // GREEK SMALL LETTER NU
  0x3BE: [711,201,508,23,490],       // GREEK SMALL LETTER XI
  0x3BF: [452,8,585,39,576],         // GREEK SMALL LETTER OMICRON
  0x3C0: [444,8,682,23,674],         // GREEK SMALL LETTER PI
  0x3C1: [451,211,612,34,603],       // GREEK SMALL LETTER RHO
  0x3C2: [451,105,424,33,457],       // GREEK SMALL LETTER FINAL SIGMA
  0x3C3: [444,8,686,35,677],         // GREEK SMALL LETTER SIGMA
  0x3C4: [444,13,521,23,610],        // GREEK SMALL LETTER TAU
  0x3C5: [453,8,631,24,604],         // GREEK SMALL LETTER UPSILON
  0x3C6: [452,216,747,53,703],       // GREEK SMALL LETTER PHI
  0x3C7: [452,201,718,32,685],       // GREEK SMALL LETTER CHI
  0x3C8: [694,202,758,24,732],       // GREEK SMALL LETTER PSI
  0x3C9: [453,8,718,24,691],         // GREEK SMALL LETTER OMEGA
  0x3D1: [701,8,692,24,656],         // GREEK THETA SYMBOL
  0x3D5: [694,202,712,51,693],       // GREEK PHI SYMBOL
  0x3D6: [444,8,975,23,961],         // GREEK PI SYMBOL
  0x3F1: [451,194,612,75,603],       // GREEK RHO SYMBOL
  0x3F5: [444,7,483,44,450]          // GREEK LUNATE EPSILON SYMBOL
};

CHTML.fontLoaded("TeX/"+font.substr(8));

})(MathJax.OutputJax.CommonHTML);
