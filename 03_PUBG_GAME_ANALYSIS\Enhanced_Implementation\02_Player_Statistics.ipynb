{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📊 PUBG Player Statistics Analysis\n", "\n", "## 🎯 Overview\n", "Comprehensive analysis of individual player performance metrics including kill/death ratios, survival times, damage dealt, and ranking progression.\n", "\n", "## 📈 Key Metrics Analyzed\n", "- **Kill/Death Ratio (K/D)** - Combat effectiveness\n", "- **Average Damage per Match** - Consistent performance\n", "- **Survival Time Analysis** - Longevity patterns\n", "- **Ranking Progression** - Placement analysis\n", "- **Weapon Effectiveness** - Equipment performance\n", "- **Movement Patterns** - Positioning strategies\n", "\n", "## 🔧 Code Organization\n", "Each section is organized in **small columns** for easy understanding:\n", "- **Setup & Data Loading** (Column 1)\n", "- **Basic Statistics** (Column 2) \n", "- **Advanced Analysis** (Column 3)\n", "- **Visualizations** (Column 4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Column 1: Setup & Data Loading\n", "\n", "### Essential imports and data preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 1: ESSENTIAL IMPORTS\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting style\n", "%matplotlib inline\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"📦 PUBG Player Statistics Analysis - Setup Complete\")\n", "print(\"✅ All libraries imported successfully\")\n", "print(\"🎨 Plotting style configured\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 1: DATA LOADING FUNCTION\n", "def load_pubg_data(sample_size=50000):\n", "    \"\"\"\n", "    Load PUBG data with error handling and sampling\n", "    \"\"\"\n", "    print(\"📊 Loading PUBG data...\")\n", "    \n", "    # Try different file paths\n", "    possible_paths = [\n", "        '../data/pubg.csv',\n", "        '../../data/pubg.csv',\n", "        'data/pubg.csv',\n", "        'pubg.csv'\n", "    ]\n", "    \n", "    for path in possible_paths:\n", "        if Path(path).exists():\n", "            print(f\"📁 Found data at: {path}\")\n", "            try:\n", "                # Load sample for performance\n", "                data = pd.read_csv(path, nrows=sample_size)\n", "                print(f\"✅ Loaded {len(data):,} rows\")\n", "                print(f\"📋 Columns: {len(data.columns)}\")\n", "                return data\n", "            except Exception as e:\n", "                print(f\"❌ Error loading {path}: {e}\")\n", "                continue\n", "    \n", "    # Create sample data if file not found\n", "    print(\"🔧 Creating sample data for demonstration...\")\n", "    np.random.seed(42)\n", "    sample_data = pd.DataFrame({\n", "        'kills': np.random.poisson(2, sample_size),\n", "        'damageDealt': np.random.gamma(2, 100, sample_size),\n", "        'walkDistance': np.random.gamma(3, 500, sample_size),\n", "        'winPlacePerc': np.random.beta(2, 5, sample_size),\n", "        'assists': np.random.poisson(1, sample_size),\n", "        'heals': np.random.poisson(3, sample_size),\n", "        'boosts': np.random.poisson(2, sample_size),\n", "        'weaponsAcquired': np.random.poisson(4, sample_size),\n", "        'rideDistance': np.random.gamma(1, 200, sample_size),\n", "        'teamKills': np.random.poisson(5, sample_size)\n", "    })\n", "    print(f\"✅ Sample data created: {sample_data.shape}\")\n", "    return sample_data\n", "\n", "# Load the data\n", "pubg_data = load_pubg_data()\n", "print(f\"\\n🎯 Data loaded successfully: {pubg_data.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Column 2: Basic Player Statistics\n", "\n", "### Core performance metrics calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 2: BASIC STATISTICS CALCULATION\n", "print(\"📊 CALCULATING BASIC PLAYER STATISTICS\")\n", "print(\"=\" * 50)\n", "\n", "# Basic statistics for key columns\n", "key_stats = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc']\n", "\n", "basic_stats = {}\n", "for col in key_stats:\n", "    if col in pubg_data.columns:\n", "        basic_stats[col] = {\n", "            'mean': pubg_data[col].mean(),\n", "            'median': pubg_data[col].median(),\n", "            'std': pubg_data[col].std(),\n", "            'min': pubg_data[col].min(),\n", "            'max': pubg_data[col].max(),\n", "            'q25': pubg_data[col].quantile(0.25),\n", "            'q75': pubg_data[col].quantile(0.75)\n", "        }\n", "\n", "# Display basic statistics\n", "for metric, stats in basic_stats.items():\n", "    print(f\"\\n📈 {metric.upper()}:\")\n", "    print(f\"   Mean: {stats['mean']:.2f}\")\n", "    print(f\"   Median: {stats['median']:.2f}\")\n", "    print(f\"   Std Dev: {stats['std']:.2f}\")\n", "    print(f\"   Range: {stats['min']:.2f} - {stats['max']:.2f}\")\n", "\n", "print(\"\\n✅ Basic statistics calculated successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 2: PLAYER PERFORMANCE CATEGORIES\n", "print(\"🎯 CREATING PLAYER PERFORMANCE CATEGORIES\")\n", "print(\"=\" * 50)\n", "\n", "# Create performance categories based on kills\n", "if 'kills' in pubg_data.columns:\n", "    def categorize_player(kills):\n", "        if kills == 0:\n", "            return 'Passive (0 kills)'\n", "        elif kills <= 2:\n", "            return 'Casual (1-2 kills)'\n", "        elif kills <= 5:\n", "            return 'Active (3-5 kills)'\n", "        elif kills <= 10:\n", "            return 'Aggressive (6-10 kills)'\n", "        else:\n", "            return 'Elite (11+ kills)'\n", "    \n", "    pubg_data['player_category'] = pubg_data['kills'].apply(categorize_player)\n", "    \n", "    # Count players in each category\n", "    category_counts = pubg_data['player_category'].value_counts()\n", "    \n", "    print(\"📊 Player Categories:\")\n", "    for category, count in category_counts.items():\n", "        percentage = (count / len(pubg_data)) * 100\n", "        print(f\"   {category}: {count:,} players ({percentage:.1f}%)\")\n", "\n", "# Create win rate categories\n", "if 'winPlacePerc' in pubg_data.columns:\n", "    def categorize_winner(win_perc):\n", "        if win_perc >= 0.9:\n", "            return 'Top 10%'\n", "        elif win_perc >= 0.75:\n", "            return 'Top 25%'\n", "        elif win_perc >= 0.5:\n", "            return 'Top 50%'\n", "        else:\n", "            return 'Bottom 50%'\n", "    \n", "    pubg_data['win_category'] = pubg_data['winPlacePerc'].apply(categorize_winner)\n", "    \n", "    win_counts = pubg_data['win_category'].value_counts()\n", "    print(\"\\n🏆 Win Categories:\")\n", "    for category, count in win_counts.items():\n", "        percentage = (count / len(pubg_data)) * 100\n", "        print(f\"   {category}: {count:,} players ({percentage:.1f}%)\")\n", "\n", "print(\"\\n✅ Player categories created successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Column 3: Advanced Analysis\n", "\n", "### Performance correlations and advanced metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 3: CORRELATION ANALYSIS\n", "print(\"🔍 ADVANCED CORRELATION ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Calculate correlations between key metrics\n", "correlation_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc']\n", "available_cols = [col for col in correlation_cols if col in pubg_data.columns]\n", "\n", "if len(available_cols) >= 2:\n", "    correlation_matrix = pubg_data[available_cols].corr()\n", "    \n", "    print(\"📈 Correlation Matrix:\")\n", "    print(correlation_matrix.round(3))\n", "    \n", "    # Find strongest correlations\n", "    print(\"\\n🎯 Strongest Correlations:\")\n", "    for i in range(len(available_cols)):\n", "        for j in range(i+1, len(available_cols)):\n", "            col1, col2 = available_cols[i], available_cols[j]\n", "            corr_value = correlation_matrix.loc[col1, col2]\n", "            print(f\"   {col1} ↔ {col2}: {corr_value:.3f}\")\n", "\n", "print(\"\\n✅ Correlation analysis completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 3: P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> EFFICIENCY METRICS\n", "print(\"⚡ CALCULATING PERFORMANCE EFFICIENCY METRICS\")\n", "print(\"=\" * 50)\n", "\n", "# Calculate efficiency metrics\n", "if 'kills' in pubg_data.columns and 'damageDealt' in pubg_data.columns:\n", "    # Damage per kill (efficiency)\n", "    pubg_data['damage_per_kill'] = pubg_data['damageDealt'] / (pubg_data['kills'] + 1)  # +1 to avoid division by zero\n", "    \n", "    print(\"💥 Damage Efficiency:\")\n", "    print(f\"   Average damage per kill: {pubg_data['damage_per_kill'].mean():.2f}\")\n", "    print(f\"   Median damage per kill: {pubg_data['damage_per_kill'].median():.2f}\")\n", "\n", "if 'walkDistance' in pubg_data.columns and 'winPlacePerc' in pubg_data.columns:\n", "    # Movement efficiency (win placement per distance)\n", "    pubg_data['movement_efficiency'] = pubg_data['winPlacePerc'] / (pubg_data['walkDistance'] + 1)\n", "    \n", "    print(\"\\n🚶 Movement Efficiency:\")\n", "    print(f\"   Average movement efficiency: {pubg_data['movement_efficiency'].mean():.6f}\")\n", "    print(f\"   Median movement efficiency: {pubg_data['movement_efficiency'].median():.6f}\")\n", "\n", "if 'assists' in pubg_data.columns and 'kills' in pubg_data.columns:\n", "    # Team play ratio\n", "    pubg_data['team_play_ratio'] = pubg_data['assists'] / (pubg_data['kills'] + pubg_data['assists'] + 1)\n", "    \n", "    print(\"\\n🤝 Team Play Analysis:\")\n", "    print(f\"   Average team play ratio: {pubg_data['team_play_ratio'].mean():.3f}\")\n", "    print(f\"   Median team play ratio: {pubg_data['team_play_ratio'].median():.3f}\")\n", "\n", "print(\"\\n✅ Efficiency metrics calculated!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 Column 4: Visualizations\n", "\n", "### Small column visualizations for easy understanding"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 4: BASIC DISTRIBUTION VISUALIZATIONS\n", "print(\"📊 CREATING PLAYER STATISTICS VISUALIZATIONS\")\n", "print(\"=\" * 50)\n", "\n", "# Create 2x2 subplot for basic distributions\n", "fig, axes = plt.subplots(2, 2, figsize=(14, 10))\n", "fig.suptitle('PUBG Player Statistics - Distribution Analysis', fontsize=16, fontweight='bold')\n", "\n", "# 1. Kills Distribution (Top-left)\n", "if 'kills' in pubg_data.columns:\n", "    axes[0, 0].hist(pubg_data['kills'], bins=range(0, min(pubg_data['kills'].max() + 2, 21)), \n", "                   alpha=0.7, color='skyblue', edgecolor='black')\n", "    axes[0, 0].axvline(pubg_data['kills'].mean(), color='red', linestyle='--', linewidth=2, \n", "                      label=f'Mean: {pubg_data[\"kills\"].mean():.2f}')\n", "    axes[0, 0].set_title('Kills Distribution', fontweight='bold')\n", "    axes[0, 0].set_xlabel('Number of Kills')\n", "    axes[0, 0].set_ylabel('Frequency')\n", "    axes[0, 0].legend()\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# 2. Damage Distribution (Top-right)\n", "if 'damageDealt' in pubg_data.columns:\n", "    axes[0, 1].hist(pubg_data['damageDealt'], bins=40, alpha=0.7, color='lightcoral', \n", "                   edgecolor='black')\n", "    axes[0, 1].axvline(pubg_data['damageDealt'].mean(), color='red', linestyle='--', linewidth=2, \n", "                      label=f'Mean: {pubg_data[\"damageDealt\"].mean():.0f}')\n", "    axes[0, 1].set_title('Damage Dealt Distribution', fontweight='bold')\n", "    axes[0, 1].set_xlabel('Damage Dealt')\n", "    axes[0, 1].set_ylabel('Frequency')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# 3. Walk Distance Distribution (Bottom-left)\n", "if 'walkDistance' in pubg_data.columns:\n", "    axes[1, 0].hist(pubg_data['walkDistance'], bins=40, alpha=0.7, color='lightgreen', \n", "                   edgecolor='black')\n", "    axes[1, 0].axvline(pubg_data['walkDistance'].mean(), color='red', linestyle='--', linewidth=2, \n", "                      label=f'Mean: {pubg_data[\"walkDistance\"].mean():.0f}m')\n", "    axes[1, 0].set_title('Walk Distance Distribution', fontweight='bold')\n", "    axes[1, 0].set_xlabel('Walk Distance (meters)')\n", "    axes[1, 0].set_ylabel('Frequency')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# 4. Win Placement Distribution (Bottom-right)\n", "if 'winPlacePerc' in pubg_data.columns:\n", "    axes[1, 1].hist(pubg_data['winPlacePerc'], bins=40, alpha=0.7, color='gold', \n", "                   edgecolor='black')\n", "    axes[1, 1].axvline(pubg_data['winPlacePerc'].mean(), color='red', linestyle='--', linewidth=2, \n", "                      label=f'Mean: {pubg_data[\"winPlacePerc\"].mean():.3f}')\n", "    axes[1, 1].set_title('Win Placement Percentile', fontweight='bold')\n", "    axes[1, 1].set_xlabel('Win Placement Percentile')\n", "    axes[1, 1].set_ylabel('Frequency')\n", "    axes[1, 1].legend()\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Distribution visualizations completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 4: PLAYER CATEGORY ANALYSIS VISUALIZATION\n", "print(\"🎯 PLAYER CATEGORY ANALYSIS VISUALIZATION\")\n", "print(\"=\" * 50)\n", "\n", "# Create 1x2 subplot for category analysis\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "fig.suptitle('PUBG Player Categories Analysis', fontsize=16, fontweight='bold')\n", "\n", "# 1. Player Performance Categories\n", "if 'player_category' in pubg_data.columns:\n", "    category_counts = pubg_data['player_category'].value_counts()\n", "    colors = ['lightcoral', 'skyblue', 'lightgreen', 'gold', 'orange']\n", "    \n", "    bars = axes[0].bar(range(len(category_counts)), category_counts.values, \n", "                      color=colors[:len(category_counts)], alpha=0.7, edgecolor='black')\n", "    \n", "    axes[0].set_title('Player Performance Categories', fontweight='bold')\n", "    axes[0].set_xlabel('Player Categories')\n", "    axes[0].set_ylabel('Number of Players')\n", "    axes[0].set_xticks(range(len(category_counts)))\n", "    axes[0].set_xticklabels(category_counts.index, rotation=45, ha='right')\n", "    axes[0].grid(True, alpha=0.3, axis='y')\n", "    \n", "    # Add percentage labels\n", "    total = category_counts.sum()\n", "    for bar, count in zip(bars, category_counts.values):\n", "        percentage = (count/total)*100\n", "        axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(category_counts.values)*0.01,\n", "                    f'{percentage:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "# 2. Win Categories\n", "if 'win_category' in pubg_data.columns:\n", "    win_counts = pubg_data['win_category'].value_counts()\n", "    colors = ['gold', 'silver', 'lightblue', 'lightcoral']\n", "    \n", "    bars = axes[1].bar(range(len(win_counts)), win_counts.values, \n", "                      color=colors[:len(win_counts)], alpha=0.7, edgecolor='black')\n", "    \n", "    axes[1].set_title('Win Placement Categories', fontweight='bold')\n", "    axes[1].set_xlabel('Win Categories')\n", "    axes[1].set_ylabel('Number of Players')\n", "    axes[1].set_xticks(range(len(win_counts)))\n", "    axes[1].set_xticklabels(win_counts.index, rotation=45, ha='right')\n", "    axes[1].grid(True, alpha=0.3, axis='y')\n", "    \n", "    # Add percentage labels\n", "    total = win_counts.sum()\n", "    for bar, count in zip(bars, win_counts.values):\n", "        percentage = (count/total)*100\n", "        axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(win_counts.values)*0.01,\n", "                    f'{percentage:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Category analysis visualizations completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 4: CORRELATION HEATMAP\n", "print(\"🔥 CORRELATION HEATMAP VISUALIZATION\")\n", "print(\"=\" * 50)\n", "\n", "# Create correlation heatmap\n", "correlation_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc']\n", "available_cols = [col for col in correlation_cols if col in pubg_data.columns]\n", "\n", "if len(available_cols) >= 2:\n", "    plt.figure(figsize=(10, 8))\n", "    \n", "    correlation_matrix = pubg_data[available_cols].corr()\n", "    \n", "    # Create heatmap\n", "    sns.heatmap(correlation_matrix, \n", "                annot=True, \n", "                cmap='RdYlBu_r', \n", "                center=0,\n", "                square=True,\n", "                fmt='.3f',\n", "                cbar_kws={'label': 'Correlation Coefficient'})\n", "    \n", "    plt.title('PUBG Player Statistics - Correlation Matrix', fontsize=16, fontweight='bold', pad=20)\n", "    plt.xlabel('Metrics', fontweight='bold')\n", "    plt.ylabel('Metrics', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"✅ Correlation heatmap completed!\")\n", "else:\n", "    print(\"⚠️ Not enough columns for correlation analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Summary & Key Insights\n", "\n", "### Final analysis summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FINAL SUMMARY\n", "print(\"📋 PUBG PLAYER STATISTICS ANALYSIS - SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"📊 Dataset Overview:\")\n", "print(f\"   Total Players Analyzed: {len(pubg_data):,}\")\n", "print(f\"   Total Metrics: {len(pubg_data.columns)}\")\n", "print(f\"   Memory Usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "\n", "if 'kills' in pubg_data.columns:\n", "    print(f\"\\n🎯 Key Performance Insights:\")\n", "    print(f\"   Average Kills per Player: {pubg_data['kills'].mean():.2f}\")\n", "    print(f\"   Players with 0 kills: {len(pubg_data[pubg_data['kills'] == 0]):,} ({len(pubg_data[pubg_data['kills'] == 0])/len(pubg_data)*100:.1f}%)\")\n", "    print(f\"   Elite players (5+ kills): {len(pubg_data[pubg_data['kills'] >= 5]):,} ({len(pubg_data[pubg_data['kills'] >= 5])/len(pubg_data)*100:.1f}%)\")\n", "\n", "if 'winPlacePerc' in pubg_data.columns:\n", "    print(f\"\\n🏆 Win Performance:\")\n", "    print(f\"   Average Win Placement: {pubg_data['winPlacePerc'].mean():.3f}\")\n", "    print(f\"   Top 10% finishers: {len(pubg_data[pubg_data['winPlacePerc'] >= 0.9]):,} ({len(pubg_data[pubg_data['winPlacePerc'] >= 0.9])/len(pubg_data)*100:.1f}%)\")\n", "\n", "if 'damageDealt' in pubg_data.columns:\n", "    print(f\"\\n💥 Combat Statistics:\")\n", "    print(f\"   Average Damage per Player: {pubg_data['damageDealt'].mean():.0f}\")\n", "    print(f\"   High damage players (>500): {len(pubg_data[pubg_data['damageDealt'] > 500]):,} ({len(pubg_data[pubg_data['damageDealt'] > 500])/len(pubg_data)*100:.1f}%)\")\n", "\n", "print(f\"\\n✅ Analysis Complete!\")\n", "print(f\"📊 All visualizations display inline\")\n", "print(f\"🎯 Player statistics successfully analyzed\")\n", "print(f\"📈 Performance categories identified\")\n", "print(f\"🔍 Correlations and patterns discovered\")\n", "\n", "print(f\"\\n🎉 PLAYER STATISTICS ANALYSIS COMPLETED SUCCESSFULLY!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}