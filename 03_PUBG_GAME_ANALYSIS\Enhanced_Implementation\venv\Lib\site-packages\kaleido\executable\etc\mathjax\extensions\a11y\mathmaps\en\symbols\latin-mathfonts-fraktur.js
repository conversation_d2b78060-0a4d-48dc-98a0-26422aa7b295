[{"locale": "en"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital a", "short": "fraktur cap a"}, "mathspeak": {"default": "fraktur upper A"}}, "key": "1D504"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital b", "short": "fraktur cap b"}, "mathspeak": {"default": "fraktur upper B"}}, "key": "1D505"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital c", "short": "fraktur cap c"}, "mathspeak": {"default": "fraktur upper C"}}, "key": "212D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital d", "short": "fraktur cap d"}, "mathspeak": {"default": "fraktur upper D"}}, "key": "1D507"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital e", "short": "fraktur cap e"}, "mathspeak": {"default": "fraktur upper E"}}, "key": "1D508"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital f", "short": "fraktur cap f"}, "mathspeak": {"default": "fraktur upper F"}}, "key": "1D509"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital g", "short": "fraktur cap g"}, "mathspeak": {"default": "fraktur upper G"}}, "key": "1D50A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital h", "short": "fraktur cap h"}, "mathspeak": {"default": "fraktur upper H"}}, "key": "210C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital i", "short": "fraktur cap i"}, "mathspeak": {"default": "fraktur upper I"}}, "key": "2111"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital j", "short": "fraktur cap j"}, "mathspeak": {"default": "fraktur upper J"}}, "key": "1D50D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital k", "short": "fraktur cap k"}, "mathspeak": {"default": "fraktur upper K"}}, "key": "1D50E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital l", "short": "fraktur cap l"}, "mathspeak": {"default": "fraktur upper L"}}, "key": "1D50F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital m", "short": "fraktur cap m"}, "mathspeak": {"default": "fraktur upper M"}}, "key": "1D510"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital n", "short": "fraktur cap n"}, "mathspeak": {"default": "fraktur upper N"}}, "key": "1D511"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital o", "short": "fraktur cap o"}, "mathspeak": {"default": "fraktur upper O"}}, "key": "1D512"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital p", "short": "fraktur cap p"}, "mathspeak": {"default": "fraktur upper P"}}, "key": "1D513"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital q", "short": "fraktur cap q"}, "mathspeak": {"default": "fraktur upper Q"}}, "key": "1D514"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital r", "short": "fraktur cap r"}, "mathspeak": {"default": "fraktur upper R"}}, "key": "211C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital s", "short": "fraktur cap s"}, "mathspeak": {"default": "fraktur upper S"}}, "key": "1D516"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital t", "short": "fraktur cap t"}, "mathspeak": {"default": "fraktur upper T"}}, "key": "1D517"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital u", "short": "fraktur cap u"}, "mathspeak": {"default": "fraktur upper U"}}, "key": "1D518"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital v", "short": "fraktur cap v"}, "mathspeak": {"default": "fraktur upper V"}}, "key": "1D519"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital w", "short": "fraktur cap w"}, "mathspeak": {"default": "fraktur upper W"}}, "key": "1D51A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital x", "short": "fraktur cap x"}, "mathspeak": {"default": "fraktur upper X"}}, "key": "1D51B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital y", "short": "fraktur cap y"}, "mathspeak": {"default": "fraktur upper Y"}}, "key": "1D51C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "fraktur capital z", "short": "fraktur cap z"}, "mathspeak": {"default": "fraktur upper Z"}}, "key": "2128"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small a", "short": "fraktur a"}}, "key": "1D51E"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small b", "short": "fraktur b"}}, "key": "1D51F"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small c", "short": "fraktur c"}}, "key": "1D520"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small d", "short": "fraktur d"}}, "key": "1D521"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small e", "short": "fraktur e"}}, "key": "1D522"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small f", "short": "fraktur f"}}, "key": "1D523"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small g", "short": "fraktur g"}}, "key": "1D524"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small h", "short": "fraktur h"}}, "key": "1D525"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small i", "short": "fraktur i"}}, "key": "1D526"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small j", "short": "fraktur j"}}, "key": "1D527"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small k", "short": "fraktur k"}}, "key": "1D528"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small l", "short": "fraktur l"}}, "key": "1D529"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small m", "short": "fraktur m"}}, "key": "1D52A"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small n", "short": "fraktur n"}}, "key": "1D52B"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small o", "short": "fraktur o"}}, "key": "1D52C"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small p", "short": "fraktur p"}}, "key": "1D52D"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small q", "short": "fraktur q"}}, "key": "1D52E"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small r", "short": "fraktur r"}}, "key": "1D52F"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small s", "short": "fraktur s"}}, "key": "1D530"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small t", "short": "fraktur t"}}, "key": "1D531"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small u", "short": "fraktur u"}}, "key": "1D532"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small v", "short": "fraktur v"}}, "key": "1D533"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small w", "short": "fraktur w"}}, "key": "1D534"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small x", "short": "fraktur x"}}, "key": "1D535"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small y", "short": "fraktur y"}}, "key": "1D536"}, {"category": "Ll", "mappings": {"default": {"default": "fraktur small z", "short": "fraktur z"}}, "key": "1D537"}]