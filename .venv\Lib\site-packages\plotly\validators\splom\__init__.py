import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._yhoverformat import <PERSON>hoverformatValidator
    from ._yaxes import Ya<PERSON>Validator
    from ._xhoverformat import XhoverformatValidator
    from ._xaxes import <PERSON>ax<PERSON>V<PERSON><PERSON><PERSON>
    from ._visible import VisibleValidator
    from ._unselected import UnselectedValidator
    from ._uirevision import UirevisionValidator
    from ._uid import UidValidator
    from ._textsrc import TextsrcValidator
    from ._text import TextValidator
    from ._stream import StreamValidator
    from ._showupperhalf import ShowupperhalfValidator
    from ._showlowerhalf import ShowlowerhalfValidator
    from ._showlegend import ShowlegendValidator
    from ._selectedpoints import Selected<PERSON><PERSON><PERSON><PERSON><PERSON>
    from ._selected import SelectedValidator
    from ._opacity import OpacityValidator
    from ._name import NameValidator
    from ._metasrc import MetasrcValidator
    from ._meta import MetaValidator
    from ._marker import MarkerValidator
    from ._legendwidth import LegendwidthValidator
    from ._legendrank import LegendrankValidator
    from ._legendgrouptitle import LegendgrouptitleVali<PERSON><PERSON>
    from ._legendgroup import LegendgroupValidator
    from ._legend import LegendVali<PERSON><PERSON>
    from ._idssrc import IdssrcValidator
    from ._ids import IdsValidator
    from ._hovertextsrc import HovertextsrcValidator
    from ._hovertext import HovertextValidator
    from ._hovertemplatesrc import HovertemplatesrcValidator
    from ._hovertemplate import HovertemplateValidator
    from ._hoverlabel import HoverlabelValidator
    from ._hoverinfosrc import HoverinfosrcValidator
    from ._hoverinfo import HoverinfoValidator
    from ._dimensiondefaults import DimensiondefaultsValidator
    from ._dimensions import DimensionsValidator
    from ._diagonal import DiagonalValidator
    from ._customdatasrc import CustomdatasrcValidator
    from ._customdata import CustomdataValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._yhoverformat.YhoverformatValidator",
            "._yaxes.YaxesValidator",
            "._xhoverformat.XhoverformatValidator",
            "._xaxes.XaxesValidator",
            "._visible.VisibleValidator",
            "._unselected.UnselectedValidator",
            "._uirevision.UirevisionValidator",
            "._uid.UidValidator",
            "._textsrc.TextsrcValidator",
            "._text.TextValidator",
            "._stream.StreamValidator",
            "._showupperhalf.ShowupperhalfValidator",
            "._showlowerhalf.ShowlowerhalfValidator",
            "._showlegend.ShowlegendValidator",
            "._selectedpoints.SelectedpointsValidator",
            "._selected.SelectedValidator",
            "._opacity.OpacityValidator",
            "._name.NameValidator",
            "._metasrc.MetasrcValidator",
            "._meta.MetaValidator",
            "._marker.MarkerValidator",
            "._legendwidth.LegendwidthValidator",
            "._legendrank.LegendrankValidator",
            "._legendgrouptitle.LegendgrouptitleValidator",
            "._legendgroup.LegendgroupValidator",
            "._legend.LegendValidator",
            "._idssrc.IdssrcValidator",
            "._ids.IdsValidator",
            "._hovertextsrc.HovertextsrcValidator",
            "._hovertext.HovertextValidator",
            "._hovertemplatesrc.HovertemplatesrcValidator",
            "._hovertemplate.HovertemplateValidator",
            "._hoverlabel.HoverlabelValidator",
            "._hoverinfosrc.HoverinfosrcValidator",
            "._hoverinfo.HoverinfoValidator",
            "._dimensiondefaults.DimensiondefaultsValidator",
            "._dimensions.DimensionsValidator",
            "._diagonal.DiagonalValidator",
            "._customdatasrc.CustomdatasrcValidator",
            "._customdata.CustomdataValidator",
        ],
    )
