/*! For license information please see 8786.a2bc3dfc1ea13c04ba94.js.LICENSE.txt */
"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[8786],{76405:(e,t,r)=>{r.d(t,{A:()=>Up});function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function n(e){if(Array.isArray(e))return e}function i(e){if(Array.isArray(e))return a(e)}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||false,a.configurable=true,"value"in a&&(a.writable=true),Object.defineProperty(e,m(a.key),a)}}function l(e,t,r){return t&&s(e.prototype,t),Object.defineProperty(e,"prototype",{writable:false}),e}function u(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=x(e))||t){r&&(e=r);var a=0,n=function(){};return{s:n,n:function(){return a>=e.length?{done:true}:{done:false,value:e[a++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=true,s=false;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=true,i=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw i}}}}function v(e,t,r){return(t=m(t))in e?Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true}):e[t]=r,e}function f(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function c(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,n,i,o,s=[],l=true,u=false;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=i.call(r)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){u=true,n=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw n}}return s}}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e,t){return n(e)||c(e,t)||x(e,t)||d()}function g(e){return i(e)||f(e)||x(e)||h()}function y(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(undefined!==r){var a=r.call(e,t);if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function m(e){var t=y(e,"string");return"symbol"==typeof t?t:t+""}function b(e){"@babel/helpers - typeof";return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function x(e,t){if(e){if("string"==typeof e)return a(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a(e,t):undefined}}var w=typeof window==="undefined"?null:window;var E=w?w.navigator:null;w?w.document:null;var T=b("");var k=b({});var C=b((function(){}));var P=typeof HTMLElement==="undefined"?"undefined":b(HTMLElement);var S=function e(t){return t&&t.instanceString&&B(t.instanceString)?t.instanceString():null};var D=function e(t){return t!=null&&b(t)==T};var B=function e(t){return t!=null&&b(t)===C};var A=function e(t){return!L(t)&&(Array.isArray?Array.isArray(t):t!=null&&t instanceof Array)};var _=function e(t){return t!=null&&b(t)===k&&!A(t)&&t.constructor===Object};var M=function e(t){return t!=null&&b(t)===k};var I=function e(t){return t!=null&&b(t)===b(1)&&!isNaN(t)};var R=function e(t){return I(t)&&Math.floor(t)===t};var N=function e(t){if("undefined"===P){return undefined}else{return null!=t&&t instanceof HTMLElement}};var L=function e(t){return O(t)||z(t)};var O=function e(t){return S(t)==="collection"&&t._private.single};var z=function e(t){return S(t)==="collection"&&!t._private.single};var F=function e(t){return S(t)==="core"};var V=function e(t){return S(t)==="stylesheet"};var j=function e(t){return S(t)==="event"};var X=function e(t){if(t===undefined||t===null){return true}else if(t===""||t.match(/^\s+$/)){return true}return false};var Y=function e(t){if(typeof HTMLElement==="undefined"){return false}else{return t instanceof HTMLElement}};var q=function e(t){return _(t)&&I(t.x1)&&I(t.x2)&&I(t.y1)&&I(t.y2)};var W=function e(t){return M(t)&&B(t.then)};var U=function e(){return E&&E.userAgent.match(/msie|trident|edge/i)};var G=function e(t,r){if(!r){r=function e(){if(arguments.length===1){return arguments[0]}else if(arguments.length===0){return"undefined"}var t=[];for(var r=0;r<arguments.length;r++){t.push(arguments[r])}return t.join("$")}}var a=function e(){var n=this;var i=arguments;var o;var s=r.apply(n,i);var l=a.cache;if(!(o=l[s])){o=l[s]=t.apply(n,i)}return o};a.cache={};return a};var H=G((function(e){return e.replace(/([A-Z])/g,(function(e){return"-"+e.toLowerCase()}))}));var K=G((function(e){return e.replace(/(-\w)/g,(function(e){return e[1].toUpperCase()}))}));var Z=G((function(e,t){return e+t[0].toUpperCase()+t.substring(1)}),(function(e,t){return e+"$"+t}));var $=function e(t){if(X(t)){return t}return t.charAt(0).toUpperCase()+t.substring(1)};var Q="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))";var J="rgb[a]?\\(("+Q+"[%]?)\\s*,\\s*("+Q+"[%]?)\\s*,\\s*("+Q+"[%]?)(?:\\s*,\\s*("+Q+"))?\\)";var ee="rgb[a]?\\((?:"+Q+"[%]?)\\s*,\\s*(?:"+Q+"[%]?)\\s*,\\s*(?:"+Q+"[%]?)(?:\\s*,\\s*(?:"+Q+"))?\\)";var te="hsl[a]?\\(("+Q+")\\s*,\\s*("+Q+"[%])\\s*,\\s*("+Q+"[%])(?:\\s*,\\s*("+Q+"))?\\)";var re="hsl[a]?\\((?:"+Q+")\\s*,\\s*(?:"+Q+"[%])\\s*,\\s*(?:"+Q+"[%])(?:\\s*,\\s*(?:"+Q+"))?\\)";var ae="\\#[0-9a-fA-F]{3}";var ne="\\#[0-9a-fA-F]{6}";var ie=function e(t,r){if(t<r){return-1}else if(t>r){return 1}else{return 0}};var oe=function e(t,r){return-1*ie(t,r)};var se=Object.assign!=null?Object.assign.bind(Object):function(e){var t=arguments;for(var r=1;r<t.length;r++){var a=t[r];if(a==null){continue}var n=Object.keys(a);for(var i=0;i<n.length;i++){var o=n[i];e[o]=a[o]}}return e};var le=function e(t){if(!(t.length===4||t.length===7)||t[0]!=="#"){return}var r=t.length===4;var a,n,i;var o=16;if(r){a=parseInt(t[1]+t[1],o);n=parseInt(t[2]+t[2],o);i=parseInt(t[3]+t[3],o)}else{a=parseInt(t[1]+t[2],o);n=parseInt(t[3]+t[4],o);i=parseInt(t[5]+t[6],o)}return[a,n,i]};var ue=function e(t){var r;var a,n,i,o,s,l,u;function v(e,t,r){if(r<0)r+=1;if(r>1)r-=1;if(r<1/6)return e+(t-e)*6*r;if(r<1/2)return t;if(r<2/3)return e+(t-e)*(2/3-r)*6;return e}var f=new RegExp("^"+te+"$").exec(t);if(f){a=parseInt(f[1]);if(a<0){a=(360- -1*a%360)%360}else if(a>360){a=a%360}a/=360;n=parseFloat(f[2]);if(n<0||n>100){return}n=n/100;i=parseFloat(f[3]);if(i<0||i>100){return}i=i/100;o=f[4];if(o!==undefined){o=parseFloat(o);if(o<0||o>1){return}}if(n===0){s=l=u=Math.round(i*255)}else{var c=i<.5?i*(1+n):i+n-i*n;var d=2*i-c;s=Math.round(255*v(d,c,a+1/3));l=Math.round(255*v(d,c,a));u=Math.round(255*v(d,c,a-1/3))}r=[s,l,u,o]}return r};var ve=function e(t){var r;var a=new RegExp("^"+J+"$").exec(t);if(a){r=[];var n=[];for(var i=1;i<=3;i++){var o=a[i];if(o[o.length-1]==="%"){n[i]=true}o=parseFloat(o);if(n[i]){o=o/100*255}if(o<0||o>255){return}r.push(Math.floor(o))}var s=n[1]||n[2]||n[3];var l=n[1]&&n[2]&&n[3];if(s&&!l){return}var u=a[4];if(u!==undefined){u=parseFloat(u);if(u<0||u>1){return}r.push(u)}}return r};var fe=function e(t){return de[t.toLowerCase()]};var ce=function e(t){return(A(t)?t:null)||fe(t)||le(t)||ve(t)||ue(t)};var de={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};var he=function e(t){var r=t.map;var a=t.keys;var n=a.length;for(var i=0;i<n;i++){var o=a[i];if(_(o)){throw Error("Tried to set map with object key")}if(i<a.length-1){if(r[o]==null){r[o]={}}r=r[o]}else{r[o]=t.value}}};var pe=function e(t){var r=t.map;var a=t.keys;var n=a.length;for(var i=0;i<n;i++){var o=a[i];if(_(o)){throw Error("Tried to get map with object key")}r=r[o];if(r==null){return r}}return r};var ge=typeof globalThis!=="undefined"?globalThis:typeof window!=="undefined"?window:typeof r.g!=="undefined"?r.g:typeof self!=="undefined"?self:{};function ye(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e["default"]:e}var me;var be;function xe(){if(be)return me;be=1;function e(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}me=e;return me}var we;var Ee;function Te(){if(Ee)return we;Ee=1;var e=typeof ge=="object"&&ge&&ge.Object===Object&&ge;we=e;return we}var ke;var Ce;function Pe(){if(Ce)return ke;Ce=1;var e=Te();var t=typeof self=="object"&&self&&self.Object===Object&&self;var r=e||t||Function("return this")();ke=r;return ke}var Se;var De;function Be(){if(De)return Se;De=1;var e=Pe();var t=function(){return e.Date.now()};Se=t;return Se}var Ae;var _e;function Me(){if(_e)return Ae;_e=1;var e=/\s/;function t(t){var r=t.length;while(r--&&e.test(t.charAt(r))){}return r}Ae=t;return Ae}var Ie;var Re;function Ne(){if(Re)return Ie;Re=1;var e=Me();var t=/^\s+/;function r(r){return r?r.slice(0,e(r)+1).replace(t,""):r}Ie=r;return Ie}var Le;var Oe;function ze(){if(Oe)return Le;Oe=1;var e=Pe();var t=e.Symbol;Le=t;return Le}var Fe;var Ve;function je(){if(Ve)return Fe;Ve=1;var e=ze();var t=Object.prototype;var r=t.hasOwnProperty;var a=t.toString;var n=e?e.toStringTag:undefined;function i(e){var t=r.call(e,n),i=e[n];try{e[n]=undefined;var o=true}catch(l){}var s=a.call(e);if(o){if(t){e[n]=i}else{delete e[n]}}return s}Fe=i;return Fe}var Xe;var Ye;function qe(){if(Ye)return Xe;Ye=1;var e=Object.prototype;var t=e.toString;function r(e){return t.call(e)}Xe=r;return Xe}var We;var Ue;function Ge(){if(Ue)return We;Ue=1;var e=ze(),t=je(),r=qe();var a="[object Null]",n="[object Undefined]";var i=e?e.toStringTag:undefined;function o(e){if(e==null){return e===undefined?n:a}return i&&i in Object(e)?t(e):r(e)}We=o;return We}var He;var Ke;function Ze(){if(Ke)return He;Ke=1;function e(e){return e!=null&&typeof e=="object"}He=e;return He}var $e;var Qe;function Je(){if(Qe)return $e;Qe=1;var e=Ge(),t=Ze();var r="[object Symbol]";function a(a){return typeof a=="symbol"||t(a)&&e(a)==r}$e=a;return $e}var et;var tt;function rt(){if(tt)return et;tt=1;var e=Ne(),t=xe(),r=Je();var a=0/0;var n=/^[-+]0x[0-9a-f]+$/i;var i=/^0b[01]+$/i;var o=/^0o[0-7]+$/i;var s=parseInt;function l(l){if(typeof l=="number"){return l}if(r(l)){return a}if(t(l)){var u=typeof l.valueOf=="function"?l.valueOf():l;l=t(u)?u+"":u}if(typeof l!="string"){return l===0?l:+l}l=e(l);var v=i.test(l);return v||o.test(l)?s(l.slice(2),v?2:8):n.test(l)?a:+l}et=l;return et}var at;var nt;function it(){if(nt)return at;nt=1;var e=xe(),t=Be(),r=rt();var a="Expected a function";var n=Math.max,i=Math.min;function o(o,s,l){var u,v,f,c,d,h,p=0,g=false,y=false,m=true;if(typeof o!="function"){throw new TypeError(a)}s=r(s)||0;if(e(l)){g=!!l.leading;y="maxWait"in l;f=y?n(r(l.maxWait)||0,s):f;m="trailing"in l?!!l.trailing:m}function b(e){var t=u,r=v;u=v=undefined;p=e;c=o.apply(r,t);return c}function x(e){p=e;d=setTimeout(T,s);return g?b(e):c}function w(e){var t=e-h,r=e-p,a=s-t;return y?i(a,f-r):a}function E(e){var t=e-h,r=e-p;return h===undefined||t>=s||t<0||y&&r>=f}function T(){var e=t();if(E(e)){return k(e)}d=setTimeout(T,w(e))}function k(e){d=undefined;if(m&&u){return b(e)}u=v=undefined;return c}function C(){if(d!==undefined){clearTimeout(d)}p=0;u=h=v=d=undefined}function P(){return d===undefined?c:k(t())}function S(){var e=t(),r=E(e);u=arguments;v=this;h=e;if(r){if(d===undefined){return x(h)}if(y){clearTimeout(d);d=setTimeout(T,s);return b(h)}}if(d===undefined){d=setTimeout(T,s)}return c}S.cancel=C;S.flush=P;return S}at=o;return at}var ot=it();var st=ye(ot);var lt=w?w.performance:null;var ut=lt&&lt.now?function(){return lt.now()}:function(){return Date.now()};var vt=function(){if(w){if(w.requestAnimationFrame){return function(e){w.requestAnimationFrame(e)}}else if(w.mozRequestAnimationFrame){return function(e){w.mozRequestAnimationFrame(e)}}else if(w.webkitRequestAnimationFrame){return function(e){w.webkitRequestAnimationFrame(e)}}else if(w.msRequestAnimationFrame){return function(e){w.msRequestAnimationFrame(e)}}}return function(e){if(e){setTimeout((function(){e(ut())}),1e3/60)}}}();var ft=function e(t){return vt(t)};var ct=ut;var dt=9261;var ht=65599;var pt=5381;var gt=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:dt;var a=r;var n;for(;;){n=t.next();if(n.done){break}a=a*ht+n.value|0}return a};var yt=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:dt;return r*ht+t|0};var mt=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:pt;return(r<<5)+r+t|0};var bt=function e(t,r){return t*2097152+r};var xt=function e(t){return t[0]*2097152+t[1]};var wt=function e(t,r){return[yt(t[0],r[0]),mt(t[1],r[1])]};var Et=function e(t,r){var a={value:0,done:false};var n=0;var i=t.length;var o={next:function e(){if(n<i){a.value=t[n++]}else{a.done=true}return a}};return gt(o,r)};var Tt=function e(t,r){var a={value:0,done:false};var n=0;var i=t.length;var o={next:function e(){if(n<i){a.value=t.charCodeAt(n++)}else{a.done=true}return a}};return gt(o,r)};var kt=function e(){return Ct(arguments)};var Ct=function e(t){var r;for(var a=0;a<t.length;a++){var n=t[a];if(a===0){r=Tt(n)}else{r=Tt(n,r)}}return r};var Pt=true;var St=console.warn!=null;var Dt=console.trace!=null;var Bt=Number.MAX_SAFE_INTEGER||9007199254740991;var At=function e(){return true};var _t=function e(){return false};var Mt=function e(){return 0};var It=function e(){};var Rt=function e(t){throw new Error(t)};var Nt=function e(t){if(t!==undefined){Pt=!!t}else{return Pt}};var Lt=function e(t){if(!Nt()){return}if(St){console.warn(t)}else{console.log(t);if(Dt){console.trace()}}};var Ot=function e(t){return se({},t)};var zt=function e(t){if(t==null){return t}if(A(t)){return t.slice()}else if(_(t)){return Ot(t)}else{return t}};var Ft=function e(t){return t.slice()};var Vt=function e(t,r){for(r=t="";t++<36;r+=t*51&52?(t^15?8^Math.random()*(t^20?16:4):4).toString(16):"-");return r};var jt={};var Xt=function e(){return jt};var Yt=function e(t){var r=Object.keys(t);return function(e){var a={};for(var n=0;n<r.length;n++){var i=r[n];var o=e==null?undefined:e[i];a[i]=o===undefined?t[i]:o}return a}};var qt=function e(t,r,a){for(var n=t.length-1;n>=0;n--){if(t[n]===r){t.splice(n,1)}}};var Wt=function e(t){t.splice(0,t.length)};var Ut=function e(t,r){for(var a=0;a<r.length;a++){var n=r[a];t.push(n)}};var Gt=function e(t,r,a){if(a){r=Z(a,r)}return t[r]};var Ht=function e(t,r,a,n){if(a){r=Z(a,r)}t[r]=n};var Kt=function(){function e(){o(this,e);this._obj={}}return l(e,[{key:"set",value:function e(t,r){this._obj[t]=r;return this}},{key:"delete",value:function e(t){this._obj[t]=undefined;return this}},{key:"clear",value:function e(){this._obj={}}},{key:"has",value:function e(t){return this._obj[t]!==undefined}},{key:"get",value:function e(t){return this._obj[t]}}])}();var Zt=typeof Map!=="undefined"?Map:Kt;var $t="undefined";var Qt=function(){function e(t){o(this,e);this._obj=Object.create(null);this.size=0;if(t!=null){var r;if(t.instanceString!=null&&t.instanceString()===this.instanceString()){r=t.toArray()}else{r=t}for(var a=0;a<r.length;a++){this.add(r[a])}}}return l(e,[{key:"instanceString",value:function e(){return"set"}},{key:"add",value:function e(t){var r=this._obj;if(r[t]!==1){r[t]=1;this.size++}}},{key:"delete",value:function e(t){var r=this._obj;if(r[t]===1){r[t]=0;this.size--}}},{key:"clear",value:function e(){this._obj=Object.create(null)}},{key:"has",value:function e(t){return this._obj[t]===1}},{key:"toArray",value:function e(){var t=this;return Object.keys(this._obj).filter((function(e){return t.has(e)}))}},{key:"forEach",value:function e(t,r){return this.toArray().forEach(t,r)}}])}();var Jt=(typeof Set==="undefined"?"undefined":b(Set))!==$t?Set:Qt;var er=function e(t,r){var a=arguments.length>2&&arguments[2]!==undefined?arguments[2]:true;if(t===undefined||r===undefined||!F(t)){Rt("An element must have a core reference and parameters set");return}var n=r.group;if(n==null){if(r.data&&r.data.source!=null&&r.data.target!=null){n="edges"}else{n="nodes"}}if(n!=="nodes"&&n!=="edges"){Rt("An element must be of type `nodes` or `edges`; you specified `"+n+"`");return}this.length=1;this[0]=this;var i=this._private={cy:t,single:true,data:r.data||{},position:r.position||{x:0,y:0},autoWidth:undefined,autoHeight:undefined,autoPadding:undefined,compoundBoundsClean:false,listeners:[],group:n,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:true,selected:r.selected?true:false,selectable:r.selectable===undefined?true:r.selectable?true:false,locked:r.locked?true:false,grabbed:false,grabbable:r.grabbable===undefined?true:r.grabbable?true:false,pannable:r.pannable===undefined?n==="edges"?true:false:r.pannable?true:false,active:false,classes:new Jt,animation:{current:[],queue:[]},rscratch:{},scratch:r.scratch||{},edges:[],children:[],parent:r.parent&&r.parent.isNode()?r.parent:null,traversalCache:{},backgrounding:false,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(i.position.x==null){i.position.x=0}if(i.position.y==null){i.position.y=0}if(r.renderedPosition){var o=r.renderedPosition;var s=t.pan();var l=t.zoom();i.position={x:(o.x-s.x)/l,y:(o.y-s.y)/l}}var u=[];if(A(r.classes)){u=r.classes}else if(D(r.classes)){u=r.classes.split(/\s+/)}for(var v=0,f=u.length;v<f;v++){var c=u[v];if(!c||c===""){continue}i.classes.add(c)}this.createEmitter();if(a===undefined||a){this.restore()}var d=r.style||r.css;if(d){Lt("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead.");this.style(d)}};var tr=function e(t){t={bfs:t.bfs||!t.dfs,dfs:t.dfs||!t.bfs};return function e(r,a,n){var i;if(_(r)&&!L(r)){i=r;r=i.roots||i.root;a=i.visit;n=i.directed}n=arguments.length===2&&!B(a)?a:n;a=B(a)?a:function(){};var o=this._private.cy;var s=r=D(r)?this.filter(r):r;var l=[];var u=[];var v={};var f={};var c={};var d=0;var h;var p=this.byGroup(),g=p.nodes,y=p.edges;for(var m=0;m<s.length;m++){var b=s[m];var x=b.id();if(b.isNode()){l.unshift(b);if(t.bfs){c[x]=true;u.push(b)}f[x]=0}}var w=function e(){var r=t.bfs?l.shift():l.pop();var i=r.id();if(t.dfs){if(c[i]){return 0}c[i]=true;u.push(r)}var o=f[i];var s=v[i];var p=s!=null?s.source():null;var m=s!=null?s.target():null;var b=s==null?undefined:r.same(p)?m[0]:p[0];var x;x=a(r,s,b,d++,o);if(x===true){h=r;return 1}if(x===false){return 1}var w=r.connectedEdges().filter((function(e){return(!n||e.source().same(r))&&y.has(e)}));for(var E=0;E<w.length;E++){var T=w[E];var k=T.connectedNodes().filter((function(e){return!e.same(r)&&g.has(e)}));var C=k.id();if(k.length!==0&&!c[C]){k=k[0];l.push(k);if(t.bfs){c[C]=true;u.push(k)}v[C]=T;f[C]=f[i]+1}}},E;while(l.length!==0){E=w();if(E===0)continue;if(E===1)break}var T=o.collection();for(var k=0;k<u.length;k++){var C=u[k];var P=v[C.id()];if(P!=null){T.push(P)}T.push(C)}return{path:o.collection(T),found:o.collection(h)}}};var rr={breadthFirstSearch:tr({bfs:true}),depthFirstSearch:tr({dfs:true})};rr.bfs=rr.breadthFirstSearch;rr.dfs=rr.depthFirstSearch;var ar={exports:{}};var nr=ar.exports;var ir;function or(){if(ir)return ar.exports;ir=1;(function(e,t){(function(){var t,r,a,n,i,o,s,l,u,v,f,c,d,h,p;a=Math.floor,v=Math.min;r=function(e,t){if(e<t){return-1}if(e>t){return 1}return 0};u=function(e,t,n,i,o){var s;if(n==null){n=0}if(o==null){o=r}if(n<0){throw new Error("lo must be non-negative")}if(i==null){i=e.length}while(n<i){s=a((n+i)/2);if(o(t,e[s])<0){i=s}else{n=s+1}}return[].splice.apply(e,[n,n-n].concat(t)),t};o=function(e,t,a){if(a==null){a=r}e.push(t);return h(e,0,e.length-1,a)};i=function(e,t){var a,n;if(t==null){t=r}a=e.pop();if(e.length){n=e[0];e[0]=a;p(e,0,t)}else{n=a}return n};l=function(e,t,a){var n;if(a==null){a=r}n=e[0];e[0]=t;p(e,0,a);return n};s=function(e,t,a){var n;if(a==null){a=r}if(e.length&&a(e[0],t)<0){n=[e[0],t],t=n[0],e[0]=n[1];p(e,0,a)}return t};n=function(e,t){var n,i,o,s,l,u;if(t==null){t=r}s=function(){u=[];for(var t=0,r=a(e.length/2);0<=r?t<r:t>r;0<=r?t++:t--){u.push(t)}return u}.apply(this).reverse();l=[];for(i=0,o=s.length;i<o;i++){n=s[i];l.push(p(e,n,t))}return l};d=function(e,t,a){var n;if(a==null){a=r}n=e.indexOf(t);if(n===-1){return}h(e,0,n,a);return p(e,n,a)};f=function(e,t,a){var i,o,l,u,v;if(a==null){a=r}o=e.slice(0,t);if(!o.length){return o}n(o,a);v=e.slice(t);for(l=0,u=v.length;l<u;l++){i=v[l];s(o,i,a)}return o.sort(a).reverse()};c=function(e,t,a){var o,s,l,f,c,d,h,p,g;if(a==null){a=r}if(t*10<=e.length){l=e.slice(0,t).sort(a);if(!l.length){return l}s=l[l.length-1];h=e.slice(t);for(f=0,d=h.length;f<d;f++){o=h[f];if(a(o,s)<0){u(l,o,0,null,a);l.pop();s=l[l.length-1]}}return l}n(e,a);g=[];for(c=0,p=v(t,e.length);0<=p?c<p:c>p;0<=p?++c:--c){g.push(i(e,a))}return g};h=function(e,t,a,n){var i,o,s;if(n==null){n=r}i=e[a];while(a>t){s=a-1>>1;o=e[s];if(n(i,o)<0){e[a]=o;a=s;continue}break}return e[a]=i};p=function(e,t,a){var n,i,o,s,l;if(a==null){a=r}i=e.length;l=t;o=e[t];n=2*t+1;while(n<i){s=n+1;if(s<i&&!(a(e[n],e[s])<0)){n=s}e[t]=e[n];t=n;n=2*t+1}e[t]=o;return h(e,l,t,a)};t=function(){e.push=o;e.pop=i;e.replace=l;e.pushpop=s;e.heapify=n;e.updateItem=d;e.nlargest=f;e.nsmallest=c;function e(e){this.cmp=e!=null?e:r;this.nodes=[]}e.prototype.push=function(e){return o(this.nodes,e,this.cmp)};e.prototype.pop=function(){return i(this.nodes,this.cmp)};e.prototype.peek=function(){return this.nodes[0]};e.prototype.contains=function(e){return this.nodes.indexOf(e)!==-1};e.prototype.replace=function(e){return l(this.nodes,e,this.cmp)};e.prototype.pushpop=function(e){return s(this.nodes,e,this.cmp)};e.prototype.heapify=function(){return n(this.nodes,this.cmp)};e.prototype.updateItem=function(e){return d(this.nodes,e,this.cmp)};e.prototype.clear=function(){return this.nodes=[]};e.prototype.empty=function(){return this.nodes.length===0};e.prototype.size=function(){return this.nodes.length};e.prototype.clone=function(){var t;t=new e;t.nodes=this.nodes.slice(0);return t};e.prototype.toArray=function(){return this.nodes.slice(0)};e.prototype.insert=e.prototype.push;e.prototype.top=e.prototype.peek;e.prototype.front=e.prototype.peek;e.prototype.has=e.prototype.contains;e.prototype.copy=e.prototype.clone;return e}();(function(t,r){{return e.exports=r()}})(this,(function(){return t}))}).call(nr)})(ar);return ar.exports}var sr;var lr;function ur(){if(lr)return sr;lr=1;sr=or();return sr}var vr=ur();var fr=ye(vr);var cr=Yt({root:null,weight:function e(t){return 1},directed:false});var dr={dijkstra:function e(t){if(!_(t)){var r=arguments;t={root:r[0],weight:r[1],directed:r[2]}}var a=cr(t),n=a.root,i=a.weight,o=a.directed;var s=this;var l=i;var u=D(n)?this.filter(n)[0]:n[0];var v={};var f={};var c={};var d=this.byGroup(),h=d.nodes,p=d.edges;p.unmergeBy((function(e){return e.isLoop()}));var g=function e(t){return v[t.id()]};var y=function e(t,r){v[t.id()]=r;m.updateItem(t)};var m=new fr((function(e,t){return g(e)-g(t)}));for(var b=0;b<h.length;b++){var x=h[b];v[x.id()]=x.same(u)?0:Infinity;m.push(x)}var w=function e(t,r){var a=(o?t.edgesTo(r):t.edgesWith(r)).intersect(p);var n=Infinity;var i;for(var s=0;s<a.length;s++){var u=a[s];var v=l(u);if(v<n||!i){n=v;i=u}}return{edge:i,dist:n}};while(m.size()>0){var E=m.pop();var T=g(E);var k=E.id();c[k]=T;if(T===Infinity){continue}var C=E.neighborhood().intersect(h);for(var P=0;P<C.length;P++){var S=C[P];var B=S.id();var A=w(E,S);var M=T+A.dist;if(M<g(S)){y(S,M);f[B]={node:E,edge:A.edge}}}}return{distanceTo:function e(t){var r=D(t)?h.filter(t)[0]:t[0];return c[r.id()]},pathTo:function e(t){var r=D(t)?h.filter(t)[0]:t[0];var a=[];var n=r;var i=n.id();if(r.length>0){a.unshift(r);while(f[i]){var o=f[i];a.unshift(o.edge);a.unshift(o.node);n=o.node;i=n.id()}}return s.spawn(a)}}}};var hr={kruskal:function e(t){t=t||function(e){return 1};var r=this.byGroup(),a=r.nodes,n=r.edges;var i=a.length;var o=new Array(i);var s=a;var l=function e(t){for(var r=0;r<o.length;r++){var a=o[r];if(a.has(t)){return r}}};for(var u=0;u<i;u++){o[u]=this.spawn(a[u])}var v=n.sort((function(e,r){return t(e)-t(r)}));for(var f=0;f<v.length;f++){var c=v[f];var d=c.source()[0];var h=c.target()[0];var p=l(d);var g=l(h);var y=o[p];var m=o[g];if(p!==g){s.merge(c);y.merge(m);o.splice(g,1)}}return s}};var pr=Yt({root:null,goal:null,weight:function e(t){return 1},heuristic:function e(t){return 0},directed:false});var gr={aStar:function e(t){var r=this.cy();var a=pr(t),n=a.root,i=a.goal,o=a.heuristic,s=a.directed,l=a.weight;n=r.collection(n)[0];i=r.collection(i)[0];var u=n.id();var v=i.id();var f={};var c={};var d={};var h=new fr((function(e,t){return c[e.id()]-c[t.id()]}));var p=new Jt;var g={};var y={};var m=function e(t,r){h.push(t);p.add(r)};var b,x;var w=function e(){b=h.pop();x=b.id();p["delete"](x)};var E=function e(t){return p.has(t)};m(n,u);f[u]=0;c[u]=o(n);var T=0;while(h.size()>0){w();T++;if(x===v){var k=[];var C=i;var P=v;var S=y[P];for(;;){k.unshift(C);if(S!=null){k.unshift(S)}C=g[P];if(C==null){break}P=C.id();S=y[P]}return{found:true,distance:f[x],path:this.spawn(k),steps:T}}d[x]=true;var D=b._private.edges;for(var B=0;B<D.length;B++){var A=D[B];if(!this.hasElementWithId(A.id())){continue}if(s&&A.data("source")!==x){continue}var _=A.source();var M=A.target();var I=_.id()!==x?_:M;var R=I.id();if(!this.hasElementWithId(R)){continue}if(d[R]){continue}var N=f[x]+l(A);if(!E(R)){f[R]=N;c[R]=N+o(I);m(I,R);g[R]=b;y[R]=A;continue}if(N<f[R]){f[R]=N;c[R]=N+o(I);g[R]=b;y[R]=A}}}return{found:false,distance:undefined,path:undefined,steps:T}}};var yr=Yt({weight:function e(t){return 1},directed:false});var mr={floydWarshall:function e(t){var r=this.cy();var a=yr(t),n=a.weight,i=a.directed;var o=n;var s=this.byGroup(),l=s.nodes,u=s.edges;var v=l.length;var f=v*v;var c=function e(t){return l.indexOf(t)};var d=function e(t){return l[t]};var h=new Array(f);for(var p=0;p<f;p++){var g=p%v;var y=(p-g)/v;if(y===g){h[p]=0}else{h[p]=Infinity}}var m=new Array(f);var b=new Array(f);for(var x=0;x<u.length;x++){var w=u[x];var E=w.source()[0];var T=w.target()[0];if(E===T){continue}var k=c(E);var C=c(T);var P=k*v+C;var S=o(w);if(h[P]>S){h[P]=S;m[P]=C;b[P]=w}if(!i){var B=C*v+k;if(!i&&h[B]>S){h[B]=S;m[B]=k;b[B]=w}}}for(var A=0;A<v;A++){for(var _=0;_<v;_++){var M=_*v+A;for(var I=0;I<v;I++){var R=_*v+I;var N=A*v+I;if(h[M]+h[N]<h[R]){h[R]=h[M]+h[N];m[R]=m[M]}}}}var L=function e(t){return(D(t)?r.filter(t):t)[0]};var O=function e(t){return c(L(t))};var z={distance:function e(t,r){var a=O(t);var n=O(r);return h[a*v+n]},path:function e(t,a){var n=O(t);var i=O(a);var o=d(n);if(n===i){return o.collection()}if(m[n*v+i]==null){return r.collection()}var e=r.collection();var s=n;var l;e.merge(o);while(n!==i){s=n;n=m[n*v+i];l=b[s*v+n];e.merge(l);e.merge(d(n))}return e}};return z}};var br=Yt({weight:function e(t){return 1},directed:false,root:null});var xr={bellmanFord:function e(t){var r=this;var a=br(t),n=a.weight,i=a.directed,o=a.root;var s=n;var l=this;var u=this.cy();var v=this.byGroup(),f=v.edges,c=v.nodes;var d=c.length;var h=new Zt;var p=false;var g=[];o=u.collection(o)[0];f.unmergeBy((function(e){return e.isLoop()}));var y=f.length;var m=function e(t){var r=h.get(t.id());if(!r){r={};h.set(t.id(),r)}return r};var b=function e(t){return(D(t)?u.$(t):t)[0]};var x=function e(t){return m(b(t)).dist};var w=function e(t){var a=arguments.length>1&&arguments[1]!==undefined?arguments[1]:o;var n=b(t);var i=[];var s=n;for(;;){if(s==null){return r.spawn()}var u=m(s),v=u.edge,f=u.pred;i.unshift(s[0]);if(s.same(a)&&i.length>0){break}if(v!=null){i.unshift(v)}s=f}return l.spawn(i)};for(var E=0;E<d;E++){var T=c[E];var k=m(T);if(T.same(o)){k.dist=0}else{k.dist=Infinity}k.pred=null;k.edge=null}var C=false;var P=function e(t,r,a,n,i,o){var s=n.dist+o;if(s<i.dist&&!a.same(n.edge)){i.dist=s;i.pred=t;i.edge=a;C=true}};for(var S=1;S<d;S++){C=false;for(var B=0;B<y;B++){var A=f[B];var _=A.source();var M=A.target();var I=s(A);var R=m(_);var N=m(M);P(_,M,A,R,N,I);if(!i){P(M,_,A,N,R,I)}}if(!C){break}}if(C){var L=[];for(var O=0;O<y;O++){var z=f[O];var F=z.source();var V=z.target();var j=s(z);var X=m(F).dist;var Y=m(V).dist;if(X+j<Y||!i&&Y+j<X){if(!p){Lt("Graph contains a negative weight cycle for Bellman-Ford");p=true}if(t.findNegativeWeightCycles!==false){var q=[];if(X+j<Y){q.push(F)}if(!i&&Y+j<X){q.push(V)}var W=q.length;for(var U=0;U<W;U++){var G=q[U];var H=[G];H.push(m(G).edge);var K=m(G).pred;while(H.indexOf(K)===-1){H.push(K);H.push(m(K).edge);K=m(K).pred}H=H.slice(H.indexOf(K));var Z=H[0].id();var $=0;for(var Q=2;Q<H.length;Q+=2){if(H[Q].id()<Z){Z=H[Q].id();$=Q}}H=H.slice($).concat(H.slice(0,$));H.push(H[0]);var J=H.map((function(e){return e.id()})).join(",");if(L.indexOf(J)===-1){g.push(l.spawn(H));L.push(J)}}}else{break}}}}return{distanceTo:x,pathTo:w,hasNegativeWeightCycle:p,negativeWeightCycles:g}}};var wr=Math.sqrt(2);var Er=function e(t,r,a){if(a.length===0){Rt("Karger-Stein must be run on a connected (sub)graph")}var n=a[t];var i=n[1];var o=n[2];var s=r[i];var l=r[o];var u=a;for(var v=u.length-1;v>=0;v--){var f=u[v];var c=f[1];var d=f[2];if(r[c]===s&&r[d]===l||r[c]===l&&r[d]===s){u.splice(v,1)}}for(var h=0;h<u.length;h++){var p=u[h];if(p[1]===l){u[h]=p.slice();u[h][1]=s}else if(p[2]===l){u[h]=p.slice();u[h][2]=s}}for(var g=0;g<r.length;g++){if(r[g]===l){r[g]=s}}return u};var Tr=function e(t,r,a,n){while(a>n){var i=Math.floor(Math.random()*r.length);r=Er(i,t,r);a--}return r};var kr={kargerStein:function e(){var t=this;var r=this.byGroup(),a=r.nodes,n=r.edges;n.unmergeBy((function(e){return e.isLoop()}));var i=a.length;var o=n.length;var s=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2));var l=Math.floor(i/wr);if(i<2){Rt("At least 2 nodes are required for Karger-Stein algorithm");return undefined}var u=[];for(var v=0;v<o;v++){var f=n[v];u.push([v,a.indexOf(f.source()),a.indexOf(f.target())])}var c=Infinity;var d=[];var h=new Array(i);var p=new Array(i);var g=new Array(i);var y=function e(t,r){for(var a=0;a<i;a++){r[a]=t[a]}};for(var m=0;m<=s;m++){for(var b=0;b<i;b++){p[b]=b}var x=Tr(p,u.slice(),i,l);var w=x.slice();y(p,g);var E=Tr(p,x,l,2);var T=Tr(g,w,l,2);if(E.length<=T.length&&E.length<c){c=E.length;d=E;y(p,h)}else if(T.length<=E.length&&T.length<c){c=T.length;d=T;y(g,h)}}var k=this.spawn(d.map((function(e){return n[e[0]]})));var C=this.spawn();var P=this.spawn();var S=h[0];for(var D=0;D<h.length;D++){var B=h[D];var A=a[D];if(B===S){C.merge(A)}else{P.merge(A)}}var _=function e(r){var a=t.spawn();r.forEach((function(e){a.merge(e);e.connectedEdges().forEach((function(e){if(t.contains(e)&&!k.contains(e)){a.merge(e)}}))}));return a};var M=[_(C),_(P)];var I={cut:k,components:M,partition1:C,partition2:P};return I}};var Cr=function e(t){return{x:t.x,y:t.y}};var Pr=function e(t,r,a){return{x:t.x*r+a.x,y:t.y*r+a.y}};var Sr=function e(t,r,a){return{x:(t.x-a.x)/r,y:(t.y-a.y)/r}};var Dr=function e(t){return{x:t[0],y:t[1]}};var Br=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var a=arguments.length>2&&arguments[2]!==undefined?arguments[2]:t.length;var e=Infinity;for(var n=r;n<a;n++){var i=t[n];if(isFinite(i)){e=Math.min(i,e)}}return e};var Ar=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var a=arguments.length>2&&arguments[2]!==undefined?arguments[2]:t.length;var e=-Infinity;for(var n=r;n<a;n++){var i=t[n];if(isFinite(i)){e=Math.max(i,e)}}return e};var _r=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var a=arguments.length>2&&arguments[2]!==undefined?arguments[2]:t.length;var n=0;var i=0;for(var o=r;o<a;o++){var s=t[o];if(isFinite(s)){n+=s;i++}}return n/i};var Mr=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var a=arguments.length>2&&arguments[2]!==undefined?arguments[2]:t.length;var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;var i=arguments.length>4&&arguments[4]!==undefined?arguments[4]:true;var o=arguments.length>5&&arguments[5]!==undefined?arguments[5]:true;if(n){t=t.slice(r,a)}else{if(a<t.length){t.splice(a,t.length-a)}if(r>0){t.splice(0,r)}}var s=0;for(var l=t.length-1;l>=0;l--){var u=t[l];if(o){if(!isFinite(u)){t[l]=-Infinity;s++}}else{t.splice(l,1)}}if(i){t.sort((function(e,t){return e-t}))}var v=t.length;var f=Math.floor(v/2);if(v%2!==0){return t[f+1+s]}else{return(t[f-1+s]+t[f+s])/2}};var Ir=function e(t){return Math.PI*t/180};var Rr=function e(t,r){return Math.atan2(r,t)-Math.PI/2};var Nr=Math.log2||function(e){return Math.log(e)/Math.log(2)};var Lr=function e(t){if(t>0){return 1}else if(t<0){return-1}else{return 0}};var Or=function e(t,r){return Math.sqrt(zr(t,r))};var zr=function e(t,r){var a=r.x-t.x;var n=r.y-t.y;return a*a+n*n};var Fr=function e(t){var r=t.length;var a=0;for(var n=0;n<r;n++){a+=t[n]}for(var i=0;i<r;i++){t[i]=t[i]/a}return t};var Vr=function e(t,r,a,n){return(1-n)*(1-n)*t+2*(1-n)*n*r+n*n*a};var jr=function e(t,r,a,n){return{x:Vr(t.x,r.x,a.x,n),y:Vr(t.y,r.y,a.y,n)}};var Xr=function e(t,r,a,n){var i={x:r.x-t.x,y:r.y-t.y};var o=Or(t,r);var s={x:i.x/o,y:i.y/o};a=a==null?0:a;n=n!=null?n:a*o;return{x:t.x+s.x*n,y:t.y+s.y*n}};var Yr=function e(t,r,a){return Math.max(t,Math.min(a,r))};var qr=function e(t){if(t==null){return{x1:Infinity,y1:Infinity,x2:-Infinity,y2:-Infinity,w:0,h:0}}else if(t.x1!=null&&t.y1!=null){if(t.x2!=null&&t.y2!=null&&t.x2>=t.x1&&t.y2>=t.y1){return{x1:t.x1,y1:t.y1,x2:t.x2,y2:t.y2,w:t.x2-t.x1,h:t.y2-t.y1}}else if(t.w!=null&&t.h!=null&&t.w>=0&&t.h>=0){return{x1:t.x1,y1:t.y1,x2:t.x1+t.w,y2:t.y1+t.h,w:t.w,h:t.h}}}};var Wr=function e(t){return{x1:t.x1,x2:t.x2,w:t.w,y1:t.y1,y2:t.y2,h:t.h}};var Ur=function e(t){t.x1=Infinity;t.y1=Infinity;t.x2=-Infinity;t.y2=-Infinity;t.w=0;t.h=0};var Gr=function e(t,r,a){return{x1:t.x1+r,x2:t.x2+r,y1:t.y1+a,y2:t.y2+a,w:t.w,h:t.h}};var Hr=function e(t,r){t.x1=Math.min(t.x1,r.x1);t.x2=Math.max(t.x2,r.x2);t.w=t.x2-t.x1;t.y1=Math.min(t.y1,r.y1);t.y2=Math.max(t.y2,r.y2);t.h=t.y2-t.y1};var Kr=function e(t,r,a){t.x1=Math.min(t.x1,r);t.x2=Math.max(t.x2,r);t.w=t.x2-t.x1;t.y1=Math.min(t.y1,a);t.y2=Math.max(t.y2,a);t.h=t.y2-t.y1};var Zr=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;t.x1-=r;t.x2+=r;t.y1-=r;t.y2+=r;t.w=t.x2-t.x1;t.h=t.y2-t.y1;return t};var $r=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:[0];var a,n,i,o;if(r.length===1){a=n=i=o=r[0]}else if(r.length===2){a=i=r[0];o=n=r[1]}else if(r.length===4){var s=p(r,4);a=s[0];n=s[1];i=s[2];o=s[3]}t.x1-=o;t.x2+=n;t.y1-=a;t.y2+=i;t.w=t.x2-t.x1;t.h=t.y2-t.y1;return t};var Qr=function e(t,r){t.x1=r.x1;t.y1=r.y1;t.x2=r.x2;t.y2=r.y2;t.w=t.x2-t.x1;t.h=t.y2-t.y1};var Jr=function e(t,r){if(t.x1>r.x2){return false}if(r.x1>t.x2){return false}if(t.x2<r.x1){return false}if(r.x2<t.x1){return false}if(t.y2<r.y1){return false}if(r.y2<t.y1){return false}if(t.y1>r.y2){return false}if(r.y1>t.y2){return false}return true};var ea=function e(t,r,a){return t.x1<=r&&r<=t.x2&&t.y1<=a&&a<=t.y2};var ta=function e(t,r){return ea(t,r.x,r.y)};var ra=function e(t,r){return ea(t,r.x1,r.y1)&&ea(t,r.x2,r.y2)};var aa=function e(t,r,a,n,i,o,s){var l=arguments.length>7&&arguments[7]!==undefined?arguments[7]:"auto";var u=l==="auto"?Pa(i,o):l;var v=i/2;var f=o/2;u=Math.min(u,v,f);var c=u!==v,d=u!==f;var h;if(c){var p=a-v+u-s;var g=n-f-s;var y=a+v-u+s;var m=g;h=ba(t,r,a,n,p,g,y,m,false);if(h.length>0){return h}}if(d){var b=a+v+s;var x=n-f+u-s;var w=b;var E=n+f-u+s;h=ba(t,r,a,n,b,x,w,E,false);if(h.length>0){return h}}if(c){var T=a-v+u-s;var k=n+f+s;var C=a+v-u+s;var P=k;h=ba(t,r,a,n,T,k,C,P,false);if(h.length>0){return h}}if(d){var S=a-v-s;var D=n-f+u-s;var B=S;var A=n+f-u+s;h=ba(t,r,a,n,S,D,B,A,false);if(h.length>0){return h}}var _;{var M=a-v+u;var I=n-f+u;_=ya(t,r,a,n,M,I,u+s);if(_.length>0&&_[0]<=M&&_[1]<=I){return[_[0],_[1]]}}{var R=a+v-u;var N=n-f+u;_=ya(t,r,a,n,R,N,u+s);if(_.length>0&&_[0]>=R&&_[1]<=N){return[_[0],_[1]]}}{var L=a+v-u;var O=n+f-u;_=ya(t,r,a,n,L,O,u+s);if(_.length>0&&_[0]>=L&&_[1]>=O){return[_[0],_[1]]}}{var z=a-v+u;var F=n+f-u;_=ya(t,r,a,n,z,F,u+s);if(_.length>0&&_[0]<=z&&_[1]>=F){return[_[0],_[1]]}}return[]};var na=function e(t,r,a,n,i,o,s){var l=s;var u=Math.min(a,i);var v=Math.max(a,i);var f=Math.min(n,o);var c=Math.max(n,o);return u-l<=t&&t<=v+l&&f-l<=r&&r<=c+l};var ia=function e(t,r,a,n,i,o,s,l,u){var v={x1:Math.min(a,s,i)-u,x2:Math.max(a,s,i)+u,y1:Math.min(n,l,o)-u,y2:Math.max(n,l,o)+u};if(t<v.x1||t>v.x2||r<v.y1||r>v.y2){return false}else{return true}};var oa=function e(t,r,a,n){a-=n;var i=r*r-4*t*a;if(i<0){return[]}var o=Math.sqrt(i);var s=2*t;var l=(-r+o)/s;var u=(-r-o)/s;return[l,u]};var sa=function e(t,r,a,n,i){var o=1e-5;if(t===0){t=o}r/=t;a/=t;n/=t;var s,l,u,v,f,c,d,h;l=(3*a-r*r)/9;u=-(27*n)+r*(9*a-2*(r*r));u/=54;s=l*l*l+u*u;i[1]=0;d=r/3;if(s>0){f=u+Math.sqrt(s);f=f<0?-Math.pow(-f,1/3):Math.pow(f,1/3);c=u-Math.sqrt(s);c=c<0?-Math.pow(-c,1/3):Math.pow(c,1/3);i[0]=-d+f+c;d+=(f+c)/2;i[4]=i[2]=-d;d=Math.sqrt(3)*(-c+f)/2;i[3]=d;i[5]=-d;return}i[5]=i[3]=0;if(s===0){h=u<0?-Math.pow(-u,1/3):Math.pow(u,1/3);i[0]=-d+2*h;i[4]=i[2]=-(h+d);return}l=-l;v=l*l*l;v=Math.acos(u/Math.sqrt(v));h=2*Math.sqrt(l);i[0]=-d+h*Math.cos(v/3);i[2]=-d+h*Math.cos((v+2*Math.PI)/3);i[4]=-d+h*Math.cos((v+4*Math.PI)/3);return};var la=function e(t,r,a,n,i,o,s,l){var u=1*a*a-4*a*i+2*a*s+4*i*i-4*i*s+s*s+n*n-4*n*o+2*n*l+4*o*o-4*o*l+l*l;var v=1*9*a*i-3*a*a-3*a*s-6*i*i+3*i*s+9*n*o-3*n*n-3*n*l-6*o*o+3*o*l;var f=1*3*a*a-6*a*i+a*s-a*t+2*i*i+2*i*t-s*t+3*n*n-6*n*o+n*l-n*r+2*o*o+2*o*r-l*r;var c=1*a*i-a*a+a*t-i*t+n*o-n*n+n*r-o*r;var d=[];sa(u,v,f,c,d);var h=1e-7;var p=[];for(var g=0;g<6;g+=2){if(Math.abs(d[g+1])<h&&d[g]>=0&&d[g]<=1){p.push(d[g])}}p.push(1);p.push(0);var y=-1;var m,b,x;for(var w=0;w<p.length;w++){m=Math.pow(1-p[w],2)*a+2*(1-p[w])*p[w]*i+p[w]*p[w]*s;b=Math.pow(1-p[w],2)*n+2*(1-p[w])*p[w]*o+p[w]*p[w]*l;x=Math.pow(m-t,2)+Math.pow(b-r,2);if(y>=0){if(x<y){y=x}}else{y=x}}return y};var ua=function e(t,r,a,n,i,o){var s=[t-a,r-n];var l=[i-a,o-n];var u=l[0]*l[0]+l[1]*l[1];var v=s[0]*s[0]+s[1]*s[1];var f=s[0]*l[0]+s[1]*l[1];var c=f*f/u;if(f<0){return v}if(c>u){return(t-i)*(t-i)+(r-o)*(r-o)}return v-c};var va=function e(t,r,a){var n,i,o,s;var l;var u=0;for(var v=0;v<a.length/2;v++){n=a[v*2];i=a[v*2+1];if(v+1<a.length/2){o=a[(v+1)*2];s=a[(v+1)*2+1]}else{o=a[(v+1-a.length/2)*2];s=a[(v+1-a.length/2)*2+1]}if(n==t&&o==t);else if(n>=t&&t>=o||n<=t&&t<=o){l=(t-n)/(o-n)*(s-i)+i;if(l>r){u++}}else{continue}}if(u%2===0){return false}else{return true}};var fa=function e(t,r,a,n,i,o,s,l,u){var v=new Array(a.length);var f;if(l[0]!=null){f=Math.atan(l[1]/l[0]);if(l[0]<0){f=f+Math.PI/2}else{f=-f-Math.PI/2}}else{f=l}var c=Math.cos(-f);var d=Math.sin(-f);for(var h=0;h<v.length/2;h++){v[h*2]=o/2*(a[h*2]*c-a[h*2+1]*d);v[h*2+1]=s/2*(a[h*2+1]*c+a[h*2]*d);v[h*2]+=n;v[h*2+1]+=i}var p;if(u>0){var g=ha(v,-u);p=da(g)}else{p=v}return va(t,r,p)};var ca=function e(t,r,a,n,i,o,s,l){var u=new Array(a.length*2);for(var v=0;v<l.length;v++){var f=l[v];u[v*4+0]=f.startX;u[v*4+1]=f.startY;u[v*4+2]=f.stopX;u[v*4+3]=f.stopY;var c=Math.pow(f.cx-t,2)+Math.pow(f.cy-r,2);if(c<=Math.pow(f.radius,2)){return true}}return va(t,r,u)};var da=function e(t){var r=new Array(t.length/2);var a,n,i,o;var s,l,u,v;for(var f=0;f<t.length/4;f++){a=t[f*4];n=t[f*4+1];i=t[f*4+2];o=t[f*4+3];if(f<t.length/4-1){s=t[(f+1)*4];l=t[(f+1)*4+1];u=t[(f+1)*4+2];v=t[(f+1)*4+3]}else{s=t[0];l=t[1];u=t[2];v=t[3]}var c=ba(a,n,i,o,s,l,u,v,true);r[f*2]=c[0];r[f*2+1]=c[1]}return r};var ha=function e(t,r){var a=new Array(t.length*2);var n,i,o,s;for(var l=0;l<t.length/2;l++){n=t[l*2];i=t[l*2+1];if(l<t.length/2-1){o=t[(l+1)*2];s=t[(l+1)*2+1]}else{o=t[0];s=t[1]}var u=s-i;var v=-(o-n);var f=Math.sqrt(u*u+v*v);var c=u/f;var d=v/f;a[l*4]=n+c*r;a[l*4+1]=i+d*r;a[l*4+2]=o+c*r;a[l*4+3]=s+d*r}return a};var pa=function e(t,r,a,n,i,o){var s=a-t;var l=n-r;s/=i;l/=o;var u=Math.sqrt(s*s+l*l);var v=u-1;if(v<0){return[]}var f=v/u;return[(a-t)*f+t,(n-r)*f+r]};var ga=function e(t,r,a,n,i,o,s){t-=i;r-=o;t/=a/2+s;r/=n/2+s;return t*t+r*r<=1};var ya=function e(t,r,a,n,i,o,s){var l=[a-t,n-r];var u=[t-i,r-o];var v=l[0]*l[0]+l[1]*l[1];var f=2*(u[0]*l[0]+u[1]*l[1]);var c=u[0]*u[0]+u[1]*u[1]-s*s;var d=f*f-4*v*c;if(d<0){return[]}var h=(-f+Math.sqrt(d))/(2*v);var p=(-f-Math.sqrt(d))/(2*v);var g=Math.min(h,p);var y=Math.max(h,p);var m=[];if(g>=0&&g<=1){m.push(g)}if(y>=0&&y<=1){m.push(y)}if(m.length===0){return[]}var b=m[0]*l[0]+t;var x=m[0]*l[1]+r;if(m.length>1){if(m[0]==m[1]){return[b,x]}else{var w=m[1]*l[0]+t;var E=m[1]*l[1]+r;return[b,x,w,E]}}else{return[b,x]}};var ma=function e(t,r,a){if(r<=t&&t<=a||a<=t&&t<=r){return t}else if(t<=r&&r<=a||a<=r&&r<=t){return r}else{return a}};var ba=function e(t,r,a,n,i,o,s,l,u){var v=t-i;var f=a-t;var c=s-i;var d=r-o;var h=n-r;var p=l-o;var g=c*d-p*v;var y=f*d-h*v;var m=p*f-c*h;if(m!==0){var b=g/m;var x=y/m;var w=.001;var E=0-w;var T=1+w;if(E<=b&&b<=T&&E<=x&&x<=T){return[t+b*f,r+b*h]}else{if(!u){return[]}else{return[t+b*f,r+b*h]}}}else{if(g===0||y===0){if(ma(t,a,s)===s){return[s,l]}if(ma(t,a,i)===i){return[i,o]}if(ma(i,s,a)===a){return[a,n]}return[]}else{return[]}}};var xa=function e(t,r,a,n,i,o,s,l){var u=[];var v;var f=new Array(a.length);var c=true;if(o==null){c=false}var d;if(c){for(var h=0;h<f.length/2;h++){f[h*2]=a[h*2]*o+n;f[h*2+1]=a[h*2+1]*s+i}if(l>0){var p=ha(f,-l);d=da(p)}else{d=f}}else{d=a}var g,y,m,b;for(var x=0;x<d.length/2;x++){g=d[x*2];y=d[x*2+1];if(x<d.length/2-1){m=d[(x+1)*2];b=d[(x+1)*2+1]}else{m=d[0];b=d[1]}v=ba(t,r,n,i,g,y,m,b);if(v.length!==0){u.push(v[0],v[1])}}return u};var wa=function e(t,r,a,n,i,o,s,l,u){var v=[];var f;var c=new Array(a.length*2);u.forEach((function(e,a){if(a===0){c[c.length-2]=e.startX;c[c.length-1]=e.startY}else{c[a*4-2]=e.startX;c[a*4-1]=e.startY}c[a*4]=e.stopX;c[a*4+1]=e.stopY;f=ya(t,r,n,i,e.cx,e.cy,e.radius);if(f.length!==0){v.push(f[0],f[1])}}));for(var d=0;d<c.length/4;d++){f=ba(t,r,n,i,c[d*4],c[d*4+1],c[d*4+2],c[d*4+3],false);if(f.length!==0){v.push(f[0],f[1])}}if(v.length>2){var h=[v[0],v[1]];var p=Math.pow(h[0]-t,2)+Math.pow(h[1]-r,2);for(var g=1;g<v.length/2;g++){var y=Math.pow(v[g*2]-t,2)+Math.pow(v[g*2+1]-r,2);if(y<=p){h[0]=v[g*2];h[1]=v[g*2+1];p=y}}return h}return v};var Ea=function e(t,r,a){var n=[t[0]-r[0],t[1]-r[1]];var i=Math.sqrt(n[0]*n[0]+n[1]*n[1]);var o=(i-a)/i;if(o<0){o=1e-5}return[r[0]+o*n[0],r[1]+o*n[1]]};var Ta=function e(t,r){var a=Ca(t,r);a=ka(a);return a};var ka=function e(t){var r,a;var n=t.length/2;var i=Infinity,o=Infinity,s=-Infinity,l=-Infinity;for(var u=0;u<n;u++){r=t[2*u];a=t[2*u+1];i=Math.min(i,r);s=Math.max(s,r);o=Math.min(o,a);l=Math.max(l,a)}var v=2/(s-i);var f=2/(l-o);for(var c=0;c<n;c++){r=t[2*c]=t[2*c]*v;a=t[2*c+1]=t[2*c+1]*f;i=Math.min(i,r);s=Math.max(s,r);o=Math.min(o,a);l=Math.max(l,a)}if(o<-1){for(var d=0;d<n;d++){a=t[2*d+1]=t[2*d+1]+(-1-o)}}return t};var Ca=function e(t,r){var a=1/t*2*Math.PI;var n=t%2===0?Math.PI/2+a/2:Math.PI/2;n+=r;var i=new Array(t*2);var o;for(var s=0;s<t;s++){o=s*a+n;i[2*s]=Math.cos(o);i[2*s+1]=Math.sin(-o)}return i};var Pa=function e(t,r){return Math.min(t/4,r/4,8)};var Sa=function e(t,r){return Math.min(t/10,r/10,8)};var Da=function e(){return 8};var Ba=function e(t,r,a){return[t-2*r+a,2*(r-t),t]};var Aa=function e(t,r){return{heightOffset:Math.min(15,.05*r),widthOffset:Math.min(100,.25*t),ctrlPtOffsetPct:.05}};var _a=Yt({dampingFactor:.8,precision:1e-6,iterations:200,weight:function e(t){return 1}});var Ma={pageRank:function e(t){var r=_a(t),a=r.dampingFactor,n=r.precision,i=r.iterations,o=r.weight;var s=this._private.cy;var l=this.byGroup(),u=l.nodes,v=l.edges;var f=u.length;var c=f*f;var d=v.length;var h=new Array(c);var p=new Array(f);var g=(1-a)/f;for(var y=0;y<f;y++){for(var m=0;m<f;m++){var b=y*f+m;h[b]=0}p[y]=0}for(var x=0;x<d;x++){var w=v[x];var E=w.data("source");var T=w.data("target");if(E===T){continue}var k=u.indexOfId(E);var C=u.indexOfId(T);var P=o(w);var S=C*f+k;h[S]+=P;p[k]+=P}var D=1/f+g;for(var B=0;B<f;B++){if(p[B]===0){for(var A=0;A<f;A++){var _=A*f+B;h[_]=D}}else{for(var M=0;M<f;M++){var I=M*f+B;h[I]=h[I]/p[B]+g}}}var R=new Array(f);var N=new Array(f);var L;for(var O=0;O<f;O++){R[O]=1}for(var z=0;z<i;z++){for(var F=0;F<f;F++){N[F]=0}for(var V=0;V<f;V++){for(var j=0;j<f;j++){var X=V*f+j;N[V]+=h[X]*R[j]}}Fr(N);L=R;R=N;N=L;var Y=0;for(var q=0;q<f;q++){var W=L[q]-R[q];Y+=W*W}if(Y<n){break}}var U={rank:function e(t){t=s.collection(t)[0];return R[u.indexOf(t)]}};return U}};var Ia=Yt({root:null,weight:function e(t){return 1},directed:false,alpha:0});var Ra={degreeCentralityNormalized:function e(t){t=Ia(t);var r=this.cy();var a=this.nodes();var n=a.length;if(!t.directed){var i={};var o=0;for(var s=0;s<n;s++){var l=a[s];t.root=l;var u=this.degreeCentrality(t);if(o<u.degree){o=u.degree}i[l.id()]=u.degree}return{degree:function e(t){if(o===0){return 0}if(D(t)){t=r.filter(t)}return i[t.id()]/o}}}else{var v={};var f={};var c=0;var d=0;for(var h=0;h<n;h++){var p=a[h];var g=p.id();t.root=p;var y=this.degreeCentrality(t);if(c<y.indegree)c=y.indegree;if(d<y.outdegree)d=y.outdegree;v[g]=y.indegree;f[g]=y.outdegree}return{indegree:function e(t){if(c==0){return 0}if(D(t)){t=r.filter(t)}return v[t.id()]/c},outdegree:function e(t){if(d===0){return 0}if(D(t)){t=r.filter(t)}return f[t.id()]/d}}}},degreeCentrality:function e(t){t=Ia(t);var r=this.cy();var a=this;var n=t,i=n.root,o=n.weight,s=n.directed,l=n.alpha;i=r.collection(i)[0];if(!s){var u=i.connectedEdges().intersection(a);var v=u.length;var f=0;for(var c=0;c<u.length;c++){f+=o(u[c])}return{degree:Math.pow(v,1-l)*Math.pow(f,l)}}else{var d=i.connectedEdges();var h=d.filter((function(e){return e.target().same(i)&&a.has(e)}));var p=d.filter((function(e){return e.source().same(i)&&a.has(e)}));var g=h.length;var y=p.length;var m=0;var b=0;for(var x=0;x<h.length;x++){m+=o(h[x])}for(var w=0;w<p.length;w++){b+=o(p[w])}return{indegree:Math.pow(g,1-l)*Math.pow(m,l),outdegree:Math.pow(y,1-l)*Math.pow(b,l)}}}};Ra.dc=Ra.degreeCentrality;Ra.dcn=Ra.degreeCentralityNormalised=Ra.degreeCentralityNormalized;var Na=Yt({harmonic:true,weight:function e(){return 1},directed:false,root:null});var La={closenessCentralityNormalized:function e(t){var r=Na(t),a=r.harmonic,n=r.weight,i=r.directed;var o=this.cy();var s={};var l=0;var u=this.nodes();var v=this.floydWarshall({weight:n,directed:i});for(var f=0;f<u.length;f++){var c=0;var d=u[f];for(var h=0;h<u.length;h++){if(f!==h){var p=v.distance(d,u[h]);if(a){c+=1/p}else{c+=p}}}if(!a){c=1/c}if(l<c){l=c}s[d.id()]=c}return{closeness:function e(t){if(l==0){return 0}if(D(t)){t=o.filter(t)[0].id()}else{t=t.id()}return s[t]/l}}},closenessCentrality:function e(t){var r=Na(t),a=r.root,n=r.weight,i=r.directed,o=r.harmonic;a=this.filter(a)[0];var s=this.dijkstra({root:a,weight:n,directed:i});var l=0;var u=this.nodes();for(var v=0;v<u.length;v++){var f=u[v];if(!f.same(a)){var c=s.distanceTo(f);if(o){l+=1/c}else{l+=c}}}return o?l:1/l}};La.cc=La.closenessCentrality;La.ccn=La.closenessCentralityNormalised=La.closenessCentralityNormalized;var Oa=Yt({weight:null,directed:false});var za={betweennessCentrality:function e(t){var r=Oa(t),a=r.directed,n=r.weight;var i=n!=null;var o=this.cy();var s=this.nodes();var l={};var u={};var v=0;var f={set:function e(t,r){u[t]=r;if(r>v){v=r}},get:function e(t){return u[t]}};for(var c=0;c<s.length;c++){var d=s[c];var h=d.id();if(a){l[h]=d.outgoers().nodes()}else{l[h]=d.openNeighborhood().nodes()}f.set(h,0)}var p=function e(){var t=s[g].id();var r=[];var a={};var u={};var v={};var c=new fr((function(e,t){return v[e]-v[t]}));for(var d=0;d<s.length;d++){var h=s[d].id();a[h]=[];u[h]=0;v[h]=Infinity}u[t]=1;v[t]=0;c.push(t);while(!c.empty()){var p=c.pop();r.push(p);if(i){for(var y=0;y<l[p].length;y++){var m=l[p][y];var b=o.getElementById(p);var x=undefined;if(b.edgesTo(m).length>0){x=b.edgesTo(m)[0]}else{x=m.edgesTo(b)[0]}var w=n(x);m=m.id();if(v[m]>v[p]+w){v[m]=v[p]+w;if(c.nodes.indexOf(m)<0){c.push(m)}else{c.updateItem(m)}u[m]=0;a[m]=[]}if(v[m]==v[p]+w){u[m]=u[m]+u[p];a[m].push(p)}}}else{for(var E=0;E<l[p].length;E++){var T=l[p][E].id();if(v[T]==Infinity){c.push(T);v[T]=v[p]+1}if(v[T]==v[p]+1){u[T]=u[T]+u[p];a[T].push(p)}}}}var k={};for(var C=0;C<s.length;C++){k[s[C].id()]=0}while(r.length>0){var P=r.pop();for(var S=0;S<a[P].length;S++){var D=a[P][S];k[D]=k[D]+u[D]/u[P]*(1+k[P])}if(P!=s[g].id()){f.set(P,f.get(P)+k[P])}}};for(var g=0;g<s.length;g++){p()}var y={betweenness:function e(t){var r=o.collection(t).id();return f.get(r)},betweennessNormalized:function e(t){if(v==0){return 0}var r=o.collection(t).id();return f.get(r)/v}};y.betweennessNormalised=y.betweennessNormalized;return y}};za.bc=za.betweennessCentrality;var Fa=Yt({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(e){return 1}]});var Va=function e(t){return Fa(t)};var ja=function e(t,r){var a=0;for(var n=0;n<r.length;n++){a+=r[n](t)}return a};var Xa=function e(t,r,a){for(var n=0;n<r;n++){t[n*r+n]=a}};var Ya=function e(t,r){var a;for(var n=0;n<r;n++){a=0;for(var i=0;i<r;i++){a+=t[i*r+n]}for(var o=0;o<r;o++){t[o*r+n]=t[o*r+n]/a}}};var qa=function e(t,r,a){var n=new Array(a*a);for(var i=0;i<a;i++){for(var o=0;o<a;o++){n[i*a+o]=0}for(var s=0;s<a;s++){for(var l=0;l<a;l++){n[i*a+l]+=t[i*a+s]*r[s*a+l]}}}return n};var Wa=function e(t,r,a){var n=t.slice(0);for(var i=1;i<a;i++){t=qa(t,n,r)}return t};var Ua=function e(t,r,a){var n=new Array(r*r);for(var i=0;i<r*r;i++){n[i]=Math.pow(t[i],a)}Ya(n,r);return n};var Ga=function e(t,r,a,n){for(var i=0;i<a;i++){var o=Math.round(t[i]*Math.pow(10,n))/Math.pow(10,n);var s=Math.round(r[i]*Math.pow(10,n))/Math.pow(10,n);if(o!==s){return false}}return true};var Ha=function e(t,r,a,n){var i=[];for(var o=0;o<r;o++){var s=[];for(var l=0;l<r;l++){if(Math.round(t[o*r+l]*1e3)/1e3>0){s.push(a[l])}}if(s.length!==0){i.push(n.collection(s))}}return i};var Ka=function e(t,r){for(var a=0;a<t.length;a++){if(!r[a]||t[a].id()!==r[a].id()){return false}}return true};var Za=function e(t){for(var r=0;r<t.length;r++){for(var a=0;a<t.length;a++){if(r!=a&&Ka(t[r],t[a])){t.splice(a,1)}}}return t};var $a=function e(t){var r=this.nodes();var a=this.edges();var n=this.cy();var i=Va(t);var o={};for(var s=0;s<r.length;s++){o[r[s].id()]=s}var l=r.length,u=l*l;var v=new Array(u),f;for(var c=0;c<u;c++){v[c]=0}for(var d=0;d<a.length;d++){var h=a[d];var p=o[h.source().id()];var g=o[h.target().id()];var y=ja(h,i.attributes);v[p*l+g]+=y;v[g*l+p]+=y}Xa(v,l,i.multFactor);Ya(v,l);var m=true;var b=0;while(m&&b<i.maxIterations){m=false;f=Wa(v,l,i.expandFactor);v=Ua(f,l,i.inflateFactor);if(!Ga(v,f,u,4)){m=true}b++}var x=Ha(v,l,r,n);x=Za(x);return x};var Qa={markovClustering:$a,mcl:$a};var Ja=function e(t){return t};var en=function e(t,r){return Math.abs(r-t)};var tn=function e(t,r,a){return t+en(r,a)};var rn=function e(t,r,a){return t+Math.pow(a-r,2)};var an=function e(t){return Math.sqrt(t)};var nn=function e(t,r,a){return Math.max(t,en(r,a))};var on=function e(t,r,a,n,i){var o=arguments.length>5&&arguments[5]!==undefined?arguments[5]:Ja;var s=n;var l,u;for(var v=0;v<t;v++){l=r(v);u=a(v);s=i(s,l,u)}return o(s)};var sn={euclidean:function e(t,r,a){if(t>=2){return on(t,r,a,0,rn,an)}else{return on(t,r,a,0,tn)}},squaredEuclidean:function e(t,r,a){return on(t,r,a,0,rn)},manhattan:function e(t,r,a){return on(t,r,a,0,tn)},max:function e(t,r,a){return on(t,r,a,-Infinity,nn)}};sn["squared-euclidean"]=sn["squaredEuclidean"];sn["squaredeuclidean"]=sn["squaredEuclidean"];function ln(e,t,r,a,n,i){var o;if(B(e)){o=e}else{o=sn[e]||sn.euclidean}if(t===0&&B(e)){return o(n,i)}else{return o(t,r,a,n,i)}}var un=Yt({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:false,testCentroids:null});var vn=function e(t){return un(t)};var fn=function e(t,r,a,n,i){var o=i!=="kMedoids";var s=o?function(e){return a[e]}:function(e){return n[e](a)};var l=function e(t){return n[t](r)};var u=a;var v=r;return ln(t,n.length,s,l,u,v)};var cn=function e(t,r,a){var n=a.length;var i=new Array(n);var o=new Array(n);var s=new Array(r);var l=null;for(var u=0;u<n;u++){i[u]=t.min(a[u]).value;o[u]=t.max(a[u]).value}for(var v=0;v<r;v++){l=[];for(var f=0;f<n;f++){l[f]=Math.random()*(o[f]-i[f])+i[f]}s[v]=l}return s};var dn=function e(t,r,a,n,i){var o=Infinity;var s=0;for(var l=0;l<r.length;l++){var u=fn(a,t,r[l],n,i);if(u<o){o=u;s=l}}return s};var hn=function e(t,r,a){var n=[];var i=null;for(var o=0;o<r.length;o++){i=r[o];if(a[i.id()]===t){n.push(i)}}return n};var pn=function e(t,r,a){return Math.abs(r-t)<=a};var gn=function e(t,r,a){for(var n=0;n<t.length;n++){for(var i=0;i<t[n].length;i++){var o=Math.abs(t[n][i]-r[n][i]);if(o>a){return false}}}return true};var yn=function e(t,r,a){for(var n=0;n<a;n++){if(t===r[n])return true}return false};var mn=function e(t,r){var a=new Array(r);if(t.length<50){for(var n=0;n<r;n++){var i=t[Math.floor(Math.random()*t.length)];while(yn(i,a,n)){i=t[Math.floor(Math.random()*t.length)]}a[n]=i}}else{for(var o=0;o<r;o++){a[o]=t[Math.floor(Math.random()*t.length)]}}return a};var bn=function e(t,r,a){var n=0;for(var i=0;i<r.length;i++){n+=fn("manhattan",r[i],t,a,"kMedoids")}return n};var xn=function e(t){var r=this.cy();var a=this.nodes();var n=null;var i=vn(t);var o=new Array(i.k);var s={};var l;if(i.testMode){if(typeof i.testCentroids==="number"){i.testCentroids;l=cn(a,i.k,i.attributes)}else if(b(i.testCentroids)==="object"){l=i.testCentroids}else{l=cn(a,i.k,i.attributes)}}else{l=cn(a,i.k,i.attributes)}var u=true;var v=0;while(u&&v<i.maxIterations){for(var f=0;f<a.length;f++){n=a[f];s[n.id()]=dn(n,l,i.distance,i.attributes,"kMeans")}u=false;for(var c=0;c<i.k;c++){var d=hn(c,a,s);if(d.length===0){continue}var h=i.attributes.length;var p=l[c];var g=new Array(h);var y=new Array(h);for(var m=0;m<h;m++){y[m]=0;for(var x=0;x<d.length;x++){n=d[x];y[m]+=i.attributes[m](n)}g[m]=y[m]/d.length;if(!pn(g[m],p[m],i.sensitivityThreshold)){u=true}}l[c]=g;o[c]=r.collection(d)}v++}return o};var wn=function e(t){var r=this.cy();var a=this.nodes();var n=null;var i=vn(t);var o=new Array(i.k);var s;var l={};var u;var v=new Array(i.k);if(i.testMode){if(typeof i.testCentroids==="number");else if(b(i.testCentroids)==="object"){s=i.testCentroids}else{s=mn(a,i.k)}}else{s=mn(a,i.k)}var f=true;var c=0;while(f&&c<i.maxIterations){for(var d=0;d<a.length;d++){n=a[d];l[n.id()]=dn(n,s,i.distance,i.attributes,"kMedoids")}f=false;for(var h=0;h<s.length;h++){var p=hn(h,a,l);if(p.length===0){continue}v[h]=bn(s[h],p,i.attributes);for(var g=0;g<p.length;g++){u=bn(p[g],p,i.attributes);if(u<v[h]){v[h]=u;s[h]=p[g];f=true}}o[h]=r.collection(p)}c++}return o};var En=function e(t,r,a,n,i){var o,s;for(var l=0;l<r.length;l++){for(var u=0;u<t.length;u++){n[l][u]=Math.pow(a[l][u],i.m)}}for(var v=0;v<t.length;v++){for(var f=0;f<i.attributes.length;f++){o=0;s=0;for(var c=0;c<r.length;c++){o+=n[c][v]*i.attributes[f](r[c]);s+=n[c][v]}t[v][f]=o/s}}};var Tn=function e(t,r,a,n,i){for(var o=0;o<t.length;o++){r[o]=t[o].slice()}var s,l,u;var v=2/(i.m-1);for(var f=0;f<a.length;f++){for(var c=0;c<n.length;c++){s=0;for(var d=0;d<a.length;d++){l=fn(i.distance,n[c],a[f],i.attributes,"cmeans");u=fn(i.distance,n[c],a[d],i.attributes,"cmeans");s+=Math.pow(l/u,v)}t[c][f]=1/s}}};var kn=function e(t,r,a,n){var i=new Array(a.k);for(var o=0;o<i.length;o++){i[o]=[]}var s;var l;for(var u=0;u<r.length;u++){s=-Infinity;l=-1;for(var v=0;v<r[0].length;v++){if(r[u][v]>s){s=r[u][v];l=v}}i[l].push(t[u])}for(var f=0;f<i.length;f++){i[f]=n.collection(i[f])}return i};var Cn=function e(t){var r=this.cy();var a=this.nodes();var n=vn(t);var i;var o;var s;var l;var u;l=new Array(a.length);for(var v=0;v<a.length;v++){l[v]=new Array(n.k)}s=new Array(a.length);for(var f=0;f<a.length;f++){s[f]=new Array(n.k)}for(var c=0;c<a.length;c++){var d=0;for(var h=0;h<n.k;h++){s[c][h]=Math.random();d+=s[c][h]}for(var p=0;p<n.k;p++){s[c][p]=s[c][p]/d}}o=new Array(n.k);for(var g=0;g<n.k;g++){o[g]=new Array(n.attributes.length)}u=new Array(a.length);for(var y=0;y<a.length;y++){u[y]=new Array(n.k)}var m=true;var b=0;while(m&&b<n.maxIterations){m=false;En(o,a,s,u,n);Tn(s,l,o,a,n);if(!gn(s,l,n.sensitivityThreshold)){m=true}b++}i=kn(a,s,n,r);return{clusters:i,degreeOfMembership:s}};var Pn={kMeans:xn,kMedoids:wn,fuzzyCMeans:Cn,fcm:Cn};var Sn=Yt({distance:"euclidean",linkage:"min",mode:"threshold",threshold:Infinity,addDendrogram:false,dendrogramDepth:0,attributes:[]});var Dn={single:"min",complete:"max"};var Bn=function e(t){var r=Sn(t);var a=Dn[r.linkage];if(a!=null){r.linkage=a}return r};var An=function e(t,r,a,n,i){var o=0;var s=Infinity;var l;var u=i.attributes;var v=function e(t,r){return ln(i.distance,u.length,(function(e){return u[e](t)}),(function(e){return u[e](r)}),t,r)};for(var f=0;f<t.length;f++){var c=t[f].key;var d=a[c][n[c]];if(d<s){o=c;s=d}}if(i.mode==="threshold"&&s>=i.threshold||i.mode==="dendrogram"&&t.length===1){return false}var h=r[o];var p=r[n[o]];var g;if(i.mode==="dendrogram"){g={left:h,right:p,key:h.key}}else{g={value:h.value.concat(p.value),key:h.key}}t[h.index]=g;t.splice(p.index,1);r[h.key]=g;for(var y=0;y<t.length;y++){var m=t[y];if(h.key===m.key){l=Infinity}else if(i.linkage==="min"){l=a[h.key][m.key];if(a[h.key][m.key]>a[p.key][m.key]){l=a[p.key][m.key]}}else if(i.linkage==="max"){l=a[h.key][m.key];if(a[h.key][m.key]<a[p.key][m.key]){l=a[p.key][m.key]}}else if(i.linkage==="mean"){l=(a[h.key][m.key]*h.size+a[p.key][m.key]*p.size)/(h.size+p.size)}else{if(i.mode==="dendrogram")l=v(m.value,h.value);else l=v(m.value[0],h.value[0])}a[h.key][m.key]=a[m.key][h.key]=l}for(var b=0;b<t.length;b++){var x=t[b].key;if(n[x]===h.key||n[x]===p.key){var w=x;for(var E=0;E<t.length;E++){var T=t[E].key;if(a[x][T]<a[x][w]){w=T}}n[x]=w}t[b].index=b}h.key=p.key=h.index=p.index=null;return true};var _n=function e(t,r,a){if(!t)return;if(t.value){r.push(t.value)}else{if(t.left)_n(t.left,r);if(t.right)_n(t.right,r)}};var Mn=function e(t,r){if(!t)return"";if(t.left&&t.right){var a=Mn(t.left,r);var n=Mn(t.right,r);var i=r.add({group:"nodes",data:{id:a+","+n}});r.add({group:"edges",data:{source:a,target:i.id()}});r.add({group:"edges",data:{source:n,target:i.id()}});return i.id()}else if(t.value){return t.value.id()}};var In=function e(t,r,a){if(!t)return[];var n=[],i=[],o=[];if(r===0){if(t.left)_n(t.left,n);if(t.right)_n(t.right,i);o=n.concat(i);return[a.collection(o)]}else if(r===1){if(t.value){return[a.collection(t.value)]}else{if(t.left)_n(t.left,n);if(t.right)_n(t.right,i);return[a.collection(n),a.collection(i)]}}else{if(t.value){return[a.collection(t.value)]}else{if(t.left)n=In(t.left,r-1,a);if(t.right)i=In(t.right,r-1,a);return n.concat(i)}}};var Rn=function e(t){var r=this.cy();var a=this.nodes();var n=Bn(t);var i=n.attributes;var o=function e(t,r){return ln(n.distance,i.length,(function(e){return i[e](t)}),(function(e){return i[e](r)}),t,r)};var s=[];var l=[];var u=[];var v=[];for(var f=0;f<a.length;f++){var c={value:n.mode==="dendrogram"?a[f]:[a[f]],key:f,index:f};s[f]=c;v[f]=c;l[f]=[];u[f]=0}for(var d=0;d<s.length;d++){for(var h=0;h<=d;h++){var p=undefined;if(n.mode==="dendrogram"){p=d===h?Infinity:o(s[d].value,s[h].value)}else{p=d===h?Infinity:o(s[d].value[0],s[h].value[0])}l[d][h]=p;l[h][d]=p;if(p<l[d][u[d]]){u[d]=h}}}var g=An(s,v,l,u,n);while(g){g=An(s,v,l,u,n)}var y;if(n.mode==="dendrogram"){y=In(s[0],n.dendrogramDepth,r);if(n.addDendrogram)Mn(s[0],r)}else{y=new Array(s.length);s.forEach((function(e,t){e.key=e.index=null;y[t]=r.collection(e.value)}))}return y};var Nn={hierarchicalClustering:Rn,hca:Rn};var Ln=Yt({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]});var On=function e(t){var r=t.damping;var a=t.preference;if(!(.5<=r&&r<1)){Rt("Damping must range on [0.5, 1).  Got: ".concat(r))}var n=["median","mean","min","max"];if(!(n.some((function(e){return e===a}))||I(a))){Rt("Preference must be one of [".concat(n.map((function(e){return"'".concat(e,"'")})).join(", "),"] or a number.  Got: ").concat(a))}return Ln(t)};var zn=function e(t,r,a,n){var i=function e(t,r){return n[r](t)};return-ln(t,n.length,(function(e){return i(r,e)}),(function(e){return i(a,e)}),r,a)};var Fn=function e(t,r){var a=null;if(r==="median"){a=Mr(t)}else if(r==="mean"){a=_r(t)}else if(r==="min"){a=Br(t)}else if(r==="max"){a=Ar(t)}else{a=r}return a};var Vn=function e(t,r,a){var n=[];for(var i=0;i<t;i++){if(r[i*t+i]+a[i*t+i]>0){n.push(i)}}return n};var jn=function e(t,r,a){var n=[];for(var i=0;i<t;i++){var o=-1;var s=-Infinity;for(var l=0;l<a.length;l++){var u=a[l];if(r[i*t+u]>s){o=u;s=r[i*t+u]}}if(o>0){n.push(o)}}for(var v=0;v<a.length;v++){n[a[v]]=a[v]}return n};var Xn=function e(t,r,a){var n=jn(t,r,a);for(var i=0;i<a.length;i++){var o=[];for(var s=0;s<n.length;s++){if(n[s]===a[i]){o.push(s)}}var l=-1;var u=-Infinity;for(var v=0;v<o.length;v++){var f=0;for(var c=0;c<o.length;c++){f+=r[o[c]*t+o[v]]}if(f>u){l=v;u=f}}a[i]=o[l]}n=jn(t,r,a);return n};var Yn=function e(t){var r=this.cy();var a=this.nodes();var n=On(t);var i={};for(var o=0;o<a.length;o++){i[a[o].id()]=o}var s;var l;var u;var v;var f;var c;s=a.length;l=s*s;u=new Array(l);for(var d=0;d<l;d++){u[d]=-Infinity}for(var h=0;h<s;h++){for(var p=0;p<s;p++){if(h!==p){u[h*s+p]=zn(n.distance,a[h],a[p],n.attributes)}}}v=Fn(u,n.preference);for(var g=0;g<s;g++){u[g*s+g]=v}f=new Array(l);for(var y=0;y<l;y++){f[y]=0}c=new Array(l);for(var m=0;m<l;m++){c[m]=0}var b=new Array(s);var x=new Array(s);var w=new Array(s);for(var E=0;E<s;E++){b[E]=0;x[E]=0;w[E]=0}var T=new Array(s*n.minIterations);for(var k=0;k<T.length;k++){T[k]=0}var C;for(C=0;C<n.maxIterations;C++){for(var P=0;P<s;P++){var S=-Infinity,D=-Infinity,B=-1,A=0;for(var _=0;_<s;_++){b[_]=f[P*s+_];A=c[P*s+_]+u[P*s+_];if(A>=S){D=S;S=A;B=_}else if(A>D){D=A}}for(var M=0;M<s;M++){f[P*s+M]=(1-n.damping)*(u[P*s+M]-S)+n.damping*b[M]}f[P*s+B]=(1-n.damping)*(u[P*s+B]-D)+n.damping*b[B]}for(var I=0;I<s;I++){var R=0;for(var N=0;N<s;N++){b[N]=c[N*s+I];x[N]=Math.max(0,f[N*s+I]);R+=x[N]}R-=x[I];x[I]=f[I*s+I];R+=x[I];for(var L=0;L<s;L++){c[L*s+I]=(1-n.damping)*Math.min(0,R-x[L])+n.damping*b[L]}c[I*s+I]=(1-n.damping)*(R-x[I])+n.damping*b[I]}var O=0;for(var z=0;z<s;z++){var F=c[z*s+z]+f[z*s+z]>0?1:0;T[C%n.minIterations*s+z]=F;O+=F}if(O>0&&(C>=n.minIterations-1||C==n.maxIterations-1)){var V=0;for(var j=0;j<s;j++){w[j]=0;for(var X=0;X<n.minIterations;X++){w[j]+=T[X*s+j]}if(w[j]===0||w[j]===n.minIterations){V++}}if(V===s){break}}}var Y=Vn(s,f,c);var q=Xn(s,u,Y);var W={};for(var U=0;U<Y.length;U++){W[Y[U]]=[]}for(var G=0;G<a.length;G++){var H=i[a[G].id()];var K=q[H];if(K!=null){W[K].push(a[G])}}var Z=new Array(Y.length);for(var $=0;$<Y.length;$++){Z[$]=r.collection(W[Y[$]])}return Z};var qn={affinityPropagation:Yn,ap:Yn};var Wn=Yt({root:undefined,directed:false});var Un={hierholzer:function e(t){if(!_(t)){var r=arguments;t={root:r[0],directed:r[1]}}var a=Wn(t),n=a.root,i=a.directed;var o=this;var s=false;var l;var u;var v;if(n)v=D(n)?this.filter(n)[0].id():n[0].id();var f={};var c={};if(i){o.forEach((function(e){var t=e.id();if(e.isNode()){var r=e.indegree(true);var a=e.outdegree(true);var n=r-a;var i=a-r;if(n==1){if(l)s=true;else l=t}else if(i==1){if(u)s=true;else u=t}else if(i>1||n>1){s=true}f[t]=[];e.outgoers().forEach((function(e){if(e.isEdge())f[t].push(e.id())}))}else{c[t]=[undefined,e.target().id()]}}))}else{o.forEach((function(e){var t=e.id();if(e.isNode()){var r=e.degree(true);if(r%2){if(!l)l=t;else if(!u)u=t;else s=true}f[t]=[];e.connectedEdges().forEach((function(e){return f[t].push(e.id())}))}else{c[t]=[e.source().id(),e.target().id()]}}))}var d={found:false,trail:undefined};if(s)return d;else if(u&&l){if(i){if(v&&u!=v){return d}v=u}else{if(v&&u!=v&&l!=v){return d}else if(!v){v=u}}}else{if(!v)v=o[0].id()}var h=function e(t){var r=t;var a=[t];var n,o,s;while(f[r].length){n=f[r].shift();o=c[n][0];s=c[n][1];if(r!=s){f[s]=f[s].filter((function(e){return e!=n}));r=s}else if(!i&&r!=o){f[o]=f[o].filter((function(e){return e!=n}));r=o}a.unshift(n);a.unshift(r)}return a};var p=[];var g=[];g=h(v);while(g.length!=1){if(f[g[0]].length==0){p.unshift(o.getElementById(g.shift()));p.unshift(o.getElementById(g.shift()))}else{g=h(g.shift()).concat(g)}}p.unshift(o.getElementById(g.shift()));for(var y in f){if(f[y].length){return d}}d.found=true;d.trail=this.spawn(p,true);return d}};var Gn=function e(){var t=this;var r={};var a=0;var n=0;var i=[];var o=[];var s={};var l=function e(a,n){var s=o.length-1;var l=[];var u=t.spawn();while(o[s].x!=a||o[s].y!=n){l.push(o.pop().edge);s--}l.push(o.pop().edge);l.forEach((function(e){var a=e.connectedNodes().intersection(t);u.merge(e);a.forEach((function(e){var a=e.id();var n=e.connectedEdges().intersection(t);u.merge(e);if(!r[a].cutVertex){u.merge(n)}else{u.merge(n.filter((function(e){return e.isLoop()})))}}))}));i.push(u)};var u=function e(v,f,c){if(v===c)n+=1;r[f]={id:a,low:a++,cutVertex:false};var d=t.getElementById(f).connectedEdges().intersection(t);if(d.size()===0){i.push(t.spawn(t.getElementById(f)))}else{var h,p,g,y;d.forEach((function(e){h=e.source().id();p=e.target().id();g=h===f?p:h;if(g!==c){y=e.id();if(!s[y]){s[y]=true;o.push({x:f,y:g,edge:e})}if(!(g in r)){u(v,g,f);r[f].low=Math.min(r[f].low,r[g].low);if(r[f].id<=r[g].low){r[f].cutVertex=true;l(f,g)}}else{r[f].low=Math.min(r[f].low,r[g].id)}}}))}};t.forEach((function(e){if(e.isNode()){var t=e.id();if(!(t in r)){n=0;u(t,t);r[t].cutVertex=n>1}}}));var v=Object.keys(r).filter((function(e){return r[e].cutVertex})).map((function(e){return t.getElementById(e)}));return{cut:t.spawn(v),components:i}};var Hn={hopcroftTarjanBiconnected:Gn,htbc:Gn,htb:Gn,hopcroftTarjanBiconnectedComponents:Gn};var Kn=function e(){var t=this;var r={};var a=0;var n=[];var i=[];var o=t.spawn(t);var s=function e(l){i.push(l);r[l]={index:a,low:a++,explored:false};var u=t.getElementById(l).connectedEdges().intersection(t);u.forEach((function(e){var t=e.target().id();if(t!==l){if(!(t in r)){s(t)}if(!r[t].explored){r[l].low=Math.min(r[l].low,r[t].low)}}}));if(r[l].index===r[l].low){var v=t.spawn();for(;;){var f=i.pop();v.merge(t.getElementById(f));r[f].low=r[l].index;r[f].explored=true;if(f===l){break}}var c=v.edgesWith(v);var d=v.merge(c);n.push(d);o=o.difference(d)}};t.forEach((function(e){if(e.isNode()){var t=e.id();if(!(t in r)){s(t)}}}));return{cut:o,components:n}};var Zn={tarjanStronglyConnected:Kn,tsc:Kn,tscc:Kn,tarjanStronglyConnectedComponents:Kn};var $n={};[rr,dr,hr,gr,mr,xr,kr,Ma,Ra,La,za,Qa,Pn,Nn,qn,Un,Hn,Zn].forEach((function(e){se($n,e)}));var Qn=0;var Jn=1;var ei=2;var ti=function e(t){if(!(this instanceof ti))return new ti(t);this.id="Thenable/1.0.7";this.state=Qn;this.fulfillValue=undefined;this.rejectReason=undefined;this.onFulfilled=[];this.onRejected=[];this.proxy={then:this.then.bind(this)};if(typeof t==="function")t.call(this,this.fulfill.bind(this),this.reject.bind(this))};ti.prototype={fulfill:function e(t){return ri(this,Jn,"fulfillValue",t)},reject:function e(t){return ri(this,ei,"rejectReason",t)},then:function e(t,r){var a=this;var n=new ti;a.onFulfilled.push(ii(t,n,"fulfill"));a.onRejected.push(ii(r,n,"reject"));ai(a);return n.proxy}};var ri=function e(t,r,a,n){if(t.state===Qn){t.state=r;t[a]=n;ai(t)}return t};var ai=function e(t){if(t.state===Jn)ni(t,"onFulfilled",t.fulfillValue);else if(t.state===ei)ni(t,"onRejected",t.rejectReason)};var ni=function e(t,r,a){if(t[r].length===0)return;var n=t[r];t[r]=[];var i=function e(){for(var t=0;t<n.length;t++)n[t](a)};if(typeof setImmediate==="function")setImmediate(i);else setTimeout(i,0)};var ii=function e(t,r,a){return function(e){if(typeof t!=="function")r[a].call(r,e);else{var n;try{n=t(e)}catch(i){r.reject(i);return}oi(r,n)}}};var oi=function e(t,r){if(t===r||t.proxy===r){t.reject(new TypeError("cannot resolve promise with itself"));return}var a;if(b(r)==="object"&&r!==null||typeof r==="function"){try{a=r.then}catch(i){t.reject(i);return}}if(typeof a==="function"){var n=false;try{a.call(r,(function(e){if(n)return;n=true;if(e===r)t.reject(new TypeError("circular thenable chain"));else oi(t,e)}),(function(e){if(n)return;n=true;t.reject(e)}))}catch(i){if(!n)t.reject(i)}return}t.fulfill(r)};ti.all=function(e){return new ti((function(t,r){var a=new Array(e.length);var n=0;var i=function r(i,o){a[i]=o;n++;if(n===e.length){t(a)}};for(var o=0;o<e.length;o++){(function(t){var a=e[t];var n=a!=null&&a.then!=null;if(n){a.then((function(e){i(t,e)}),(function(e){r(e)}))}else{var o=a;i(t,o)}})(o)}}))};ti.resolve=function(e){return new ti((function(t,r){t(e)}))};ti.reject=function(e){return new ti((function(t,r){r(e)}))};var si=typeof Promise!=="undefined"?Promise:ti;var li=function e(t,r,a){var n=F(t);var i=!n;var o=this._private=se({duration:1e3},r,a);o.target=t;o.style=o.style||o.css;o.started=false;o.playing=false;o.hooked=false;o.applying=false;o.progress=0;o.completes=[];o.frames=[];if(o.complete&&B(o.complete)){o.completes.push(o.complete)}if(i){var s=t.position();o.startPosition=o.startPosition||{x:s.x,y:s.y};o.startStyle=o.startStyle||t.cy().style().getAnimationStartStyle(t,o.style)}if(n){var l=t.pan();o.startPan={x:l.x,y:l.y};o.startZoom=t.zoom()}this.length=1;this[0]=this};var ui=li.prototype;se(ui,{instanceString:function e(){return"animation"},hook:function e(){var t=this._private;if(!t.hooked){var r;var a=t.target._private.animation;if(t.queue){r=a.queue}else{r=a.current}r.push(this);if(L(t.target)){t.target.cy().addToAnimationPool(t.target)}t.hooked=true}return this},play:function e(){var t=this._private;if(t.progress===1){t.progress=0}t.playing=true;t.started=false;t.stopped=false;this.hook();return this},playing:function e(){return this._private.playing},apply:function e(){var t=this._private;t.applying=true;t.started=false;t.stopped=false;this.hook();return this},applying:function e(){return this._private.applying},pause:function e(){var t=this._private;t.playing=false;t.started=false;return this},stop:function e(){var t=this._private;t.playing=false;t.started=false;t.stopped=true;return this},rewind:function e(){return this.progress(0)},fastforward:function e(){return this.progress(1)},time:function e(t){var r=this._private;if(t===undefined){return r.progress*r.duration}else{return this.progress(t/r.duration)}},progress:function e(t){var r=this._private;var a=r.playing;if(t===undefined){return r.progress}else{if(a){this.pause()}r.progress=t;r.started=false;if(a){this.play()}}return this},completed:function e(){return this._private.progress===1},reverse:function e(){var t=this._private;var r=t.playing;if(r){this.pause()}t.progress=1-t.progress;t.started=false;var a=function e(r,a){var n=t[r];if(n==null){return}t[r]=t[a];t[a]=n};a("zoom","startZoom");a("pan","startPan");a("position","startPosition");if(t.style){for(var n=0;n<t.style.length;n++){var i=t.style[n];var o=i.name;var s=t.startStyle[o];t.startStyle[o]=i;t.style[n]=s}}if(r){this.play()}return this},promise:function e(t){var r=this._private;var a;switch(t){case"frame":a=r.frames;break;default:case"complete":case"completed":a=r.completes}return new si((function(e,t){a.push((function(){e()}))}))}});ui.complete=ui.completed;ui.run=ui.play;ui.running=ui.playing;var vi={animated:function e(){return function e(){var t=this;var r=t.length!==undefined;var a=r?t:[t];var n=this._private.cy||this;if(!n.styleEnabled()){return false}var i=a[0];if(i){return i._private.animation.current.length>0}}},clearQueue:function e(){return function e(){var t=this;var r=t.length!==undefined;var a=r?t:[t];var n=this._private.cy||this;if(!n.styleEnabled()){return this}for(var i=0;i<a.length;i++){var o=a[i];o._private.animation.queue=[]}return this}},delay:function e(){return function e(t,r){var a=this._private.cy||this;if(!a.styleEnabled()){return this}return this.animate({delay:t,duration:t,complete:r})}},delayAnimation:function e(){return function e(t,r){var a=this._private.cy||this;if(!a.styleEnabled()){return this}return this.animation({delay:t,duration:t,complete:r})}},animation:function e(){return function e(t,r){var a=this;var n=a.length!==undefined;var i=n?a:[a];var o=this._private.cy||this;var s=!n;var l=!s;if(!o.styleEnabled()){return this}var u=o.style();t=se({},t,r);var v=Object.keys(t).length===0;if(v){return new li(i[0],t)}if(t.duration===undefined){t.duration=400}switch(t.duration){case"slow":t.duration=600;break;case"fast":t.duration=200;break}if(l){t.style=u.getPropsList(t.style||t.css);t.css=undefined}if(l&&t.renderedPosition!=null){var f=t.renderedPosition;var c=o.pan();var d=o.zoom();t.position=Sr(f,d,c)}if(s&&t.panBy!=null){var h=t.panBy;var p=o.pan();t.pan={x:p.x+h.x,y:p.y+h.y}}var g=t.center||t.centre;if(s&&g!=null){var y=o.getCenterPan(g.eles,t.zoom);if(y!=null){t.pan=y}}if(s&&t.fit!=null){var m=t.fit;var b=o.getFitViewport(m.eles||m.boundingBox,m.padding);if(b!=null){t.pan=b.pan;t.zoom=b.zoom}}if(s&&_(t.zoom)){var x=o.getZoomedViewport(t.zoom);if(x!=null){if(x.zoomed){t.zoom=x.zoom}if(x.panned){t.pan=x.pan}}else{t.zoom=null}}return new li(i[0],t)}},animate:function e(){return function e(t,r){var a=this;var n=a.length!==undefined;var i=n?a:[a];var o=this._private.cy||this;if(!o.styleEnabled()){return this}if(r){t=se({},t,r)}for(var s=0;s<i.length;s++){var l=i[s];var u=l.animated()&&(t.queue===undefined||t.queue);var v=l.animation(t,u?{queue:true}:undefined);v.play()}return this}},stop:function e(){return function e(t,r){var a=this;var n=a.length!==undefined;var i=n?a:[a];var o=this._private.cy||this;if(!o.styleEnabled()){return this}for(var s=0;s<i.length;s++){var l=i[s];var u=l._private;var v=u.animation.current;for(var f=0;f<v.length;f++){var c=v[f];var d=c._private;if(r){d.duration=0}}if(t){u.animation.queue=[]}if(!r){u.animation.current=[]}}o.notify("draw");return this}}};var fi;var ci;function di(){if(ci)return fi;ci=1;var e=Array.isArray;fi=e;return fi}var hi;var pi;function gi(){if(pi)return hi;pi=1;var e=di(),t=Je();var r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;function n(n,i){if(e(n)){return false}var o=typeof n;if(o=="number"||o=="symbol"||o=="boolean"||n==null||t(n)){return true}return a.test(n)||!r.test(n)||i!=null&&n in Object(i)}hi=n;return hi}var yi;var mi;function bi(){if(mi)return yi;mi=1;var e=Ge(),t=xe();var r="[object AsyncFunction]",a="[object Function]",n="[object GeneratorFunction]",i="[object Proxy]";function o(o){if(!t(o)){return false}var s=e(o);return s==a||s==n||s==r||s==i}yi=o;return yi}var xi;var wi;function Ei(){if(wi)return xi;wi=1;var e=Pe();var t=e["__core-js_shared__"];xi=t;return xi}var Ti;var ki;function Ci(){if(ki)return Ti;ki=1;var e=Ei();var t=function(){var t=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function r(e){return!!t&&t in e}Ti=r;return Ti}var Pi;var Si;function Di(){if(Si)return Pi;Si=1;var e=Function.prototype;var t=e.toString;function r(e){if(e!=null){try{return t.call(e)}catch(r){}try{return e+""}catch(r){}}return""}Pi=r;return Pi}var Bi;var Ai;function _i(){if(Ai)return Bi;Ai=1;var e=bi(),t=Ci(),r=xe(),a=Di();var n=/[\\^$.*+?()[\]{}|]/g;var i=/^\[object .+?Constructor\]$/;var o=Function.prototype,s=Object.prototype;var l=o.toString;var u=s.hasOwnProperty;var v=RegExp("^"+l.call(u).replace(n,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function f(n){if(!r(n)||t(n)){return false}var o=e(n)?v:i;return o.test(a(n))}Bi=f;return Bi}var Mi;var Ii;function Ri(){if(Ii)return Mi;Ii=1;function e(e,t){return e==null?undefined:e[t]}Mi=e;return Mi}var Ni;var Li;function Oi(){if(Li)return Ni;Li=1;var e=_i(),t=Ri();function r(r,a){var n=t(r,a);return e(n)?n:undefined}Ni=r;return Ni}var zi;var Fi;function Vi(){if(Fi)return zi;Fi=1;var e=Oi();var t=e(Object,"create");zi=t;return zi}var ji;var Xi;function Yi(){if(Xi)return ji;Xi=1;var e=Vi();function t(){this.__data__=e?e(null):{};this.size=0}ji=t;return ji}var qi;var Wi;function Ui(){if(Wi)return qi;Wi=1;function e(e){var t=this.has(e)&&delete this.__data__[e];this.size-=t?1:0;return t}qi=e;return qi}var Gi;var Hi;function Ki(){if(Hi)return Gi;Hi=1;var e=Vi();var t="__lodash_hash_undefined__";var r=Object.prototype;var a=r.hasOwnProperty;function n(r){var n=this.__data__;if(e){var i=n[r];return i===t?undefined:i}return a.call(n,r)?n[r]:undefined}Gi=n;return Gi}var Zi;var $i;function Qi(){if($i)return Zi;$i=1;var e=Vi();var t=Object.prototype;var r=t.hasOwnProperty;function a(t){var a=this.__data__;return e?a[t]!==undefined:r.call(a,t)}Zi=a;return Zi}var Ji;var eo;function to(){if(eo)return Ji;eo=1;var e=Vi();var t="__lodash_hash_undefined__";function r(r,a){var n=this.__data__;this.size+=this.has(r)?0:1;n[r]=e&&a===undefined?t:a;return this}Ji=r;return Ji}var ro;var ao;function no(){if(ao)return ro;ao=1;var e=Yi(),t=Ui(),r=Ki(),a=Qi(),n=to();function i(e){var t=-1,r=e==null?0:e.length;this.clear();while(++t<r){var a=e[t];this.set(a[0],a[1])}}i.prototype.clear=e;i.prototype["delete"]=t;i.prototype.get=r;i.prototype.has=a;i.prototype.set=n;ro=i;return ro}var io;var oo;function so(){if(oo)return io;oo=1;function e(){this.__data__=[];this.size=0}io=e;return io}var lo;var uo;function vo(){if(uo)return lo;uo=1;function e(e,t){return e===t||e!==e&&t!==t}lo=e;return lo}var fo;var co;function ho(){if(co)return fo;co=1;var e=vo();function t(t,r){var a=t.length;while(a--){if(e(t[a][0],r)){return a}}return-1}fo=t;return fo}var po;var go;function yo(){if(go)return po;go=1;var e=ho();var t=Array.prototype;var r=t.splice;function a(t){var a=this.__data__,n=e(a,t);if(n<0){return false}var i=a.length-1;if(n==i){a.pop()}else{r.call(a,n,1)}--this.size;return true}po=a;return po}var mo;var bo;function xo(){if(bo)return mo;bo=1;var e=ho();function t(t){var r=this.__data__,a=e(r,t);return a<0?undefined:r[a][1]}mo=t;return mo}var wo;var Eo;function To(){if(Eo)return wo;Eo=1;var e=ho();function t(t){return e(this.__data__,t)>-1}wo=t;return wo}var ko;var Co;function Po(){if(Co)return ko;Co=1;var e=ho();function t(t,r){var a=this.__data__,n=e(a,t);if(n<0){++this.size;a.push([t,r])}else{a[n][1]=r}return this}ko=t;return ko}var So;var Do;function Bo(){if(Do)return So;Do=1;var e=so(),t=yo(),r=xo(),a=To(),n=Po();function i(e){var t=-1,r=e==null?0:e.length;this.clear();while(++t<r){var a=e[t];this.set(a[0],a[1])}}i.prototype.clear=e;i.prototype["delete"]=t;i.prototype.get=r;i.prototype.has=a;i.prototype.set=n;So=i;return So}var Ao;var _o;function Mo(){if(_o)return Ao;_o=1;var e=Oi(),t=Pe();var r=e(t,"Map");Ao=r;return Ao}var Io;var Ro;function No(){if(Ro)return Io;Ro=1;var e=no(),t=Bo(),r=Mo();function a(){this.size=0;this.__data__={hash:new e,map:new(r||t),string:new e}}Io=a;return Io}var Lo;var Oo;function zo(){if(Oo)return Lo;Oo=1;function e(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}Lo=e;return Lo}var Fo;var Vo;function jo(){if(Vo)return Fo;Vo=1;var e=zo();function t(t,r){var a=t.__data__;return e(r)?a[typeof r=="string"?"string":"hash"]:a.map}Fo=t;return Fo}var Xo;var Yo;function qo(){if(Yo)return Xo;Yo=1;var e=jo();function t(t){var r=e(this,t)["delete"](t);this.size-=r?1:0;return r}Xo=t;return Xo}var Wo;var Uo;function Go(){if(Uo)return Wo;Uo=1;var e=jo();function t(t){return e(this,t).get(t)}Wo=t;return Wo}var Ho;var Ko;function Zo(){if(Ko)return Ho;Ko=1;var e=jo();function t(t){return e(this,t).has(t)}Ho=t;return Ho}var $o;var Qo;function Jo(){if(Qo)return $o;Qo=1;var e=jo();function t(t,r){var a=e(this,t),n=a.size;a.set(t,r);this.size+=a.size==n?0:1;return this}$o=t;return $o}var es;var ts;function rs(){if(ts)return es;ts=1;var e=No(),t=qo(),r=Go(),a=Zo(),n=Jo();function i(e){var t=-1,r=e==null?0:e.length;this.clear();while(++t<r){var a=e[t];this.set(a[0],a[1])}}i.prototype.clear=e;i.prototype["delete"]=t;i.prototype.get=r;i.prototype.has=a;i.prototype.set=n;es=i;return es}var as;var ns;function is(){if(ns)return as;ns=1;var e=rs();var t="Expected a function";function r(a,n){if(typeof a!="function"||n!=null&&typeof n!="function"){throw new TypeError(t)}var i=function(){var e=arguments,t=n?n.apply(this,e):e[0],r=i.cache;if(r.has(t)){return r.get(t)}var o=a.apply(this,e);i.cache=r.set(t,o)||r;return o};i.cache=new(r.Cache||e);return i}r.Cache=e;as=r;return as}var os;var ss;function ls(){if(ss)return os;ss=1;var e=is();var t=500;function r(r){var a=e(r,(function(e){if(n.size===t){n.clear()}return e}));var n=a.cache;return a}os=r;return os}var us;var vs;function fs(){if(vs)return us;vs=1;var e=ls();var t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;var r=/\\(\\)?/g;var a=e((function(e){var a=[];if(e.charCodeAt(0)===46){a.push("")}e.replace(t,(function(e,t,n,i){a.push(n?i.replace(r,"$1"):t||e)}));return a}));us=a;return us}var cs;var ds;function hs(){if(ds)return cs;ds=1;function e(e,t){var r=-1,a=e==null?0:e.length,n=Array(a);while(++r<a){n[r]=t(e[r],r,e)}return n}cs=e;return cs}var ps;var gs;function ys(){if(gs)return ps;gs=1;var e=ze(),t=hs(),r=di(),a=Je();var n=e?e.prototype:undefined,i=n?n.toString:undefined;function o(e){if(typeof e=="string"){return e}if(r(e)){return t(e,o)+""}if(a(e)){return i?i.call(e):""}var n=e+"";return n=="0"&&1/e==-Infinity?"-0":n}ps=o;return ps}var ms;var bs;function xs(){if(bs)return ms;bs=1;var e=ys();function t(t){return t==null?"":e(t)}ms=t;return ms}var ws;var Es;function Ts(){if(Es)return ws;Es=1;var e=di(),t=gi(),r=fs(),a=xs();function n(n,i){if(e(n)){return n}return t(n,i)?[n]:r(a(n))}ws=n;return ws}var ks;var Cs;function Ps(){if(Cs)return ks;Cs=1;var e=Je();function t(t){if(typeof t=="string"||e(t)){return t}var r=t+"";return r=="0"&&1/t==-Infinity?"-0":r}ks=t;return ks}var Ss;var Ds;function Bs(){if(Ds)return Ss;Ds=1;var e=Ts(),t=Ps();function r(r,a){a=e(a,r);var n=0,i=a.length;while(r!=null&&n<i){r=r[t(a[n++])]}return n&&n==i?r:undefined}Ss=r;return Ss}var As;var _s;function Ms(){if(_s)return As;_s=1;var e=Bs();function t(t,r,a){var n=t==null?undefined:e(t,r);return n===undefined?a:n}As=t;return As}var Is=Ms();var Rs=ye(Is);var Ns;var Ls;function Os(){if(Ls)return Ns;Ls=1;var e=Oi();var t=function(){try{var t=e(Object,"defineProperty");t({},"",{});return t}catch(r){}}();Ns=t;return Ns}var zs;var Fs;function Vs(){if(Fs)return zs;Fs=1;var e=Os();function t(t,r,a){if(r=="__proto__"&&e){e(t,r,{configurable:true,enumerable:true,value:a,writable:true})}else{t[r]=a}}zs=t;return zs}var js;var Xs;function Ys(){if(Xs)return js;Xs=1;var e=Vs(),t=vo();var r=Object.prototype;var a=r.hasOwnProperty;function n(r,n,i){var o=r[n];if(!(a.call(r,n)&&t(o,i))||i===undefined&&!(n in r)){e(r,n,i)}}js=n;return js}var qs;var Ws;function Us(){if(Ws)return qs;Ws=1;var e=9007199254740991;var t=/^(?:0|[1-9]\d*)$/;function r(r,a){var n=typeof r;a=a==null?e:a;return!!a&&(n=="number"||n!="symbol"&&t.test(r))&&(r>-1&&r%1==0&&r<a)}qs=r;return qs}var Gs;var Hs;function Ks(){if(Hs)return Gs;Hs=1;var e=Ys(),t=Ts(),r=Us(),a=xe(),n=Ps();function i(i,o,s,l){if(!a(i)){return i}o=t(o,i);var u=-1,v=o.length,f=v-1,c=i;while(c!=null&&++u<v){var d=n(o[u]),h=s;if(d==="__proto__"||d==="constructor"||d==="prototype"){return i}if(u!=f){var p=c[d];h=l?l(p,d,c):undefined;if(h===undefined){h=a(p)?p:r(o[u+1])?[]:{}}}e(c,d,h);c=c[d]}return i}Gs=i;return Gs}var Zs;var $s;function Qs(){if($s)return Zs;$s=1;var e=Ks();function t(t,r,a){return t==null?t:e(t,r,a)}Zs=t;return Zs}var Js=Qs();var el=ye(Js);var tl;var rl;function al(){if(rl)return tl;rl=1;function e(e,t){var r=-1,a=e.length;t||(t=Array(a));while(++r<a){t[r]=e[r]}return t}tl=e;return tl}var nl;var il;function ol(){if(il)return nl;il=1;var e=hs(),t=al(),r=di(),a=Je(),n=fs(),i=Ps(),o=xs();function s(s){if(r(s)){return e(s,i)}return a(s)?[s]:t(n(o(s)))}nl=s;return nl}var sl=ol();var ll=ye(sl);var ul={data:function e(t){var r={field:"data",bindingEvent:"data",allowBinding:false,allowSetting:false,allowGetting:false,settingEvent:"data",settingTriggersEvent:false,triggerFnName:"trigger",immutableKeys:{},updateStyle:false,beforeGet:function e(t){},beforeSet:function e(t,r){},onSet:function e(t){},canSet:function e(t){return true}};t=se({},r,t);return function e(r,a){var n=t;var i=this;var o=i.length!==undefined;var s=o?i:[i];var l=o?i[0]:i;if(D(r)){var u=r.indexOf(".")!==-1;var f=u&&ll(r);if(n.allowGetting&&a===undefined){var c;if(l){n.beforeGet(l);if(f&&l._private[n.field][r]===undefined){c=Rs(l._private[n.field],f)}else{c=l._private[n.field][r]}}return c}else if(n.allowSetting&&a!==undefined){var d=!n.immutableKeys[r];if(d){var h=v({},r,a);n.beforeSet(i,h);for(var p=0,g=s.length;p<g;p++){var y=s[p];if(n.canSet(y)){if(f&&l._private[n.field][r]===undefined){el(y._private[n.field],f,a)}else{y._private[n.field][r]=a}}}if(n.updateStyle){i.updateStyle()}n.onSet(i);if(n.settingTriggersEvent){i[n.triggerFnName](n.settingEvent)}}}}else if(n.allowSetting&&_(r)){var m=r;var b,x;var w=Object.keys(m);n.beforeSet(i,m);for(var E=0;E<w.length;E++){b=w[E];x=m[b];var T=!n.immutableKeys[b];if(T){for(var k=0;k<s.length;k++){var C=s[k];if(n.canSet(C)){C._private[n.field][b]=x}}}}if(n.updateStyle){i.updateStyle()}n.onSet(i);if(n.settingTriggersEvent){i[n.triggerFnName](n.settingEvent)}}else if(n.allowBinding&&B(r)){var P=r;i.on(n.bindingEvent,P)}else if(n.allowGetting&&r===undefined){var S;if(l){n.beforeGet(l);S=l._private[n.field]}return S}return i}},removeData:function e(t){var r={field:"data",event:"data",triggerFnName:"trigger",triggerEvent:false,immutableKeys:{}};t=se({},r,t);return function e(r){var a=t;var n=this;var i=n.length!==undefined;var o=i?n:[n];if(D(r)){var s=r.split(/\s+/);var l=s.length;for(var u=0;u<l;u++){var v=s[u];if(X(v)){continue}var f=!a.immutableKeys[v];if(f){for(var c=0,d=o.length;c<d;c++){o[c]._private[a.field][v]=undefined}}}if(a.triggerEvent){n[a.triggerFnName](a.event)}}else if(r===undefined){for(var h=0,p=o.length;h<p;h++){var g=o[h]._private[a.field];var y=Object.keys(g);for(var m=0;m<y.length;m++){var b=y[m];var x=!a.immutableKeys[b];if(x){g[b]=undefined}}}if(a.triggerEvent){n[a.triggerFnName](a.event)}}return n}}};var vl={eventAliasesOn:function e(t){var r=t;r.addListener=r.listen=r.bind=r.on;r.unlisten=r.unbind=r.off=r.removeListener;r.trigger=r.emit;r.pon=r.promiseOn=function(e,t){var r=this;var a=Array.prototype.slice.call(arguments,0);return new si((function(e,t){var n=function t(a){r.off.apply(r,o);e(a)};var i=a.concat([n]);var o=i.concat([]);r.on.apply(r,i)}))}}};var fl={};[vi,ul,vl].forEach((function(e){se(fl,e)}));var cl={animate:fl.animate(),animation:fl.animation(),animated:fl.animated(),clearQueue:fl.clearQueue(),delay:fl.delay(),delayAnimation:fl.delayAnimation(),stop:fl.stop()};var dl={classes:function e(t){var r=this;if(t===undefined){var a=[];r[0]._private.classes.forEach((function(e){return a.push(e)}));return a}else if(!A(t)){t=(t||"").match(/\S+/g)||[]}var n=[];var i=new Jt(t);for(var o=0;o<r.length;o++){var s=r[o];var l=s._private;var u=l.classes;var v=false;for(var f=0;f<t.length;f++){var c=t[f];var d=u.has(c);if(!d){v=true;break}}if(!v){v=u.size!==t.length}if(v){l.classes=i;n.push(s)}}if(n.length>0){this.spawn(n).updateStyle().emit("class")}return r},addClass:function e(t){return this.toggleClass(t,true)},hasClass:function e(t){var r=this[0];return r!=null&&r._private.classes.has(t)},toggleClass:function e(t,r){if(!A(t)){t=t.match(/\S+/g)||[]}var a=this;var n=r===undefined;var i=[];for(var o=0,s=a.length;o<s;o++){var l=a[o];var u=l._private.classes;var v=false;for(var f=0;f<t.length;f++){var c=t[f];var d=u.has(c);var h=false;if(r||n&&!d){u.add(c);h=true}else if(!r||n&&d){u["delete"](c);h=true}if(!v&&h){i.push(l);v=true}}}if(i.length>0){this.spawn(i).updateStyle().emit("class")}return a},removeClass:function e(t){return this.toggleClass(t,false)},flashClass:function e(t,r){var a=this;if(r==null){r=250}else if(r===0){return a}a.addClass(t);setTimeout((function(){a.removeClass(t)}),r);return a}};dl.className=dl.classNames=dl.classes;var hl={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:'"(?:\\\\"|[^"])*"'+"|"+"'(?:\\\\'|[^'])*'",number:Q,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};hl.variable="(?:[\\w-.]|(?:\\\\"+hl.metaChar+"))+";hl.className="(?:[\\w-]|(?:\\\\"+hl.metaChar+"))+";hl.value=hl.string+"|"+hl.number;hl.id=hl.variable;(function(){var e,t,r;e=hl.comparatorOp.split("|");for(r=0;r<e.length;r++){t=e[r];hl.comparatorOp+="|@"+t}e=hl.comparatorOp.split("|");for(r=0;r<e.length;r++){t=e[r];if(t.indexOf("!")>=0){continue}if(t==="="){continue}hl.comparatorOp+="|\\!"+t}})();var pl=function e(){return{checks:[]}};var gl={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20};var yl=[{selector:":selected",matches:function e(t){return t.selected()}},{selector:":unselected",matches:function e(t){return!t.selected()}},{selector:":selectable",matches:function e(t){return t.selectable()}},{selector:":unselectable",matches:function e(t){return!t.selectable()}},{selector:":locked",matches:function e(t){return t.locked()}},{selector:":unlocked",matches:function e(t){return!t.locked()}},{selector:":visible",matches:function e(t){return t.visible()}},{selector:":hidden",matches:function e(t){return!t.visible()}},{selector:":transparent",matches:function e(t){return t.transparent()}},{selector:":grabbed",matches:function e(t){return t.grabbed()}},{selector:":free",matches:function e(t){return!t.grabbed()}},{selector:":removed",matches:function e(t){return t.removed()}},{selector:":inside",matches:function e(t){return!t.removed()}},{selector:":grabbable",matches:function e(t){return t.grabbable()}},{selector:":ungrabbable",matches:function e(t){return!t.grabbable()}},{selector:":animated",matches:function e(t){return t.animated()}},{selector:":unanimated",matches:function e(t){return!t.animated()}},{selector:":parent",matches:function e(t){return t.isParent()}},{selector:":childless",matches:function e(t){return t.isChildless()}},{selector:":child",matches:function e(t){return t.isChild()}},{selector:":orphan",matches:function e(t){return t.isOrphan()}},{selector:":nonorphan",matches:function e(t){return t.isChild()}},{selector:":compound",matches:function e(t){if(t.isNode()){return t.isParent()}else{return t.source().isParent()||t.target().isParent()}}},{selector:":loop",matches:function e(t){return t.isLoop()}},{selector:":simple",matches:function e(t){return t.isSimple()}},{selector:":active",matches:function e(t){return t.active()}},{selector:":inactive",matches:function e(t){return!t.active()}},{selector:":backgrounding",matches:function e(t){return t.backgrounding()}},{selector:":nonbackgrounding",matches:function e(t){return!t.backgrounding()}}].sort((function(e,t){return oe(e.selector,t.selector)}));var ml=function(){var e={};var t;for(var r=0;r<yl.length;r++){t=yl[r];e[t.selector]=t.matches}return e}();var bl=function e(t,r){return ml[t](r)};var xl="("+yl.map((function(e){return e.selector})).join("|")+")";var wl=function e(t){return t.replace(new RegExp("\\\\("+hl.metaChar+")","g"),(function(e,t){return t}))};var El=function e(t,r,a){t[t.length-1]=a};var Tl=[{name:"group",query:true,regex:"("+hl.group+")",populate:function e(t,r,a){var n=p(a,1),i=n[0];r.checks.push({type:gl.GROUP,value:i==="*"?i:i+"s"})}},{name:"state",query:true,regex:xl,populate:function e(t,r,a){var n=p(a,1),i=n[0];r.checks.push({type:gl.STATE,value:i})}},{name:"id",query:true,regex:"\\#("+hl.id+")",populate:function e(t,r,a){var n=p(a,1),i=n[0];r.checks.push({type:gl.ID,value:wl(i)})}},{name:"className",query:true,regex:"\\.("+hl.className+")",populate:function e(t,r,a){var n=p(a,1),i=n[0];r.checks.push({type:gl.CLASS,value:wl(i)})}},{name:"dataExists",query:true,regex:"\\[\\s*("+hl.variable+")\\s*\\]",populate:function e(t,r,a){var n=p(a,1),i=n[0];r.checks.push({type:gl.DATA_EXIST,field:wl(i)})}},{name:"dataCompare",query:true,regex:"\\[\\s*("+hl.variable+")\\s*("+hl.comparatorOp+")\\s*("+hl.value+")\\s*\\]",populate:function e(t,r,a){var n=p(a,3),i=n[0],o=n[1],s=n[2];var l=new RegExp("^"+hl.string+"$").exec(s)!=null;if(l){s=s.substring(1,s.length-1)}else{s=parseFloat(s)}r.checks.push({type:gl.DATA_COMPARE,field:wl(i),operator:o,value:s})}},{name:"dataBool",query:true,regex:"\\[\\s*("+hl.boolOp+")\\s*("+hl.variable+")\\s*\\]",populate:function e(t,r,a){var n=p(a,2),i=n[0],o=n[1];r.checks.push({type:gl.DATA_BOOL,field:wl(o),operator:i})}},{name:"metaCompare",query:true,regex:"\\[\\[\\s*("+hl.meta+")\\s*("+hl.comparatorOp+")\\s*("+hl.number+")\\s*\\]\\]",populate:function e(t,r,a){var n=p(a,3),i=n[0],o=n[1],s=n[2];r.checks.push({type:gl.META_COMPARE,field:wl(i),operator:o,value:parseFloat(s)})}},{name:"nextQuery",separator:true,regex:hl.separator,populate:function e(t,r){var a=t.currentSubject;var n=t.edgeCount;var i=t.compoundCount;var o=t[t.length-1];if(a!=null){o.subject=a;t.currentSubject=null}o.edgeCount=n;o.compoundCount=i;t.edgeCount=0;t.compoundCount=0;var s=t[t.length++]=pl();return s}},{name:"directedEdge",separator:true,regex:hl.directedEdge,populate:function e(t,r){if(t.currentSubject==null){var a=pl();var n=r;var i=pl();a.checks.push({type:gl.DIRECTED_EDGE,source:n,target:i});El(t,r,a);t.edgeCount++;return i}else{var o=pl();var s=r;var l=pl();o.checks.push({type:gl.NODE_SOURCE,source:s,target:l});El(t,r,o);t.edgeCount++;return l}}},{name:"undirectedEdge",separator:true,regex:hl.undirectedEdge,populate:function e(t,r){if(t.currentSubject==null){var a=pl();var n=r;var i=pl();a.checks.push({type:gl.UNDIRECTED_EDGE,nodes:[n,i]});El(t,r,a);t.edgeCount++;return i}else{var o=pl();var s=r;var l=pl();o.checks.push({type:gl.NODE_NEIGHBOR,node:s,neighbor:l});El(t,r,o);return l}}},{name:"child",separator:true,regex:hl.child,populate:function e(t,r){if(t.currentSubject==null){var a=pl();var n=pl();var i=t[t.length-1];a.checks.push({type:gl.CHILD,parent:i,child:n});El(t,r,a);t.compoundCount++;return n}else if(t.currentSubject===r){var o=pl();var s=t[t.length-1];var l=pl();var u=pl();var v=pl();var f=pl();o.checks.push({type:gl.COMPOUND_SPLIT,left:s,right:l,subject:u});u.checks=r.checks;r.checks=[{type:gl.TRUE}];f.checks.push({type:gl.TRUE});l.checks.push({type:gl.PARENT,parent:f,child:v});El(t,s,o);t.currentSubject=u;t.compoundCount++;return v}else{var c=pl();var d=pl();var h=[{type:gl.PARENT,parent:c,child:d}];c.checks=r.checks;r.checks=h;t.compoundCount++;return d}}},{name:"descendant",separator:true,regex:hl.descendant,populate:function e(t,r){if(t.currentSubject==null){var a=pl();var n=pl();var i=t[t.length-1];a.checks.push({type:gl.DESCENDANT,ancestor:i,descendant:n});El(t,r,a);t.compoundCount++;return n}else if(t.currentSubject===r){var o=pl();var s=t[t.length-1];var l=pl();var u=pl();var v=pl();var f=pl();o.checks.push({type:gl.COMPOUND_SPLIT,left:s,right:l,subject:u});u.checks=r.checks;r.checks=[{type:gl.TRUE}];f.checks.push({type:gl.TRUE});l.checks.push({type:gl.ANCESTOR,ancestor:f,descendant:v});El(t,s,o);t.currentSubject=u;t.compoundCount++;return v}else{var c=pl();var d=pl();var h=[{type:gl.ANCESTOR,ancestor:c,descendant:d}];c.checks=r.checks;r.checks=h;t.compoundCount++;return d}}},{name:"subject",modifier:true,regex:hl.subject,populate:function e(t,r){if(t.currentSubject!=null&&t.currentSubject!==r){Lt("Redefinition of subject in selector `"+t.toString()+"`");return false}t.currentSubject=r;var a=t[t.length-1];var n=a.checks[0];var i=n==null?null:n.type;if(i===gl.DIRECTED_EDGE){n.type=gl.NODE_TARGET}else if(i===gl.UNDIRECTED_EDGE){n.type=gl.NODE_NEIGHBOR;n.node=n.nodes[1];n.neighbor=n.nodes[0];n.nodes=null}}}];Tl.forEach((function(e){return e.regexObj=new RegExp("^"+e.regex)}));var kl=function e(t){var r;var a;var n;for(var i=0;i<Tl.length;i++){var o=Tl[i];var s=o.name;var l=t.match(o.regexObj);if(l!=null){a=l;r=o;n=s;var u=l[0];t=t.substring(u.length);break}}return{expr:r,match:a,name:n,remaining:t}};var Cl=function e(t){var r=t.match(/^\s+/);if(r){var a=r[0];t=t.substring(a.length)}return t};var Pl=function e(t){var r=this;var a=r.inputText=t;var n=r[0]=pl();r.length=1;a=Cl(a);for(;;){var i=kl(a);if(i.expr==null){Lt("The selector `"+t+"`is invalid");return false}else{var o=i.match.slice(1);var s=i.expr.populate(r,n,o);if(s===false){return false}else if(s!=null){n=s}}a=i.remaining;if(a.match(/^\s*$/)){break}}var l=r[r.length-1];if(r.currentSubject!=null){l.subject=r.currentSubject}l.edgeCount=r.edgeCount;l.compoundCount=r.compoundCount;for(var u=0;u<r.length;u++){var v=r[u];if(v.compoundCount>0&&v.edgeCount>0){Lt("The selector `"+t+"` is invalid because it uses both a compound selector and an edge selector");return false}if(v.edgeCount>1){Lt("The selector `"+t+"` is invalid because it uses multiple edge selectors");return false}else if(v.edgeCount===1){Lt("The selector `"+t+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}}return true};var Sl=function e(){if(this.toStringCache!=null){return this.toStringCache}var t=function e(t){if(t==null){return""}else{return t}};var r=function e(r){if(D(r)){return'"'+r+'"'}else{return t(r)}};var a=function e(t){return" "+t+" "};var n=function e(n,o){var s=n.type,l=n.value;switch(s){case gl.GROUP:{var u=t(l);return u.substring(0,u.length-1)}case gl.DATA_COMPARE:{var v=n.field,f=n.operator;return"["+v+a(t(f))+r(l)+"]"}case gl.DATA_BOOL:{var c=n.operator,d=n.field;return"["+t(c)+d+"]"}case gl.DATA_EXIST:{var h=n.field;return"["+h+"]"}case gl.META_COMPARE:{var p=n.operator,g=n.field;return"[["+g+a(t(p))+r(l)+"]]"}case gl.STATE:{return l}case gl.ID:{return"#"+l}case gl.CLASS:{return"."+l}case gl.PARENT:case gl.CHILD:{return i(n.parent,o)+a(">")+i(n.child,o)}case gl.ANCESTOR:case gl.DESCENDANT:{return i(n.ancestor,o)+" "+i(n.descendant,o)}case gl.COMPOUND_SPLIT:{var y=i(n.left,o);var m=i(n.subject,o);var b=i(n.right,o);return y+(y.length>0?" ":"")+m+b}case gl.TRUE:{return""}}};var i=function e(t,r){return t.checks.reduce((function(e,a,i){return e+(r===t&&i===0?"$":"")+n(a,r)}),"")};var o="";for(var s=0;s<this.length;s++){var l=this[s];o+=i(l,l.subject);if(this.length>1&&s<this.length-1){o+=", "}}this.toStringCache=o;return o};var Dl={parse:Pl,toString:Sl};var Bl=function e(t,r,a){var n;var i=D(t);var o=I(t);var s=D(a);var l,u;var v=false;var f=false;var c=false;if(r.indexOf("!")>=0){r=r.replace("!","");f=true}if(r.indexOf("@")>=0){r=r.replace("@","");v=true}if(i||s||v){l=!i&&!o?"":""+t;u=""+a}if(v){t=l=l.toLowerCase();a=u=u.toLowerCase()}switch(r){case"*=":n=l.indexOf(u)>=0;break;case"$=":n=l.indexOf(u,l.length-u.length)>=0;break;case"^=":n=l.indexOf(u)===0;break;case"=":n=t===a;break;case">":c=true;n=t>a;break;case">=":c=true;n=t>=a;break;case"<":c=true;n=t<a;break;case"<=":c=true;n=t<=a;break;default:n=false;break}if(f&&(t!=null||!c)){n=!n}return n};var Al=function e(t,r){switch(r){case"?":return t?true:false;case"!":return t?false:true;case"^":return t===undefined}};var _l=function e(t){return t!==undefined};var Ml=function e(t,r){return t.data(r)};var Il=function e(t,r){return t[r]()};var Rl=[];var Nl=function e(t,r){return t.checks.every((function(e){return Rl[e.type](e,r)}))};Rl[gl.GROUP]=function(e,t){var r=e.value;return r==="*"||r===t.group()};Rl[gl.STATE]=function(e,t){var r=e.value;return bl(r,t)};Rl[gl.ID]=function(e,t){var r=e.value;return t.id()===r};Rl[gl.CLASS]=function(e,t){var r=e.value;return t.hasClass(r)};Rl[gl.META_COMPARE]=function(e,t){var r=e.field,a=e.operator,n=e.value;return Bl(Il(t,r),a,n)};Rl[gl.DATA_COMPARE]=function(e,t){var r=e.field,a=e.operator,n=e.value;return Bl(Ml(t,r),a,n)};Rl[gl.DATA_BOOL]=function(e,t){var r=e.field,a=e.operator;return Al(Ml(t,r),a)};Rl[gl.DATA_EXIST]=function(e,t){var r=e.field;e.operator;return _l(Ml(t,r))};Rl[gl.UNDIRECTED_EDGE]=function(e,t){var r=e.nodes[0];var a=e.nodes[1];var n=t.source();var i=t.target();return Nl(r,n)&&Nl(a,i)||Nl(a,n)&&Nl(r,i)};Rl[gl.NODE_NEIGHBOR]=function(e,t){return Nl(e.node,t)&&t.neighborhood().some((function(t){return t.isNode()&&Nl(e.neighbor,t)}))};Rl[gl.DIRECTED_EDGE]=function(e,t){return Nl(e.source,t.source())&&Nl(e.target,t.target())};Rl[gl.NODE_SOURCE]=function(e,t){return Nl(e.source,t)&&t.outgoers().some((function(t){return t.isNode()&&Nl(e.target,t)}))};Rl[gl.NODE_TARGET]=function(e,t){return Nl(e.target,t)&&t.incomers().some((function(t){return t.isNode()&&Nl(e.source,t)}))};Rl[gl.CHILD]=function(e,t){return Nl(e.child,t)&&Nl(e.parent,t.parent())};Rl[gl.PARENT]=function(e,t){return Nl(e.parent,t)&&t.children().some((function(t){return Nl(e.child,t)}))};Rl[gl.DESCENDANT]=function(e,t){return Nl(e.descendant,t)&&t.ancestors().some((function(t){return Nl(e.ancestor,t)}))};Rl[gl.ANCESTOR]=function(e,t){return Nl(e.ancestor,t)&&t.descendants().some((function(t){return Nl(e.descendant,t)}))};Rl[gl.COMPOUND_SPLIT]=function(e,t){return Nl(e.subject,t)&&Nl(e.left,t)&&Nl(e.right,t)};Rl[gl.TRUE]=function(){return true};Rl[gl.COLLECTION]=function(e,t){var r=e.value;return r.has(t)};Rl[gl.FILTER]=function(e,t){var r=e.value;return r(t)};var Ll=function e(t){var r=this;if(r.length===1&&r[0].checks.length===1&&r[0].checks[0].type===gl.ID){return t.getElementById(r[0].checks[0].value).collection()}var a=function e(t){for(var a=0;a<r.length;a++){var n=r[a];if(Nl(n,t)){return true}}return false};if(r.text()==null){a=function e(){return true}}return t.filter(a)};var Ol=function e(t){var r=this;for(var a=0;a<r.length;a++){var n=r[a];if(Nl(n,t)){return true}}return false};var zl={matches:Ol,filter:Ll};var Fl=function e(t){this.inputText=t;this.currentSubject=null;this.compoundCount=0;this.edgeCount=0;this.length=0;if(t==null||D(t)&&t.match(/^\s*$/));else if(L(t)){this.addQuery({checks:[{type:gl.COLLECTION,value:t.collection()}]})}else if(B(t)){this.addQuery({checks:[{type:gl.FILTER,value:t}]})}else if(D(t)){if(!this.parse(t)){this.invalid=true}}else{Rt("A selector must be created from a string; found ")}};var Vl=Fl.prototype;[Dl,zl].forEach((function(e){return se(Vl,e)}));Vl.text=function(){return this.inputText};Vl.size=function(){return this.length};Vl.eq=function(e){return this[e]};Vl.sameText=function(e){return!this.invalid&&!e.invalid&&this.text()===e.text()};Vl.addQuery=function(e){this[this.length++]=e};Vl.selector=Vl.toString;var jl={allAre:function e(t){var r=new Fl(t);return this.every((function(e){return r.matches(e)}))},is:function e(t){var r=new Fl(t);return this.some((function(e){return r.matches(e)}))},some:function e(t,r){for(var a=0;a<this.length;a++){var n=!r?t(this[a],a,this):t.apply(r,[this[a],a,this]);if(n){return true}}return false},every:function e(t,r){for(var a=0;a<this.length;a++){var n=!r?t(this[a],a,this):t.apply(r,[this[a],a,this]);if(!n){return false}}return true},same:function e(t){if(this===t){return true}t=this.cy().collection(t);var r=this.length;var a=t.length;if(r!==a){return false}if(r===1){return this[0]===t[0]}return this.every((function(e){return t.hasElementWithId(e.id())}))},anySame:function e(t){t=this.cy().collection(t);return this.some((function(e){return t.hasElementWithId(e.id())}))},allAreNeighbors:function e(t){t=this.cy().collection(t);var r=this.neighborhood();return t.every((function(e){return r.hasElementWithId(e.id())}))},contains:function e(t){t=this.cy().collection(t);var r=this;return t.every((function(e){return r.hasElementWithId(e.id())}))}};jl.allAreNeighbours=jl.allAreNeighbors;jl.has=jl.contains;jl.equal=jl.equals=jl.same;var Xl=function e(t,r){return function e(a,n,i,o){var s=a;var l=this;var u;if(s==null){u=""}else if(L(s)&&s.length===1){u=s.id()}if(l.length===1&&u){var v=l[0]._private;var f=v.traversalCache=v.traversalCache||{};var c=f[r]=f[r]||[];var d=Tt(u);var h=c[d];if(h){return h}else{return c[d]=t.call(l,a,n,i,o)}}else{return t.call(l,a,n,i,o)}}};var Yl={parent:function e(t){var r=[];if(this.length===1){var e=this[0]._private.parent;if(e){return e}}for(var a=0;a<this.length;a++){var n=this[a];var i=n._private.parent;if(i){r.push(i)}}return this.spawn(r,true).filter(t)},parents:function e(t){var e=[];var r=this.parent();while(r.nonempty()){for(var a=0;a<r.length;a++){var n=r[a];e.push(n)}r=r.parent()}return this.spawn(e,true).filter(t)},commonAncestors:function e(t){var r;for(var a=0;a<this.length;a++){var n=this[a];var i=n.parents();r=r||i;r=r.intersect(i)}return r.filter(t)},orphans:function e(t){return this.stdFilter((function(e){return e.isOrphan()})).filter(t)},nonorphans:function e(t){return this.stdFilter((function(e){return e.isChild()})).filter(t)},children:Xl((function(e){var t=[];for(var r=0;r<this.length;r++){var a=this[r];var n=a._private.children;for(var i=0;i<n.length;i++){t.push(n[i])}}return this.spawn(t,true).filter(e)}),"children"),siblings:function e(t){return this.parent().children().not(this).filter(t)},isParent:function e(){var t=this[0];if(t){return t.isNode()&&t._private.children.length!==0}},isChildless:function e(){var t=this[0];if(t){return t.isNode()&&t._private.children.length===0}},isChild:function e(){var t=this[0];if(t){return t.isNode()&&t._private.parent!=null}},isOrphan:function e(){var t=this[0];if(t){return t.isNode()&&t._private.parent==null}},descendants:function e(t){var r=[];function a(e){for(var t=0;t<e.length;t++){var n=e[t];r.push(n);if(n.children().nonempty()){a(n.children())}}}a(this.children());return this.spawn(r,true).filter(t)}};function ql(e,t,r,a){var n=[];var i=new Jt;var o=e.cy();var s=o.hasCompoundNodes();for(var l=0;l<e.length;l++){var u=e[l];if(r){n.push(u)}else if(s){a(n,i,u)}}while(n.length>0){var v=n.shift();t(v);i.add(v.id());if(s){a(n,i,v)}}return e}function Wl(e,t,r){if(r.isParent()){var a=r._private.children;for(var n=0;n<a.length;n++){var i=a[n];if(!t.has(i.id())){e.push(i)}}}}Yl.forEachDown=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;return ql(this,e,t,Wl)};function Ul(e,t,r){if(r.isChild()){var a=r._private.parent;if(!t.has(a.id())){e.push(a)}}}Yl.forEachUp=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;return ql(this,e,t,Ul)};function Gl(e,t,r){Ul(e,t,r);Wl(e,t,r)}Yl.forEachUpAndDown=function(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;return ql(this,e,t,Gl)};Yl.ancestors=Yl.parents;var Hl,Kl;Hl=Kl={data:fl.data({field:"data",bindingEvent:"data",allowBinding:true,allowSetting:true,settingEvent:"data",settingTriggersEvent:true,triggerFnName:"trigger",allowGetting:true,immutableKeys:{id:true,source:true,target:true,parent:true},updateStyle:true}),removeData:fl.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:true,immutableKeys:{id:true,source:true,target:true,parent:true},updateStyle:true}),scratch:fl.data({field:"scratch",bindingEvent:"scratch",allowBinding:true,allowSetting:true,settingEvent:"scratch",settingTriggersEvent:true,triggerFnName:"trigger",allowGetting:true,updateStyle:true}),removeScratch:fl.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:true,updateStyle:true}),rscratch:fl.data({field:"rscratch",allowBinding:false,allowSetting:true,settingTriggersEvent:false,allowGetting:true}),removeRscratch:fl.removeData({field:"rscratch",triggerEvent:false}),id:function e(){var t=this[0];if(t){return t._private.data.id}}};Hl.attr=Hl.data;Hl.removeAttr=Hl.removeData;var Zl=Kl;var $l={};function Ql(e){return function(t){var r=this;if(t===undefined){t=true}if(r.length===0){return}if(r.isNode()&&!r.removed()){var a=0;var n=r[0];var i=n._private.edges;for(var o=0;o<i.length;o++){var s=i[o];if(!t&&s.isLoop()){continue}a+=e(n,s)}return a}else{return}}}se($l,{degree:Ql((function(e,t){if(t.source().same(t.target())){return 2}else{return 1}})),indegree:Ql((function(e,t){if(t.target().same(e)){return 1}else{return 0}})),outdegree:Ql((function(e,t){if(t.source().same(e)){return 1}else{return 0}}))});function Jl(e,t){return function(r){var a;var n=this.nodes();for(var i=0;i<n.length;i++){var o=n[i];var s=o[e](r);if(s!==undefined&&(a===undefined||t(s,a))){a=s}}return a}}se($l,{minDegree:Jl("degree",(function(e,t){return e<t})),maxDegree:Jl("degree",(function(e,t){return e>t})),minIndegree:Jl("indegree",(function(e,t){return e<t})),maxIndegree:Jl("indegree",(function(e,t){return e>t})),minOutdegree:Jl("outdegree",(function(e,t){return e<t})),maxOutdegree:Jl("outdegree",(function(e,t){return e>t}))});se($l,{totalDegree:function e(t){var r=0;var a=this.nodes();for(var n=0;n<a.length;n++){r+=a[n].degree(t)}return r}});var eu,tu;var ru=function e(t,r,a){for(var n=0;n<t.length;n++){var i=t[n];if(!i.locked()){var o=i._private.position;var s={x:r.x!=null?r.x-o.x:0,y:r.y!=null?r.y-o.y:0};if(i.isParent()&&!(s.x===0&&s.y===0)){i.children().shift(s,a)}i.dirtyBoundingBoxCache()}}};var au={field:"position",bindingEvent:"position",allowBinding:true,allowSetting:true,settingEvent:"position",settingTriggersEvent:true,triggerFnName:"emitAndNotify",allowGetting:true,validKeys:["x","y"],beforeGet:function e(t){t.updateCompoundBounds()},beforeSet:function e(t,r){ru(t,r,false)},onSet:function e(t){t.dirtyCompoundBoundsCache()},canSet:function e(t){return!t.locked()}};eu=tu={position:fl.data(au),silentPosition:fl.data(se({},au,{allowBinding:false,allowSetting:true,settingTriggersEvent:false,allowGetting:false,beforeSet:function e(t,r){ru(t,r,true)},onSet:function e(t){t.dirtyCompoundBoundsCache()}})),positions:function e(t,r){if(_(t)){if(r){this.silentPosition(t)}else{this.position(t)}}else if(B(t)){var a=t;var n=this.cy();n.startBatch();for(var i=0;i<this.length;i++){var o=this[i];var s=undefined;if(s=a(o,i)){if(r){o.silentPosition(s)}else{o.position(s)}}}n.endBatch()}return this},silentPositions:function e(t){return this.positions(t,true)},shift:function e(t,r,a){var n;if(_(t)){n={x:I(t.x)?t.x:0,y:I(t.y)?t.y:0};a=r}else if(D(t)&&I(r)){n={x:0,y:0};n[t]=r}if(n!=null){var i=this.cy();i.startBatch();for(var o=0;o<this.length;o++){var s=this[o];if(i.hasCompoundNodes()&&s.isChild()&&s.ancestors().anySame(this)){continue}var l=s.position();var u={x:l.x+n.x,y:l.y+n.y};if(a){s.silentPosition(u)}else{s.position(u)}}i.endBatch()}return this},silentShift:function e(t,r){if(_(t)){this.shift(t,true)}else if(D(t)&&I(r)){this.shift(t,r,true)}return this},renderedPosition:function e(t,r){var a=this[0];var n=this.cy();var i=n.zoom();var o=n.pan();var s=_(t)?t:undefined;var l=s!==undefined||r!==undefined&&D(t);if(a&&a.isNode()){if(l){for(var u=0;u<this.length;u++){var v=this[u];if(r!==undefined){v.position(t,(r-o[t])/i)}else if(s!==undefined){v.position(Sr(s,i,o))}}}else{var f=a.position();s=Pr(f,i,o);if(t===undefined){return s}else{return s[t]}}}else if(!l){return undefined}return this},relativePosition:function e(t,r){var a=this[0];var n=this.cy();var i=_(t)?t:undefined;var o=i!==undefined||r!==undefined&&D(t);var s=n.hasCompoundNodes();if(a&&a.isNode()){if(o){for(var l=0;l<this.length;l++){var u=this[l];var v=s?u.parent():null;var f=v&&v.length>0;var c=f;if(f){v=v[0]}var d=c?v.position():{x:0,y:0};if(r!==undefined){u.position(t,r+d[t])}else if(i!==undefined){u.position({x:i.x+d.x,y:i.y+d.y})}}}else{var h=a.position();var p=s?a.parent():null;var g=p&&p.length>0;var y=g;if(g){p=p[0]}var m=y?p.position():{x:0,y:0};i={x:h.x-m.x,y:h.y-m.y};if(t===undefined){return i}else{return i[t]}}}else if(!o){return undefined}return this}};eu.modelPosition=eu.point=eu.position;eu.modelPositions=eu.points=eu.positions;eu.renderedPoint=eu.renderedPosition;eu.relativePoint=eu.relativePosition;var nu=tu;var iu,ou;iu=ou={};ou.renderedBoundingBox=function(e){var t=this.boundingBox(e);var r=this.cy();var a=r.zoom();var n=r.pan();var i=t.x1*a+n.x;var o=t.x2*a+n.x;var s=t.y1*a+n.y;var l=t.y2*a+n.y;return{x1:i,x2:o,y1:s,y2:l,w:o-i,h:l-s}};ou.dirtyCompoundBoundsCache=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var t=this.cy();if(!t.styleEnabled()||!t.hasCompoundNodes()){return this}this.forEachUp((function(t){if(t.isParent()){var r=t._private;r.compoundBoundsClean=false;r.bbCache=null;if(!e){t.emitAndNotify("bounds")}}}));return this};ou.updateCompoundBounds=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var t=this.cy();if(!t.styleEnabled()||!t.hasCompoundNodes()){return this}if(!e&&t.batching()){return this}function r(e){if(!e.isParent()){return}var t=e._private;var r=e.children();var a=e.pstyle("compound-sizing-wrt-labels").value==="include";var n={width:{val:e.pstyle("min-width").pfValue,left:e.pstyle("min-width-bias-left"),right:e.pstyle("min-width-bias-right")},height:{val:e.pstyle("min-height").pfValue,top:e.pstyle("min-height-bias-top"),bottom:e.pstyle("min-height-bias-bottom")}};var i=r.boundingBox({includeLabels:a,includeOverlays:false,useCache:false});var o=t.position;if(i.w===0||i.h===0){i={w:e.pstyle("width").pfValue,h:e.pstyle("height").pfValue};i.x1=o.x-i.w/2;i.x2=o.x+i.w/2;i.y1=o.y-i.h/2;i.y2=o.y+i.h/2}function s(e,t,r){var a=0;var n=0;var i=t+r;if(e>0&&i>0){a=t/i*e;n=r/i*e}return{biasDiff:a,biasComplementDiff:n}}function l(e,t,r,a){if(r.units==="%"){switch(a){case"width":return e>0?r.pfValue*e:0;case"height":return t>0?r.pfValue*t:0;case"average":return e>0&&t>0?r.pfValue*(e+t)/2:0;case"min":return e>0&&t>0?e>t?r.pfValue*t:r.pfValue*e:0;case"max":return e>0&&t>0?e>t?r.pfValue*e:r.pfValue*t:0;default:return 0}}else if(r.units==="px"){return r.pfValue}else{return 0}}var u=n.width.left.value;if(n.width.left.units==="px"&&n.width.val>0){u=u*100/n.width.val}var v=n.width.right.value;if(n.width.right.units==="px"&&n.width.val>0){v=v*100/n.width.val}var f=n.height.top.value;if(n.height.top.units==="px"&&n.height.val>0){f=f*100/n.height.val}var c=n.height.bottom.value;if(n.height.bottom.units==="px"&&n.height.val>0){c=c*100/n.height.val}var d=s(n.width.val-i.w,u,v);var h=d.biasDiff;var p=d.biasComplementDiff;var g=s(n.height.val-i.h,f,c);var y=g.biasDiff;var m=g.biasComplementDiff;t.autoPadding=l(i.w,i.h,e.pstyle("padding"),e.pstyle("padding-relative-to").value);t.autoWidth=Math.max(i.w,n.width.val);o.x=(-h+i.x1+i.x2+p)/2;t.autoHeight=Math.max(i.h,n.height.val);o.y=(-y+i.y1+i.y2+m)/2}for(var a=0;a<this.length;a++){var n=this[a];var i=n._private;if(!i.compoundBoundsClean||e){r(n);if(!t.batching()){i.compoundBoundsClean=true}}}return this};var su=function e(t){if(t===Infinity||t===-Infinity){return 0}return t};var lu=function e(t,r,a,n,i){if(n-r===0||i-a===0){return}if(r==null||a==null||n==null||i==null){return}t.x1=r<t.x1?r:t.x1;t.x2=n>t.x2?n:t.x2;t.y1=a<t.y1?a:t.y1;t.y2=i>t.y2?i:t.y2;t.w=t.x2-t.x1;t.h=t.y2-t.y1};var uu=function e(t,r){if(r==null){return t}return lu(t,r.x1,r.y1,r.x2,r.y2)};var vu=function e(t,r,a){return Gt(t,r,a)};var fu=function e(t,r,a){if(r.cy().headless()){return}var n=r._private;var i=n.rstyle;var o=i.arrowWidth/2;var s=r.pstyle(a+"-arrow-shape").value;var l;var u;if(s!=="none"){if(a==="source"){l=i.srcX;u=i.srcY}else if(a==="target"){l=i.tgtX;u=i.tgtY}else{l=i.midX;u=i.midY}var v=n.arrowBounds=n.arrowBounds||{};var f=v[a]=v[a]||{};f.x1=l-o;f.y1=u-o;f.x2=l+o;f.y2=u+o;f.w=f.x2-f.x1;f.h=f.y2-f.y1;Zr(f,1);lu(t,f.x1,f.y1,f.x2,f.y2)}};var cu=function e(t,r,a){if(r.cy().headless()){return}var n;if(a){n=a+"-"}else{n=""}var i=r._private;var o=i.rstyle;var s=r.pstyle(n+"label").strValue;if(s){var l=r.pstyle("text-halign");var u=r.pstyle("text-valign");var v=vu(o,"labelWidth",a);var f=vu(o,"labelHeight",a);var c=vu(o,"labelX",a);var d=vu(o,"labelY",a);var h=r.pstyle(n+"text-margin-x").pfValue;var p=r.pstyle(n+"text-margin-y").pfValue;var g=r.isEdge();var y=r.pstyle(n+"text-rotation");var m=r.pstyle("text-outline-width").pfValue;var b=r.pstyle("text-border-width").pfValue;var x=b/2;var w=r.pstyle("text-background-padding").pfValue;var E=2;var T=f;var k=v;var C=k/2;var P=T/2;var S,D,B,A;if(g){S=c-C;D=c+C;B=d-P;A=d+P}else{switch(l.value){case"left":S=c-k;D=c;break;case"center":S=c-C;D=c+C;break;case"right":S=c;D=c+k;break}switch(u.value){case"top":B=d-T;A=d;break;case"center":B=d-P;A=d+P;break;case"bottom":B=d;A=d+T;break}}var _=h-Math.max(m,x)-w-E;var M=h+Math.max(m,x)+w+E;var I=p-Math.max(m,x)-w-E;var R=p+Math.max(m,x)+w+E;S+=_;D+=M;B+=I;A+=R;var N=a||"main";var L=i.labelBounds;var O=L[N]=L[N]||{};O.x1=S;O.y1=B;O.x2=D;O.y2=A;O.w=D-S;O.h=A-B;O.leftPad=_;O.rightPad=M;O.topPad=I;O.botPad=R;var z=g&&y.strValue==="autorotate";var F=y.pfValue!=null&&y.pfValue!==0;if(z||F){var V=z?vu(i.rstyle,"labelAngle",a):y.pfValue;var j=Math.cos(V);var X=Math.sin(V);var Y=(S+D)/2;var q=(B+A)/2;if(!g){switch(l.value){case"left":Y=D;break;case"right":Y=S;break}switch(u.value){case"top":q=A;break;case"bottom":q=B;break}}var W=function e(t,r){t=t-Y;r=r-q;return{x:t*j-r*X+Y,y:t*X+r*j+q}};var U=W(S,B);var G=W(S,A);var H=W(D,B);var K=W(D,A);S=Math.min(U.x,G.x,H.x,K.x);D=Math.max(U.x,G.x,H.x,K.x);B=Math.min(U.y,G.y,H.y,K.y);A=Math.max(U.y,G.y,H.y,K.y)}var Z=N+"Rot";var $=L[Z]=L[Z]||{};$.x1=S;$.y1=B;$.x2=D;$.y2=A;$.w=D-S;$.h=A-B;lu(t,S,B,D,A);lu(i.labelBounds.all,S,B,D,A)}return t};var du=function e(t,r){if(r.cy().headless()){return}var a=r.pstyle("outline-opacity").value;var n=r.pstyle("outline-width").value;if(a>0&&n>0){var i=r.pstyle("outline-offset").value;var o=r.pstyle("shape").value;var s=n+i;var l=(t.w+s*2)/t.w;var u=(t.h+s*2)/t.h;var v=0;var f=0;if(["diamond","pentagon","round-triangle"].includes(o)){l=(t.w+s*2.4)/t.w;f=-s/3.6}else if(["concave-hexagon","rhomboid","right-rhomboid"].includes(o)){l=(t.w+s*2.4)/t.w}else if(o==="star"){l=(t.w+s*2.8)/t.w;u=(t.h+s*2.6)/t.h;f=-s/3.8}else if(o==="triangle"){l=(t.w+s*2.8)/t.w;u=(t.h+s*2.4)/t.h;f=-s/1.4}else if(o==="vee"){l=(t.w+s*4.4)/t.w;u=(t.h+s*3.8)/t.h;f=-s*.5}var c=t.h*u-t.h;var d=t.w*l-t.w;$r(t,[Math.ceil(c/2),Math.ceil(d/2)]);if(v!=0||f!==0){var h=Gr(t,v,f);Hr(t,h)}}};var hu=function e(t,r){var a=t._private.cy;var n=a.styleEnabled();var i=a.headless();var o=qr();var s=t._private;var l=t.isNode();var u=t.isEdge();var v,f,c,d;var h,p;var g=s.rstyle;var y=l&&n?t.pstyle("bounds-expansion").pfValue:[0];var m=function e(t){return t.pstyle("display").value!=="none"};var b=!n||m(t)&&(!u||m(t.source())&&m(t.target()));if(b){var x=0;var w=0;if(n&&r.includeOverlays){x=t.pstyle("overlay-opacity").value;if(x!==0){w=t.pstyle("overlay-padding").value}}var E=0;var T=0;if(n&&r.includeUnderlays){E=t.pstyle("underlay-opacity").value;if(E!==0){T=t.pstyle("underlay-padding").value}}var k=Math.max(w,T);var C=0;var P=0;if(n){C=t.pstyle("width").pfValue;P=C/2}if(l&&r.includeNodes){var S=t.position();h=S.x;p=S.y;var D=t.outerWidth();var B=D/2;var A=t.outerHeight();var _=A/2;v=h-B;f=h+B;c=p-_;d=p+_;lu(o,v,c,f,d);if(n&&r.includeOutlines){du(o,t)}}else if(u&&r.includeEdges){if(n&&!i){var M=t.pstyle("curve-style").strValue;v=Math.min(g.srcX,g.midX,g.tgtX);f=Math.max(g.srcX,g.midX,g.tgtX);c=Math.min(g.srcY,g.midY,g.tgtY);d=Math.max(g.srcY,g.midY,g.tgtY);v-=P;f+=P;c-=P;d+=P;lu(o,v,c,f,d);if(M==="haystack"){var I=g.haystackPts;if(I&&I.length===2){v=I[0].x;c=I[0].y;f=I[1].x;d=I[1].y;if(v>f){var R=v;v=f;f=R}if(c>d){var N=c;c=d;d=N}lu(o,v-P,c-P,f+P,d+P)}}else if(M==="bezier"||M==="unbundled-bezier"||M.endsWith("segments")||M.endsWith("taxi")){var L;switch(M){case"bezier":case"unbundled-bezier":L=g.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":L=g.linePts;break}if(L!=null){for(var O=0;O<L.length;O++){var z=L[O];v=z.x-P;f=z.x+P;c=z.y-P;d=z.y+P;lu(o,v,c,f,d)}}}}else{var F=t.source();var V=F.position();var j=t.target();var X=j.position();v=V.x;f=X.x;c=V.y;d=X.y;if(v>f){var Y=v;v=f;f=Y}if(c>d){var q=c;c=d;d=q}v-=P;f+=P;c-=P;d+=P;lu(o,v,c,f,d)}}if(n&&r.includeEdges&&u){fu(o,t,"mid-source");fu(o,t,"mid-target");fu(o,t,"source");fu(o,t,"target")}if(n){var W=t.pstyle("ghost").value==="yes";if(W){var U=t.pstyle("ghost-offset-x").pfValue;var G=t.pstyle("ghost-offset-y").pfValue;lu(o,o.x1+U,o.y1+G,o.x2+U,o.y2+G)}}var H=s.bodyBounds=s.bodyBounds||{};Qr(H,o);$r(H,y);Zr(H,1);if(n){v=o.x1;f=o.x2;c=o.y1;d=o.y2;lu(o,v-k,c-k,f+k,d+k)}var K=s.overlayBounds=s.overlayBounds||{};Qr(K,o);$r(K,y);Zr(K,1);var Z=s.labelBounds=s.labelBounds||{};if(Z.all!=null){Ur(Z.all)}else{Z.all=qr()}if(n&&r.includeLabels){if(r.includeMainLabels){cu(o,t,null)}if(u){if(r.includeSourceLabels){cu(o,t,"source")}if(r.includeTargetLabels){cu(o,t,"target")}}}}o.x1=su(o.x1);o.y1=su(o.y1);o.x2=su(o.x2);o.y2=su(o.y2);o.w=su(o.x2-o.x1);o.h=su(o.y2-o.y1);if(o.w>0&&o.h>0&&b){$r(o,y);Zr(o,1)}return o};var pu=function e(t){var r=0;var a=function e(t){return(t?1:0)<<r++};var n=0;n+=a(t.incudeNodes);n+=a(t.includeEdges);n+=a(t.includeLabels);n+=a(t.includeMainLabels);n+=a(t.includeSourceLabels);n+=a(t.includeTargetLabels);n+=a(t.includeOverlays);n+=a(t.includeOutlines);return n};var gu=function e(t){if(t.isEdge()){var r=t.source().position();var a=t.target().position();var n=function e(t){return Math.round(t)};return Et([n(r.x),n(r.y),n(a.x),n(a.y)])}else{return 0}};var yu=function e(t,r){var a=t._private;var n;var i=t.isEdge();var o=r==null?bu:pu(r);var s=o===bu;var l=gu(t);var u=a.bbCachePosKey===l;var v=r.useCache;var f=function e(t){return t._private.bbCache==null||t._private.styleDirty};var c=!v||f(t)||i&&(f(t.source())||f(t.target()));if(c){if(!u){t.recalculateRenderedStyle(v)}n=hu(t,mu);a.bbCache=n;a.bbCachePosKey=l}else{n=a.bbCache}if(!s){var d=t.isNode();n=qr();if(r.includeNodes&&d||r.includeEdges&&!d){if(r.includeOverlays){uu(n,a.overlayBounds)}else{uu(n,a.bodyBounds)}}if(r.includeLabels){if(r.includeMainLabels&&(!i||r.includeSourceLabels&&r.includeTargetLabels)){uu(n,a.labelBounds.all)}else{if(r.includeMainLabels){uu(n,a.labelBounds.mainRot)}if(r.includeSourceLabels){uu(n,a.labelBounds.sourceRot)}if(r.includeTargetLabels){uu(n,a.labelBounds.targetRot)}}}n.w=n.x2-n.x1;n.h=n.y2-n.y1}return n};var mu={includeNodes:true,includeEdges:true,includeLabels:true,includeMainLabels:true,includeSourceLabels:true,includeTargetLabels:true,includeOverlays:true,includeUnderlays:true,includeOutlines:true,useCache:true};var bu=pu(mu);var xu=Yt(mu);ou.boundingBox=function(e){var t;if(this.length===1&&this[0]._private.bbCache!=null&&!this[0]._private.styleDirty&&(e===undefined||e.useCache===undefined||e.useCache===true)){if(e===undefined){e=mu}else{e=xu(e)}t=yu(this[0],e)}else{t=qr();e=e||mu;var r=xu(e);var a=this;var n=a.cy();var i=n.styleEnabled();if(i){for(var o=0;o<a.length;o++){var s=a[o];var l=s._private;var u=gu(s);var v=l.bbCachePosKey===u;var f=r.useCache&&v&&!l.styleDirty;s.recalculateRenderedStyle(f)}}this.updateCompoundBounds(!e.useCache);for(var c=0;c<a.length;c++){var d=a[c];uu(t,yu(d,r))}}t.x1=su(t.x1);t.y1=su(t.y1);t.x2=su(t.x2);t.y2=su(t.y2);t.w=su(t.x2-t.x1);t.h=su(t.y2-t.y1);return t};ou.dirtyBoundingBoxCache=function(){for(var e=0;e<this.length;e++){var t=this[e]._private;t.bbCache=null;t.bbCachePosKey=null;t.bodyBounds=null;t.overlayBounds=null;t.labelBounds.all=null;t.labelBounds.source=null;t.labelBounds.target=null;t.labelBounds.main=null;t.labelBounds.sourceRot=null;t.labelBounds.targetRot=null;t.labelBounds.mainRot=null;t.arrowBounds.source=null;t.arrowBounds.target=null;t.arrowBounds["mid-source"]=null;t.arrowBounds["mid-target"]=null}this.emitAndNotify("bounds");return this};ou.boundingBoxAt=function(e){var t=this.nodes();var r=this.cy();var a=r.hasCompoundNodes();var n=r.collection();if(a){n=t.filter((function(e){return e.isParent()}));t=t.not(n)}if(_(e)){var i=e;e=function e(){return i}}var o=function t(r,a){return r._private.bbAtOldPos=e(r,a)};var s=function e(t){return t._private.bbAtOldPos};r.startBatch();t.forEach(o).silentPositions(e);if(a){n.dirtyCompoundBoundsCache();n.dirtyBoundingBoxCache();n.updateCompoundBounds(true)}var l=Wr(this.boundingBox({useCache:false}));t.silentPositions(s);if(a){n.dirtyCompoundBoundsCache();n.dirtyBoundingBoxCache();n.updateCompoundBounds(true)}r.endBatch();return l};iu.boundingbox=iu.bb=iu.boundingBox;iu.renderedBoundingbox=iu.renderedBoundingBox;var wu=ou;var Eu,Tu;Eu=Tu={};var ku=function e(t){t.uppercaseName=$(t.name);t.autoName="auto"+t.uppercaseName;t.labelName="label"+t.uppercaseName;t.outerName="outer"+t.uppercaseName;t.uppercaseOuterName=$(t.outerName);Eu[t.name]=function e(){var r=this[0];var a=r._private;var n=a.cy;var i=n._private.styleEnabled;if(r){if(i){if(r.isParent()){r.updateCompoundBounds();return a[t.autoName]||0}var o=r.pstyle(t.name);switch(o.strValue){case"label":r.recalculateRenderedStyle();return a.rstyle[t.labelName]||0;default:return o.pfValue}}else{return 1}}};Eu["outer"+t.uppercaseName]=function e(){var r=this[0];var a=r._private;var n=a.cy;var i=n._private.styleEnabled;if(r){if(i){var o=r[t.name]();var s=r.pstyle("border-width").pfValue;var l=2*r.padding();return o+s+l}else{return 1}}};Eu["rendered"+t.uppercaseName]=function e(){var r=this[0];if(r){var a=r[t.name]();return a*this.cy().zoom()}};Eu["rendered"+t.uppercaseOuterName]=function e(){var r=this[0];if(r){var a=r[t.outerName]();return a*this.cy().zoom()}}};ku({name:"width"});ku({name:"height"});Tu.padding=function(){var e=this[0];var t=e._private;if(e.isParent()){e.updateCompoundBounds();if(t.autoPadding!==undefined){return t.autoPadding}else{return e.pstyle("padding").pfValue}}else{return e.pstyle("padding").pfValue}};Tu.paddedHeight=function(){var e=this[0];return e.height()+2*e.padding()};Tu.paddedWidth=function(){var e=this[0];return e.width()+2*e.padding()};var Cu=Tu;var Pu=function e(t,r){if(t.isEdge()&&t.takesUpSpace()){return r(t)}};var Su=function e(t,r){if(t.isEdge()&&t.takesUpSpace()){var a=t.cy();return Pr(r(t),a.zoom(),a.pan())}};var Du=function e(t,r){if(t.isEdge()&&t.takesUpSpace()){var a=t.cy();var n=a.pan();var i=a.zoom();return r(t).map((function(e){return Pr(e,i,n)}))}};var Bu=function e(t){return t.renderer().getControlPoints(t)};var Au=function e(t){return t.renderer().getSegmentPoints(t)};var _u=function e(t){return t.renderer().getSourceEndpoint(t)};var Mu=function e(t){return t.renderer().getTargetEndpoint(t)};var Iu=function e(t){return t.renderer().getEdgeMidpoint(t)};var Ru={controlPoints:{get:Bu,mult:true},segmentPoints:{get:Au,mult:true},sourceEndpoint:{get:_u},targetEndpoint:{get:Mu},midpoint:{get:Iu}};var Nu=function e(t){return"rendered"+t[0].toUpperCase()+t.substr(1)};var Lu=Object.keys(Ru).reduce((function(e,t){var r=Ru[t];var a=Nu(t);e[t]=function(){return Pu(this,r.get)};if(r.mult){e[a]=function(){return Du(this,r.get)}}else{e[a]=function(){return Su(this,r.get)}}return e}),{});var Ou=se({},nu,wu,Cu,Lu);var zu=function e(t,r){this.recycle(t,r)};function Fu(){return false}function Vu(){return true}zu.prototype={instanceString:function e(){return"event"},recycle:function e(t,r){this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=Fu;if(t!=null&&t.preventDefault){this.type=t.type;this.isDefaultPrevented=t.defaultPrevented?Vu:Fu}else if(t!=null&&t.type){r=t}else{this.type=t}if(r!=null){this.originalEvent=r.originalEvent;this.type=r.type!=null?r.type:this.type;this.cy=r.cy;this.target=r.target;this.position=r.position;this.renderedPosition=r.renderedPosition;this.namespace=r.namespace;this.layout=r.layout}if(this.cy!=null&&this.position!=null&&this.renderedPosition==null){var a=this.position;var n=this.cy.zoom();var i=this.cy.pan();this.renderedPosition={x:a.x*n+i.x,y:a.y*n+i.y}}this.timeStamp=t&&t.timeStamp||Date.now()},preventDefault:function e(){this.isDefaultPrevented=Vu;var t=this.originalEvent;if(!t){return}if(t.preventDefault){t.preventDefault()}},stopPropagation:function e(){this.isPropagationStopped=Vu;var t=this.originalEvent;if(!t){return}if(t.stopPropagation){t.stopPropagation()}},stopImmediatePropagation:function e(){this.isImmediatePropagationStopped=Vu;this.stopPropagation()},isDefaultPrevented:Fu,isPropagationStopped:Fu,isImmediatePropagationStopped:Fu};var ju=/^([^.]+)(\.(?:[^.]+))?$/;var Xu=".*";var Yu={qualifierCompare:function e(t,r){return t===r},eventMatches:function e(){return true},addEventFields:function e(){},callbackContext:function e(t){return t},beforeEmit:function e(){},afterEmit:function e(){},bubble:function e(){return false},parent:function e(){return null},context:null};var qu=Object.keys(Yu);var Wu={};function Uu(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:Wu;var t=arguments.length>1?arguments[1]:undefined;for(var r=0;r<qu.length;r++){var a=qu[r];this[a]=e[a]||Yu[a]}this.context=t||this.context;this.listeners=[];this.emitting=0}var Gu=Uu.prototype;var Hu=function e(t,r,a,n,i,o,s){if(B(n)){i=n;n=null}if(s){if(o==null){o=s}else{o=se({},o,s)}}var l=A(a)?a:a.split(/\s+/);for(var u=0;u<l.length;u++){var v=l[u];if(X(v)){continue}var f=v.match(ju);if(f){var c=f[1];var d=f[2]?f[2]:null;var h=r(t,v,c,d,n,i,o);if(h===false){break}}}};var Ku=function e(t,r){t.addEventFields(t.context,r);return new zu(r.type,r)};var Zu=function e(t,r,a){if(j(a)){r(t,a);return}else if(_(a)){r(t,Ku(t,a));return}var n=A(a)?a:a.split(/\s+/);for(var i=0;i<n.length;i++){var o=n[i];if(X(o)){continue}var s=o.match(ju);if(s){var l=s[1];var u=s[2]?s[2]:null;var v=Ku(t,{type:l,namespace:u,target:t.context});r(t,v)}}};Gu.on=Gu.addListener=function(e,t,r,a,n){Hu(this,(function(e,t,r,a,n,i,o){if(B(i)){e.listeners.push({event:t,callback:i,type:r,namespace:a,qualifier:n,conf:o})}}),e,t,r,a,n);return this};Gu.one=function(e,t,r,a){return this.on(e,t,r,a,{one:true})};Gu.removeListener=Gu.off=function(e,t,r,a){var n=this;if(this.emitting!==0){this.listeners=Ft(this.listeners)}var i=this.listeners;var o=function o(s){var l=i[s];Hu(n,(function(t,r,a,n,o,u){if((l.type===a||e==="*")&&(!n&&l.namespace!==".*"||l.namespace===n)&&(!o||t.qualifierCompare(l.qualifier,o))&&(!u||l.callback===u)){i.splice(s,1);return false}}),e,t,r,a)};for(var s=i.length-1;s>=0;s--){o(s)}return this};Gu.removeAllListeners=function(){return this.removeListener("*")};Gu.emit=Gu.trigger=function(e,t,r){var a=this.listeners;var n=a.length;this.emitting++;if(!A(t)){t=[t]}Zu(this,(function(e,i){if(r!=null){a=[{event:i.event,type:i.type,namespace:i.namespace,callback:r}];n=a.length}var o=function r(){var n=a[s];if(n.type===i.type&&(!n.namespace||n.namespace===i.namespace||n.namespace===Xu)&&e.eventMatches(e.context,n,i)){var o=[i];if(t!=null){Ut(o,t)}e.beforeEmit(e.context,n,i);if(n.conf&&n.conf.one){e.listeners=e.listeners.filter((function(e){return e!==n}))}var l=e.callbackContext(e.context,n,i);var u=n.callback.apply(l,o);e.afterEmit(e.context,n,i);if(u===false){i.stopPropagation();i.preventDefault()}}};for(var s=0;s<n;s++){o()}if(e.bubble(e.context)&&!i.isPropagationStopped()){e.parent(e.context).emit(i,t)}}),e);this.emitting--;return this};var $u={qualifierCompare:function e(t,r){if(t==null||r==null){return t==null&&r==null}else{return t.sameText(r)}},eventMatches:function e(t,r,a){var n=r.qualifier;if(n!=null){return t!==a.target&&O(a.target)&&n.matches(a.target)}return true},addEventFields:function e(t,r){r.cy=t.cy();r.target=t},callbackContext:function e(t,r,a){return r.qualifier!=null?a.target:t},beforeEmit:function e(t,r){if(r.conf&&r.conf.once){r.conf.onceCollection.removeListener(r.event,r.qualifier,r.callback)}},bubble:function e(){return true},parent:function e(t){return t.isChild()?t.parent():t.cy()}};var Qu=function e(t){if(D(t)){return new Fl(t)}else{return t}};var Ju={createEmitter:function e(){for(var t=0;t<this.length;t++){var r=this[t];var a=r._private;if(!a.emitter){a.emitter=new Uu($u,r)}}return this},emitter:function e(){return this._private.emitter},on:function e(t,r,a){var n=Qu(r);for(var i=0;i<this.length;i++){var o=this[i];o.emitter().on(t,n,a)}return this},removeListener:function e(t,r,a){var n=Qu(r);for(var i=0;i<this.length;i++){var o=this[i];o.emitter().removeListener(t,n,a)}return this},removeAllListeners:function e(){for(var t=0;t<this.length;t++){var r=this[t];r.emitter().removeAllListeners()}return this},one:function e(t,r,a){var n=Qu(r);for(var i=0;i<this.length;i++){var o=this[i];o.emitter().one(t,n,a)}return this},once:function e(t,r,a){var n=Qu(r);for(var i=0;i<this.length;i++){var o=this[i];o.emitter().on(t,n,a,{once:true,onceCollection:this})}},emit:function e(t,r){for(var a=0;a<this.length;a++){var n=this[a];n.emitter().emit(t,r)}return this},emitAndNotify:function e(t,r){if(this.length===0){return}this.cy().notify(t,this);this.emit(t,r);return this}};fl.eventAliasesOn(Ju);var ev={nodes:function e(t){return this.filter((function(e){return e.isNode()})).filter(t)},edges:function e(t){return this.filter((function(e){return e.isEdge()})).filter(t)},byGroup:function e(){var t=this.spawn();var r=this.spawn();for(var a=0;a<this.length;a++){var n=this[a];if(n.isNode()){t.push(n)}else{r.push(n)}}return{nodes:t,edges:r}},filter:function e(t,r){if(t===undefined){return this}else if(D(t)||L(t)){return new Fl(t).filter(this)}else if(B(t)){var a=this.spawn();var n=this;for(var i=0;i<n.length;i++){var o=n[i];var s=r?t.apply(r,[o,i,n]):t(o,i,n);if(s){a.push(o)}}return a}return this.spawn()},not:function e(t){if(!t){return this}else{if(D(t)){t=this.filter(t)}var r=this.spawn();for(var a=0;a<this.length;a++){var n=this[a];var i=t.has(n);if(!i){r.push(n)}}return r}},absoluteComplement:function e(){var t=this.cy();return t.mutableElements().not(this)},intersect:function e(t){if(D(t)){var r=t;return this.filter(r)}var a=this.spawn();var n=this;var i=t;var o=this.length<t.length;var s=o?n:i;var l=o?i:n;for(var u=0;u<s.length;u++){var v=s[u];if(l.has(v)){a.push(v)}}return a},xor:function e(t){var r=this._private.cy;if(D(t)){t=r.$(t)}var a=this.spawn();var n=this;var i=t;var o=function e(t,r){for(var n=0;n<t.length;n++){var i=t[n];var o=i._private.data.id;var s=r.hasElementWithId(o);if(!s){a.push(i)}}};o(n,i);o(i,n);return a},diff:function e(t){var r=this._private.cy;if(D(t)){t=r.$(t)}var a=this.spawn();var n=this.spawn();var i=this.spawn();var o=this;var s=t;var l=function e(t,r,a){for(var n=0;n<t.length;n++){var o=t[n];var s=o._private.data.id;var l=r.hasElementWithId(s);if(l){i.merge(o)}else{a.push(o)}}};l(o,s,a);l(s,o,n);return{left:a,right:n,both:i}},add:function e(t){var r=this._private.cy;if(!t){return this}if(D(t)){var a=t;t=r.mutableElements().filter(a)}var n=this.spawnSelf();for(var i=0;i<t.length;i++){var o=t[i];var e=!this.has(o);if(e){n.push(o)}}return n},merge:function e(t){var r=this._private;var a=r.cy;if(!t){return this}if(t&&D(t)){var n=t;t=a.mutableElements().filter(n)}var i=r.map;for(var o=0;o<t.length;o++){var s=t[o];var l=s._private.data.id;var u=!i.has(l);if(u){var v=this.length++;this[v]=s;i.set(l,{ele:s,index:v})}}return this},unmergeAt:function e(t){var r=this[t];var a=r.id();var n=this._private;var i=n.map;this[t]=undefined;i["delete"](a);var o=t===this.length-1;if(this.length>1&&!o){var s=this.length-1;var l=this[s];var u=l._private.data.id;this[s]=undefined;this[t]=l;i.set(u,{ele:l,index:t})}this.length--;return this},unmergeOne:function e(t){t=t[0];var r=this._private;var a=t._private.data.id;var n=r.map;var i=n.get(a);if(!i){return this}var o=i.index;this.unmergeAt(o);return this},unmerge:function e(t){var r=this._private.cy;if(!t){return this}if(t&&D(t)){var a=t;t=r.mutableElements().filter(a)}for(var n=0;n<t.length;n++){this.unmergeOne(t[n])}return this},unmergeBy:function e(t){for(var r=this.length-1;r>=0;r--){var a=this[r];if(t(a)){this.unmergeAt(r)}}return this},map:function e(t,r){var a=[];var n=this;for(var i=0;i<n.length;i++){var o=n[i];var s=r?t.apply(r,[o,i,n]):t(o,i,n);a.push(s)}return a},reduce:function e(t,r){var a=r;var n=this;for(var i=0;i<n.length;i++){a=t(a,n[i],i,n)}return a},max:function e(t,r){var e=-Infinity;var a;var n=this;for(var i=0;i<n.length;i++){var o=n[i];var s=r?t.apply(r,[o,i,n]):t(o,i,n);if(s>e){e=s;a=o}}return{value:e,ele:a}},min:function e(t,r){var e=Infinity;var a;var n=this;for(var i=0;i<n.length;i++){var o=n[i];var s=r?t.apply(r,[o,i,n]):t(o,i,n);if(s<e){e=s;a=o}}return{value:e,ele:a}}};var tv=ev;tv["u"]=tv["|"]=tv["+"]=tv.union=tv.or=tv.add;tv["\\"]=tv["!"]=tv["-"]=tv.difference=tv.relativeComplement=tv.subtract=tv.not;tv["n"]=tv["&"]=tv["."]=tv.and=tv.intersection=tv.intersect;tv["^"]=tv["(+)"]=tv["(-)"]=tv.symmetricDifference=tv.symdiff=tv.xor;tv.fnFilter=tv.filterFn=tv.stdFilter=tv.filter;tv.complement=tv.abscomp=tv.absoluteComplement;var rv={isNode:function e(){return this.group()==="nodes"},isEdge:function e(){return this.group()==="edges"},isLoop:function e(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function e(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function e(){var t=this[0];if(t){return t._private.group}}};var av=function e(t,r){var a=t.cy();var n=a.hasCompoundNodes();function i(e){var t=e.pstyle("z-compound-depth");if(t.value==="auto"){return n?e.zDepth():0}else if(t.value==="bottom"){return-1}else if(t.value==="top"){return Bt}return 0}var o=i(t)-i(r);if(o!==0){return o}function s(e){var t=e.pstyle("z-index-compare");if(t.value==="auto"){return e.isNode()?1:0}return 0}var l=s(t)-s(r);if(l!==0){return l}var u=t.pstyle("z-index").value-r.pstyle("z-index").value;if(u!==0){return u}return t.poolIndex()-r.poolIndex()};var nv={forEach:function e(t,r){if(B(t)){var a=this.length;for(var n=0;n<a;n++){var i=this[n];var o=r?t.apply(r,[i,n,this]):t(i,n,this);if(o===false){break}}}return this},toArray:function e(){var t=[];for(var r=0;r<this.length;r++){t.push(this[r])}return t},slice:function e(t,r){var a=[];var n=this.length;if(r==null){r=n}if(t==null){t=0}if(t<0){t=n+t}if(r<0){r=n+r}for(var i=t;i>=0&&i<r&&i<n;i++){a.push(this[i])}return this.spawn(a)},size:function e(){return this.length},eq:function e(t){return this[t]||this.spawn()},first:function e(){return this[0]||this.spawn()},last:function e(){return this[this.length-1]||this.spawn()},empty:function e(){return this.length===0},nonempty:function e(){return!this.empty()},sort:function e(t){if(!B(t)){return this}var r=this.toArray().sort(t);return this.spawn(r)},sortByZIndex:function e(){return this.sort(av)},zDepth:function e(){var t=this[0];if(!t){return undefined}var r=t._private;var a=r.group;if(a==="nodes"){var n=r.data.parent?t.parents().size():0;if(!t.isParent()){return Bt-1}return n}else{var i=r.source;var o=r.target;var s=i.zDepth();var l=o.zDepth();return Math.max(s,l,0)}}};nv.each=nv.forEach;var iv=function e(){var t="undefined";var r=(typeof Symbol==="undefined"?"undefined":b(Symbol))!=t&&b(Symbol.iterator)!=t;if(r){nv[Symbol.iterator]=function(){var e=this;var t={value:undefined,done:false};var r=0;var a=this.length;return v({next:function n(){if(r<a){t.value=e[r++]}else{t.value=undefined;t.done=true}return t}},Symbol.iterator,(function(){return this}))}}};iv();var ov=Yt({nodeDimensionsIncludeLabels:false});var sv={layoutDimensions:function e(t){t=ov(t);var r;if(!this.takesUpSpace()){r={w:0,h:0}}else if(t.nodeDimensionsIncludeLabels){var a=this.boundingBox();r={w:a.w,h:a.h}}else{r={w:this.outerWidth(),h:this.outerHeight()}}if(r.w===0||r.h===0){r.w=r.h=1}return r},layoutPositions:function e(t,r,a){var n=this.nodes().filter((function(e){return!e.isParent()}));var i=this.cy();var o=r.eles;var s=function e(t){return t.id()};var l=G(a,s);t.emit({type:"layoutstart",layout:t});t.animations=[];var u=function e(t,r,a){var n={x:r.x1+r.w/2,y:r.y1+r.h/2};var i={x:(a.x-n.x)*t,y:(a.y-n.y)*t};return{x:n.x+i.x,y:n.y+i.y}};var v=r.spacingFactor&&r.spacingFactor!==1;var f=function e(){if(!v){return null}var t=qr();for(var r=0;r<n.length;r++){var a=n[r];var i=l(a,r);Kr(t,i.x,i.y)}return t};var c=f();var d=G((function(e,t){var a=l(e,t);if(v){var n=Math.abs(r.spacingFactor);a=u(n,c,a)}if(r.transform!=null){a=r.transform(e,a)}return a}),s);if(r.animate){for(var h=0;h<n.length;h++){var p=n[h];var g=d(p,h);var y=r.animateFilter==null||r.animateFilter(p,h);if(y){var m=p.animation({position:g,duration:r.animationDuration,easing:r.animationEasing});t.animations.push(m)}else{p.position(g)}}if(r.fit){var b=i.animation({fit:{boundingBox:o.boundingBoxAt(d),padding:r.padding},duration:r.animationDuration,easing:r.animationEasing});t.animations.push(b)}else if(r.zoom!==undefined&&r.pan!==undefined){var x=i.animation({zoom:r.zoom,pan:r.pan,duration:r.animationDuration,easing:r.animationEasing});t.animations.push(x)}t.animations.forEach((function(e){return e.play()}));t.one("layoutready",r.ready);t.emit({type:"layoutready",layout:t});si.all(t.animations.map((function(e){return e.promise()}))).then((function(){t.one("layoutstop",r.stop);t.emit({type:"layoutstop",layout:t})}))}else{n.positions(d);if(r.fit){i.fit(r.eles,r.padding)}if(r.zoom!=null){i.zoom(r.zoom)}if(r.pan){i.pan(r.pan)}t.one("layoutready",r.ready);t.emit({type:"layoutready",layout:t});t.one("layoutstop",r.stop);t.emit({type:"layoutstop",layout:t})}return this},layout:function e(t){var r=this.cy();return r.makeLayout(se({},t,{eles:this}))}};sv.createLayout=sv.makeLayout=sv.layout;function lv(e,t,r){var a=r._private;var n=a.styleCache=a.styleCache||[];var i;if((i=n[e])!=null){return i}else{i=n[e]=t(r);return i}}function uv(e,t){e=Tt(e);return function r(a){return lv(e,t,a)}}function vv(e,t){e=Tt(e);var r=function e(r){return t.call(r)};return function t(){var a=this[0];if(a){return lv(e,r,a)}}}var fv={recalculateRenderedStyle:function e(t){var r=this.cy();var a=r.renderer();var n=r.styleEnabled();if(a&&n){a.recalculateRenderedStyle(this,t)}return this},dirtyStyleCache:function e(){var t=this.cy();var r=function e(t){return t._private.styleCache=null};if(t.hasCompoundNodes()){var a;a=this.spawnSelf().merge(this.descendants()).merge(this.parents());a.merge(a.connectedEdges());a.forEach(r)}else{this.forEach((function(e){r(e);e.connectedEdges().forEach(r)}))}return this},updateStyle:function e(t){var r=this._private.cy;if(!r.styleEnabled()){return this}if(r.batching()){var a=r._private.batchStyleEles;a.merge(this);return this}var n=r.hasCompoundNodes();var i=this;t=t||t===undefined?true:false;if(n){i=this.spawnSelf().merge(this.descendants()).merge(this.parents())}var o=i;if(t){o.emitAndNotify("style")}else{o.emit("style")}i.forEach((function(e){return e._private.styleDirty=true}));return this},cleanStyle:function e(){var t=this.cy();if(!t.styleEnabled()){return}for(var r=0;r<this.length;r++){var a=this[r];if(a._private.styleDirty){a._private.styleDirty=false;t.style().apply(a)}}},parsedStyle:function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;var a=this[0];var n=a.cy();if(!n.styleEnabled()){return}if(a){if(a._private.styleDirty){a._private.styleDirty=false;n.style().apply(a)}var i=a._private.style[t];if(i!=null){return i}else if(r){return n.style().getDefaultProperty(t)}else{return null}}},numericStyle:function e(t){var r=this[0];if(!r.cy().styleEnabled()){return}if(r){var a=r.pstyle(t);return a.pfValue!==undefined?a.pfValue:a.value}},numericStyleUnits:function e(t){var r=this[0];if(!r.cy().styleEnabled()){return}if(r){return r.pstyle(t).units}},renderedStyle:function e(t){var r=this.cy();if(!r.styleEnabled()){return this}var a=this[0];if(a){return r.style().getRenderedStyle(a,t)}},style:function e(t,r){var a=this.cy();if(!a.styleEnabled()){return this}var n=false;var e=a.style();if(_(t)){var i=t;e.applyBypass(this,i,n);this.emitAndNotify("style")}else if(D(t)){if(r===undefined){var o=this[0];if(o){return e.getStylePropertyValue(o,t)}else{return}}else{e.applyBypass(this,t,r,n);this.emitAndNotify("style")}}else if(t===undefined){var s=this[0];if(s){return e.getRawStyle(s)}else{return}}return this},removeStyle:function e(t){var r=this.cy();if(!r.styleEnabled()){return this}var a=false;var n=r.style();var i=this;if(t===undefined){for(var o=0;o<i.length;o++){var s=i[o];n.removeAllBypasses(s,a)}}else{t=t.split(/\s+/);for(var l=0;l<i.length;l++){var u=i[l];n.removeBypasses(u,t,a)}}this.emitAndNotify("style");return this},show:function e(){this.css("display","element");return this},hide:function e(){this.css("display","none");return this},effectiveOpacity:function e(){var t=this.cy();if(!t.styleEnabled()){return 1}var r=t.hasCompoundNodes();var a=this[0];if(a){var n=a._private;var i=a.pstyle("opacity").value;if(!r){return i}var o=!n.data.parent?null:a.parents();if(o){for(var s=0;s<o.length;s++){var l=o[s];var u=l.pstyle("opacity").value;i=u*i}}return i}},transparent:function e(){var t=this.cy();if(!t.styleEnabled()){return false}var r=this[0];var a=r.cy().hasCompoundNodes();if(r){if(!a){return r.pstyle("opacity").value===0}else{return r.effectiveOpacity()===0}}},backgrounding:function e(){var t=this.cy();if(!t.styleEnabled()){return false}var r=this[0];return r._private.backgrounding?true:false}};function cv(e,t){var r=e._private;var a=r.data.parent?e.parents():null;if(a){for(var n=0;n<a.length;n++){var i=a[n];if(!t(i)){return false}}}return true}function dv(e){var t=e.ok;var r=e.edgeOkViaNode||e.ok;var a=e.parentOk||e.ok;return function(){var e=this.cy();if(!e.styleEnabled()){return true}var n=this[0];var i=e.hasCompoundNodes();if(n){var o=n._private;if(!t(n)){return false}if(n.isNode()){return!i||cv(n,a)}else{var s=o.source;var l=o.target;return r(s)&&(!i||cv(s,r))&&(s===l||r(l)&&(!i||cv(l,r)))}}}}var hv=uv("eleTakesUpSpace",(function(e){return e.pstyle("display").value==="element"&&e.width()!==0&&(e.isNode()?e.height()!==0:true)}));fv.takesUpSpace=vv("takesUpSpace",dv({ok:hv}));var pv=uv("eleInteractive",(function(e){return e.pstyle("events").value==="yes"&&e.pstyle("visibility").value==="visible"&&hv(e)}));var gv=uv("parentInteractive",(function(e){return e.pstyle("visibility").value==="visible"&&hv(e)}));fv.interactive=vv("interactive",dv({ok:pv,parentOk:gv,edgeOkViaNode:hv}));fv.noninteractive=function(){var e=this[0];if(e){return!e.interactive()}};var yv=uv("eleVisible",(function(e){return e.pstyle("visibility").value==="visible"&&e.pstyle("opacity").pfValue!==0&&hv(e)}));var mv=hv;fv.visible=vv("visible",dv({ok:yv,edgeOkViaNode:mv}));fv.hidden=function(){var e=this[0];if(e){return!e.visible()}};fv.isBundledBezier=vv("isBundledBezier",(function(){if(!this.cy().styleEnabled()){return false}return!this.removed()&&this.pstyle("curve-style").value==="bezier"&&this.takesUpSpace()}));fv.bypass=fv.css=fv.style;fv.renderedCss=fv.renderedStyle;fv.removeBypass=fv.removeCss=fv.removeStyle;fv.pstyle=fv.parsedStyle;var bv={};function xv(e){return function(){var t=arguments;var r=[];if(t.length===2){var a=t[0];var n=t[1];this.on(e.event,a,n)}else if(t.length===1&&B(t[0])){var i=t[0];this.on(e.event,i)}else if(t.length===0||t.length===1&&A(t[0])){var o=t.length===1?t[0]:null;for(var s=0;s<this.length;s++){var l=this[s];var u=!e.ableField||l._private[e.ableField];var v=l._private[e.field]!=e.value;if(e.overrideAble){var f=e.overrideAble(l);if(f!==undefined){u=f;if(!f){return this}}}if(u){l._private[e.field]=e.value;if(v){r.push(l)}}}var c=this.spawn(r);c.updateStyle();c.emit(e.event);if(o){c.emit(o)}}return this}}function wv(e){bv[e.field]=function(){var t=this[0];if(t){if(e.overrideField){var r=e.overrideField(t);if(r!==undefined){return r}}return t._private[e.field]}};bv[e.on]=xv({event:e.on,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:true});bv[e.off]=xv({event:e.off,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:false})}wv({field:"locked",overrideField:function e(t){return t.cy().autolock()?true:undefined},on:"lock",off:"unlock"});wv({field:"grabbable",overrideField:function e(t){return t.cy().autoungrabify()||t.pannable()?false:undefined},on:"grabify",off:"ungrabify"});wv({field:"selected",ableField:"selectable",overrideAble:function e(t){return t.cy().autounselectify()?false:undefined},on:"select",off:"unselect"});wv({field:"selectable",overrideField:function e(t){return t.cy().autounselectify()?false:undefined},on:"selectify",off:"unselectify"});bv.deselect=bv.unselect;bv.grabbed=function(){var e=this[0];if(e){return e._private.grabbed}};wv({field:"active",on:"activate",off:"unactivate"});wv({field:"pannable",on:"panify",off:"unpanify"});bv.inactive=function(){var e=this[0];if(e){return!e._private.active}};var Ev={};var Tv=function e(t){return function e(r){var a=this;var n=[];for(var i=0;i<a.length;i++){var o=a[i];if(!o.isNode()){continue}var s=false;var l=o.connectedEdges();for(var u=0;u<l.length;u++){var v=l[u];var f=v.source();var c=v.target();if(t.noIncomingEdges&&c===o&&f!==o||t.noOutgoingEdges&&f===o&&c!==o){s=true;break}}if(!s){n.push(o)}}return this.spawn(n,true).filter(r)}};var kv=function e(t){return function(e){var r=this;var a=[];for(var n=0;n<r.length;n++){var i=r[n];if(!i.isNode()){continue}var o=i.connectedEdges();for(var s=0;s<o.length;s++){var l=o[s];var u=l.source();var v=l.target();if(t.outgoing&&u===i){a.push(l);a.push(v)}else if(t.incoming&&v===i){a.push(l);a.push(u)}}}return this.spawn(a,true).filter(e)}};var Cv=function e(t){return function(e){var r=this;var a=[];var n={};for(;;){var i=t.outgoing?r.outgoers():r.incomers();if(i.length===0){break}var o=false;for(var s=0;s<i.length;s++){var l=i[s];var u=l.id();if(!n[u]){n[u]=true;a.push(l);o=true}}if(!o){break}r=i}return this.spawn(a,true).filter(e)}};Ev.clearTraversalCache=function(){for(var e=0;e<this.length;e++){this[e]._private.traversalCache=null}};se(Ev,{roots:Tv({noIncomingEdges:true}),leaves:Tv({noOutgoingEdges:true}),outgoers:Xl(kv({outgoing:true}),"outgoers"),successors:Cv({outgoing:true}),incomers:Xl(kv({incoming:true}),"incomers"),predecessors:Cv({})});se(Ev,{neighborhood:Xl((function(e){var t=[];var r=this.nodes();for(var a=0;a<r.length;a++){var n=r[a];var i=n.connectedEdges();for(var o=0;o<i.length;o++){var s=i[o];var l=s.source();var u=s.target();var v=n===l?u:l;if(v.length>0){t.push(v[0])}t.push(s[0])}}return this.spawn(t,true).filter(e)}),"neighborhood"),closedNeighborhood:function e(t){return this.neighborhood().add(this).filter(t)},openNeighborhood:function e(t){return this.neighborhood(t)}});Ev.neighbourhood=Ev.neighborhood;Ev.closedNeighbourhood=Ev.closedNeighborhood;Ev.openNeighbourhood=Ev.openNeighborhood;se(Ev,{source:Xl((function e(t){var r=this[0];var a;if(r){a=r._private.source||r.cy().collection()}return a&&t?a.filter(t):a}),"source"),target:Xl((function e(t){var r=this[0];var a;if(r){a=r._private.target||r.cy().collection()}return a&&t?a.filter(t):a}),"target"),sources:Pv({attr:"source"}),targets:Pv({attr:"target"})});function Pv(e){return function t(r){var a=[];for(var n=0;n<this.length;n++){var i=this[n];var o=i._private[e.attr];if(o){a.push(o)}}return this.spawn(a,true).filter(r)}}se(Ev,{edgesWith:Xl(Sv(),"edgesWith"),edgesTo:Xl(Sv({thisIsSrc:true}),"edgesTo")});function Sv(e){return function t(r){var a=[];var n=this._private.cy;var i=e||{};if(D(r)){r=n.$(r)}for(var o=0;o<r.length;o++){var s=r[o]._private.edges;for(var l=0;l<s.length;l++){var u=s[l];var v=u._private.data;var f=this.hasElementWithId(v.source)&&r.hasElementWithId(v.target);var c=r.hasElementWithId(v.source)&&this.hasElementWithId(v.target);var d=f||c;if(!d){continue}if(i.thisIsSrc||i.thisIsTgt){if(i.thisIsSrc&&!f){continue}if(i.thisIsTgt&&!c){continue}}a.push(u)}}return this.spawn(a,true)}}se(Ev,{connectedEdges:Xl((function(e){var t=[];var r=this;for(var a=0;a<r.length;a++){var n=r[a];if(!n.isNode()){continue}var i=n._private.edges;for(var o=0;o<i.length;o++){var s=i[o];t.push(s)}}return this.spawn(t,true).filter(e)}),"connectedEdges"),connectedNodes:Xl((function(e){var t=[];var r=this;for(var a=0;a<r.length;a++){var n=r[a];if(!n.isEdge()){continue}t.push(n.source()[0]);t.push(n.target()[0])}return this.spawn(t,true).filter(e)}),"connectedNodes"),parallelEdges:Xl(Dv(),"parallelEdges"),codirectedEdges:Xl(Dv({codirected:true}),"codirectedEdges")});function Dv(e){var t={codirected:false};e=se({},t,e);return function t(r){var a=[];var n=this.edges();var i=e;for(var o=0;o<n.length;o++){var s=n[o];var l=s._private;var u=l.source;var v=u._private.data.id;var f=l.data.target;var c=u._private.edges;for(var d=0;d<c.length;d++){var h=c[d];var p=h._private.data;var g=p.target;var y=p.source;var m=g===f&&y===v;var b=v===g&&f===y;if(i.codirected&&m||!i.codirected&&(m||b)){a.push(h)}}}return this.spawn(a,true).filter(r)}}se(Ev,{components:function e(t){var r=this;var a=r.cy();var n=a.collection();var i=t==null?r.nodes():t.nodes();var e=[];if(t!=null&&i.empty()){i=t.sources()}var o=function e(t,r){n.merge(t);i.unmerge(t);r.merge(t)};if(i.empty()){return r.spawn()}var s=function t(){var n=a.collection();e.push(n);var s=i[0];o(s,n);r.bfs({directed:false,roots:s,visit:function e(t){return o(t,n)}});n.forEach((function(e){e.connectedEdges().forEach((function(e){if(r.has(e)&&n.has(e.source())&&n.has(e.target())){n.merge(e)}}))}))};do{s()}while(i.length>0);return e},component:function e(){var t=this[0];return t.cy().mutableElements().components(t)[0]}});Ev.componentsOf=Ev.components;var Bv=function e(t,r){var a=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;if(t===undefined){Rt("A collection must have a reference to the core");return}var i=new Zt;var o=false;if(!r){r=[]}else if(r.length>0&&_(r[0])&&!O(r[0])){o=true;var s=[];var l=new Jt;for(var u=0,v=r.length;u<v;u++){var f=r[u];if(f.data==null){f.data={}}var c=f.data;if(c.id==null){c.id=Vt()}else if(t.hasElementWithId(c.id)||l.has(c.id)){continue}var d=new er(t,f,false);s.push(d);l.add(c.id)}r=s}this.length=0;for(var h=0,p=r.length;h<p;h++){var g=r[h][0];if(g==null){continue}var y=g._private.data.id;if(!a||!i.has(y)){if(a){i.set(y,{index:this.length,ele:g})}this[this.length]=g;this.length++}}this._private={eles:this,cy:t,get map(){if(this.lazyMap==null){this.rebuildMap()}return this.lazyMap},set map(e){this.lazyMap=e},rebuildMap:function e(){var t=this.lazyMap=new Zt;var r=this.eles;for(var a=0;a<r.length;a++){var n=r[a];t.set(n.id(),{index:a,ele:n})}}};if(a){this._private.map=i}if(o&&!n){this.restore()}};var Av=er.prototype=Bv.prototype=Object.create(Array.prototype);Av.instanceString=function(){return"collection"};Av.spawn=function(e,t){return new Bv(this.cy(),e,t)};Av.spawnSelf=function(){return this.spawn(this)};Av.cy=function(){return this._private.cy};Av.renderer=function(){return this._private.cy.renderer()};Av.element=function(){return this[0]};Av.collection=function(){if(z(this)){return this}else{return new Bv(this._private.cy,[this])}};Av.unique=function(){return new Bv(this._private.cy,this,true)};Av.hasElementWithId=function(e){e=""+e;return this._private.map.has(e)};Av.getElementById=function(e){e=""+e;var t=this._private.cy;var r=this._private.map.get(e);return r?r.ele:new Bv(t)};Av.$id=Av.getElementById;Av.poolIndex=function(){var e=this._private.cy;var t=e._private.elements;var r=this[0]._private.data.id;return t._private.map.get(r).index};Av.indexOf=function(e){var t=e[0]._private.data.id;return this._private.map.get(t).index};Av.indexOfId=function(e){e=""+e;return this._private.map.get(e).index};Av.json=function(e){var t=this.element();var r=this.cy();if(t==null&&e){return this}if(t==null){return undefined}var a=t._private;if(_(e)){r.startBatch();if(e.data){t.data(e.data);var n=a.data;if(t.isEdge()){var i=false;var o={};var s=e.data.source;var l=e.data.target;if(s!=null&&s!=n.source){o.source=""+s;i=true}if(l!=null&&l!=n.target){o.target=""+l;i=true}if(i){t=t.move(o)}}else{var u="parent"in e.data;var v=e.data.parent;if(u&&(v!=null||n.parent!=null)&&v!=n.parent){if(v===undefined){v=null}if(v!=null){v=""+v}t=t.move({parent:v})}}}if(e.position){t.position(e.position)}var f=function r(n,i,o){var s=e[n];if(s!=null&&s!==a[n]){if(s){t[i]()}else{t[o]()}}};f("removed","remove","restore");f("selected","select","unselect");f("selectable","selectify","unselectify");f("locked","lock","unlock");f("grabbable","grabify","ungrabify");f("pannable","panify","unpanify");if(e.classes!=null){t.classes(e.classes)}r.endBatch();return this}else if(e===undefined){var c={data:zt(a.data),position:zt(a.position),group:a.group,removed:a.removed,selected:a.selected,selectable:a.selectable,locked:a.locked,grabbable:a.grabbable,pannable:a.pannable,classes:null};c.classes="";var d=0;a.classes.forEach((function(e){return c.classes+=d++===0?e:" "+e}));return c}};Av.jsons=function(){var e=[];for(var t=0;t<this.length;t++){var r=this[t];var a=r.json();e.push(a)}return e};Av.clone=function(){var e=this.cy();var t=[];for(var r=0;r<this.length;r++){var a=this[r];var n=a.json();var i=new er(e,n,false);t.push(i)}return new Bv(e,t)};Av.copy=Av.clone;Av.restore=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;var r=this;var a=r.cy();var n=a._private;var i=[];var o=[];var s;for(var l=0,u=r.length;l<u;l++){var v=r[l];if(t&&!v.removed()){continue}if(v.isNode()){i.push(v)}else{o.push(v)}}s=i.concat(o);var f;var c=function e(){s.splice(f,1);f--};for(f=0;f<s.length;f++){var d=s[f];var h=d._private;var p=h.data;d.clearTraversalCache();if(!t&&!h.removed);else if(p.id===undefined){p.id=Vt()}else if(I(p.id)){p.id=""+p.id}else if(X(p.id)||!D(p.id)){Rt("Can not create element with invalid string ID `"+p.id+"`");c();continue}else if(a.hasElementWithId(p.id)){Rt("Can not create second element with ID `"+p.id+"`");c();continue}var g=p.id;if(d.isNode()){var y=h.position;if(y.x==null){y.x=0}if(y.y==null){y.y=0}}if(d.isEdge()){var m=d;var b=["source","target"];var x=b.length;var w=false;for(var E=0;E<x;E++){var T=b[E];var k=p[T];if(I(k)){k=p[T]=""+p[T]}if(k==null||k===""){Rt("Can not create edge `"+g+"` with unspecified "+T);w=true}else if(!a.hasElementWithId(k)){Rt("Can not create edge `"+g+"` with nonexistant "+T+" `"+k+"`");w=true}}if(w){c();continue}var C=a.getElementById(p.source);var P=a.getElementById(p.target);if(C.same(P)){C._private.edges.push(m)}else{C._private.edges.push(m);P._private.edges.push(m)}m._private.source=C;m._private.target=P}h.map=new Zt;h.map.set(g,{ele:d,index:0});h.removed=false;if(t){a.addToPool(d)}}for(var S=0;S<i.length;S++){var B=i[S];var A=B._private.data;if(I(A.parent)){A.parent=""+A.parent}var _=A.parent;var M=_!=null;if(M||B._private.parent){var R=B._private.parent?a.collection().merge(B._private.parent):a.getElementById(_);if(R.empty()){A.parent=undefined}else if(R[0].removed()){Lt("Node added with missing parent, reference to parent removed");A.parent=undefined;B._private.parent=null}else{var N=false;var L=R;while(!L.empty()){if(B.same(L)){N=true;A.parent=undefined;break}L=L.parent()}if(!N){R[0]._private.children.push(B);B._private.parent=R[0];n.hasCompoundNodes=true}}}}if(s.length>0){var O=s.length===r.length?r:new Bv(a,s);for(var z=0;z<O.length;z++){var F=O[z];if(F.isNode()){continue}F.parallelEdges().clearTraversalCache();F.source().clearTraversalCache();F.target().clearTraversalCache()}var V;if(n.hasCompoundNodes){V=a.collection().merge(O).merge(O.connectedNodes()).merge(O.parent())}else{V=O}V.dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(e);if(e){O.emitAndNotify("add")}else if(t){O.emit("add")}}return r};Av.removed=function(){var e=this[0];return e&&e._private.removed};Av.inside=function(){var e=this[0];return e&&!e._private.removed};Av.remove=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;var r=this;var a=[];var n={};var i=r._private.cy;function o(e){var t=e._private.edges;for(var r=0;r<t.length;r++){l(t[r])}}function s(e){var t=e._private.children;for(var r=0;r<t.length;r++){l(t[r])}}function l(e){var r=n[e.id()];if(t&&e.removed()||r){return}else{n[e.id()]=true}if(e.isNode()){a.push(e);o(e);s(e)}else{a.unshift(e)}}for(var u=0,v=r.length;u<v;u++){var f=r[u];l(f)}function c(e,t){var r=e._private.edges;qt(r,t);e.clearTraversalCache()}function d(e){e.clearTraversalCache()}var h=[];h.ids={};function p(e,t){t=t[0];e=e[0];var r=e._private.children;var a=e.id();qt(r,t);t._private.parent=null;if(!h.ids[a]){h.ids[a]=true;h.push(e)}}r.dirtyCompoundBoundsCache();if(t){i.removeFromPool(a)}for(var g=0;g<a.length;g++){var y=a[g];if(y.isEdge()){var m=y.source()[0];var b=y.target()[0];c(m,y);c(b,y);var x=y.parallelEdges();for(var w=0;w<x.length;w++){var E=x[w];d(E);if(E.isBundledBezier()){E.dirtyBoundingBoxCache()}}}else{var T=y.parent();if(T.length!==0){p(T,y)}}if(t){y._private.removed=true}}var k=i._private.elements;i._private.hasCompoundNodes=false;for(var C=0;C<k.length;C++){var P=k[C];if(P.isParent()){i._private.hasCompoundNodes=true;break}}var S=new Bv(this.cy(),a);if(S.size()>0){if(e){S.emitAndNotify("remove")}else if(t){S.emit("remove")}}for(var D=0;D<h.length;D++){var B=h[D];if(!t||!B.removed()){B.updateStyle()}}return S};Av.move=function(e){var t=this._private.cy;var r=this;var a=false;var n=false;var i=function e(t){return t==null?t:""+t};if(e.source!==undefined||e.target!==undefined){var o=i(e.source);var s=i(e.target);var l=o!=null&&t.hasElementWithId(o);var u=s!=null&&t.hasElementWithId(s);if(l||u){t.batch((function(){r.remove(a,n);r.emitAndNotify("moveout");for(var e=0;e<r.length;e++){var t=r[e];var i=t._private.data;if(t.isEdge()){if(l){i.source=o}if(u){i.target=s}}}r.restore(a,n)}));r.emitAndNotify("move")}}else if(e.parent!==undefined){var v=i(e.parent);var f=v===null||t.hasElementWithId(v);if(f){var c=v===null?undefined:v;t.batch((function(){var e=r.remove(a,n);e.emitAndNotify("moveout");for(var t=0;t<r.length;t++){var i=r[t];var o=i._private.data;if(i.isNode()){o.parent=c}}e.restore(a,n)}));r.emitAndNotify("move")}}return this};[$n,cl,dl,jl,Yl,Zl,$l,Ou,Ju,ev,rv,nv,sv,fv,bv,Ev].forEach((function(e){se(Av,e)}));var _v={add:function e(t){var r;var a=this;if(L(t)){var n=t;if(n._private.cy===a){r=n.restore()}else{var i=[];for(var o=0;o<n.length;o++){var s=n[o];i.push(s.json())}r=new Bv(a,i)}}else if(A(t)){var l=t;r=new Bv(a,l)}else if(_(t)&&(A(t.nodes)||A(t.edges))){var u=t;var v=[];var f=["nodes","edges"];for(var c=0,d=f.length;c<d;c++){var h=f[c];var p=u[h];if(A(p)){for(var g=0,y=p.length;g<y;g++){var m=se({group:h},p[g]);v.push(m)}}}r=new Bv(a,v)}else{var b=t;r=new er(a,b).collection()}return r},remove:function e(t){if(L(t));else if(D(t)){var r=t;t=this.$(r)}return t.remove()}};function Mv(e,t,r,a){var n=4,i=.001,o=1e-7,s=10,l=11,u=1/(l-1),v=typeof Float32Array!=="undefined";if(arguments.length!==4){return false}for(var f=0;f<4;++f){if(typeof arguments[f]!=="number"||isNaN(arguments[f])||!isFinite(arguments[f])){return false}}e=Math.min(e,1);r=Math.min(r,1);e=Math.max(e,0);r=Math.max(r,0);var c=v?new Float32Array(l):new Array(l);function d(e,t){return 1-3*t+3*e}function h(e,t){return 3*t-6*e}function p(e){return 3*e}function g(e,t,r){return((d(t,r)*e+h(t,r))*e+p(t))*e}function y(e,t,r){return 3*d(t,r)*e*e+2*h(t,r)*e+p(t)}function m(t,a){for(var i=0;i<n;++i){var o=y(a,e,r);if(o===0){return a}var s=g(a,e,r)-t;a-=s/o}return a}function b(){for(var t=0;t<l;++t){c[t]=g(t*u,e,r)}}function x(t,a,n){var i,l,u=0;do{l=a+(n-a)/2;i=g(l,e,r)-t;if(i>0){n=l}else{a=l}}while(Math.abs(i)>o&&++u<s);return l}function w(t){var a=0,n=1,o=l-1;for(;n!==o&&c[n]<=t;++n){a+=u}--n;var s=(t-c[n])/(c[n+1]-c[n]),v=a+s*u,f=y(v,e,r);if(f>=i){return m(t,v)}else if(f===0){return v}else{return x(t,a,a+u)}}var E=false;function T(){E=true;if(e!==t||r!==a){b()}}var k=function n(i){if(!E){T()}if(e===t&&r===a){return i}if(i===0){return 0}if(i===1){return 1}return g(w(i),t,a)};k.getControlPoints=function(){return[{x:e,y:t},{x:r,y:a}]};var C="generateBezier("+[e,t,r,a]+")";k.toString=function(){return C};return k}var Iv=function(){function e(e){return-e.tension*e.x-e.friction*e.v}function t(t,r,a){var n={x:t.x+a.dx*r,v:t.v+a.dv*r,tension:t.tension,friction:t.friction};return{dx:n.v,dv:e(n)}}function r(r,a){var n={dx:r.v,dv:e(r)},i=t(r,a*.5,n),o=t(r,a*.5,i),s=t(r,a,o),l=1/6*(n.dx+2*(i.dx+o.dx)+s.dx),u=1/6*(n.dv+2*(i.dv+o.dv)+s.dv);r.x=r.x+l*a;r.v=r.v+u*a;return r}return function e(t,a,n){var i={x:-1,v:0,tension:null,friction:null},o=[0],s=0,l=1/1e4,u=16/1e3,v,f,c;t=parseFloat(t)||500;a=parseFloat(a)||20;n=n||null;i.tension=t;i.friction=a;v=n!==null;if(v){s=e(t,a);f=s/n*u}else{f=u}for(;;){c=r(c||i,f);o.push(1+c.x);s+=16;if(!(Math.abs(c.x)>l&&Math.abs(c.v)>l)){break}}return!v?s:function(e){return o[e*(o.length-1)|0]}}}();var Rv=function e(t,r,a,n){var i=Mv(t,r,a,n);return function(e,t,r){return e+(t-e)*i(r)}};var Nv={linear:function e(t,r,a){return t+(r-t)*a},ease:Rv(.25,.1,.25,1),"ease-in":Rv(.42,0,1,1),"ease-out":Rv(0,0,.58,1),"ease-in-out":Rv(.42,0,.58,1),"ease-in-sine":Rv(.47,0,.745,.715),"ease-out-sine":Rv(.39,.575,.565,1),"ease-in-out-sine":Rv(.445,.05,.55,.95),"ease-in-quad":Rv(.55,.085,.68,.53),"ease-out-quad":Rv(.25,.46,.45,.94),"ease-in-out-quad":Rv(.455,.03,.515,.955),"ease-in-cubic":Rv(.55,.055,.675,.19),"ease-out-cubic":Rv(.215,.61,.355,1),"ease-in-out-cubic":Rv(.645,.045,.355,1),"ease-in-quart":Rv(.895,.03,.685,.22),"ease-out-quart":Rv(.165,.84,.44,1),"ease-in-out-quart":Rv(.77,0,.175,1),"ease-in-quint":Rv(.755,.05,.855,.06),"ease-out-quint":Rv(.23,1,.32,1),"ease-in-out-quint":Rv(.86,0,.07,1),"ease-in-expo":Rv(.95,.05,.795,.035),"ease-out-expo":Rv(.19,1,.22,1),"ease-in-out-expo":Rv(1,0,0,1),"ease-in-circ":Rv(.6,.04,.98,.335),"ease-out-circ":Rv(.075,.82,.165,1),"ease-in-out-circ":Rv(.785,.135,.15,.86),spring:function e(t,r,a){if(a===0){return Nv.linear}var e=Iv(t,r,a);return function(t,r,a){return t+(r-t)*e(a)}},"cubic-bezier":Rv};function Lv(e,t,r,a,n){if(a===1){return r}if(t===r){return r}var i=n(t,r,a);if(e==null){return i}if(e.roundValue||e.color){i=Math.round(i)}if(e.min!==undefined){i=Math.max(i,e.min)}if(e.max!==undefined){i=Math.min(i,e.max)}return i}function Ov(e,t){if(e.pfValue!=null||e.value!=null){if(e.pfValue!=null&&(t==null||t.type.units!=="%")){return e.pfValue}else{return e.value}}else{return e}}function zv(e,t,r,a,n){var i=n!=null?n.type:null;if(r<0){r=0}else if(r>1){r=1}var o=Ov(e,n);var s=Ov(t,n);if(I(o)&&I(s)){return Lv(i,o,s,r,a)}else if(A(o)&&A(s)){var l=[];for(var u=0;u<s.length;u++){var v=o[u];var f=s[u];if(v!=null&&f!=null){var c=Lv(i,v,f,r,a);l.push(c)}else{l.push(f)}}return l}return undefined}function Fv(e,t,r,a){var n=!a;var i=e._private;var o=t._private;var s=o.easing;var l=o.startTime;var u=a?e:e.cy();var v=u.style();if(!o.easingImpl){if(s==null){o.easingImpl=Nv["linear"]}else{var f;if(D(s)){var c=v.parse("transition-timing-function",s);f=c.value}else{f=s}var d,h;if(D(f)){d=f;h=[]}else{d=f[1];h=f.slice(2).map((function(e){return+e}))}if(h.length>0){if(d==="spring"){h.push(o.duration)}o.easingImpl=Nv[d].apply(null,h)}else{o.easingImpl=Nv[d]}}}var p=o.easingImpl;var g;if(o.duration===0){g=1}else{g=(r-l)/o.duration}if(o.applying){g=o.progress}if(g<0){g=0}else if(g>1){g=1}if(o.delay==null){var y=o.startPosition;var m=o.position;if(m&&n&&!e.locked()){var b={};if(Vv(y.x,m.x)){b.x=zv(y.x,m.x,g,p)}if(Vv(y.y,m.y)){b.y=zv(y.y,m.y,g,p)}e.position(b)}var x=o.startPan;var w=o.pan;var E=i.pan;var T=w!=null&&a;if(T){if(Vv(x.x,w.x)){E.x=zv(x.x,w.x,g,p)}if(Vv(x.y,w.y)){E.y=zv(x.y,w.y,g,p)}e.emit("pan")}var k=o.startZoom;var C=o.zoom;var P=C!=null&&a;if(P){if(Vv(k,C)){i.zoom=Yr(i.minZoom,zv(k,C,g,p),i.maxZoom)}e.emit("zoom")}if(T||P){e.emit("viewport")}var S=o.style;if(S&&S.length>0&&n){for(var B=0;B<S.length;B++){var A=S[B];var _=A.name;var M=A;var I=o.startStyle[_];var R=v.properties[I.name];var N=zv(I,M,g,p,R);v.overrideBypass(e,_,N)}e.emit("style")}}o.progress=g;return g}function Vv(e,t){if(e==null||t==null){return false}if(I(e)&&I(t)){return true}else if(e&&t){return true}return false}function jv(e,t,r,a){var n=t._private;n.started=true;n.startTime=r-n.progress*n.duration}function Xv(e,t){var r=t._private.aniEles;var a=[];function n(t,r){var n=t._private;var i=n.animation.current;var o=n.animation.queue;var s=false;if(i.length===0){var l=o.shift();if(l){i.push(l)}}var u=function e(t){for(var r=t.length-1;r>=0;r--){var a=t[r];a()}t.splice(0,t.length)};for(var v=i.length-1;v>=0;v--){var f=i[v];var c=f._private;if(c.stopped){i.splice(v,1);c.hooked=false;c.playing=false;c.started=false;u(c.frames);continue}if(!c.playing&&!c.applying){continue}if(c.playing&&c.applying){c.applying=false}if(!c.started){jv(t,f,e)}Fv(t,f,e,r);if(c.applying){c.applying=false}u(c.frames);if(c.step!=null){c.step(e)}if(f.completed()){i.splice(v,1);c.hooked=false;c.playing=false;c.started=false;u(c.completes)}s=true}if(!r&&i.length===0&&o.length===0){a.push(t)}return s}var i=false;for(var o=0;o<r.length;o++){var s=r[o];var l=n(s);i=i||l}var u=n(t,true);if(i||u){if(r.length>0){t.notify("draw",r)}else{t.notify("draw")}}r.unmerge(a);t.emit("step")}var Yv={animate:fl.animate(),animation:fl.animation(),animated:fl.animated(),clearQueue:fl.clearQueue(),delay:fl.delay(),delayAnimation:fl.delayAnimation(),stop:fl.stop(),addToAnimationPool:function e(t){var r=this;if(!r.styleEnabled()){return}r._private.aniEles.merge(t)},stopAnimationLoop:function e(){this._private.animationsRunning=false},startAnimationLoop:function e(){var t=this;t._private.animationsRunning=true;if(!t.styleEnabled()){return}function r(){if(!t._private.animationsRunning){return}ft((function e(a){Xv(a,t);r()}))}var a=t.renderer();if(a&&a.beforeRender){a.beforeRender((function e(r,a){Xv(a,t)}),a.beforeRenderPriorities.animations)}else{r()}}};var qv={qualifierCompare:function e(t,r){if(t==null||r==null){return t==null&&r==null}else{return t.sameText(r)}},eventMatches:function e(t,r,a){var n=r.qualifier;if(n!=null){return t!==a.target&&O(a.target)&&n.matches(a.target)}return true},addEventFields:function e(t,r){r.cy=t;r.target=t},callbackContext:function e(t,r,a){return r.qualifier!=null?a.target:t}};var Wv=function e(t){if(D(t)){return new Fl(t)}else{return t}};var Uv={createEmitter:function e(){var t=this._private;if(!t.emitter){t.emitter=new Uu(qv,this)}return this},emitter:function e(){return this._private.emitter},on:function e(t,r,a){this.emitter().on(t,Wv(r),a);return this},removeListener:function e(t,r,a){this.emitter().removeListener(t,Wv(r),a);return this},removeAllListeners:function e(){this.emitter().removeAllListeners();return this},one:function e(t,r,a){this.emitter().one(t,Wv(r),a);return this},once:function e(t,r,a){this.emitter().one(t,Wv(r),a);return this},emit:function e(t,r){this.emitter().emit(t,r);return this},emitAndNotify:function e(t,r){this.emit(t);this.notify(t,r);return this}};fl.eventAliasesOn(Uv);var Gv={png:function e(t){var r=this._private.renderer;t=t||{};return r.png(t)},jpg:function e(t){var r=this._private.renderer;t=t||{};t.bg=t.bg||"#fff";return r.jpg(t)}};Gv.jpeg=Gv.jpg;var Hv={layout:function e(t){var r=this;if(t==null){Rt("Layout options must be specified to make a layout");return}if(t.name==null){Rt("A `name` must be specified to make a layout");return}var a=t.name;var n=r.extension("layout",a);if(n==null){Rt("No such layout `"+a+"` found.  Did you forget to import it and `cytoscape.use()` it?");return}var i;if(D(t.eles)){i=r.$(t.eles)}else{i=t.eles!=null?t.eles:r.$()}var e=new n(se({},t,{cy:r,eles:i}));return e}};Hv.createLayout=Hv.makeLayout=Hv.layout;var Kv={notify:function e(t,r){var a=this._private;if(this.batching()){a.batchNotifications=a.batchNotifications||{};var n=a.batchNotifications[t]=a.batchNotifications[t]||this.collection();if(r!=null){n.merge(r)}return}if(!a.notificationsEnabled){return}var i=this.renderer();if(this.destroyed()||!i){return}i.notify(t,r)},notifications:function e(t){var r=this._private;if(t===undefined){return r.notificationsEnabled}else{r.notificationsEnabled=t?true:false}return this},noNotifications:function e(t){this.notifications(false);t();this.notifications(true)},batching:function e(){return this._private.batchCount>0},startBatch:function e(){var t=this._private;if(t.batchCount==null){t.batchCount=0}if(t.batchCount===0){t.batchStyleEles=this.collection();t.batchNotifications={}}t.batchCount++;return this},endBatch:function e(){var t=this._private;if(t.batchCount===0){return this}t.batchCount--;if(t.batchCount===0){t.batchStyleEles.updateStyle();var r=this.renderer();Object.keys(t.batchNotifications).forEach((function(e){var a=t.batchNotifications[e];if(a.empty()){r.notify(e)}else{r.notify(e,a)}}))}return this},batch:function e(t){this.startBatch();t();this.endBatch();return this},batchData:function e(t){var r=this;return this.batch((function(){var e=Object.keys(t);for(var a=0;a<e.length;a++){var n=e[a];var i=t[n];var o=r.getElementById(n);o.data(i)}}))}};var Zv=Yt({hideEdgesOnViewport:false,textureOnViewport:false,motionBlur:false,motionBlurOpacity:.05,pixelRatio:undefined,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:false,showFps:false,webgl:false,webglDebug:false,webglDebugShowAtlases:false,webglTexSize:2048,webglTexRows:36,webglTexRowsNodes:18,webglBatchSize:2048,webglTexPerBatch:14,webglBgColor:[255,255,255]});var $v={renderTo:function e(t,r,a,n){var i=this._private.renderer;i.renderTo(t,r,a,n);return this},renderer:function e(){return this._private.renderer},forceRender:function e(){this.notify("draw");return this},resize:function e(){this.invalidateSize();this.emitAndNotify("resize");return this},initRenderer:function e(t){var r=this;var a=r.extension("renderer",t.name);if(a==null){Rt("Can not initialise: No such renderer `".concat(t.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));return}if(t.wheelSensitivity!==undefined){Lt("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.")}var n=Zv(t);n.cy=r;r._private.renderer=new a(n);this.notify("init")},destroyRenderer:function e(){var t=this;t.notify("destroy");var r=t.container();if(r){r._cyreg=null;while(r.childNodes.length>0){r.removeChild(r.childNodes[0])}}t._private.renderer=null;t.mutableElements().forEach((function(e){var t=e._private;t.rscratch={};t.rstyle={};t.animation.current=[];t.animation.queue=[]}))},onRender:function e(t){return this.on("render",t)},offRender:function e(t){return this.off("render",t)}};$v.invalidateDimensions=$v.resize;var Qv={collection:function e(t,r){if(D(t)){return this.$(t)}else if(L(t)){return t.collection()}else if(A(t)){if(!r){r={}}return new Bv(this,t,r.unique,r.removed)}return new Bv(this)},nodes:function e(t){var e=this.$((function(e){return e.isNode()}));if(t){return e.filter(t)}return e},edges:function e(t){var e=this.$((function(e){return e.isEdge()}));if(t){return e.filter(t)}return e},$:function e(t){var r=this._private.elements;if(t){return r.filter(t)}else{return r.spawnSelf()}},mutableElements:function e(){return this._private.elements}};Qv.elements=Qv.filter=Qv.$;var Jv={};var ef="t";var tf="f";Jv.apply=function(e){var t=this;var r=t._private;var a=r.cy;var n=a.collection();for(var i=0;i<e.length;i++){var o=e[i];var s=t.getContextMeta(o);if(s.empty){continue}var l=t.getContextStyle(s);var u=t.applyContextStyle(s,l,o);if(o._private.appliedInitStyle){t.updateTransitions(o,u.diffProps)}else{o._private.appliedInitStyle=true}var v=t.updateStyleHints(o);if(v){n.push(o)}}return n};Jv.getPropertiesDiff=function(e,t){var r=this;var a=r._private.propDiffs=r._private.propDiffs||{};var n=e+"-"+t;var i=a[n];if(i){return i}var o=[];var s={};for(var l=0;l<r.length;l++){var u=r[l];var v=e[l]===ef;var f=t[l]===ef;var c=v!==f;var d=u.mappedProperties.length>0;if(c||f&&d){var h=undefined;if(c&&d){h=u.properties}else if(c){h=u.properties}else if(d){h=u.mappedProperties}for(var p=0;p<h.length;p++){var g=h[p];var y=g.name;var m=false;for(var b=l+1;b<r.length;b++){var x=r[b];var w=t[b]===ef;if(!w){continue}m=x.properties[g.name]!=null;if(m){break}}if(!s[y]&&!m){s[y]=true;o.push(y)}}}}a[n]=o;return o};Jv.getContextMeta=function(e){var t=this;var r="";var a;var n=e._private.styleCxtKey||"";for(var i=0;i<t.length;i++){var o=t[i];var s=o.selector&&o.selector.matches(e);if(s){r+=ef}else{r+=tf}}a=t.getPropertiesDiff(n,r);e._private.styleCxtKey=r;return{key:r,diffPropNames:a,empty:a.length===0}};Jv.getContextStyle=function(e){var t=e.key;var r=this;var a=this._private.contextStyles=this._private.contextStyles||{};if(a[t]){return a[t]}var n={_private:{key:t}};for(var i=0;i<r.length;i++){var o=r[i];var s=t[i]===ef;if(!s){continue}for(var l=0;l<o.properties.length;l++){var u=o.properties[l];n[u.name]=u}}a[t]=n;return n};Jv.applyContextStyle=function(e,t,r){var a=this;var n=e.diffPropNames;var i={};var o=a.types;for(var s=0;s<n.length;s++){var l=n[s];var u=t[l];var v=r.pstyle(l);if(!u){if(!v){continue}else if(v.bypass){u={name:l,deleteBypassed:true}}else{u={name:l,delete:true}}}if(v===u){continue}if(u.mapped===o.fn&&v!=null&&v.mapping!=null&&v.mapping.value===u.value){var f=v.mapping;var c=f.fnValue=u.value(r);if(c===f.prevFnValue){continue}}var d=i[l]={prev:v};a.applyParsedProperty(r,u);d.next=r.pstyle(l);if(d.next&&d.next.bypass){d.next=d.next.bypassed}}return{diffProps:i}};Jv.updateStyleHints=function(e){var t=e._private;var r=this;var a=r.propertyGroupNames;var n=r.propertyGroupKeys;var i=function e(t,a,n){return r.getPropertiesHash(t,a,n)};var o=t.styleKey;if(e.removed()){return false}var s=t.group==="nodes";var l=e._private.style;a=Object.keys(l);for(var u=0;u<n.length;u++){var v=n[u];t.styleKeys[v]=[dt,pt]}var f=function e(r,a){return t.styleKeys[a][0]=yt(r,t.styleKeys[a][0])};var c=function e(r,a){return t.styleKeys[a][1]=mt(r,t.styleKeys[a][1])};var d=function e(t,r){f(t,r);c(t,r)};var h=function e(t,r){for(var a=0;a<t.length;a++){var n=t.charCodeAt(a);f(n,r);c(n,r)}};var p=2e9;var g=function e(t){return-128<t&&t<128&&Math.floor(t)!==t?p-(t*1024|0):t};for(var y=0;y<a.length;y++){var m=a[y];var b=l[m];if(b==null){continue}var x=this.properties[m];var w=x.type;var E=x.groupKey;var T=undefined;if(x.hashOverride!=null){T=x.hashOverride(e,b)}else if(b.pfValue!=null){T=b.pfValue}var k=x.enums==null?b.value:null;var C=T!=null;var P=k!=null;var S=C||P;var D=b.units;if(w.number&&S&&!w.multiple){var B=C?T:k;d(g(B),E);if(!C&&D!=null){h(D,E)}}else{h(b.strValue,E)}}var A=[dt,pt];for(var _=0;_<n.length;_++){var M=n[_];var I=t.styleKeys[M];A[0]=yt(I[0],A[0]);A[1]=mt(I[1],A[1])}t.styleKey=bt(A[0],A[1]);var R=t.styleKeys;t.labelDimsKey=xt(R.labelDimensions);var N=i(e,["label"],R.labelDimensions);t.labelKey=xt(N);t.labelStyleKey=xt(wt(R.commonLabel,N));if(!s){var L=i(e,["source-label"],R.labelDimensions);t.sourceLabelKey=xt(L);t.sourceLabelStyleKey=xt(wt(R.commonLabel,L));var O=i(e,["target-label"],R.labelDimensions);t.targetLabelKey=xt(O);t.targetLabelStyleKey=xt(wt(R.commonLabel,O))}if(s){var z=t.styleKeys,F=z.nodeBody,V=z.nodeBorder,j=z.nodeOutline,X=z.backgroundImage,Y=z.compound,q=z.pie;var W=[F,V,j,X,Y,q].filter((function(e){return e!=null})).reduce(wt,[dt,pt]);t.nodeKey=xt(W);t.hasPie=q!=null&&q[0]!==dt&&q[1]!==pt}return o!==t.styleKey};Jv.clearStyleHints=function(e){var t=e._private;t.styleCxtKey="";t.styleKeys={};t.styleKey=null;t.labelKey=null;t.labelStyleKey=null;t.sourceLabelKey=null;t.sourceLabelStyleKey=null;t.targetLabelKey=null;t.targetLabelStyleKey=null;t.nodeKey=null;t.hasPie=null};Jv.applyParsedProperty=function(e,t){var r=this;var a=t;var n=e._private.style;var i;var o=r.types;var s=r.properties[a.name].type;var l=a.bypass;var u=n[a.name];var v=u&&u.bypass;var f=e._private;var c="mapping";var d=function e(t){if(t==null){return null}else if(t.pfValue!=null){return t.pfValue}else{return t.value}};var h=function t(){var n=d(u);var i=d(a);r.checkTriggers(e,a.name,n,i)};if(t.name==="curve-style"&&e.isEdge()&&(t.value!=="bezier"&&e.isLoop()||t.value==="haystack"&&(e.source().isParent()||e.target().isParent()))){a=t=this.parse(t.name,"bezier",l)}if(a["delete"]){n[a.name]=undefined;h();return true}if(a.deleteBypassed){if(!u){h();return true}else if(u.bypass){u.bypassed=undefined;h();return true}else{return false}}if(a.deleteBypass){if(!u){h();return true}else if(u.bypass){n[a.name]=u.bypassed;h();return true}else{return false}}var p=function t(){Lt("Do not assign mappings to elements without corresponding data (i.e. ele `"+e.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case o.mapData:{var g=a.field.split(".");var y=f.data;for(var m=0;m<g.length&&y;m++){var b=g[m];y=y[b]}if(y==null){p();return false}var x;if(!I(y)){Lt("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+y+"` for `"+e.id()+"` is non-numeric)");return false}else{var w=a.fieldMax-a.fieldMin;if(w===0){x=0}else{x=(y-a.fieldMin)/w}}if(x<0){x=0}else if(x>1){x=1}if(s.color){var E=a.valueMin[0];var T=a.valueMax[0];var k=a.valueMin[1];var C=a.valueMax[1];var P=a.valueMin[2];var S=a.valueMax[2];var D=a.valueMin[3]==null?1:a.valueMin[3];var B=a.valueMax[3]==null?1:a.valueMax[3];var A=[Math.round(E+(T-E)*x),Math.round(k+(C-k)*x),Math.round(P+(S-P)*x),Math.round(D+(B-D)*x)];i={bypass:a.bypass,name:a.name,value:A,strValue:"rgb("+A[0]+", "+A[1]+", "+A[2]+")"}}else if(s.number){var _=a.valueMin+(a.valueMax-a.valueMin)*x;i=this.parse(a.name,_,a.bypass,c)}else{return false}if(!i){p();return false}i.mapping=a;a=i;break}case o.data:{var M=a.field.split(".");var R=f.data;for(var N=0;N<M.length&&R;N++){var L=M[N];R=R[L]}if(R!=null){i=this.parse(a.name,R,a.bypass,c)}if(!i){p();return false}i.mapping=a;a=i;break}case o.fn:{var O=a.value;var z=a.fnValue!=null?a.fnValue:O(e);a.prevFnValue=z;if(z==null){Lt("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+e.id()+"` is null)");return false}i=this.parse(a.name,z,a.bypass,c);if(!i){Lt("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+e.id()+"` is invalid)");return false}i.mapping=zt(a);a=i;break}case undefined:break;default:return false}if(l){if(v){a.bypassed=u.bypassed}else{a.bypassed=u}n[a.name]=a}else{if(v){u.bypassed=a}else{n[a.name]=a}}h();return true};Jv.cleanElements=function(e,t){for(var r=0;r<e.length;r++){var a=e[r];this.clearStyleHints(a);a.dirtyCompoundBoundsCache();a.dirtyBoundingBoxCache();if(!t){a._private.style={}}else{var n=a._private.style;var i=Object.keys(n);for(var o=0;o<i.length;o++){var s=i[o];var l=n[s];if(l!=null){if(l.bypass){l.bypassed=null}else{n[s]=null}}}}}};Jv.update=function(){var e=this._private.cy;var t=e.mutableElements();t.updateStyle()};Jv.updateTransitions=function(e,t){var r=this;var a=e._private;var n=e.pstyle("transition-property").value;var i=e.pstyle("transition-duration").pfValue;var o=e.pstyle("transition-delay").pfValue;if(n.length>0&&i>0){var s={};var l=false;for(var u=0;u<n.length;u++){var v=n[u];var f=e.pstyle(v);var c=t[v];if(!c){continue}var d=c.prev;var h=d;var p=c.next!=null?c.next:f;var g=false;var y=undefined;var m=1e-6;if(!h){continue}if(I(h.pfValue)&&I(p.pfValue)){g=p.pfValue-h.pfValue;y=h.pfValue+m*g}else if(I(h.value)&&I(p.value)){g=p.value-h.value;y=h.value+m*g}else if(A(h.value)&&A(p.value)){g=h.value[0]!==p.value[0]||h.value[1]!==p.value[1]||h.value[2]!==p.value[2];y=h.strValue}if(g){s[v]=p.strValue;this.applyBypass(e,v,y);l=true}}if(!l){return}a.transitioning=true;new si((function(t){if(o>0){e.delayAnimation(o).play().promise().then(t)}else{t()}})).then((function(){return e.animation({style:s,duration:i,easing:e.pstyle("transition-timing-function").value,queue:false}).play().promise()})).then((function(){r.removeBypasses(e,n);e.emitAndNotify("style");a.transitioning=false}))}else if(a.transitioning){this.removeBypasses(e,n);e.emitAndNotify("style");a.transitioning=false}};Jv.checkTrigger=function(e,t,r,a,n,i){var o=this.properties[t];var s=n(o);if(e.removed()){return}if(s!=null&&s(r,a,e)){i(o)}};Jv.checkZOrderTrigger=function(e,t,r,a){var n=this;this.checkTrigger(e,t,r,a,(function(e){return e.triggersZOrder}),(function(){n._private.cy.notify("zorder",e)}))};Jv.checkBoundsTrigger=function(e,t,r,a){this.checkTrigger(e,t,r,a,(function(e){return e.triggersBounds}),(function(t){e.dirtyCompoundBoundsCache();e.dirtyBoundingBoxCache()}))};Jv.checkConnectedEdgesBoundsTrigger=function(e,t,r,a){this.checkTrigger(e,t,r,a,(function(e){return e.triggersBoundsOfConnectedEdges}),(function(t){e.connectedEdges().forEach((function(e){e.dirtyBoundingBoxCache()}))}))};Jv.checkParallelEdgesBoundsTrigger=function(e,t,r,a){this.checkTrigger(e,t,r,a,(function(e){return e.triggersBoundsOfParallelEdges}),(function(t){e.parallelEdges().forEach((function(e){e.dirtyBoundingBoxCache()}))}))};Jv.checkTriggers=function(e,t,r,a){e.dirtyStyleCache();this.checkZOrderTrigger(e,t,r,a);this.checkBoundsTrigger(e,t,r,a);this.checkConnectedEdgesBoundsTrigger(e,t,r,a);this.checkParallelEdgesBoundsTrigger(e,t,r,a)};var rf={};rf.applyBypass=function(e,t,r,a){var n=this;var i=[];var o=true;if(t==="*"||t==="**"){if(r!==undefined){for(var s=0;s<n.properties.length;s++){var l=n.properties[s];var u=l.name;var v=this.parse(u,r,true);if(v){i.push(v)}}}}else if(D(t)){var f=this.parse(t,r,true);if(f){i.push(f)}}else if(_(t)){var c=t;a=r;var d=Object.keys(c);for(var h=0;h<d.length;h++){var p=d[h];var g=c[p];if(g===undefined){g=c[K(p)]}if(g!==undefined){var y=this.parse(p,g,true);if(y){i.push(y)}}}}else{return false}if(i.length===0){return false}var m=false;for(var b=0;b<e.length;b++){var x=e[b];var w={};var E=undefined;for(var T=0;T<i.length;T++){var k=i[T];if(a){var C=x.pstyle(k.name);E=w[k.name]={prev:C}}m=this.applyParsedProperty(x,zt(k))||m;if(a){E.next=x.pstyle(k.name)}}if(m){this.updateStyleHints(x)}if(a){this.updateTransitions(x,w,o)}}return m};rf.overrideBypass=function(e,t,r){t=H(t);for(var a=0;a<e.length;a++){var n=e[a];var i=n._private.style[t];var o=this.properties[t].type;var s=o.color;var l=o.mutiple;var u=!i?null:i.pfValue!=null?i.pfValue:i.value;if(!i||!i.bypass){this.applyBypass(n,t,r)}else{i.value=r;if(i.pfValue!=null){i.pfValue=r}if(s){i.strValue="rgb("+r.join(",")+")"}else if(l){i.strValue=r.join(" ")}else{i.strValue=""+r}this.updateStyleHints(n)}this.checkTriggers(n,t,u,r)}};rf.removeAllBypasses=function(e,t){return this.removeBypasses(e,this.propertyNames,t)};rf.removeBypasses=function(e,t,r){var a=true;for(var n=0;n<e.length;n++){var i=e[n];var o={};for(var s=0;s<t.length;s++){var l=t[s];var u=this.properties[l];var v=i.pstyle(u.name);if(!v||!v.bypass){continue}var f="";var c=this.parse(l,f,true);var d=o[u.name]={prev:v};this.applyParsedProperty(i,c);d.next=i.pstyle(u.name)}this.updateStyleHints(i);if(r){this.updateTransitions(i,o,a)}}};var af={};af.getEmSizeInPixels=function(){var e=this.containerCss("font-size");if(e!=null){return parseFloat(e)}else{return 1}};af.containerCss=function(e){var t=this._private.cy;var r=t.container();var a=t.window();if(a&&r&&a.getComputedStyle){return a.getComputedStyle(r).getPropertyValue(e)}};var nf={};nf.getRenderedStyle=function(e,t){if(t){return this.getStylePropertyValue(e,t,true)}else{return this.getRawStyle(e,true)}};nf.getRawStyle=function(e,t){var r=this;e=e[0];if(e){var a={};for(var n=0;n<r.properties.length;n++){var i=r.properties[n];var o=r.getStylePropertyValue(e,i.name,t);if(o!=null){a[i.name]=o;a[K(i.name)]=o}}return a}};nf.getIndexedStyle=function(e,t,r,a){var n=e.pstyle(t)[r][a];return n!=null?n:e.cy().style().getDefaultProperty(t)[r][0]};nf.getStylePropertyValue=function(e,t,r){var a=this;e=e[0];if(e){var n=a.properties[t];if(n.alias){n=n.pointsTo}var i=n.type;var o=e.pstyle(n.name);if(o){var s=o.value,l=o.units,u=o.strValue;if(r&&i.number&&s!=null&&I(s)){var v=e.cy().zoom();var f=function e(t){return t*v};var c=function e(t,r){return f(t)+r};var d=A(s);var h=d?l.every((function(e){return e!=null})):l!=null;if(h){if(d){return s.map((function(e,t){return c(e,l[t])})).join(" ")}else{return c(s,l)}}else{if(d){return s.map((function(e){return D(e)?e:""+f(e)})).join(" ")}else{return""+f(s)}}}else if(u!=null){return u}}return null}};nf.getAnimationStartStyle=function(e,t){var r={};for(var a=0;a<t.length;a++){var n=t[a];var i=n.name;var o=e.pstyle(i);if(o!==undefined){if(_(o)){o=this.parse(i,o.strValue)}else{o=this.parse(i,o)}}if(o){r[i]=o}}return r};nf.getPropsList=function(e){var t=this;var r=[];var a=e;var n=t.properties;if(a){var i=Object.keys(a);for(var o=0;o<i.length;o++){var s=i[o];var l=a[s];var u=n[s]||n[H(s)];var v=this.parse(u.name,l);if(v){r.push(v)}}}return r};nf.getNonDefaultPropertiesHash=function(e,t,r){var a=r.slice();var n,i,o,s;var l,u;for(l=0;l<t.length;l++){n=t[l];i=e.pstyle(n,false);if(i==null){continue}else if(i.pfValue!=null){a[0]=yt(s,a[0]);a[1]=mt(s,a[1])}else{o=i.strValue;for(u=0;u<o.length;u++){s=o.charCodeAt(u);a[0]=yt(s,a[0]);a[1]=mt(s,a[1])}}}return a};nf.getPropertiesHash=nf.getNonDefaultPropertiesHash;var of={};of.appendFromJson=function(e){var t=this;for(var r=0;r<e.length;r++){var a=e[r];var n=a.selector;var i=a.style||a.css;var o=Object.keys(i);t.selector(n);for(var s=0;s<o.length;s++){var l=o[s];var u=i[l];t.css(l,u)}}return t};of.fromJson=function(e){var t=this;t.resetToDefault();t.appendFromJson(e);return t};of.json=function(){var e=[];for(var t=this.defaultLength;t<this.length;t++){var r=this[t];var a=r.selector;var n=r.properties;var i={};for(var o=0;o<n.length;o++){var s=n[o];i[s.name]=s.strValue}e.push({selector:!a?"core":a.toString(),style:i})}return e};var sf={};sf.appendFromString=function(e){var t=this;var r=this;var a=""+e;var n;var i;var o;a=a.replace(/[/][*](\s|.)+?[*][/]/g,"");function s(){if(a.length>n.length){a=a.substr(n.length)}else{a=""}}function l(){if(i.length>o.length){i=i.substr(o.length)}else{i=""}}for(;;){var u=a.match(/^\s*$/);if(u){break}var v=a.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!v){Lt("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+a);break}n=v[0];var f=v[1];if(f!=="core"){var c=new Fl(f);if(c.invalid){Lt("Skipping parsing of block: Invalid selector found in string stylesheet: "+f);s();continue}}var d=v[2];var h=false;i=d;var p=[];for(;;){var g=i.match(/^\s*$/);if(g){break}var y=i.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!y){Lt("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+d);h=true;break}o=y[0];var m=y[1];var b=y[2];var x=t.properties[m];if(!x){Lt("Skipping property: Invalid property name in: "+o);l();continue}var w=r.parse(m,b);if(!w){Lt("Skipping property: Invalid property definition in: "+o);l();continue}p.push({name:m,val:b});l()}if(h){s();break}r.selector(f);for(var E=0;E<p.length;E++){var T=p[E];r.css(T.name,T.val)}s()}return r};sf.fromString=function(e){var t=this;t.resetToDefault();t.appendFromString(e);return t};var lf={};(function(){var e=Q;var t=ee;var r=re;var a=ae;var n=ne;var i=function e(t){return"^"+t+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"};var o=function i(o){var s=e+"|\\w+|"+t+"|"+r+"|"+a+"|"+n;return"^"+o+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+e+")\\s*\\,\\s*("+e+")\\s*,\\s*("+s+")\\s*\\,\\s*("+s+")\\)$"};var s=["^url\\s*\\(\\s*['\"]?(.+?)['\"]?\\s*\\)$","^(none)$","^(.+)$"];lf.types={time:{number:true,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:true,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:true,min:0,max:100,units:"%",implicitUnits:"%",multiple:true},zeroOneNumber:{number:true,min:0,max:1,unitless:true},zeroOneNumbers:{number:true,min:0,max:1,unitless:true,multiple:true},nOneOneNumber:{number:true,min:-1,max:1,unitless:true},nonNegativeInt:{number:true,min:0,integer:true,unitless:true},nonNegativeNumber:{number:true,min:0,unitless:true},position:{enums:["parent","origin"]},nodeSize:{number:true,min:0,enums:["label"]},number:{number:true,unitless:true},numbers:{number:true,unitless:true,multiple:true},positiveNumber:{number:true,unitless:true,min:0,strictMin:true},size:{number:true,min:0},bidirectionalSize:{number:true},bidirectionalSizeMaybePercent:{number:true,allowPercent:true},bidirectionalSizes:{number:true,multiple:true},sizeMaybePercent:{number:true,min:0,allowPercent:true},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:true,min:0,allowPercent:true,enums:["auto"],multiple:true},bgPos:{number:true,allowPercent:true,multiple:true},bgRelativeTo:{enums:["inner","include-padding"],multiple:true},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:true},bgFit:{enums:["none","contain","cover"],multiple:true},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:true},bgClip:{enums:["none","node"],multiple:true},bgContainment:{enums:["inside","over"],multiple:true},color:{color:true},colors:{color:true,multiple:true},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:true},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:true},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:true,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:true,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:true},data:{mapping:true,regex:i("data")},layoutData:{mapping:true,regex:i("layoutData")},scratch:{mapping:true,regex:i("scratch")},mapData:{mapping:true,regex:o("mapData")},mapLayoutData:{mapping:true,regex:o("mapLayoutData")},mapScratch:{mapping:true,regex:o("mapScratch")},fn:{mapping:true,fn:true},url:{regexes:s,singleRegexMatchValue:true},urls:{regexes:s,singleRegexMatchValue:true,multiple:true},propList:{propList:true},angle:{number:true,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:true,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:true,multiple:true,evenMultiple:true,min:-1,max:1,unitless:true},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:true,multiple:true,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:true,validate:function e(t,r){switch(t.length){case 2:return r[0]!=="deg"&&r[0]!=="rad"&&r[1]!=="deg"&&r[1]!=="rad";case 1:return D(t[0])||r[0]==="deg"||r[0]==="rad";default:return false}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+e+")\\s*,\\s*("+e+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+e+")\\s*,\\s*("+e+")\\s*,\\s*("+e+")\\s*,\\s*("+e+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:true,multiple:true,min:0,validate:function e(t){var r=t.length;return r===1||r===2||r===4}}};var l={zeroNonZero:function e(t,r){if((t==null||r==null)&&t!==r){return true}if(t==0&&r!=0){return true}else if(t!=0&&r==0){return true}else{return false}},any:function e(t,r){return t!=r},emptyNonEmpty:function e(t,r){var a=X(t);var n=X(r);return a&&!n||!a&&n}};var u=lf.types;var v=[{name:"label",type:u.text,triggersBounds:l.any,triggersZOrder:l.emptyNonEmpty},{name:"text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any}];var f=[{name:"source-label",type:u.text,triggersBounds:l.any},{name:"source-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"source-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-offset",type:u.size,triggersBounds:l.any}];var c=[{name:"target-label",type:u.text,triggersBounds:l.any},{name:"target-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"target-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-offset",type:u.size,triggersBounds:l.any}];var d=[{name:"font-family",type:u.fontFamily,triggersBounds:l.any},{name:"font-style",type:u.fontStyle,triggersBounds:l.any},{name:"font-weight",type:u.fontWeight,triggersBounds:l.any},{name:"font-size",type:u.size,triggersBounds:l.any},{name:"text-transform",type:u.textTransform,triggersBounds:l.any},{name:"text-wrap",type:u.textWrap,triggersBounds:l.any},{name:"text-overflow-wrap",type:u.textOverflowWrap,triggersBounds:l.any},{name:"text-max-width",type:u.size,triggersBounds:l.any},{name:"text-outline-width",type:u.size,triggersBounds:l.any},{name:"line-height",type:u.positiveNumber,triggersBounds:l.any}];var h=[{name:"text-valign",type:u.valign,triggersBounds:l.any},{name:"text-halign",type:u.halign,triggersBounds:l.any},{name:"color",type:u.color},{name:"text-outline-color",type:u.color},{name:"text-outline-opacity",type:u.zeroOneNumber},{name:"text-background-color",type:u.color},{name:"text-background-opacity",type:u.zeroOneNumber},{name:"text-background-padding",type:u.size,triggersBounds:l.any},{name:"text-border-opacity",type:u.zeroOneNumber},{name:"text-border-color",type:u.color},{name:"text-border-width",type:u.size,triggersBounds:l.any},{name:"text-border-style",type:u.borderStyle,triggersBounds:l.any},{name:"text-background-shape",type:u.textBackgroundShape,triggersBounds:l.any},{name:"text-justification",type:u.justification}];var p=[{name:"events",type:u.bool,triggersZOrder:l.any},{name:"text-events",type:u.bool,triggersZOrder:l.any}];var g=[{name:"display",type:u.display,triggersZOrder:l.any,triggersBounds:l.any,triggersBoundsOfConnectedEdges:l.any,triggersBoundsOfParallelEdges:function e(t,r,a){if(t===r){return false}return a.pstyle("curve-style").value==="bezier"}},{name:"visibility",type:u.visibility,triggersZOrder:l.any},{name:"opacity",type:u.zeroOneNumber,triggersZOrder:l.zeroNonZero},{name:"text-opacity",type:u.zeroOneNumber},{name:"min-zoomed-font-size",type:u.size},{name:"z-compound-depth",type:u.zCompoundDepth,triggersZOrder:l.any},{name:"z-index-compare",type:u.zIndexCompare,triggersZOrder:l.any},{name:"z-index",type:u.number,triggersZOrder:l.any}];var y=[{name:"overlay-padding",type:u.size,triggersBounds:l.any},{name:"overlay-color",type:u.color},{name:"overlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"overlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"overlay-corner-radius",type:u.cornerRadius}];var m=[{name:"underlay-padding",type:u.size,triggersBounds:l.any},{name:"underlay-color",type:u.color},{name:"underlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"underlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"underlay-corner-radius",type:u.cornerRadius}];var b=[{name:"transition-property",type:u.propList},{name:"transition-duration",type:u.time},{name:"transition-delay",type:u.time},{name:"transition-timing-function",type:u.easing}];var x=function e(t,r){if(r.value==="label"){return-t.poolIndex()}else{return r.pfValue}};var w=[{name:"height",type:u.nodeSize,triggersBounds:l.any,hashOverride:x},{name:"width",type:u.nodeSize,triggersBounds:l.any,hashOverride:x},{name:"shape",type:u.nodeShape,triggersBounds:l.any},{name:"shape-polygon-points",type:u.polygonPointList,triggersBounds:l.any},{name:"corner-radius",type:u.cornerRadius},{name:"background-color",type:u.color},{name:"background-fill",type:u.fill},{name:"background-opacity",type:u.zeroOneNumber},{name:"background-blacken",type:u.nOneOneNumber},{name:"background-gradient-stop-colors",type:u.colors},{name:"background-gradient-stop-positions",type:u.percentages},{name:"background-gradient-direction",type:u.gradientDirection},{name:"padding",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"padding-relative-to",type:u.paddingRelativeTo,triggersBounds:l.any},{name:"bounds-expansion",type:u.boundsExpansion,triggersBounds:l.any}];var E=[{name:"border-color",type:u.color},{name:"border-opacity",type:u.zeroOneNumber},{name:"border-width",type:u.size,triggersBounds:l.any},{name:"border-style",type:u.borderStyle},{name:"border-cap",type:u.lineCap},{name:"border-join",type:u.lineJoin},{name:"border-dash-pattern",type:u.numbers},{name:"border-dash-offset",type:u.number},{name:"border-position",type:u.linePosition}];var T=[{name:"outline-color",type:u.color},{name:"outline-opacity",type:u.zeroOneNumber},{name:"outline-width",type:u.size,triggersBounds:l.any},{name:"outline-style",type:u.borderStyle},{name:"outline-offset",type:u.size,triggersBounds:l.any}];var k=[{name:"background-image",type:u.urls},{name:"background-image-crossorigin",type:u.bgCrossOrigin},{name:"background-image-opacity",type:u.zeroOneNumbers},{name:"background-image-containment",type:u.bgContainment},{name:"background-image-smoothing",type:u.bools},{name:"background-position-x",type:u.bgPos},{name:"background-position-y",type:u.bgPos},{name:"background-width-relative-to",type:u.bgRelativeTo},{name:"background-height-relative-to",type:u.bgRelativeTo},{name:"background-repeat",type:u.bgRepeat},{name:"background-fit",type:u.bgFit},{name:"background-clip",type:u.bgClip},{name:"background-width",type:u.bgWH},{name:"background-height",type:u.bgWH},{name:"background-offset-x",type:u.bgPos},{name:"background-offset-y",type:u.bgPos}];var C=[{name:"position",type:u.position,triggersBounds:l.any},{name:"compound-sizing-wrt-labels",type:u.compoundIncludeLabels,triggersBounds:l.any},{name:"min-width",type:u.size,triggersBounds:l.any},{name:"min-width-bias-left",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-width-bias-right",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height",type:u.size,triggersBounds:l.any},{name:"min-height-bias-top",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height-bias-bottom",type:u.sizeMaybePercent,triggersBounds:l.any}];var P=[{name:"line-style",type:u.lineStyle},{name:"line-color",type:u.color},{name:"line-fill",type:u.fill},{name:"line-cap",type:u.lineCap},{name:"line-opacity",type:u.zeroOneNumber},{name:"line-dash-pattern",type:u.numbers},{name:"line-dash-offset",type:u.number},{name:"line-outline-width",type:u.size},{name:"line-outline-color",type:u.color},{name:"line-gradient-stop-colors",type:u.colors},{name:"line-gradient-stop-positions",type:u.percentages},{name:"curve-style",type:u.curveStyle,triggersBounds:l.any,triggersBoundsOfParallelEdges:function e(t,r){if(t===r){return false}return t==="bezier"||r==="bezier"}},{name:"haystack-radius",type:u.zeroOneNumber,triggersBounds:l.any},{name:"source-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"target-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"control-point-step-size",type:u.size,triggersBounds:l.any},{name:"control-point-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"control-point-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"segment-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-radii",type:u.numbers,triggersBounds:l.any},{name:"radius-type",type:u.radiusType,triggersBounds:l.any},{name:"taxi-turn",type:u.bidirectionalSizeMaybePercent,triggersBounds:l.any},{name:"taxi-turn-min-distance",type:u.size,triggersBounds:l.any},{name:"taxi-direction",type:u.axisDirection,triggersBounds:l.any},{name:"taxi-radius",type:u.number,triggersBounds:l.any},{name:"edge-distances",type:u.edgeDistances,triggersBounds:l.any},{name:"arrow-scale",type:u.positiveNumber,triggersBounds:l.any},{name:"loop-direction",type:u.angle,triggersBounds:l.any},{name:"loop-sweep",type:u.angle,triggersBounds:l.any},{name:"source-distance-from-node",type:u.size,triggersBounds:l.any},{name:"target-distance-from-node",type:u.size,triggersBounds:l.any}];var S=[{name:"ghost",type:u.bool,triggersBounds:l.any},{name:"ghost-offset-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-offset-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-opacity",type:u.zeroOneNumber}];var B=[{name:"selection-box-color",type:u.color},{name:"selection-box-opacity",type:u.zeroOneNumber},{name:"selection-box-border-color",type:u.color},{name:"selection-box-border-width",type:u.size},{name:"active-bg-color",type:u.color},{name:"active-bg-opacity",type:u.zeroOneNumber},{name:"active-bg-size",type:u.size},{name:"outside-texture-bg-color",type:u.color},{name:"outside-texture-bg-opacity",type:u.zeroOneNumber}];var A=[];lf.pieBackgroundN=16;A.push({name:"pie-size",type:u.sizeMaybePercent});for(var _=1;_<=lf.pieBackgroundN;_++){A.push({name:"pie-"+_+"-background-color",type:u.color});A.push({name:"pie-"+_+"-background-size",type:u.percent});A.push({name:"pie-"+_+"-background-opacity",type:u.zeroOneNumber})}var M=[];var I=lf.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:u.arrowShape,triggersBounds:l.any},{name:"arrow-color",type:u.color},{name:"arrow-fill",type:u.arrowFill},{name:"arrow-width",type:u.arrowWidth}].forEach((function(e){I.forEach((function(t){var r=t+"-"+e.name;var a=e.type,n=e.triggersBounds;M.push({name:r,type:a,triggersBounds:n})}))}),{});var R=lf.properties=[].concat(p,b,g,y,m,S,h,d,v,f,c,w,E,T,k,A,C,P,M,B);var N=lf.propertyGroups={behavior:p,transition:b,visibility:g,overlay:y,underlay:m,ghost:S,commonLabel:h,labelDimensions:d,mainLabel:v,sourceLabel:f,targetLabel:c,nodeBody:w,nodeBorder:E,nodeOutline:T,backgroundImage:k,pie:A,compound:C,edgeLine:P,edgeArrow:M,core:B};var L=lf.propertyGroupNames={};var O=lf.propertyGroupKeys=Object.keys(N);O.forEach((function(e){L[e]=N[e].map((function(e){return e.name}));N[e].forEach((function(t){return t.groupKey=e}))}));var z=lf.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];lf.propertyNames=R.map((function(e){return e.name}));for(var F=0;F<R.length;F++){var V=R[F];R[V.name]=V}for(var j=0;j<z.length;j++){var Y=z[j];var q=R[Y.pointsTo];var W={name:Y.name,alias:true,pointsTo:q};R.push(W);R[Y.name]=W}})();lf.getDefaultProperty=function(e){return this.getDefaultProperties()[e]};lf.getDefaultProperties=function(){var e=this._private;if(e.defaultProperties!=null){return e.defaultProperties}var t=se({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce((function(e,t){for(var r=1;r<=lf.pieBackgroundN;r++){var a=t.name.replace("{{i}}",r);var n=t.value;e[a]=n}return e}),{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce((function(e,t){lf.arrowPrefixes.forEach((function(r){var a=r+"-"+t.name;var n=t.value;e[a]=n}));return e}),{}));var r={};for(var a=0;a<this.properties.length;a++){var n=this.properties[a];if(n.pointsTo){continue}var i=n.name;var o=t[i];var s=this.parse(i,o);r[i]=s}e.defaultProperties=r;return e.defaultProperties};lf.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25});this.defaultLength=this.length};var uf={};uf.parse=function(e,t,r,a){var n=this;if(B(t)){return n.parseImplWarn(e,t,r,a)}var i=a==="mapping"||a===true||a===false||a==null?"dontcare":a;var o=r?"t":"f";var s=""+t;var l=kt(e,s,o,i);var u=n.propCache=n.propCache||[];var v;if(!(v=u[l])){v=u[l]=n.parseImplWarn(e,t,r,a)}if(r||a==="mapping"){v=zt(v);if(v){v.value=zt(v.value)}}return v};uf.parseImplWarn=function(e,t,r,a){var n=this.parseImpl(e,t,r,a);if(!n&&t!=null){Lt("The style property `".concat(e,": ").concat(t,"` is invalid"))}if(n&&(n.name==="width"||n.name==="height")&&t==="label"){Lt("The style value of `label` is deprecated for `"+n.name+"`")}return n};uf.parseImpl=function(e,t,r,a){var n=this;e=H(e);var i=n.properties[e];var o=t;var s=n.types;if(!i){return null}if(t===undefined){return null}if(i.alias){i=i.pointsTo;e=i.name}var l=D(t);if(l){t=t.trim()}var u=i.type;if(!u){return null}if(r&&(t===""||t===null)){return{name:e,value:t,bypass:true,deleteBypass:true}}if(B(t)){return{name:e,value:t,strValue:"fn",mapped:s.fn,bypass:r}}var v,f;if(!l||a||t.length<7||t[1]!=="a");else if(t.length>=7&&t[0]==="d"&&(v=new RegExp(s.data.regex).exec(t))){if(r){return false}var c=s.data;return{name:e,value:v,strValue:""+t,mapped:c,field:v[1],bypass:r}}else if(t.length>=10&&t[0]==="m"&&(f=new RegExp(s.mapData.regex).exec(t))){if(r){return false}if(u.multiple){return false}var d=s.mapData;if(!(u.color||u.number)){return false}var h=this.parse(e,f[4]);if(!h||h.mapped){return false}var p=this.parse(e,f[5]);if(!p||p.mapped){return false}if(h.pfValue===p.pfValue||h.strValue===p.strValue){Lt("`"+e+": "+t+"` is not a valid mapper because the output range is zero; converting to `"+e+": "+h.strValue+"`");return this.parse(e,h.strValue)}else if(u.color){var g=h.value;var y=p.value;var m=g[0]===y[0]&&g[1]===y[1]&&g[2]===y[2]&&(g[3]===y[3]||(g[3]==null||g[3]===1)&&(y[3]==null||y[3]===1));if(m){return false}}return{name:e,value:f,strValue:""+t,mapped:d,field:f[1],fieldMin:parseFloat(f[2]),fieldMax:parseFloat(f[3]),valueMin:h.value,valueMax:p.value,bypass:r}}if(u.multiple&&a!=="multiple"){var b;if(l){b=t.split(/\s+/)}else if(A(t)){b=t}else{b=[t]}if(u.evenMultiple&&b.length%2!==0){return null}var x=[];var w=[];var E=[];var T="";var k=false;for(var C=0;C<b.length;C++){var P=n.parse(e,b[C],r,"multiple");k=k||D(P.value);x.push(P.value);E.push(P.pfValue!=null?P.pfValue:P.value);w.push(P.units);T+=(C>0?" ":"")+P.strValue}if(u.validate&&!u.validate(x,w)){return null}if(u.singleEnum&&k){if(x.length===1&&D(x[0])){return{name:e,value:x[0],strValue:x[0],bypass:r}}else{return null}}return{name:e,value:x,pfValue:E,strValue:T,bypass:r,units:w}}var S=function a(){for(var n=0;n<u.enums.length;n++){var i=u.enums[n];if(i===t){return{name:e,value:t,strValue:""+t,bypass:r}}}return null};if(u.number){var _;var M="px";if(u.units){_=u.units}if(u.implicitUnits){M=u.implicitUnits}if(!u.unitless){if(l){var I="px|em"+(u.allowPercent?"|\\%":"");if(_){I=_}var N=t.match("^("+Q+")("+I+")?"+"$");if(N){t=N[1];_=N[2]||M}}else if(!_||u.implicitUnits){_=M}}t=parseFloat(t);if(isNaN(t)&&u.enums===undefined){return null}if(isNaN(t)&&u.enums!==undefined){t=o;return S()}if(u.integer&&!R(t)){return null}if(u.min!==undefined&&(t<u.min||u.strictMin&&t===u.min)||u.max!==undefined&&(t>u.max||u.strictMax&&t===u.max)){return null}var L={name:e,value:t,strValue:""+t+(_?_:""),units:_,bypass:r};if(u.unitless||_!=="px"&&_!=="em"){L.pfValue=t}else{L.pfValue=_==="px"||!_?t:this.getEmSizeInPixels()*t}if(_==="ms"||_==="s"){L.pfValue=_==="ms"?t:1e3*t}if(_==="deg"||_==="rad"){L.pfValue=_==="rad"?t:Ir(t)}if(_==="%"){L.pfValue=t/100}return L}else if(u.propList){var O=[];var z=""+t;if(z==="none");else{var F=z.split(/\s*,\s*|\s+/);for(var V=0;V<F.length;V++){var j=F[V].trim();if(n.properties[j]){O.push(j)}else{Lt("`"+j+"` is not a valid property name")}}if(O.length===0){return null}}return{name:e,value:O,strValue:O.length===0?"none":O.join(" "),bypass:r}}else if(u.color){var X=ce(t);if(!X){return null}return{name:e,value:X,pfValue:X,strValue:"rgb("+X[0]+","+X[1]+","+X[2]+")",bypass:r}}else if(u.regex||u.regexes){if(u.enums){var Y=S();if(Y){return Y}}var q=u.regexes?u.regexes:[u.regex];for(var W=0;W<q.length;W++){var U=new RegExp(q[W]);var G=U.exec(t);if(G){return{name:e,value:u.singleRegexMatchValue?G[1]:G,strValue:""+t,bypass:r}}}return null}else if(u.string){return{name:e,value:""+t,strValue:""+t,bypass:r}}else if(u.enums){return S()}else{return null}};var vf=function e(t){if(!(this instanceof vf)){return new vf(t)}if(!F(t)){Rt("A style must have a core reference");return}this._private={cy:t,coreStyle:{}};this.length=0;this.resetToDefault()};var ff=vf.prototype;ff.instanceString=function(){return"style"};ff.clear=function(){var e=this._private;var t=e.cy;var r=t.elements();for(var a=0;a<this.length;a++){this[a]=undefined}this.length=0;e.contextStyles={};e.propDiffs={};this.cleanElements(r,true);r.forEach((function(e){var t=e[0]._private;t.styleDirty=true;t.appliedInitStyle=false}));return this};ff.resetToDefault=function(){this.clear();this.addDefaultStylesheet();return this};ff.core=function(e){return this._private.coreStyle[e]||this.getDefaultProperty(e)};ff.selector=function(e){var t=e==="core"?null:new Fl(e);var r=this.length++;this[r]={selector:t,properties:[],mappedProperties:[],index:r};return this};ff.css=function(){var e=this;var t=arguments;if(t.length===1){var r=t[0];for(var a=0;a<e.properties.length;a++){var n=e.properties[a];var i=r[n.name];if(i===undefined){i=r[K(n.name)]}if(i!==undefined){this.cssRule(n.name,i)}}}else if(t.length===2){this.cssRule(t[0],t[1])}return this};ff.style=ff.css;ff.cssRule=function(e,t){var r=this.parse(e,t);if(r){var a=this.length-1;this[a].properties.push(r);this[a].properties[r.name]=r;if(r.name.match(/pie-(\d+)-background-size/)&&r.value){this._private.hasPie=true}if(r.mapped){this[a].mappedProperties.push(r)}var n=!this[a].selector;if(n){this._private.coreStyle[r.name]=r}}return this};ff.append=function(e){if(V(e)){e.appendToStyle(this)}else if(A(e)){this.appendFromJson(e)}else if(D(e)){this.appendFromString(e)}return this};vf.fromJson=function(e,t){var r=new vf(e);r.fromJson(t);return r};vf.fromString=function(e,t){return new vf(e).fromString(t)};[Jv,rf,af,nf,of,sf,lf,uf].forEach((function(e){se(ff,e)}));vf.types=ff.types;vf.properties=ff.properties;vf.propertyGroups=ff.propertyGroups;vf.propertyGroupNames=ff.propertyGroupNames;vf.propertyGroupKeys=ff.propertyGroupKeys;var cf={style:function e(t){if(t){var r=this.setStyle(t);r.update()}return this._private.style},setStyle:function e(t){var r=this._private;if(V(t)){r.style=t.generateStyle(this)}else if(A(t)){r.style=vf.fromJson(this,t)}else if(D(t)){r.style=vf.fromString(this,t)}else{r.style=vf(this)}return r.style},updateStyle:function e(){this.mutableElements().updateStyle()}};var df="single";var hf={autolock:function e(t){if(t!==undefined){this._private.autolock=t?true:false}else{return this._private.autolock}return this},autoungrabify:function e(t){if(t!==undefined){this._private.autoungrabify=t?true:false}else{return this._private.autoungrabify}return this},autounselectify:function e(t){if(t!==undefined){this._private.autounselectify=t?true:false}else{return this._private.autounselectify}return this},selectionType:function e(t){var r=this._private;if(r.selectionType==null){r.selectionType=df}if(t!==undefined){if(t==="additive"||t==="single"){r.selectionType=t}}else{return r.selectionType}return this},panningEnabled:function e(t){if(t!==undefined){this._private.panningEnabled=t?true:false}else{return this._private.panningEnabled}return this},userPanningEnabled:function e(t){if(t!==undefined){this._private.userPanningEnabled=t?true:false}else{return this._private.userPanningEnabled}return this},zoomingEnabled:function e(t){if(t!==undefined){this._private.zoomingEnabled=t?true:false}else{return this._private.zoomingEnabled}return this},userZoomingEnabled:function e(t){if(t!==undefined){this._private.userZoomingEnabled=t?true:false}else{return this._private.userZoomingEnabled}return this},boxSelectionEnabled:function e(t){if(t!==undefined){this._private.boxSelectionEnabled=t?true:false}else{return this._private.boxSelectionEnabled}return this},pan:function e(){var t=arguments;var e=this._private.pan;var r,a,n,i,o;switch(t.length){case 0:return e;case 1:if(D(t[0])){r=t[0];return e[r]}else if(_(t[0])){if(!this._private.panningEnabled){return this}n=t[0];i=n.x;o=n.y;if(I(i)){e.x=i}if(I(o)){e.y=o}this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled){return this}r=t[0];a=t[1];if((r==="x"||r==="y")&&I(a)){e[r]=a}this.emit("pan viewport");break}this.notify("viewport");return this},panBy:function e(t,r){var a=arguments;var n=this._private.pan;var i,o,s,l,u;if(!this._private.panningEnabled){return this}switch(a.length){case 1:if(_(t)){s=a[0];l=s.x;u=s.y;if(I(l)){n.x+=l}if(I(u)){n.y+=u}this.emit("pan viewport")}break;case 2:i=t;o=r;if((i==="x"||i==="y")&&I(o)){n[i]+=o}this.emit("pan viewport");break}this.notify("viewport");return this},gc:function e(){this.notify("gc")},fit:function e(t,r){var a=this.getFitViewport(t,r);if(a){var n=this._private;n.zoom=a.zoom;n.pan=a.pan;this.emit("pan zoom viewport");this.notify("viewport")}return this},getFitViewport:function e(t,r){if(I(t)&&r===undefined){r=t;t=undefined}if(!this._private.panningEnabled||!this._private.zoomingEnabled){return}var a;if(D(t)){var n=t;t=this.$(n)}else if(q(t)){var i=t;a={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2};a.w=a.x2-a.x1;a.h=a.y2-a.y1}else if(!L(t)){t=this.mutableElements()}if(L(t)&&t.empty()){return}a=a||t.boundingBox();var o=this.width();var s=this.height();var l;r=I(r)?r:0;if(!isNaN(o)&&!isNaN(s)&&o>0&&s>0&&!isNaN(a.w)&&!isNaN(a.h)&&a.w>0&&a.h>0){l=Math.min((o-2*r)/a.w,(s-2*r)/a.h);l=l>this._private.maxZoom?this._private.maxZoom:l;l=l<this._private.minZoom?this._private.minZoom:l;var u={x:(o-l*(a.x1+a.x2))/2,y:(s-l*(a.y1+a.y2))/2};return{zoom:l,pan:u}}return},zoomRange:function e(t,r){var a=this._private;if(r==null){var n=t;t=n.min;r=n.max}if(I(t)&&I(r)&&t<=r){a.minZoom=t;a.maxZoom=r}else if(I(t)&&r===undefined&&t<=a.maxZoom){a.minZoom=t}else if(I(r)&&t===undefined&&r>=a.minZoom){a.maxZoom=r}return this},minZoom:function e(t){if(t===undefined){return this._private.minZoom}else{return this.zoomRange({min:t})}},maxZoom:function e(t){if(t===undefined){return this._private.maxZoom}else{return this.zoomRange({max:t})}},getZoomedViewport:function e(t){var r=this._private;var a=r.pan;var n=r.zoom;var i;var o;var s=false;if(!r.zoomingEnabled){s=true}if(I(t)){o=t}else if(_(t)){o=t.level;if(t.position!=null){i=Pr(t.position,n,a)}else if(t.renderedPosition!=null){i=t.renderedPosition}if(i!=null&&!r.panningEnabled){s=true}}o=o>r.maxZoom?r.maxZoom:o;o=o<r.minZoom?r.minZoom:o;if(s||!I(o)||o===n||i!=null&&(!I(i.x)||!I(i.y))){return null}if(i!=null){var l=a;var u=n;var v=o;var f={x:-v/u*(i.x-l.x)+i.x,y:-v/u*(i.y-l.y)+i.y};return{zoomed:true,panned:true,zoom:v,pan:f}}else{return{zoomed:true,panned:false,zoom:o,pan:a}}},zoom:function e(t){if(t===undefined){return this._private.zoom}else{var r=this.getZoomedViewport(t);var a=this._private;if(r==null||!r.zoomed){return this}a.zoom=r.zoom;if(r.panned){a.pan.x=r.pan.x;a.pan.y=r.pan.y}this.emit("zoom"+(r.panned?" pan":"")+" viewport");this.notify("viewport");return this}},viewport:function e(t){var r=this._private;var a=true;var n=true;var i=[];var o=false;var s=false;if(!t){return this}if(!I(t.zoom)){a=false}if(!_(t.pan)){n=false}if(!a&&!n){return this}if(a){var l=t.zoom;if(l<r.minZoom||l>r.maxZoom||!r.zoomingEnabled){o=true}else{r.zoom=l;i.push("zoom")}}if(n&&(!o||!t.cancelOnFailedZoom)&&r.panningEnabled){var u=t.pan;if(I(u.x)){r.pan.x=u.x;s=false}if(I(u.y)){r.pan.y=u.y;s=false}if(!s){i.push("pan")}}if(i.length>0){i.push("viewport");this.emit(i.join(" "));this.notify("viewport")}return this},center:function e(t){var r=this.getCenterPan(t);if(r){this._private.pan=r;this.emit("pan viewport");this.notify("viewport")}return this},getCenterPan:function e(t,r){if(!this._private.panningEnabled){return}if(D(t)){var a=t;t=this.mutableElements().filter(a)}else if(!L(t)){t=this.mutableElements()}if(t.length===0){return}var n=t.boundingBox();var i=this.width();var o=this.height();r=r===undefined?this._private.zoom:r;var s={x:(i-r*(n.x1+n.x2))/2,y:(o-r*(n.y1+n.y2))/2};return s},reset:function e(){if(!this._private.panningEnabled||!this._private.zoomingEnabled){return this}this.viewport({pan:{x:0,y:0},zoom:1});return this},invalidateSize:function e(){this._private.sizeCache=null},size:function e(){var t=this._private;var r=t.container;var a=this;return t.sizeCache=t.sizeCache||(r?function(){var e=a.window().getComputedStyle(r);var t=function t(r){return parseFloat(e.getPropertyValue(r))};return{width:r.clientWidth-t("padding-left")-t("padding-right"),height:r.clientHeight-t("padding-top")-t("padding-bottom")}}():{width:1,height:1})},width:function e(){return this.size().width},height:function e(){return this.size().height},extent:function e(){var t=this._private.pan;var r=this._private.zoom;var a=this.renderedExtent();var n={x1:(a.x1-t.x)/r,x2:(a.x2-t.x)/r,y1:(a.y1-t.y)/r,y2:(a.y2-t.y)/r};n.w=n.x2-n.x1;n.h=n.y2-n.y1;return n},renderedExtent:function e(){var t=this.width();var r=this.height();return{x1:0,y1:0,x2:t,y2:r,w:t,h:r}},multiClickDebounceTime:function e(t){if(t)this._private.multiClickDebounceTime=t;else return this._private.multiClickDebounceTime;return this}};hf.centre=hf.center;hf.autolockNodes=hf.autolock;hf.autoungrabifyNodes=hf.autoungrabify;var pf={data:fl.data({field:"data",bindingEvent:"data",allowBinding:true,allowSetting:true,settingEvent:"data",settingTriggersEvent:true,triggerFnName:"trigger",allowGetting:true,updateStyle:true}),removeData:fl.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:true,updateStyle:true}),scratch:fl.data({field:"scratch",bindingEvent:"scratch",allowBinding:true,allowSetting:true,settingEvent:"scratch",settingTriggersEvent:true,triggerFnName:"trigger",allowGetting:true,updateStyle:true}),removeScratch:fl.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:true,updateStyle:true})};pf.attr=pf.data;pf.removeAttr=pf.removeData;var gf=function e(t){var r=this;t=se({},t);var a=t.container;if(a&&!N(a)&&N(a[0])){a=a[0]}var n=a?a._cyreg:null;n=n||{};if(n&&n.cy){n.cy.destroy();n={}}var i=n.readies=n.readies||[];if(a){a._cyreg=n}n.cy=r;var o=w!==undefined&&a!==undefined&&!t.headless;var s=t;s.layout=se({name:o?"grid":"null"},s.layout);s.renderer=se({name:o?"canvas":"null"},s.renderer);var l=function e(t,r,a){if(r!==undefined){return r}else if(a!==undefined){return a}else{return t}};var u=this._private={container:a,ready:false,options:s,elements:new Bv(this),listeners:[],aniEles:new Bv(this),data:s.data||{},scratch:{},layout:null,renderer:null,destroyed:false,notificationsEnabled:true,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:l(true,s.zoomingEnabled),userZoomingEnabled:l(true,s.userZoomingEnabled),panningEnabled:l(true,s.panningEnabled),userPanningEnabled:l(true,s.userPanningEnabled),boxSelectionEnabled:l(true,s.boxSelectionEnabled),autolock:l(false,s.autolock,s.autolockNodes),autoungrabify:l(false,s.autoungrabify,s.autoungrabifyNodes),autounselectify:l(false,s.autounselectify),styleEnabled:s.styleEnabled===undefined?o:s.styleEnabled,zoom:I(s.zoom)?s.zoom:1,pan:{x:_(s.pan)&&I(s.pan.x)?s.pan.x:0,y:_(s.pan)&&I(s.pan.y)?s.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:false,multiClickDebounceTime:l(250,s.multiClickDebounceTime)};this.createEmitter();this.selectionType(s.selectionType);this.zoomRange({min:s.minZoom,max:s.maxZoom});var v=function e(t,r){var a=t.some(W);if(a){return si.all(t).then(r)}else{r(t)}};if(u.styleEnabled){r.setStyle([])}var f=se({},s,s.renderer);r.initRenderer(f);var c=function e(t,a,n){r.notifications(false);var i=r.mutableElements();if(i.length>0){i.remove()}if(t!=null){if(_(t)||A(t)){r.add(t)}}r.one("layoutready",(function(e){r.notifications(true);r.emit(e);r.one("load",a);r.emitAndNotify("load")})).one("layoutstop",(function(){r.one("done",n);r.emit("done")}));var o=se({},r._private.options.layout);o.eles=r.elements();r.layout(o).run()};v([s.style,s.elements],(function(e){var t=e[0];var a=e[1];if(u.styleEnabled){r.style().append(t)}c(a,(function(){r.startAnimationLoop();u.ready=true;if(B(s.ready)){r.on("ready",s.ready)}for(var e=0;e<i.length;e++){var t=i[e];r.on("ready",t)}if(n){n.readies=[]}r.emit("ready")}),s.done)}))};var yf=gf.prototype;se(yf,{instanceString:function e(){return"core"},isReady:function e(){return this._private.ready},destroyed:function e(){return this._private.destroyed},ready:function e(t){if(this.isReady()){this.emitter().emit("ready",[],t)}else{this.on("ready",t)}return this},destroy:function e(){var t=this;if(t.destroyed())return;t.stopAnimationLoop();t.destroyRenderer();this.emit("destroy");t._private.destroyed=true;return t},hasElementWithId:function e(t){return this._private.elements.hasElementWithId(t)},getElementById:function e(t){return this._private.elements.getElementById(t)},hasCompoundNodes:function e(){return this._private.hasCompoundNodes},headless:function e(){return this._private.renderer.isHeadless()},styleEnabled:function e(){return this._private.styleEnabled},addToPool:function e(t){this._private.elements.merge(t);return this},removeFromPool:function e(t){this._private.elements.unmerge(t);return this},container:function e(){return this._private.container||null},window:function e(){var t=this._private.container;if(t==null)return w;var r=this._private.container.ownerDocument;if(r===undefined||r==null){return w}return r.defaultView||w},mount:function e(t){if(t==null){return}var r=this;var a=r._private;var n=a.options;if(!N(t)&&N(t[0])){t=t[0]}r.stopAnimationLoop();r.destroyRenderer();a.container=t;a.styleEnabled=true;r.invalidateSize();r.initRenderer(se({},n,n.renderer,{name:n.renderer.name==="null"?"canvas":n.renderer.name}));r.startAnimationLoop();r.style(n.style);r.emit("mount");return r},unmount:function e(){var t=this;t.stopAnimationLoop();t.destroyRenderer();t.initRenderer({name:"null"});t.emit("unmount");return t},options:function e(){return zt(this._private.options)},json:function e(t){var r=this;var a=r._private;var n=r.mutableElements();var i=function e(t){return r.getElementById(t.id())};if(_(t)){r.startBatch();if(t.elements){var o={};var s=function e(t,a){var n=[];var i=[];for(var s=0;s<t.length;s++){var l=t[s];if(!l.data.id){Lt("cy.json() cannot handle elements without an ID attribute");continue}var u=""+l.data.id;var v=r.getElementById(u);o[u]=true;if(v.length!==0){i.push({ele:v,json:l})}else{if(a){l.group=a;n.push(l)}else{n.push(l)}}}r.add(n);for(var f=0;f<i.length;f++){var c=i[f],d=c.ele,h=c.json;d.json(h)}};if(A(t.elements)){s(t.elements)}else{var l=["nodes","edges"];for(var u=0;u<l.length;u++){var v=l[u];var f=t.elements[v];if(A(f)){s(f,v)}}}var c=r.collection();n.filter((function(e){return!o[e.id()]})).forEach((function(e){if(e.isParent()){c.merge(e)}else{e.remove()}}));c.forEach((function(e){return e.children().move({parent:null})}));c.forEach((function(e){return i(e).remove()}))}if(t.style){r.style(t.style)}if(t.zoom!=null&&t.zoom!==a.zoom){r.zoom(t.zoom)}if(t.pan){if(t.pan.x!==a.pan.x||t.pan.y!==a.pan.y){r.pan(t.pan)}}if(t.data){r.data(t.data)}var d=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"];for(var h=0;h<d.length;h++){var p=d[h];if(t[p]!=null){r[p](t[p])}}r.endBatch();return this}else{var g=!!t;var e={};if(g){e.elements=this.elements().map((function(e){return e.json()}))}else{e.elements={};n.forEach((function(t){var r=t.group();if(!e.elements[r]){e.elements[r]=[]}e.elements[r].push(t.json())}))}if(this._private.styleEnabled){e.style=r.style().json()}e.data=zt(r.data());var y=a.options;e.zoomingEnabled=a.zoomingEnabled;e.userZoomingEnabled=a.userZoomingEnabled;e.zoom=a.zoom;e.minZoom=a.minZoom;e.maxZoom=a.maxZoom;e.panningEnabled=a.panningEnabled;e.userPanningEnabled=a.userPanningEnabled;e.pan=zt(a.pan);e.boxSelectionEnabled=a.boxSelectionEnabled;e.renderer=zt(y.renderer);e.hideEdgesOnViewport=y.hideEdgesOnViewport;e.textureOnViewport=y.textureOnViewport;e.wheelSensitivity=y.wheelSensitivity;e.motionBlur=y.motionBlur;e.multiClickDebounceTime=y.multiClickDebounceTime;return e}}});yf.$id=yf.getElementById;[_v,Yv,Uv,Gv,Hv,Kv,$v,Qv,cf,hf,pf].forEach((function(e){se(yf,e)}));var mf={fit:true,directed:false,padding:30,circle:false,grid:false,spacingFactor:1.75,boundingBox:undefined,avoidOverlap:true,nodeDimensionsIncludeLabels:false,roots:undefined,depthSort:undefined,animate:false,animationDuration:500,animationEasing:undefined,animateFilter:function e(t,r){return true},ready:undefined,stop:undefined,transform:function e(t,r){return r}};var bf={maximal:false,acyclic:false};var xf=function e(t){return t.scratch("breadthfirst")};var wf=function e(t,r){return t.scratch("breadthfirst",r)};function Ef(e){this.options=se({},mf,bf,e)}Ef.prototype.run=function(){var e=this.options;var t=e.cy;var r=e.eles;var a=r.nodes().filter((function(e){return e.isChildless()}));var n=r;var i=e.directed;var o=e.acyclic||e.maximal||e.maximalAdjustments>0;var s=!!e.boundingBox;var l=t.extent();var u=qr(s?e.boundingBox:{x1:l.x1,y1:l.y1,w:l.w,h:l.h});var v;if(L(e.roots)){v=e.roots}else if(A(e.roots)){var f=[];for(var c=0;c<e.roots.length;c++){var d=e.roots[c];var h=t.getElementById(d);f.push(h)}v=t.collection(f)}else if(D(e.roots)){v=t.$(e.roots)}else{if(i){v=a.roots()}else{var p=r.components();v=t.collection();var g=function e(){var t=p[y];var r=t.maxDegree(false);var a=t.filter((function(e){return e.degree(false)===r}));v=v.add(a)};for(var y=0;y<p.length;y++){g()}}}var m=[];var b={};var x=function e(t,r){if(m[r]==null){m[r]=[]}var a=m[r].length;m[r].push(t);wf(t,{index:a,depth:r})};var w=function e(t,r){var a=xf(t),n=a.depth,i=a.index;m[n][i]=null;if(t.isChildless())x(t,r)};n.bfs({roots:v,directed:e.directed,visit:function e(t,r,a,n,i){var o=t[0];var s=o.id();if(o.isChildless())x(o,i);b[s]=true}});var E=[];for(var T=0;T<a.length;T++){var k=a[T];if(b[k.id()]){continue}else{E.push(k)}}var C=function e(t){var r=m[t];for(var a=0;a<r.length;a++){var n=r[a];if(n==null){r.splice(a,1);a--;continue}wf(n,{depth:t,index:a})}};var P=function t(a,n){var i=xf(a);var o=a.incomers().filter((function(e){return e.isNode()&&r.has(e)}));var s=-1;var l=a.id();for(var u=0;u<o.length;u++){var v=o[u];var f=xf(v);s=Math.max(s,f.depth)}if(i.depth<=s){if(!e.acyclic&&n[l]){return null}var c=s+1;w(a,c);n[l]=c;return true}return false};if(i&&o){var S=[];var B={};var _=function e(t){return S.push(t)};var M=function e(){return S.shift()};a.forEach((function(e){return S.push(e)}));while(S.length>0){var I=M();var R=P(I,B);if(R){I.outgoers().filter((function(e){return e.isNode()&&r.has(e)})).forEach(_)}else if(R===null){Lt("Detected double maximal shift for node `"+I.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}var N=0;if(e.avoidOverlap){for(var O=0;O<a.length;O++){var z=a[O];var F=z.layoutDimensions(e);var V=F.w;var j=F.h;N=Math.max(N,V,j)}}var X={};var Y=function e(t){if(X[t.id()]){return X[t.id()]}var r=xf(t).depth;var n=t.neighborhood();var i=0;var o=0;for(var s=0;s<n.length;s++){var l=n[s];if(l.isEdge()||l.isParent()||!a.has(l)){continue}var u=xf(l);if(u==null){continue}var v=u.index;var f=u.depth;if(v==null||f==null){continue}var c=m[f].length;if(f<r){i+=v/c;o++}}o=Math.max(1,o);i=i/o;if(o===0){i=0}X[t.id()]=i;return i};var q=function e(t,r){var a=Y(t);var n=Y(r);var i=a-n;if(i===0){return ie(t.id(),r.id())}else{return i}};if(e.depthSort!==undefined){q=e.depthSort}var W=m.length;for(var U=0;U<W;U++){m[U].sort(q);C(U)}var G=[];for(var H=0;H<E.length;H++){G.push(E[H])}var K=function e(){for(var t=0;t<W;t++){C(t)}};if(G.length){m.unshift(G);W=m.length;K()}var Z=0;for(var $=0;$<W;$++){Z=Math.max(m[$].length,Z)}var Q={x:u.x1+u.w/2,y:u.y1+u.h/2};var J=a.reduce((function(t,r){return function(e){return{w:t.w===-1?e.w:(t.w+e.w)/2,h:t.h===-1?e.h:(t.h+e.h)/2}}(r.boundingBox({includeLabels:e.nodeDimensionsIncludeLabels}))}),{w:-1,h:-1});var ee=Math.max(W===1?0:s?(u.h-e.padding*2-J.h)/(W-1):(u.h-e.padding*2-J.h)/(W+1),N);var te=m.reduce((function(e,t){return Math.max(e,t.length)}),0);var re=function t(r){var a=xf(r),n=a.depth,i=a.index;if(e.circle){var o=Math.min(u.w/2/W,u.h/2/W);o=Math.max(o,N);var l=o*n+o-(W>0&&m[0].length<=3?o/2:0);var v=2*Math.PI/m[n].length*i;if(n===0&&m[0].length===1){l=1}return{x:Q.x+l*Math.cos(v),y:Q.y+l*Math.sin(v)}}else{var f=m[n].length;var c=Math.max(f===1?0:s?(u.w-e.padding*2-J.w)/((e.grid?te:f)-1):(u.w-e.padding*2-J.w)/((e.grid?te:f)+1),N);var d={x:Q.x+(i+1-(f+1)/2)*c,y:Q.y+(n+1-(W+1)/2)*ee};return d}};r.nodes().layoutPositions(this,e,re);return this};var Tf={fit:true,padding:30,boundingBox:undefined,avoidOverlap:true,nodeDimensionsIncludeLabels:false,spacingFactor:undefined,radius:undefined,startAngle:3/2*Math.PI,sweep:undefined,clockwise:true,sort:undefined,animate:false,animationDuration:500,animationEasing:undefined,animateFilter:function e(t,r){return true},ready:undefined,stop:undefined,transform:function e(t,r){return r}};function kf(e){this.options=se({},Tf,e)}kf.prototype.run=function(){var e=this.options;var t=e;var r=e.cy;var a=t.eles;var n=t.counterclockwise!==undefined?!t.counterclockwise:t.clockwise;var i=a.nodes().not(":parent");if(t.sort){i=i.sort(t.sort)}var o=qr(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()});var s={x:o.x1+o.w/2,y:o.y1+o.h/2};var l=t.sweep===undefined?2*Math.PI-2*Math.PI/i.length:t.sweep;var u=l/Math.max(1,i.length-1);var v;var f=0;for(var c=0;c<i.length;c++){var d=i[c];var h=d.layoutDimensions(t);var p=h.w;var g=h.h;f=Math.max(f,p,g)}if(I(t.radius)){v=t.radius}else if(i.length<=1){v=0}else{v=Math.min(o.h,o.w)/2-f}if(i.length>1&&t.avoidOverlap){f*=1.75;var y=Math.cos(u)-Math.cos(0);var m=Math.sin(u)-Math.sin(0);var b=Math.sqrt(f*f/(y*y+m*m));v=Math.max(b,v)}var x=function e(r,a){var i=t.startAngle+a*u*(n?1:-1);var o=v*Math.cos(i);var l=v*Math.sin(i);var f={x:s.x+o,y:s.y+l};return f};a.nodes().layoutPositions(this,t,x);return this};var Cf={fit:true,padding:30,startAngle:3/2*Math.PI,sweep:undefined,clockwise:true,equidistant:false,minNodeSpacing:10,boundingBox:undefined,avoidOverlap:true,nodeDimensionsIncludeLabels:false,height:undefined,width:undefined,spacingFactor:undefined,concentric:function e(t){return t.degree()},levelWidth:function e(t){return t.maxDegree()/4},animate:false,animationDuration:500,animationEasing:undefined,animateFilter:function e(t,r){return true},ready:undefined,stop:undefined,transform:function e(t,r){return r}};function Pf(e){this.options=se({},Cf,e)}Pf.prototype.run=function(){var e=this.options;var t=e;var r=t.counterclockwise!==undefined?!t.counterclockwise:t.clockwise;var a=e.cy;var n=t.eles;var i=n.nodes().not(":parent");var o=qr(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:a.width(),h:a.height()});var s={x:o.x1+o.w/2,y:o.y1+o.h/2};var l=[];var u=0;for(var v=0;v<i.length;v++){var f=i[v];var c=undefined;c=t.concentric(f);l.push({value:c,node:f});f._private.scratch.concentric=c}i.updateStyle();for(var d=0;d<i.length;d++){var h=i[d];var p=h.layoutDimensions(t);u=Math.max(u,p.w,p.h)}l.sort((function(e,t){return t.value-e.value}));var g=t.levelWidth(i);var y=[[]];var m=y[0];for(var b=0;b<l.length;b++){var x=l[b];if(m.length>0){var w=Math.abs(m[0].value-x.value);if(w>=g){m=[];y.push(m)}}m.push(x)}var E=u+t.minNodeSpacing;if(!t.avoidOverlap){var T=y.length>0&&y[0].length>1;var k=Math.min(o.w,o.h)/2-E;var C=k/(y.length+T?1:0);E=Math.min(E,C)}var P=0;for(var S=0;S<y.length;S++){var D=y[S];var B=t.sweep===undefined?2*Math.PI-2*Math.PI/D.length:t.sweep;var A=D.dTheta=B/Math.max(1,D.length-1);if(D.length>1&&t.avoidOverlap){var _=Math.cos(A)-Math.cos(0);var M=Math.sin(A)-Math.sin(0);var I=Math.sqrt(E*E/(_*_+M*M));P=Math.max(I,P)}D.r=P;P+=E}if(t.equidistant){var R=0;var N=0;for(var L=0;L<y.length;L++){var O=y[L];var z=O.r-N;R=Math.max(R,z)}N=0;for(var F=0;F<y.length;F++){var V=y[F];if(F===0){N=V.r}V.r=N;N+=R}}var j={};for(var X=0;X<y.length;X++){var Y=y[X];var q=Y.dTheta;var W=Y.r;for(var U=0;U<Y.length;U++){var G=Y[U];var H=t.startAngle+(r?1:-1)*q*U;var K={x:s.x+W*Math.cos(H),y:s.y+W*Math.sin(H)};j[G.node.id()]=K}}n.nodes().layoutPositions(this,t,(function(e){var t=e.id();return j[t]}));return this};var Sf;var Df={ready:function e(){},stop:function e(){},animate:true,animationEasing:undefined,animationDuration:undefined,animateFilter:function e(t,r){return true},animationThreshold:250,refresh:20,fit:true,padding:30,boundingBox:undefined,nodeDimensionsIncludeLabels:false,randomize:false,componentSpacing:40,nodeRepulsion:function e(t){return 2048},nodeOverlap:4,idealEdgeLength:function e(t){return 32},edgeElasticity:function e(t){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function Bf(e){this.options=se({},Df,e);this.options.layout=this;var t=this.options.eles.nodes();var r=this.options.eles.edges();var a=r.filter((function(e){var r=e.source().data("id");var a=e.target().data("id");var n=t.some((function(e){return e.data("id")===r}));var i=t.some((function(e){return e.data("id")===a}));return!n||!i}));this.options.eles=this.options.eles.not(a)}Bf.prototype.run=function(){var e=this.options;var t=e.cy;var r=this;r.stopped=false;if(e.animate===true||e.animate===false){r.emit({type:"layoutstart",layout:r})}if(true===e.debug){Sf=true}else{Sf=false}var a=Af(t,r,e);if(Sf){If(a)}if(e.randomize){Rf(a)}var n=ct();var i=function r(){Lf(a,t,e);if(true===e.fit){t.fit(e.padding)}};var o=function t(n){if(r.stopped||n>=e.numIter){return false}Of(a,e);a.temperature=a.temperature*e.coolingFactor;if(a.temperature<e.minTemp){return false}return true};var s=function t(){if(e.animate===true||e.animate===false){i();r.one("layoutstop",e.stop);r.emit({type:"layoutstop",layout:r})}else{var n=e.eles.nodes();var o=Nf(a,e,n);n.layoutPositions(r,e,o)}};var l=0;var u=true;if(e.animate===true){var v=function t(){var r=0;while(u&&r<e.refresh){u=o(l);l++;r++}if(!u){Kf(a,e);s()}else{var f=ct();if(f-n>=e.animationThreshold){i()}ft(v)}};v()}else{while(u){u=o(l);l++}Kf(a,e);s()}return this};Bf.prototype.stop=function(){this.stopped=true;if(this.thread){this.thread.stop()}this.emit("layoutstop");return this};Bf.prototype.destroy=function(){if(this.thread){this.thread.stop()}return this};var Af=function e(t,r,a){var n=a.eles.edges();var i=a.eles.nodes();var o=qr(a.boundingBox?a.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});var s={isCompound:t.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:n.size(),temperature:a.initialTemp,clientWidth:o.w,clientHeight:o.h,boundingBox:o};var l=a.eles.components();var u={};for(var v=0;v<l.length;v++){var f=l[v];for(var c=0;c<f.length;c++){var d=f[c];u[d.id()]=v}}for(var v=0;v<s.nodeSize;v++){var h=i[v];var p=h.layoutDimensions(a);var g={};g.isLocked=h.locked();g.id=h.data("id");g.parentId=h.data("parent");g.cmptId=u[h.id()];g.children=[];g.positionX=h.position("x");g.positionY=h.position("y");g.offsetX=0;g.offsetY=0;g.height=p.w;g.width=p.h;g.maxX=g.positionX+g.width/2;g.minX=g.positionX-g.width/2;g.maxY=g.positionY+g.height/2;g.minY=g.positionY-g.height/2;g.padLeft=parseFloat(h.style("padding"));g.padRight=parseFloat(h.style("padding"));g.padTop=parseFloat(h.style("padding"));g.padBottom=parseFloat(h.style("padding"));g.nodeRepulsion=B(a.nodeRepulsion)?a.nodeRepulsion(h):a.nodeRepulsion;s.layoutNodes.push(g);s.idToIndex[g.id]=v}var y=[];var m=0;var b=-1;var x=[];for(var v=0;v<s.nodeSize;v++){var h=s.layoutNodes[v];var w=h.parentId;if(null!=w){s.layoutNodes[s.idToIndex[w]].children.push(h.id)}else{y[++b]=h.id;x.push(h.id)}}s.graphSet.push(x);while(m<=b){var E=y[m++];var T=s.idToIndex[E];var d=s.layoutNodes[T];var k=d.children;if(k.length>0){s.graphSet.push(k);for(var v=0;v<k.length;v++){y[++b]=k[v]}}}for(var v=0;v<s.graphSet.length;v++){var C=s.graphSet[v];for(var c=0;c<C.length;c++){var P=s.idToIndex[C[c]];s.indexToGraph[P]=v}}for(var v=0;v<s.edgeSize;v++){var S=n[v];var D={};D.id=S.data("id");D.sourceId=S.data("source");D.targetId=S.data("target");var A=B(a.idealEdgeLength)?a.idealEdgeLength(S):a.idealEdgeLength;var _=B(a.edgeElasticity)?a.edgeElasticity(S):a.edgeElasticity;var M=s.idToIndex[D.sourceId];var I=s.idToIndex[D.targetId];var R=s.indexToGraph[M];var N=s.indexToGraph[I];if(R!=N){var L=_f(D.sourceId,D.targetId,s);var O=s.graphSet[L];var z=0;var g=s.layoutNodes[M];while(-1===O.indexOf(g.id)){g=s.layoutNodes[s.idToIndex[g.parentId]];z++}g=s.layoutNodes[I];while(-1===O.indexOf(g.id)){g=s.layoutNodes[s.idToIndex[g.parentId]];z++}A*=z*a.nestingFactor}D.idealLength=A;D.elasticity=_;s.layoutEdges.push(D)}return s};var _f=function e(t,r,a){var n=Mf(t,r,0,a);if(2>n.count){return 0}else{return n.graph}};var Mf=function e(t,r,a,n){var i=n.graphSet[a];if(-1<i.indexOf(t)&&-1<i.indexOf(r)){return{count:2,graph:a}}var o=0;for(var s=0;s<i.length;s++){var l=i[s];var u=n.idToIndex[l];var v=n.layoutNodes[u].children;if(0===v.length){continue}var f=n.indexToGraph[n.idToIndex[v[0]]];var c=Mf(t,r,f,n);if(0===c.count){continue}else if(1===c.count){o++;if(2===o){break}}else{return c}}return{count:o,graph:a}};var If;var Rf=function e(t,r){var a=t.clientWidth;var n=t.clientHeight;for(var i=0;i<t.nodeSize;i++){var o=t.layoutNodes[i];if(0===o.children.length&&!o.isLocked){o.positionX=Math.random()*a;o.positionY=Math.random()*n}}};var Nf=function e(t,r,a){var n=t.boundingBox;var i={x1:Infinity,x2:-Infinity,y1:Infinity,y2:-Infinity};if(r.boundingBox){a.forEach((function(e){var r=t.layoutNodes[t.idToIndex[e.data("id")]];i.x1=Math.min(i.x1,r.positionX);i.x2=Math.max(i.x2,r.positionX);i.y1=Math.min(i.y1,r.positionY);i.y2=Math.max(i.y2,r.positionY)}));i.w=i.x2-i.x1;i.h=i.y2-i.y1}return function(e,a){var o=t.layoutNodes[t.idToIndex[e.data("id")]];if(r.boundingBox){var s=(o.positionX-i.x1)/i.w;var l=(o.positionY-i.y1)/i.h;return{x:n.x1+s*n.w,y:n.y1+l*n.h}}else{return{x:o.positionX,y:o.positionY}}}};var Lf=function e(t,r,a){var n=a.layout;var i=a.eles.nodes();var o=Nf(t,a,i);i.positions(o);if(true!==t.ready){t.ready=true;n.one("layoutready",a.ready);n.emit({type:"layoutready",layout:this})}};var Of=function e(t,r,a){zf(t,r);Yf(t);qf(t,r);Wf(t);Uf(t)};var zf=function e(t,r){for(var a=0;a<t.graphSet.length;a++){var n=t.graphSet[a];var i=n.length;for(var o=0;o<i;o++){var s=t.layoutNodes[t.idToIndex[n[o]]];for(var l=o+1;l<i;l++){var u=t.layoutNodes[t.idToIndex[n[l]]];Vf(s,u,t,r)}}}};var Ff=function e(t){return-1+2*t*Math.random()};var Vf=function e(t,r,a,n){var i=t.cmptId;var o=r.cmptId;if(i!==o&&!a.isCompound){return}var s=r.positionX-t.positionX;var l=r.positionY-t.positionY;var u=1;if(0===s&&0===l){s=Ff(u);l=Ff(u)}var v=jf(t,r,s,l);if(v>0){var f=n.nodeOverlap*v;var c=Math.sqrt(s*s+l*l);var d=f*s/c;var h=f*l/c}else{var p=Xf(t,s,l);var g=Xf(r,-1*s,-1*l);var y=g.x-p.x;var m=g.y-p.y;var b=y*y+m*m;var c=Math.sqrt(b);var f=(t.nodeRepulsion+r.nodeRepulsion)/b;var d=f*y/c;var h=f*m/c}if(!t.isLocked){t.offsetX-=d;t.offsetY-=h}if(!r.isLocked){r.offsetX+=d;r.offsetY+=h}return};var jf=function e(t,r,a,n){if(a>0){var i=t.maxX-r.minX}else{var i=r.maxX-t.minX}if(n>0){var o=t.maxY-r.minY}else{var o=r.maxY-t.minY}if(i>=0&&o>=0){return Math.sqrt(i*i+o*o)}else{return 0}};var Xf=function e(t,r,a){var n=t.positionX;var i=t.positionY;var o=t.height||1;var s=t.width||1;var l=a/r;var u=o/s;var v={};if(0===r&&0<a){v.x=n;v.y=i+o/2;return v}if(0===r&&0>a){v.x=n;v.y=i+o/2;return v}if(0<r&&-1*u<=l&&l<=u){v.x=n+s/2;v.y=i+s*a/2/r;return v}if(0>r&&-1*u<=l&&l<=u){v.x=n-s/2;v.y=i-s*a/2/r;return v}if(0<a&&(l<=-1*u||l>=u)){v.x=n+o*r/2/a;v.y=i+o/2;return v}if(0>a&&(l<=-1*u||l>=u)){v.x=n-o*r/2/a;v.y=i-o/2;return v}return v};var Yf=function e(t,r){for(var a=0;a<t.edgeSize;a++){var n=t.layoutEdges[a];var i=t.idToIndex[n.sourceId];var o=t.layoutNodes[i];var s=t.idToIndex[n.targetId];var l=t.layoutNodes[s];var u=l.positionX-o.positionX;var v=l.positionY-o.positionY;if(0===u&&0===v){continue}var f=Xf(o,u,v);var c=Xf(l,-1*u,-1*v);var d=c.x-f.x;var h=c.y-f.y;var p=Math.sqrt(d*d+h*h);var g=Math.pow(n.idealLength-p,2)/n.elasticity;if(0!==p){var y=g*d/p;var m=g*h/p}else{var y=0;var m=0}if(!o.isLocked){o.offsetX+=y;o.offsetY+=m}if(!l.isLocked){l.offsetX-=y;l.offsetY-=m}}};var qf=function e(t,r){if(r.gravity===0){return}var a=1;for(var n=0;n<t.graphSet.length;n++){var i=t.graphSet[n];var o=i.length;if(0===n){var s=t.clientHeight/2;var l=t.clientWidth/2}else{var u=t.layoutNodes[t.idToIndex[i[0]]];var v=t.layoutNodes[t.idToIndex[u.parentId]];var s=v.positionX;var l=v.positionY}for(var f=0;f<o;f++){var c=t.layoutNodes[t.idToIndex[i[f]]];if(c.isLocked){continue}var d=s-c.positionX;var h=l-c.positionY;var p=Math.sqrt(d*d+h*h);if(p>a){var g=r.gravity*d/p;var y=r.gravity*h/p;c.offsetX+=g;c.offsetY+=y}}}};var Wf=function e(t,r){var a=[];var n=0;var i=-1;a.push.apply(a,t.graphSet[0]);i+=t.graphSet[0].length;while(n<=i){var o=a[n++];var s=t.idToIndex[o];var l=t.layoutNodes[s];var u=l.children;if(0<u.length&&!l.isLocked){var v=l.offsetX;var f=l.offsetY;for(var c=0;c<u.length;c++){var d=t.layoutNodes[t.idToIndex[u[c]]];d.offsetX+=v;d.offsetY+=f;a[++i]=u[c]}l.offsetX=0;l.offsetY=0}}};var Uf=function e(t,r){for(var a=0;a<t.nodeSize;a++){var n=t.layoutNodes[a];if(0<n.children.length){n.maxX=undefined;n.minX=undefined;n.maxY=undefined;n.minY=undefined}}for(var a=0;a<t.nodeSize;a++){var n=t.layoutNodes[a];if(0<n.children.length||n.isLocked){continue}var i=Gf(n.offsetX,n.offsetY,t.temperature);n.positionX+=i.x;n.positionY+=i.y;n.offsetX=0;n.offsetY=0;n.minX=n.positionX-n.width;n.maxX=n.positionX+n.width;n.minY=n.positionY-n.height;n.maxY=n.positionY+n.height;Hf(n,t)}for(var a=0;a<t.nodeSize;a++){var n=t.layoutNodes[a];if(0<n.children.length&&!n.isLocked){n.positionX=(n.maxX+n.minX)/2;n.positionY=(n.maxY+n.minY)/2;n.width=n.maxX-n.minX;n.height=n.maxY-n.minY}}};var Gf=function e(t,r,a){var n=Math.sqrt(t*t+r*r);if(n>a){var i={x:a*t/n,y:a*r/n}}else{var i={x:t,y:r}}return i};var Hf=function e(t,r){var a=t.parentId;if(null==a){return}var n=r.layoutNodes[r.idToIndex[a]];var i=false;if(null==n.maxX||t.maxX+n.padRight>n.maxX){n.maxX=t.maxX+n.padRight;i=true}if(null==n.minX||t.minX-n.padLeft<n.minX){n.minX=t.minX-n.padLeft;i=true}if(null==n.maxY||t.maxY+n.padBottom>n.maxY){n.maxY=t.maxY+n.padBottom;i=true}if(null==n.minY||t.minY-n.padTop<n.minY){n.minY=t.minY-n.padTop;i=true}if(i){return Hf(n,r)}return};var Kf=function e(t,r){var a=t.layoutNodes;var n=[];for(var i=0;i<a.length;i++){var o=a[i];var s=o.cmptId;var l=n[s]=n[s]||[];l.push(o)}var u=0;for(var i=0;i<n.length;i++){var v=n[i];if(!v){continue}v.x1=Infinity;v.x2=-Infinity;v.y1=Infinity;v.y2=-Infinity;for(var f=0;f<v.length;f++){var c=v[f];v.x1=Math.min(v.x1,c.positionX-c.width/2);v.x2=Math.max(v.x2,c.positionX+c.width/2);v.y1=Math.min(v.y1,c.positionY-c.height/2);v.y2=Math.max(v.y2,c.positionY+c.height/2)}v.w=v.x2-v.x1;v.h=v.y2-v.y1;u+=v.w*v.h}n.sort((function(e,t){return t.w*t.h-e.w*e.h}));var d=0;var h=0;var p=0;var g=0;var y=Math.sqrt(u)*t.clientWidth/t.clientHeight;for(var i=0;i<n.length;i++){var v=n[i];if(!v){continue}for(var f=0;f<v.length;f++){var c=v[f];if(!c.isLocked){c.positionX+=d-v.x1;c.positionY+=h-v.y1}}d+=v.w+r.componentSpacing;p+=v.w+r.componentSpacing;g=Math.max(g,v.h);if(p>y){h+=g+r.componentSpacing;d=0;p=0;g=0}}};var Zf={fit:true,padding:30,boundingBox:undefined,avoidOverlap:true,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:false,spacingFactor:undefined,condense:false,rows:undefined,cols:undefined,position:function e(t){},sort:undefined,animate:false,animationDuration:500,animationEasing:undefined,animateFilter:function e(t,r){return true},ready:undefined,stop:undefined,transform:function e(t,r){return r}};function $f(e){this.options=se({},Zf,e)}$f.prototype.run=function(){var e=this.options;var t=e;var r=e.cy;var a=t.eles;var n=a.nodes().not(":parent");if(t.sort){n=n.sort(t.sort)}var i=qr(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()});if(i.h===0||i.w===0){a.nodes().layoutPositions(this,t,(function(e){return{x:i.x1,y:i.y1}}))}else{var o=n.size();var s=Math.sqrt(o*i.h/i.w);var l=Math.round(s);var u=Math.round(i.w/i.h*s);var v=function e(t){if(t==null){return Math.min(l,u)}else{var r=Math.min(l,u);if(r==l){l=t}else{u=t}}};var f=function e(t){if(t==null){return Math.max(l,u)}else{var r=Math.max(l,u);if(r==l){l=t}else{u=t}}};var c=t.rows;var d=t.cols!=null?t.cols:t.columns;if(c!=null&&d!=null){l=c;u=d}else if(c!=null&&d==null){l=c;u=Math.ceil(o/l)}else if(c==null&&d!=null){u=d;l=Math.ceil(o/u)}else if(u*l>o){var h=v();var p=f();if((h-1)*p>=o){v(h-1)}else if((p-1)*h>=o){f(p-1)}}else{while(u*l<o){var g=v();var y=f();if((y+1)*g>=o){f(y+1)}else{v(g+1)}}}var m=i.w/u;var b=i.h/l;if(t.condense){m=0;b=0}if(t.avoidOverlap){for(var x=0;x<n.length;x++){var w=n[x];var E=w._private.position;if(E.x==null||E.y==null){E.x=0;E.y=0}var T=w.layoutDimensions(t);var k=t.avoidOverlapPadding;var C=T.w+k;var P=T.h+k;m=Math.max(m,C);b=Math.max(b,P)}}var S={};var D=function e(t,r){return S["c-"+t+"-"+r]?true:false};var B=function e(t,r){S["c-"+t+"-"+r]=true};var A=0;var _=0;var M=function e(){_++;if(_>=u){_=0;A++}};var I={};for(var R=0;R<n.length;R++){var N=n[R];var L=t.position(N);if(L&&(L.row!==undefined||L.col!==undefined)){var O={row:L.row,col:L.col};if(O.col===undefined){O.col=0;while(D(O.row,O.col)){O.col++}}else if(O.row===undefined){O.row=0;while(D(O.row,O.col)){O.row++}}I[N.id()]=O;B(O.row,O.col)}}var z=function e(t,r){var a,n;if(t.locked()||t.isParent()){return false}var o=I[t.id()];if(o){a=o.col*m+m/2+i.x1;n=o.row*b+b/2+i.y1}else{while(D(A,_)){M()}a=_*m+m/2+i.x1;n=A*b+b/2+i.y1;B(A,_);M()}return{x:a,y:n}};n.layoutPositions(this,t,z)}return this};var Qf={ready:function e(){},stop:function e(){}};function Jf(e){this.options=se({},Qf,e)}Jf.prototype.run=function(){var e=this.options;var t=e.eles;var r=this;e.cy;r.emit("layoutstart");t.nodes().positions((function(){return{x:0,y:0}}));r.one("layoutready",e.ready);r.emit("layoutready");r.one("layoutstop",e.stop);r.emit("layoutstop");return this};Jf.prototype.stop=function(){return this};var ec={positions:undefined,zoom:undefined,pan:undefined,fit:true,padding:30,spacingFactor:undefined,animate:false,animationDuration:500,animationEasing:undefined,animateFilter:function e(t,r){return true},ready:undefined,stop:undefined,transform:function e(t,r){return r}};function tc(e){this.options=se({},ec,e)}tc.prototype.run=function(){var e=this.options;var t=e.eles;var r=t.nodes();var a=B(e.positions);function n(t){if(e.positions==null){return Cr(t.position())}if(a){return e.positions(t)}var r=e.positions[t._private.data.id];if(r==null){return null}return r}r.layoutPositions(this,e,(function(e,t){var r=n(e);if(e.locked()||r==null){return false}return r}));return this};var rc={fit:true,padding:30,boundingBox:undefined,animate:false,animationDuration:500,animationEasing:undefined,animateFilter:function e(t,r){return true},ready:undefined,stop:undefined,transform:function e(t,r){return r}};function ac(e){this.options=se({},rc,e)}ac.prototype.run=function(){var e=this.options;var t=e.cy;var r=e.eles;var a=qr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});var n=function e(t,r){return{x:a.x1+Math.round(Math.random()*a.w),y:a.y1+Math.round(Math.random()*a.h)}};r.nodes().layoutPositions(this,e,n);return this};var nc=[{name:"breadthfirst",impl:Ef},{name:"circle",impl:kf},{name:"concentric",impl:Pf},{name:"cose",impl:Bf},{name:"grid",impl:$f},{name:"null",impl:Jf},{name:"preset",impl:tc},{name:"random",impl:ac}];function ic(e){this.options=e;this.notifications=0}var oc=function e(){};var sc=function e(){throw new Error("A headless instance can not render images")};ic.prototype={recalculateRenderedStyle:oc,notify:function e(){this.notifications++},init:oc,isHeadless:function e(){return true},png:sc,jpg:sc};var lc={};lc.arrowShapeWidth=.3;lc.registerArrowShapes=function(){var e=this.arrowShapes={};var t=this;var r=function e(t,r,a,n,i,o,s){var l=i.x-a/2-s;var u=i.x+a/2+s;var v=i.y-a/2-s;var f=i.y+a/2+s;var c=l<=t&&t<=u&&v<=r&&r<=f;return c};var a=function e(t,r,a,n,i){var o=t*Math.cos(n)-r*Math.sin(n);var s=t*Math.sin(n)+r*Math.cos(n);var l=o*a;var u=s*a;var v=l+i.x;var f=u+i.y;return{x:v,y:f}};var n=function e(t,r,n,i){var o=[];for(var s=0;s<t.length;s+=2){var l=t[s];var u=t[s+1];o.push(a(l,u,r,n,i))}return o};var i=function e(t){var r=[];for(var a=0;a<t.length;a++){var n=t[a];r.push(n.x,n.y)}return r};var o=function e(t){return t.pstyle("width").pfValue*t.pstyle("arrow-scale").pfValue*2};var s=function a(s,l){if(D(l)){l=e[l]}e[s]=se({name:s,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function e(t,r,a,o,s,l){var u=i(n(this.points,a+2*l,o,s));var v=va(t,r,u);return v},roughCollide:r,draw:function e(r,a,i,o){var s=n(this.points,a,i,o);t.arrowShapeImpl("polygon")(r,s)},spacing:function e(t){return 0},gap:o},l)};s("none",{collide:_t,roughCollide:_t,draw:It,spacing:Mt,gap:Mt});s("triangle",{points:[-.15,-.3,0,0,.15,-.3]});s("arrow","triangle");s("triangle-backcurve",{points:e["triangle"].points,controlPoint:[0,-.15],roughCollide:r,draw:function e(r,i,o,s,l){var u=n(this.points,i,o,s);var v=this.controlPoint;var f=a(v[0],v[1],i,o,s);t.arrowShapeImpl(this.name)(r,u,f)},gap:function e(t){return o(t)*.8}});s("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function e(t,r,a,o,s,l,u){var v=i(n(this.points,a+2*u,o,s));var f=i(n(this.pointsTee,a+2*u,o,s));var c=va(t,r,v)||va(t,r,f);return c},draw:function e(r,a,i,o,s){var l=n(this.points,a,i,o);var u=n(this.pointsTee,a,i,o);t.arrowShapeImpl(this.name)(r,l,u)}});s("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function e(t,r,a,o,s,l,u){var v=s;var f=Math.pow(v.x-t,2)+Math.pow(v.y-r,2)<=Math.pow((a+2*u)*this.radius,2);var c=i(n(this.points,a+2*u,o,s));return va(t,r,c)||f},draw:function e(r,a,i,o,s){var l=n(this.pointsTr,a,i,o);t.arrowShapeImpl(this.name)(r,l,o.x,o.y,this.radius*a)},spacing:function e(r){return t.getArrowWidth(r.pstyle("width").pfValue,r.pstyle("arrow-scale").value)*this.radius}});s("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function e(t,r){var a=this.baseCrossLinePts.slice();var n=r/t;var i=3;var o=5;a[i]=a[i]-n;a[o]=a[o]-n;return a},collide:function e(t,r,a,o,s,l,u){var v=i(n(this.points,a+2*u,o,s));var f=i(n(this.crossLinePts(a,l),a+2*u,o,s));var c=va(t,r,v)||va(t,r,f);return c},draw:function e(r,a,i,o,s){var l=n(this.points,a,i,o);var u=n(this.crossLinePts(a,s),a,i,o);t.arrowShapeImpl(this.name)(r,l,u)}});s("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function e(t){return o(t)*.525}});s("circle",{radius:.15,collide:function e(t,r,a,n,i,o,s){var l=i;var u=Math.pow(l.x-t,2)+Math.pow(l.y-r,2)<=Math.pow((a+2*s)*this.radius,2);return u},draw:function e(r,a,n,i,o){t.arrowShapeImpl(this.name)(r,i.x,i.y,this.radius*a)},spacing:function e(r){return t.getArrowWidth(r.pstyle("width").pfValue,r.pstyle("arrow-scale").value)*this.radius}});s("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function e(t){return 1},gap:function e(t){return 1}});s("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]});s("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function e(t){return t.pstyle("width").pfValue*t.pstyle("arrow-scale").value}});s("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function e(t){return.95*t.pstyle("width").pfValue*t.pstyle("arrow-scale").value}})};var uc={};uc.projectIntoViewport=function(e,t){var r=this.cy;var a=this.findContainerClientCoords();var n=a[0];var i=a[1];var o=a[4];var s=r.pan();var l=r.zoom();var u=((e-n)/o-s.x)/l;var v=((t-i)/o-s.y)/l;return[u,v]};uc.findContainerClientCoords=function(){if(this.containerBB){return this.containerBB}var e=this.container;var t=e.getBoundingClientRect();var r=this.cy.window().getComputedStyle(e);var a=function e(t){return parseFloat(r.getPropertyValue(t))};var n={left:a("padding-left"),right:a("padding-right"),top:a("padding-top"),bottom:a("padding-bottom")};var i={left:a("border-left-width"),right:a("border-right-width"),top:a("border-top-width"),bottom:a("border-bottom-width")};var o=e.clientWidth;var s=e.clientHeight;var l=n.left+n.right;var u=n.top+n.bottom;var v=i.left+i.right;var f=t.width/(o+v);var c=o-l;var d=s-u;var h=t.left+n.left+i.left;var p=t.top+n.top+i.top;return this.containerBB=[h,p,c,d,f]};uc.invalidateContainerClientCoordsCache=function(){this.containerBB=null};uc.findNearestElement=function(e,t,r,a){return this.findNearestElements(e,t,r,a)[0]};uc.findNearestElements=function(e,t,r,a){var n=this;var i=this;var o=i.getCachedZSortedEles();var s=[];var l=i.cy.zoom();var u=i.cy.hasCompoundNodes();var v=(a?24:8)/l;var f=(a?8:2)/l;var c=(a?8:2)/l;var d=Infinity;var h;var p;if(r){o=o.interactive}function g(e,t){if(e.isNode()){if(p){return}else{p=e;s.push(e)}}if(e.isEdge()&&(t==null||t<d)){if(h){if(h.pstyle("z-compound-depth").value===e.pstyle("z-compound-depth").value&&h.pstyle("z-compound-depth").value===e.pstyle("z-compound-depth").value){for(var r=0;r<s.length;r++){if(s[r].isEdge()){s[r]=e;h=e;d=t!=null?t:d;break}}}}else{s.push(e);h=e;d=t!=null?t:d}}}function y(r){var a=r.outerWidth()+2*f;var o=r.outerHeight()+2*f;var s=a/2;var l=o/2;var u=r.position();var v=r.pstyle("corner-radius").value==="auto"?"auto":r.pstyle("corner-radius").pfValue;var c=r._private.rscratch;if(u.x-s<=e&&e<=u.x+s&&u.y-l<=t&&t<=u.y+l){var d=i.nodeShapes[n.getNodeShape(r)];if(d.checkPoint(e,t,0,a,o,u.x,u.y,v,c)){g(r,0);return true}}}function m(r){var a=r._private;var o=a.rscratch;var l=r.pstyle("width").pfValue;var f=r.pstyle("arrow-scale").value;var c=l/2+v;var d=c*c;var h=c*2;var p=a.source;var m=a.target;var b;if(o.edgeType==="segments"||o.edgeType==="straight"||o.edgeType==="haystack"){var x=o.allpts;for(var w=0;w+3<x.length;w+=2){if(na(e,t,x[w],x[w+1],x[w+2],x[w+3],h)&&d>(b=ua(e,t,x[w],x[w+1],x[w+2],x[w+3]))){g(r,b);return true}}}else if(o.edgeType==="bezier"||o.edgeType==="multibezier"||o.edgeType==="self"||o.edgeType==="compound"){var x=o.allpts;for(var w=0;w+5<o.allpts.length;w+=4){if(ia(e,t,x[w],x[w+1],x[w+2],x[w+3],x[w+4],x[w+5],h)&&d>(b=la(e,t,x[w],x[w+1],x[w+2],x[w+3],x[w+4],x[w+5]))){g(r,b);return true}}}var p=p||a.source;var m=m||a.target;var E=n.getArrowWidth(l,f);var T=[{name:"source",x:o.arrowStartX,y:o.arrowStartY,angle:o.srcArrowAngle},{name:"target",x:o.arrowEndX,y:o.arrowEndY,angle:o.tgtArrowAngle},{name:"mid-source",x:o.midX,y:o.midY,angle:o.midsrcArrowAngle},{name:"mid-target",x:o.midX,y:o.midY,angle:o.midtgtArrowAngle}];for(var w=0;w<T.length;w++){var k=T[w];var C=i.arrowShapes[r.pstyle(k.name+"-arrow-shape").value];var P=r.pstyle("width").pfValue;if(C.roughCollide(e,t,E,k.angle,{x:k.x,y:k.y},P,v)&&C.collide(e,t,E,k.angle,{x:k.x,y:k.y},P,v)){g(r);return true}}if(u&&s.length>0){y(p);y(m)}}function b(e,t,r){return Gt(e,t,r)}function x(r,a){var n=r._private;var i=c;var o;if(a){o=a+"-"}else{o=""}r.boundingBox();var s=n.labelBounds[a||"main"];var l=r.pstyle(o+"label").value;var u=r.pstyle("text-events").strValue==="yes";if(!u||!l){return}var v=b(n.rscratch,"labelX",a);var f=b(n.rscratch,"labelY",a);var d=b(n.rscratch,"labelAngle",a);var h=r.pstyle(o+"text-margin-x").pfValue;var p=r.pstyle(o+"text-margin-y").pfValue;var y=s.x1-i-h;var m=s.x2+i-h;var x=s.y1-i-p;var w=s.y2+i-p;if(d){var E=Math.cos(d);var T=Math.sin(d);var k=function e(t,r){t=t-v;r=r-f;return{x:t*E-r*T+v,y:t*T+r*E+f}};var C=k(y,x);var P=k(y,w);var S=k(m,x);var D=k(m,w);var B=[C.x+h,C.y+p,S.x+h,S.y+p,D.x+h,D.y+p,P.x+h,P.y+p];if(va(e,t,B)){g(r);return true}}else{if(ea(s,e,t)){g(r);return true}}}for(var w=o.length-1;w>=0;w--){var E=o[w];if(E.isNode()){y(E)||x(E)}else{m(E)||x(E)||x(E,"source")||x(E,"target")}}return s};uc.getAllInBox=function(e,t,r,a){var n=this.getCachedZSortedEles().interactive;var i=[];var o=Math.min(e,r);var s=Math.max(e,r);var l=Math.min(t,a);var u=Math.max(t,a);e=o;r=s;t=l;a=u;var v=qr({x1:e,y1:t,x2:r,y2:a});for(var f=0;f<n.length;f++){var c=n[f];if(c.isNode()){var d=c;var h=d.boundingBox({includeNodes:true,includeEdges:false,includeLabels:false});if(Jr(v,h)&&!ra(h,v)){i.push(d)}}else{var p=c;var g=p._private;var y=g.rscratch;if(y.startX!=null&&y.startY!=null&&!ea(v,y.startX,y.startY)){continue}if(y.endX!=null&&y.endY!=null&&!ea(v,y.endX,y.endY)){continue}if(y.edgeType==="bezier"||y.edgeType==="multibezier"||y.edgeType==="self"||y.edgeType==="compound"||y.edgeType==="segments"||y.edgeType==="haystack"){var m=g.rstyle.bezierPts||g.rstyle.linePts||g.rstyle.haystackPts;var b=true;for(var x=0;x<m.length;x++){if(!ta(v,m[x])){b=false;break}}if(b){i.push(p)}}else if(y.edgeType==="haystack"||y.edgeType==="straight"){i.push(p)}}}return i};var vc={};vc.calculateArrowAngles=function(e){var t=e._private.rscratch;var r=t.edgeType==="haystack";var a=t.edgeType==="bezier";var n=t.edgeType==="multibezier";var i=t.edgeType==="segments";var o=t.edgeType==="compound";var s=t.edgeType==="self";var l,u;var v,f,c,d,h,p;if(r){v=t.haystackPts[0];f=t.haystackPts[1];c=t.haystackPts[2];d=t.haystackPts[3]}else{v=t.arrowStartX;f=t.arrowStartY;c=t.arrowEndX;d=t.arrowEndY}h=t.midX;p=t.midY;if(i){l=v-t.segpts[0];u=f-t.segpts[1]}else if(n||o||s||a){var g=t.allpts;var y=Vr(g[0],g[2],g[4],.1);var m=Vr(g[1],g[3],g[5],.1);l=v-y;u=f-m}else{l=v-h;u=f-p}t.srcArrowAngle=Rr(l,u);var h=t.midX;var p=t.midY;if(r){h=(v+c)/2;p=(f+d)/2}l=c-v;u=d-f;if(i){var g=t.allpts;if(g.length/2%2===0){var b=g.length/2;var x=b-2;l=g[b]-g[x];u=g[b+1]-g[x+1]}else if(t.isRound){l=t.midVector[1];u=-t.midVector[0]}else{var b=g.length/2-1;var x=b-2;l=g[b]-g[x];u=g[b+1]-g[x+1]}}else if(n||o||s){var g=t.allpts;var w=t.ctrlpts;var E,T;var k,C;if(w.length/2%2===0){var P=g.length/2-1;var S=P+2;var D=S+2;E=Vr(g[P],g[S],g[D],0);T=Vr(g[P+1],g[S+1],g[D+1],0);k=Vr(g[P],g[S],g[D],1e-4);C=Vr(g[P+1],g[S+1],g[D+1],1e-4)}else{var S=g.length/2-1;var P=S-2;var D=S+2;E=Vr(g[P],g[S],g[D],.4999);T=Vr(g[P+1],g[S+1],g[D+1],.4999);k=Vr(g[P],g[S],g[D],.5);C=Vr(g[P+1],g[S+1],g[D+1],.5)}l=k-E;u=C-T}t.midtgtArrowAngle=Rr(l,u);t.midDispX=l;t.midDispY=u;l*=-1;u*=-1;if(i){var g=t.allpts;if(g.length/2%2===0);else if(!t.isRound){var b=g.length/2-1;var B=b+2;l=-(g[B]-g[b]);u=-(g[B+1]-g[b+1])}}t.midsrcArrowAngle=Rr(l,u);if(i){l=c-t.segpts[t.segpts.length-2];u=d-t.segpts[t.segpts.length-1]}else if(n||o||s||a){var g=t.allpts;var A=g.length;var y=Vr(g[A-6],g[A-4],g[A-2],.9);var m=Vr(g[A-5],g[A-3],g[A-1],.9);l=c-y;u=d-m}else{l=c-h;u=d-p}t.tgtArrowAngle=Rr(l,u)};vc.getArrowWidth=vc.getArrowHeight=function(e,t){var r=this.arrowWidthCache=this.arrowWidthCache||{};var a=r[e+", "+t];if(a){return a}a=Math.max(Math.pow(e*13.37,.9),29)*t;r[e+", "+t]=a;return a};var fc,cc,dc={},hc={},pc,gc,yc,mc,bc,xc,wc,Ec,Tc,kc;var Cc,Pc,Sc,Dc;var Bc;var Ac=function e(t,r,a){a.x=r.x-t.x;a.y=r.y-t.y;a.len=Math.sqrt(a.x*a.x+a.y*a.y);a.nx=a.x/a.len;a.ny=a.y/a.len;a.ang=Math.atan2(a.ny,a.nx)};var _c=function e(t,r){r.x=t.x*-1;r.y=t.y*-1;r.nx=t.nx*-1;r.ny=t.ny*-1;r.ang=t.ang>0?-(Math.PI-t.ang):Math.PI+t.ang};var Mc=function e(t,r,a,n,i){t!==Bc?Ac(r,t,dc):_c(hc,dc);Ac(r,a,hc);pc=dc.nx*hc.ny-dc.ny*hc.nx;gc=dc.nx*hc.nx-dc.ny*-hc.ny;bc=Math.asin(Math.max(-1,Math.min(1,pc)));if(Math.abs(bc)<1e-6){fc=r.x;cc=r.y;wc=Tc=0;return}yc=1;mc=false;if(gc<0){if(bc<0){bc=Math.PI+bc}else{bc=Math.PI-bc;yc=-1;mc=true}}else{if(bc>0){yc=-1;mc=true}}if(r.radius!==undefined){Tc=r.radius}else{Tc=n}xc=bc/2;kc=Math.min(dc.len/2,hc.len/2);if(i){Ec=Math.abs(Math.cos(xc)*Tc/Math.sin(xc));if(Ec>kc){Ec=kc;wc=Math.abs(Ec*Math.sin(xc)/Math.cos(xc))}else{wc=Tc}}else{Ec=Math.min(kc,Tc);wc=Math.abs(Ec*Math.sin(xc)/Math.cos(xc))}Sc=r.x+hc.nx*Ec;Dc=r.y+hc.ny*Ec;fc=Sc-hc.ny*wc*yc;cc=Dc+hc.nx*wc*yc;Cc=r.x+dc.nx*Ec;Pc=r.y+dc.ny*Ec;Bc=r};function Ic(e,t){if(t.radius===0)e.lineTo(t.cx,t.cy);else e.arc(t.cx,t.cy,t.radius,t.startAngle,t.endAngle,t.counterClockwise)}function Rc(e,t,r,a){var n=arguments.length>4&&arguments[4]!==undefined?arguments[4]:true;if(a===0||t.radius===0)return{cx:t.x,cy:t.y,radius:0,startX:t.x,startY:t.y,stopX:t.x,stopY:t.y,startAngle:undefined,endAngle:undefined,counterClockwise:undefined};Mc(e,t,r,a,n);return{cx:fc,cy:cc,radius:wc,startX:Cc,startY:Pc,stopX:Sc,stopY:Dc,startAngle:dc.ang+Math.PI/2*yc,endAngle:hc.ang-Math.PI/2*yc,counterClockwise:mc}}var Nc={};Nc.findMidptPtsEtc=function(e,t){var r=t.posPts,a=t.intersectionPts,n=t.vectorNormInverse;var i;var o=e.pstyle("source-endpoint");var s=e.pstyle("target-endpoint");var l=o.units!=null&&s.units!=null;var u=function e(t,r,a,n){var i=n-r;var o=a-t;var s=Math.sqrt(o*o+i*i);return{x:-i/s,y:o/s}};var v=e.pstyle("edge-distances").value;switch(v){case"node-position":i=r;break;case"intersection":i=a;break;case"endpoints":{if(l){var f=this.manualEndptToPx(e.source()[0],o),c=p(f,2),d=c[0],h=c[1];var g=this.manualEndptToPx(e.target()[0],s),y=p(g,2),m=y[0],b=y[1];var x={x1:d,y1:h,x2:m,y2:b};n=u(d,h,m,b);i=x}else{Lt("Edge ".concat(e.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default)."));i=a}break}}return{midptPts:i,vectorNormInverse:n}};Nc.findHaystackPoints=function(e){for(var t=0;t<e.length;t++){var r=e[t];var a=r._private;var n=a.rscratch;if(!n.haystack){var i=Math.random()*2*Math.PI;n.source={x:Math.cos(i),y:Math.sin(i)};i=Math.random()*2*Math.PI;n.target={x:Math.cos(i),y:Math.sin(i)}}var o=a.source;var s=a.target;var l=o.position();var u=s.position();var v=o.width();var f=s.width();var c=o.height();var d=s.height();var h=r.pstyle("haystack-radius").value;var p=h/2;n.haystackPts=n.allpts=[n.source.x*v*p+l.x,n.source.y*c*p+l.y,n.target.x*f*p+u.x,n.target.y*d*p+u.y];n.midX=(n.allpts[0]+n.allpts[2])/2;n.midY=(n.allpts[1]+n.allpts[3])/2;n.edgeType="haystack";n.haystack=true;this.storeEdgeProjections(r);this.calculateArrowAngles(r);this.recalculateEdgeLabelProjections(r);this.calculateLabelAngles(r)}};Nc.findSegmentsPoints=function(e,t){var r=e._private.rscratch;var a=e.pstyle("segment-weights");var n=e.pstyle("segment-distances");var i=e.pstyle("segment-radii");var o=e.pstyle("radius-type");var s=Math.min(a.pfValue.length,n.pfValue.length);var l=i.pfValue[i.pfValue.length-1];var u=o.pfValue[o.pfValue.length-1];r.edgeType="segments";r.segpts=[];r.radii=[];r.isArcRadius=[];for(var v=0;v<s;v++){var f=a.pfValue[v];var c=n.pfValue[v];var d=1-f;var h=f;var p=this.findMidptPtsEtc(e,t),g=p.midptPts,y=p.vectorNormInverse;var m={x:g.x1*d+g.x2*h,y:g.y1*d+g.y2*h};r.segpts.push(m.x+y.x*c,m.y+y.y*c);r.radii.push(i.pfValue[v]!==undefined?i.pfValue[v]:l);r.isArcRadius.push((o.pfValue[v]!==undefined?o.pfValue[v]:u)==="arc-radius")}};Nc.findLoopPoints=function(e,t,r,a){var n=e._private.rscratch;var i=t.dirCounts,o=t.srcPos;var s=e.pstyle("control-point-distances");var l=s?s.pfValue[0]:undefined;var u=e.pstyle("loop-direction").pfValue;var v=e.pstyle("loop-sweep").pfValue;var f=e.pstyle("control-point-step-size").pfValue;n.edgeType="self";var c=r;var d=f;if(a){c=0;d=l}var h=u-Math.PI/2;var p=h-v/2;var g=h+v/2;var y=String(u+"_"+v);c=i[y]===undefined?i[y]=0:++i[y];n.ctrlpts=[o.x+Math.cos(p)*1.4*d*(c/3+1),o.y+Math.sin(p)*1.4*d*(c/3+1),o.x+Math.cos(g)*1.4*d*(c/3+1),o.y+Math.sin(g)*1.4*d*(c/3+1)]};Nc.findCompoundLoopPoints=function(e,t,r,a){var n=e._private.rscratch;n.edgeType="compound";var i=t.srcPos,o=t.tgtPos,s=t.srcW,l=t.srcH,u=t.tgtW,v=t.tgtH;var f=e.pstyle("control-point-step-size").pfValue;var c=e.pstyle("control-point-distances");var d=c?c.pfValue[0]:undefined;var h=r;var p=f;if(a){h=0;p=d}var g=50;var y={x:i.x-s/2,y:i.y-l/2};var m={x:o.x-u/2,y:o.y-v/2};var b={x:Math.min(y.x,m.x),y:Math.min(y.y,m.y)};var x=.5;var w=Math.max(x,Math.log(s*.01));var E=Math.max(x,Math.log(u*.01));n.ctrlpts=[b.x,b.y-(1+Math.pow(g,1.12)/100)*p*(h/3+1)*w,b.x-(1+Math.pow(g,1.12)/100)*p*(h/3+1)*E,b.y]};Nc.findStraightEdgePoints=function(e){e._private.rscratch.edgeType="straight"};Nc.findBezierPoints=function(e,t,r,a,n){var i=e._private.rscratch;var o=e.pstyle("control-point-step-size").pfValue;var s=e.pstyle("control-point-distances");var l=e.pstyle("control-point-weights");var u=s&&l?Math.min(s.value.length,l.value.length):1;var v=s?s.pfValue[0]:undefined;var f=l.value[0];var c=a;i.edgeType=c?"multibezier":"bezier";i.ctrlpts=[];for(var d=0;d<u;d++){var h=(.5-t.eles.length/2+r)*o*(n?-1:1);var p=undefined;var g=Lr(h);if(c){v=s?s.pfValue[d]:o;f=l.value[d]}if(a){p=v}else{p=v!==undefined?g*v:undefined}var y=p!==undefined?p:h;var m=1-f;var b=f;var x=this.findMidptPtsEtc(e,t),w=x.midptPts,E=x.vectorNormInverse;var T={x:w.x1*m+w.x2*b,y:w.y1*m+w.y2*b};i.ctrlpts.push(T.x+E.x*y,T.y+E.y*y)}};Nc.findTaxiPoints=function(e,t){var r=e._private.rscratch;r.edgeType="segments";var a="vertical";var n="horizontal";var i="leftward";var o="rightward";var s="downward";var l="upward";var u="auto";var v=t.posPts,f=t.srcW,c=t.srcH,d=t.tgtW,h=t.tgtH;var p=e.pstyle("edge-distances").value;var g=p!=="node-position";var y=e.pstyle("taxi-direction").value;var m=y;var b=e.pstyle("taxi-turn");var x=b.units==="%";var w=b.pfValue;var E=w<0;var T=e.pstyle("taxi-turn-min-distance").pfValue;var k=g?(f+d)/2:0;var C=g?(c+h)/2:0;var P=v.x2-v.x1;var S=v.y2-v.y1;var D=function e(t,r){if(t>0){return Math.max(t-r,0)}else{return Math.min(t+r,0)}};var B=D(P,k);var A=D(S,C);var _=false;if(m===u){y=Math.abs(B)>Math.abs(A)?n:a}else if(m===l||m===s){y=a;_=true}else if(m===i||m===o){y=n;_=true}var M=y===a;var I=M?A:B;var R=M?S:P;var N=Lr(R);var L=false;if(!(_&&(x||E))&&(m===s&&R<0||m===l&&R>0||m===i&&R>0||m===o&&R<0)){N*=-1;I=N*Math.abs(I);L=true}var O;if(x){var z=w<0?1+w:w;O=z*I}else{var F=w<0?I:0;O=F+w*N}var V=function e(t){return Math.abs(t)<T||Math.abs(t)>=Math.abs(I)};var j=V(O);var X=V(Math.abs(I)-Math.abs(O));var Y=j||X;if(Y&&!L){if(M){var q=Math.abs(R)<=c/2;var W=Math.abs(P)<=d/2;if(q){var U=(v.x1+v.x2)/2;var G=v.y1,H=v.y2;r.segpts=[U,G,U,H]}else if(W){var K=(v.y1+v.y2)/2;var Z=v.x1,$=v.x2;r.segpts=[Z,K,$,K]}else{r.segpts=[v.x1,v.y2]}}else{var Q=Math.abs(R)<=f/2;var J=Math.abs(S)<=h/2;if(Q){var ee=(v.y1+v.y2)/2;var te=v.x1,re=v.x2;r.segpts=[te,ee,re,ee]}else if(J){var ae=(v.x1+v.x2)/2;var ne=v.y1,ie=v.y2;r.segpts=[ae,ne,ae,ie]}else{r.segpts=[v.x2,v.y1]}}}else{if(M){var oe=v.y1+O+(g?c/2*N:0);var se=v.x1,le=v.x2;r.segpts=[se,oe,le,oe]}else{var ue=v.x1+O+(g?f/2*N:0);var ve=v.y1,fe=v.y2;r.segpts=[ue,ve,ue,fe]}}if(r.isRound){var ce=e.pstyle("taxi-radius").value;var de=e.pstyle("radius-type").value[0]==="arc-radius";r.radii=new Array(r.segpts.length/2).fill(ce);r.isArcRadius=new Array(r.segpts.length/2).fill(de)}};Nc.tryToCorrectInvalidPoints=function(e,t){var r=e._private.rscratch;if(r.edgeType==="bezier"){var a=t.srcPos,n=t.tgtPos,i=t.srcW,o=t.srcH,s=t.tgtW,l=t.tgtH,u=t.srcShape,v=t.tgtShape,f=t.srcCornerRadius,c=t.tgtCornerRadius,d=t.srcRs,h=t.tgtRs;var p=!I(r.startX)||!I(r.startY);var g=!I(r.arrowStartX)||!I(r.arrowStartY);var y=!I(r.endX)||!I(r.endY);var m=!I(r.arrowEndX)||!I(r.arrowEndY);var b=3;var x=this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth;var w=b*x;var E=Or({x:r.ctrlpts[0],y:r.ctrlpts[1]},{x:r.startX,y:r.startY});var T=E<w;var k=Or({x:r.ctrlpts[0],y:r.ctrlpts[1]},{x:r.endX,y:r.endY});var C=k<w;var P=false;if(p||g||T){P=true;var S={x:r.ctrlpts[0]-a.x,y:r.ctrlpts[1]-a.y};var D=Math.sqrt(S.x*S.x+S.y*S.y);var B={x:S.x/D,y:S.y/D};var A=Math.max(i,o);var _={x:r.ctrlpts[0]+B.x*2*A,y:r.ctrlpts[1]+B.y*2*A};var M=u.intersectLine(a.x,a.y,i,o,_.x,_.y,0,f,d);if(T){r.ctrlpts[0]=r.ctrlpts[0]+B.x*(w-E);r.ctrlpts[1]=r.ctrlpts[1]+B.y*(w-E)}else{r.ctrlpts[0]=M[0]+B.x*w;r.ctrlpts[1]=M[1]+B.y*w}}if(y||m||C){P=true;var R={x:r.ctrlpts[0]-n.x,y:r.ctrlpts[1]-n.y};var N=Math.sqrt(R.x*R.x+R.y*R.y);var L={x:R.x/N,y:R.y/N};var O=Math.max(i,o);var z={x:r.ctrlpts[0]+L.x*2*O,y:r.ctrlpts[1]+L.y*2*O};var F=v.intersectLine(n.x,n.y,s,l,z.x,z.y,0,c,h);if(C){r.ctrlpts[0]=r.ctrlpts[0]+L.x*(w-k);r.ctrlpts[1]=r.ctrlpts[1]+L.y*(w-k)}else{r.ctrlpts[0]=F[0]+L.x*w;r.ctrlpts[1]=F[1]+L.y*w}}if(P){this.findEndpoints(e)}}};Nc.storeAllpts=function(e){var t=e._private.rscratch;if(t.edgeType==="multibezier"||t.edgeType==="bezier"||t.edgeType==="self"||t.edgeType==="compound"){t.allpts=[];t.allpts.push(t.startX,t.startY);for(var r=0;r+1<t.ctrlpts.length;r+=2){t.allpts.push(t.ctrlpts[r],t.ctrlpts[r+1]);if(r+3<t.ctrlpts.length){t.allpts.push((t.ctrlpts[r]+t.ctrlpts[r+2])/2,(t.ctrlpts[r+1]+t.ctrlpts[r+3])/2)}}t.allpts.push(t.endX,t.endY);var a,n;if(t.ctrlpts.length/2%2===0){a=t.allpts.length/2-1;t.midX=t.allpts[a];t.midY=t.allpts[a+1]}else{a=t.allpts.length/2-3;n=.5;t.midX=Vr(t.allpts[a],t.allpts[a+2],t.allpts[a+4],n);t.midY=Vr(t.allpts[a+1],t.allpts[a+3],t.allpts[a+5],n)}}else if(t.edgeType==="straight"){t.allpts=[t.startX,t.startY,t.endX,t.endY];t.midX=(t.startX+t.endX+t.arrowStartX+t.arrowEndX)/4;t.midY=(t.startY+t.endY+t.arrowStartY+t.arrowEndY)/4}else if(t.edgeType==="segments"){t.allpts=[];t.allpts.push(t.startX,t.startY);t.allpts.push.apply(t.allpts,t.segpts);t.allpts.push(t.endX,t.endY);if(t.isRound){t.roundCorners=[];for(var i=2;i+3<t.allpts.length;i+=2){var o=t.radii[i/2-1];var s=t.isArcRadius[i/2-1];t.roundCorners.push(Rc({x:t.allpts[i-2],y:t.allpts[i-1]},{x:t.allpts[i],y:t.allpts[i+1],radius:o},{x:t.allpts[i+2],y:t.allpts[i+3]},o,s))}}if(t.segpts.length%4===0){var l=t.segpts.length/2;var u=l-2;t.midX=(t.segpts[u]+t.segpts[l])/2;t.midY=(t.segpts[u+1]+t.segpts[l+1])/2}else{var v=t.segpts.length/2-1;if(!t.isRound){t.midX=t.segpts[v];t.midY=t.segpts[v+1]}else{var f={x:t.segpts[v],y:t.segpts[v+1]};var c=t.roundCorners[v/2];if(c.radius===0){var d={x:t.segpts[v+2],y:t.segpts[v+3]};t.midX=f.x;t.midY=f.y;t.midVector=[f.y-d.y,d.x-f.x]}else{var h=[f.x-c.cx,f.y-c.cy];var p=c.radius/Math.sqrt(Math.pow(h[0],2)+Math.pow(h[1],2));h=h.map((function(e){return e*p}));t.midX=c.cx+h[0];t.midY=c.cy+h[1];t.midVector=h}}}}};Nc.checkForInvalidEdgeWarning=function(e){var t=e[0]._private.rscratch;if(t.nodesOverlap||I(t.startX)&&I(t.startY)&&I(t.endX)&&I(t.endY)){t.loggedErr=false}else{if(!t.loggedErr){t.loggedErr=true;Lt("Edge `"+e.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap.")}}};Nc.findEdgeControlPoints=function(e){var t=this;if(!e||e.length===0){return}var r=this;var a=r.cy;var n=a.hasCompoundNodes();var i=new Zt;var o=function e(t,r){return[].concat(g(t),[r?1:0]).join("-")};var s=[];var l=[];for(var u=0;u<e.length;u++){var v=e[u];var f=v._private;var c=v.pstyle("curve-style").value;if(v.removed()||!v.takesUpSpace()){continue}if(c==="haystack"){l.push(v);continue}var d=c==="unbundled-bezier"||c.endsWith("segments")||c==="straight"||c==="straight-triangle"||c.endsWith("taxi");var h=c==="unbundled-bezier"||c==="bezier";var p=f.source;var y=f.target;var m=p.poolIndex();var b=y.poolIndex();var x=[m,b].sort();var w=o(x,d);var E=i.get(w);if(E==null){E={eles:[]};s.push({pairId:x,edgeIsUnbundled:d});i.set(w,E)}E.eles.push(v);if(d){E.hasUnbundled=true}if(h){E.hasBezier=true}}var T=function e(){var a=s[k],l=a.pairId,u=a.edgeIsUnbundled;var v=o(l,u);var f=i.get(v);var c;if(!f.hasUnbundled){var d=f.eles[0].parallelEdges().filter((function(e){return e.isBundledBezier()}));Wt(f.eles);d.forEach((function(e){return f.eles.push(e)}));f.eles.sort((function(e,t){return e.poolIndex()-t.poolIndex()}))}var h=f.eles[0];var p=h.source();var g=h.target();if(p.poolIndex()>g.poolIndex()){var y=p;p=g;g=y}var m=f.srcPos=p.position();var b=f.tgtPos=g.position();var x=f.srcW=p.outerWidth();var w=f.srcH=p.outerHeight();var E=f.tgtW=g.outerWidth();var T=f.tgtH=g.outerHeight();var C=f.srcShape=r.nodeShapes[t.getNodeShape(p)];var P=f.tgtShape=r.nodeShapes[t.getNodeShape(g)];var S=f.srcCornerRadius=p.pstyle("corner-radius").value==="auto"?"auto":p.pstyle("corner-radius").pfValue;var D=f.tgtCornerRadius=g.pstyle("corner-radius").value==="auto"?"auto":g.pstyle("corner-radius").pfValue;var B=f.tgtRs=g._private.rscratch;var A=f.srcRs=p._private.rscratch;f.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var _=0;_<f.eles.length;_++){var M=f.eles[_];var R=M[0]._private.rscratch;var N=M.pstyle("curve-style").value;var L=N==="unbundled-bezier"||N.endsWith("segments")||N.endsWith("taxi");var O=!p.same(M.source());if(!f.calculatedIntersection&&p!==g&&(f.hasBezier||f.hasUnbundled)){f.calculatedIntersection=true;var z=C.intersectLine(m.x,m.y,x,w,b.x,b.y,0,S,A);var F=f.srcIntn=z;var V=P.intersectLine(b.x,b.y,E,T,m.x,m.y,0,D,B);var j=f.tgtIntn=V;var X=f.intersectionPts={x1:z[0],x2:V[0],y1:z[1],y2:V[1]};var Y=f.posPts={x1:m.x,x2:b.x,y1:m.y,y2:b.y};var q=V[1]-z[1];var W=V[0]-z[0];var U=Math.sqrt(W*W+q*q);var G=f.vector={x:W,y:q};var H=f.vectorNorm={x:G.x/U,y:G.y/U};var K={x:-H.y,y:H.x};f.nodesOverlap=!I(U)||P.checkPoint(z[0],z[1],0,E,T,b.x,b.y,D,B)||C.checkPoint(V[0],V[1],0,x,w,m.x,m.y,S,A);f.vectorNormInverse=K;c={nodesOverlap:f.nodesOverlap,dirCounts:f.dirCounts,calculatedIntersection:true,hasBezier:f.hasBezier,hasUnbundled:f.hasUnbundled,eles:f.eles,srcPos:b,srcRs:B,tgtPos:m,tgtRs:A,srcW:E,srcH:T,tgtW:x,tgtH:w,srcIntn:j,tgtIntn:F,srcShape:P,tgtShape:C,posPts:{x1:Y.x2,y1:Y.y2,x2:Y.x1,y2:Y.y1},intersectionPts:{x1:X.x2,y1:X.y2,x2:X.x1,y2:X.y1},vector:{x:-G.x,y:-G.y},vectorNorm:{x:-H.x,y:-H.y},vectorNormInverse:{x:-K.x,y:-K.y}}}var Z=O?c:f;R.nodesOverlap=Z.nodesOverlap;R.srcIntn=Z.srcIntn;R.tgtIntn=Z.tgtIntn;R.isRound=N.startsWith("round");if(n&&(p.isParent()||p.isChild()||g.isParent()||g.isChild())&&(p.parents().anySame(g)||g.parents().anySame(p)||p.same(g)&&p.isParent())){t.findCompoundLoopPoints(M,Z,_,L)}else if(p===g){t.findLoopPoints(M,Z,_,L)}else if(N.endsWith("segments")){t.findSegmentsPoints(M,Z)}else if(N.endsWith("taxi")){t.findTaxiPoints(M,Z)}else if(N==="straight"||!L&&f.eles.length%2===1&&_===Math.floor(f.eles.length/2)){t.findStraightEdgePoints(M)}else{t.findBezierPoints(M,Z,_,L,O)}t.findEndpoints(M);t.tryToCorrectInvalidPoints(M,Z);t.checkForInvalidEdgeWarning(M);t.storeAllpts(M);t.storeEdgeProjections(M);t.calculateArrowAngles(M);t.recalculateEdgeLabelProjections(M);t.calculateLabelAngles(M)}};for(var k=0;k<s.length;k++){T()}this.findHaystackPoints(l)};function Lc(e){var t=[];if(e==null){return}for(var r=0;r<e.length;r+=2){var a=e[r];var n=e[r+1];t.push({x:a,y:n})}return t}Nc.getSegmentPoints=function(e){var t=e[0]._private.rscratch;this.recalculateRenderedStyle(e);var r=t.edgeType;if(r==="segments"){return Lc(t.segpts)}};Nc.getControlPoints=function(e){var t=e[0]._private.rscratch;this.recalculateRenderedStyle(e);var r=t.edgeType;if(r==="bezier"||r==="multibezier"||r==="self"||r==="compound"){return Lc(t.ctrlpts)}};Nc.getEdgeMidpoint=function(e){var t=e[0]._private.rscratch;this.recalculateRenderedStyle(e);return{x:t.midX,y:t.midY}};var Oc={};Oc.manualEndptToPx=function(e,t){var r=this;var a=e.position();var n=e.outerWidth();var i=e.outerHeight();var o=e._private.rscratch;if(t.value.length===2){var s=[t.pfValue[0],t.pfValue[1]];if(t.units[0]==="%"){s[0]=s[0]*n}if(t.units[1]==="%"){s[1]=s[1]*i}s[0]+=a.x;s[1]+=a.y;return s}else{var l=t.pfValue[0];l=-Math.PI/2+l;var u=2*Math.max(n,i);var v=[a.x+Math.cos(l)*u,a.y+Math.sin(l)*u];return r.nodeShapes[this.getNodeShape(e)].intersectLine(a.x,a.y,n,i,v[0],v[1],0,e.pstyle("corner-radius").value==="auto"?"auto":e.pstyle("corner-radius").pfValue,o)}};Oc.findEndpoints=function(e){var t=this;var r;var a=e.source()[0];var n=e.target()[0];var i=a.position();var o=n.position();var s=e.pstyle("target-arrow-shape").value;var l=e.pstyle("source-arrow-shape").value;var u=e.pstyle("target-distance-from-node").pfValue;var v=e.pstyle("source-distance-from-node").pfValue;var f=a._private.rscratch;var c=n._private.rscratch;var d=e.pstyle("curve-style").value;var h=e._private.rscratch;var p=h.edgeType;var g=d==="taxi";var y=p==="self"||p==="compound";var m=p==="bezier"||p==="multibezier"||y;var b=p!=="bezier";var x=p==="straight"||p==="segments";var w=p==="segments";var E=m||b||x;var T=y||g;var k=e.pstyle("source-endpoint");var C=T?"outside-to-node":k.value;var P=a.pstyle("corner-radius").value==="auto"?"auto":a.pstyle("corner-radius").pfValue;var S=e.pstyle("target-endpoint");var D=T?"outside-to-node":S.value;var B=n.pstyle("corner-radius").value==="auto"?"auto":n.pstyle("corner-radius").pfValue;h.srcManEndpt=k;h.tgtManEndpt=S;var A;var _;var M;var R;if(m){var N=[h.ctrlpts[0],h.ctrlpts[1]];var L=b?[h.ctrlpts[h.ctrlpts.length-2],h.ctrlpts[h.ctrlpts.length-1]]:N;A=L;_=N}else if(x){var O=!w?[o.x,o.y]:h.segpts.slice(0,2);var z=!w?[i.x,i.y]:h.segpts.slice(h.segpts.length-2);A=z;_=O}if(D==="inside-to-node"){r=[o.x,o.y]}else if(S.units){r=this.manualEndptToPx(n,S)}else if(D==="outside-to-line"){r=h.tgtIntn}else{if(D==="outside-to-node"||D==="outside-to-node-or-label"){M=A}else if(D==="outside-to-line"||D==="outside-to-line-or-label"){M=[i.x,i.y]}r=t.nodeShapes[this.getNodeShape(n)].intersectLine(o.x,o.y,n.outerWidth(),n.outerHeight(),M[0],M[1],0,B,c);if(D==="outside-to-node-or-label"||D==="outside-to-line-or-label"){var F=n._private.rscratch;var V=F.labelWidth;var j=F.labelHeight;var X=F.labelX;var Y=F.labelY;var q=V/2;var W=j/2;var U=n.pstyle("text-valign").value;if(U==="top"){Y-=W}else if(U==="bottom"){Y+=W}var G=n.pstyle("text-halign").value;if(G==="left"){X-=q}else if(G==="right"){X+=q}var H=xa(M[0],M[1],[X-q,Y-W,X+q,Y-W,X+q,Y+W,X-q,Y+W],o.x,o.y);if(H.length>0){var K=i;var Z=zr(K,Dr(r));var $=zr(K,Dr(H));var Q=Z;if($<Z){r=H;Q=$}if(H.length>2){var J=zr(K,{x:H[2],y:H[3]});if(J<Q){r=[H[2],H[3]]}}}}}var ee=Ea(r,A,t.arrowShapes[s].spacing(e)+u);var te=Ea(r,A,t.arrowShapes[s].gap(e)+u);h.endX=te[0];h.endY=te[1];h.arrowEndX=ee[0];h.arrowEndY=ee[1];if(C==="inside-to-node"){r=[i.x,i.y]}else if(k.units){r=this.manualEndptToPx(a,k)}else if(C==="outside-to-line"){r=h.srcIntn}else{if(C==="outside-to-node"||C==="outside-to-node-or-label"){R=_}else if(C==="outside-to-line"||C==="outside-to-line-or-label"){R=[o.x,o.y]}r=t.nodeShapes[this.getNodeShape(a)].intersectLine(i.x,i.y,a.outerWidth(),a.outerHeight(),R[0],R[1],0,P,f);if(C==="outside-to-node-or-label"||C==="outside-to-line-or-label"){var re=a._private.rscratch;var ae=re.labelWidth;var ne=re.labelHeight;var ie=re.labelX;var oe=re.labelY;var se=ae/2;var le=ne/2;var ue=a.pstyle("text-valign").value;if(ue==="top"){oe-=le}else if(ue==="bottom"){oe+=le}var ve=a.pstyle("text-halign").value;if(ve==="left"){ie-=se}else if(ve==="right"){ie+=se}var fe=xa(R[0],R[1],[ie-se,oe-le,ie+se,oe-le,ie+se,oe+le,ie-se,oe+le],i.x,i.y);if(fe.length>0){var ce=o;var de=zr(ce,Dr(r));var he=zr(ce,Dr(fe));var pe=de;if(he<de){r=[fe[0],fe[1]];pe=he}if(fe.length>2){var ge=zr(ce,{x:fe[2],y:fe[3]});if(ge<pe){r=[fe[2],fe[3]]}}}}}var ye=Ea(r,_,t.arrowShapes[l].spacing(e)+v);var me=Ea(r,_,t.arrowShapes[l].gap(e)+v);h.startX=me[0];h.startY=me[1];h.arrowStartX=ye[0];h.arrowStartY=ye[1];if(E){if(!I(h.startX)||!I(h.startY)||!I(h.endX)||!I(h.endY)){h.badLine=true}else{h.badLine=false}}};Oc.getSourceEndpoint=function(e){var t=e[0]._private.rscratch;this.recalculateRenderedStyle(e);switch(t.edgeType){case"haystack":return{x:t.haystackPts[0],y:t.haystackPts[1]};default:return{x:t.arrowStartX,y:t.arrowStartY}}};Oc.getTargetEndpoint=function(e){var t=e[0]._private.rscratch;this.recalculateRenderedStyle(e);switch(t.edgeType){case"haystack":return{x:t.haystackPts[2],y:t.haystackPts[3]};default:return{x:t.arrowEndX,y:t.arrowEndY}}};var zc={};function Fc(e,t,r){var a=function e(t,r,a,n){return Vr(t,r,a,n)};var n=t._private;var i=n.rstyle.bezierPts;for(var o=0;o<e.bezierProjPcts.length;o++){var s=e.bezierProjPcts[o];i.push({x:a(r[0],r[2],r[4],s),y:a(r[1],r[3],r[5],s)})}}zc.storeEdgeProjections=function(e){var t=e._private;var r=t.rscratch;var a=r.edgeType;t.rstyle.bezierPts=null;t.rstyle.linePts=null;t.rstyle.haystackPts=null;if(a==="multibezier"||a==="bezier"||a==="self"||a==="compound"){t.rstyle.bezierPts=[];for(var n=0;n+5<r.allpts.length;n+=4){Fc(this,e,r.allpts.slice(n,n+6))}}else if(a==="segments"){var i=t.rstyle.linePts=[];for(var n=0;n+1<r.allpts.length;n+=2){i.push({x:r.allpts[n],y:r.allpts[n+1]})}}else if(a==="haystack"){var o=r.haystackPts;t.rstyle.haystackPts=[{x:o[0],y:o[1]},{x:o[2],y:o[3]}]}t.rstyle.arrowWidth=this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth};zc.recalculateEdgeProjections=function(e){this.findEdgeControlPoints(e)};var Vc={};Vc.recalculateNodeLabelProjection=function(e){var t=e.pstyle("label").strValue;if(X(t)){return}var r,a;var n=e._private;var i=e.width();var o=e.height();var s=e.padding();var l=e.position();var u=e.pstyle("text-halign").strValue;var v=e.pstyle("text-valign").strValue;var f=n.rscratch;var c=n.rstyle;switch(u){case"left":r=l.x-i/2-s;break;case"right":r=l.x+i/2+s;break;default:r=l.x}switch(v){case"top":a=l.y-o/2-s;break;case"bottom":a=l.y+o/2+s;break;default:a=l.y}f.labelX=r;f.labelY=a;c.labelX=r;c.labelY=a;this.calculateLabelAngles(e);this.applyLabelDimensions(e)};var jc=function e(t,r){var a=Math.atan(r/t);if(t===0&&a<0){a=a*-1}return a};var Xc=function e(t,r){var a=r.x-t.x;var n=r.y-t.y;return jc(a,n)};var Yc=function e(t,r,a,n){var i=Yr(0,n-.001,1);var o=Yr(0,n+.001,1);var s=jr(t,r,a,i);var l=jr(t,r,a,o);return Xc(s,l)};Vc.recalculateEdgeLabelProjections=function(e){var t;var r=e._private;var a=r.rscratch;var n=this;var i={mid:e.pstyle("label").strValue,source:e.pstyle("source-label").strValue,target:e.pstyle("target-label").strValue};if(i.mid||i.source||i.target);else{return}t={x:a.midX,y:a.midY};var o=function e(t,a,n){Ht(r.rscratch,t,a,n);Ht(r.rstyle,t,a,n)};o("labelX",null,t.x);o("labelY",null,t.y);var s=jc(a.midDispX,a.midDispY);o("labelAutoAngle",null,s);var l=function e(){if(l.cache){return l.cache}var t=[];for(var i=0;i+5<a.allpts.length;i+=4){var o={x:a.allpts[i],y:a.allpts[i+1]};var s={x:a.allpts[i+2],y:a.allpts[i+3]};var u={x:a.allpts[i+4],y:a.allpts[i+5]};t.push({p0:o,p1:s,p2:u,startDist:0,length:0,segments:[]})}var v=r.rstyle.bezierPts;var f=n.bezierProjPcts.length;function c(e,t,r,a,n){var i=Or(t,r);var o=e.segments[e.segments.length-1];var s={p0:t,p1:r,t0:a,t1:n,startDist:o?o.startDist+o.length:0,length:i};e.segments.push(s);e.length+=i}for(var d=0;d<t.length;d++){var h=t[d];var p=t[d-1];if(p){h.startDist=p.startDist+p.length}c(h,h.p0,v[d*f],0,n.bezierProjPcts[0]);for(var g=0;g<f-1;g++){c(h,v[d*f+g],v[d*f+g+1],n.bezierProjPcts[g],n.bezierProjPcts[g+1])}c(h,v[d*f+f-1],h.p2,n.bezierProjPcts[f-1],1)}return l.cache=t};var u=function r(n){var s;var u=n==="source";if(!i[n]){return}var v=e.pstyle(n+"-text-offset").pfValue;switch(a.edgeType){case"self":case"compound":case"bezier":case"multibezier":{var f=l();var c;var d=0;var h=0;for(var p=0;p<f.length;p++){var g=f[u?p:f.length-1-p];for(var y=0;y<g.segments.length;y++){var m=g.segments[u?y:g.segments.length-1-y];var b=p===f.length-1&&y===g.segments.length-1;d=h;h+=m.length;if(h>=v||b){c={cp:g,segment:m};break}}if(c){break}}var x=c.cp;var w=c.segment;var E=(v-d)/w.length;var T=w.t1-w.t0;var k=u?w.t0+T*E:w.t1-T*E;k=Yr(0,k,1);t=jr(x.p0,x.p1,x.p2,k);s=Yc(x.p0,x.p1,x.p2,k);break}case"straight":case"segments":case"haystack":{var C=0,P,S;var D,B;var A=a.allpts.length;for(var _=0;_+3<A;_+=2){if(u){D={x:a.allpts[_],y:a.allpts[_+1]};B={x:a.allpts[_+2],y:a.allpts[_+3]}}else{D={x:a.allpts[A-2-_],y:a.allpts[A-1-_]};B={x:a.allpts[A-4-_],y:a.allpts[A-3-_]}}P=Or(D,B);S=C;C+=P;if(C>=v){break}}var M=v-S;var I=M/P;I=Yr(0,I,1);t=Xr(D,B,I);s=Xc(D,B);break}}o("labelX",n,t.x);o("labelY",n,t.y);o("labelAutoAngle",n,s)};u("source");u("target");this.applyLabelDimensions(e)};Vc.applyLabelDimensions=function(e){this.applyPrefixedLabelDimensions(e);if(e.isEdge()){this.applyPrefixedLabelDimensions(e,"source");this.applyPrefixedLabelDimensions(e,"target")}};Vc.applyPrefixedLabelDimensions=function(e,t){var r=e._private;var a=this.getLabelText(e,t);var n=this.calculateLabelDimensions(e,a);var i=e.pstyle("line-height").pfValue;var o=e.pstyle("text-wrap").strValue;var s=Gt(r.rscratch,"labelWrapCachedLines",t)||[];var l=o!=="wrap"?1:Math.max(s.length,1);var u=n.height/l;var v=u*i;var f=n.width;var c=n.height+(l-1)*(i-1)*u;Ht(r.rstyle,"labelWidth",t,f);Ht(r.rscratch,"labelWidth",t,f);Ht(r.rstyle,"labelHeight",t,c);Ht(r.rscratch,"labelHeight",t,c);Ht(r.rscratch,"labelLineHeight",t,v)};Vc.getLabelText=function(e,t){var r=e._private;var a=t?t+"-":"";var n=e.pstyle(a+"label").strValue;var i=e.pstyle("text-transform").value;var o=function e(a,n){if(n){Ht(r.rscratch,a,t,n);return n}else{return Gt(r.rscratch,a,t)}};if(!n){return""}if(i=="none");else if(i=="uppercase"){n=n.toUpperCase()}else if(i=="lowercase"){n=n.toLowerCase()}var s=e.pstyle("text-wrap").value;if(s==="wrap"){var l=o("labelKey");if(l!=null&&o("labelWrapKey")===l){return o("labelWrapCachedText")}var v="​";var f=n.split("\n");var c=e.pstyle("text-max-width").pfValue;var d=e.pstyle("text-overflow-wrap").value;var h=d==="anywhere";var p=[];var g=/[\s\u200b]+|$/g;for(var y=0;y<f.length;y++){var m=f[y];var b=this.calculateLabelDimensions(e,m);var x=b.width;if(h){var w=m.split("").join(v);m=w}if(x>c){var E=m.matchAll(g);var T="";var k=0;var C=u(E),P;try{for(C.s();!(P=C.n()).done;){var S=P.value;var D=S[0];var B=m.substring(k,S.index);k=S.index+D.length;var A=T.length===0?B:T+B+D;var _=this.calculateLabelDimensions(e,A);var M=_.width;if(M<=c){T+=B+D}else{if(T){p.push(T)}T=B+D}}}catch(F){C.e(F)}finally{C.f()}if(!T.match(/^[\s\u200b]+$/)){p.push(T)}}else{p.push(m)}}o("labelWrapCachedLines",p);n=o("labelWrapCachedText",p.join("\n"));o("labelWrapKey",l)}else if(s==="ellipsis"){var I=e.pstyle("text-max-width").pfValue;var R="";var N="…";var L=false;if(this.calculateLabelDimensions(e,n).width<I){return n}for(var O=0;O<n.length;O++){var z=this.calculateLabelDimensions(e,R+n[O]+N).width;if(z>I){break}R+=n[O];if(O===n.length-1){L=true}}if(!L){R+=N}return R}return n};Vc.getLabelJustification=function(e){var t=e.pstyle("text-justification").strValue;var r=e.pstyle("text-halign").strValue;if(t==="auto"){if(e.isNode()){switch(r){case"left":return"right";case"right":return"left";default:return"center"}}else{return"center"}}else{return t}};Vc.calculateLabelDimensions=function(e,t){var r=this;var a=r.cy.window();var n=a.document;var i=Tt(t,e._private.labelDimsKey);var o=r.labelDimCache||(r.labelDimCache=[]);var s=o[i];if(s!=null){return s}var l=0;var u=e.pstyle("font-style").strValue;var v=e.pstyle("font-size").pfValue;var f=e.pstyle("font-family").strValue;var c=e.pstyle("font-weight").strValue;var d=this.labelCalcCanvas;var h=this.labelCalcCanvasContext;if(!d){d=this.labelCalcCanvas=n.createElement("canvas");h=this.labelCalcCanvasContext=d.getContext("2d");var p=d.style;p.position="absolute";p.left="-9999px";p.top="-9999px";p.zIndex="-1";p.visibility="hidden";p.pointerEvents="none"}h.font="".concat(u," ").concat(c," ").concat(v,"px ").concat(f);var g=0;var y=0;var m=t.split("\n");for(var b=0;b<m.length;b++){var x=m[b];var w=h.measureText(x);var E=Math.ceil(w.width);var T=v;g=Math.max(E,g);y+=T}g+=l;y+=l;return o[i]={width:g,height:y}};Vc.calculateLabelAngle=function(e,t){var r=e._private;var a=r.rscratch;var n=e.isEdge();var i=t?t+"-":"";var o=e.pstyle(i+"text-rotation");var s=o.strValue;if(s==="none"){return 0}else if(n&&s==="autorotate"){return a.labelAutoAngle}else if(s==="autorotate"){return 0}else{return o.pfValue}};Vc.calculateLabelAngles=function(e){var t=this;var r=e.isEdge();var a=e._private;var n=a.rscratch;n.labelAngle=t.calculateLabelAngle(e);if(r){n.sourceLabelAngle=t.calculateLabelAngle(e,"source");n.targetLabelAngle=t.calculateLabelAngle(e,"target")}};var qc={};var Wc=28;var Uc=false;qc.getNodeShape=function(e){var t=this;var r=e.pstyle("shape").value;if(r==="cutrectangle"&&(e.width()<Wc||e.height()<Wc)){if(!Uc){Lt("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead");Uc=true}return"rectangle"}if(e.isParent()){if(r==="rectangle"||r==="roundrectangle"||r==="round-rectangle"||r==="cutrectangle"||r==="cut-rectangle"||r==="barrel"){return r}else{return"rectangle"}}if(r==="polygon"){var a=e.pstyle("shape-polygon-points").value;return t.nodeShapes.makePolygon(a).name}return r};var Gc={};Gc.registerCalculationListeners=function(){var e=this.cy;var t=e.collection();var r=this;var a=function e(r){var a=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;t.merge(r);if(a){for(var n=0;n<r.length;n++){var i=r[n];var o=i._private;var s=o.rstyle;s.clean=false;s.cleanConnected=false}}};r.binder(e).on("bounds.* dirty.*",(function e(t){var r=t.target;a(r)})).on("style.* background.*",(function e(t){var r=t.target;a(r,false)}));var n=function n(i){if(i){var o=r.onUpdateEleCalcsFns;t.cleanStyle();for(var s=0;s<t.length;s++){var l=t[s];var u=l._private.rstyle;if(l.isNode()&&!u.cleanConnected){a(l.connectedEdges());u.cleanConnected=true}}if(o){for(var v=0;v<o.length;v++){var f=o[v];f(i,t)}}r.recalculateRenderedStyle(t);t=e.collection()}};r.flushRenderedStyleQueue=function(){n(true)};r.beforeRender(n,r.beforeRenderPriorities.eleCalcs)};Gc.onUpdateEleCalcs=function(e){var t=this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[];t.push(e)};Gc.recalculateRenderedStyle=function(e,t){var r=function e(t){return t._private.rstyle.cleanConnected};if(e.length===0){return}var a=[];var n=[];if(this.destroyed){return}if(t===undefined){t=true}for(var i=0;i<e.length;i++){var o=e[i];var s=o._private;var l=s.rstyle;if(o.isEdge()&&(!r(o.source())||!r(o.target()))){l.clean=false}if(o.isEdge()&&o.isBundledBezier()){if(o.parallelEdges().some((function(e){return!e._private.rstyle.clean&&e.isBundledBezier()}))){l.clean=false}}if(t&&l.clean||o.removed()){continue}if(o.pstyle("display").value==="none"){continue}if(s.group==="nodes"){n.push(o)}else{a.push(o)}l.clean=true}for(var u=0;u<n.length;u++){var v=n[u];var f=v._private;var c=f.rstyle;var d=v.position();this.recalculateNodeLabelProjection(v);c.nodeX=d.x;c.nodeY=d.y;c.nodeW=v.pstyle("width").pfValue;c.nodeH=v.pstyle("height").pfValue}this.recalculateEdgeProjections(a);for(var h=0;h<a.length;h++){var p=a[h];var g=p._private;var y=g.rstyle;var m=g.rscratch;y.srcX=m.arrowStartX;y.srcY=m.arrowStartY;y.tgtX=m.arrowEndX;y.tgtY=m.arrowEndY;y.midX=m.midX;y.midY=m.midY;y.labelAngle=m.labelAngle;y.sourceLabelAngle=m.sourceLabelAngle;y.targetLabelAngle=m.targetLabelAngle}};var Hc={};Hc.updateCachedGrabbedEles=function(){var e=this.cachedZSortedEles;if(!e){return}e.drag=[];e.nondrag=[];var t=[];for(var r=0;r<e.length;r++){var a=e[r];var n=a._private.rscratch;if(a.grabbed()&&!a.isParent()){t.push(a)}else if(n.inDragLayer){e.drag.push(a)}else{e.nondrag.push(a)}}for(var r=0;r<t.length;r++){var a=t[r];e.drag.push(a)}};Hc.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null};Hc.getCachedZSortedEles=function(e){if(e||!this.cachedZSortedEles){var t=this.cy.mutableElements().toArray();t.sort(av);t.interactive=t.filter((function(e){return e.interactive()}));this.cachedZSortedEles=t;this.updateCachedGrabbedEles()}else{t=this.cachedZSortedEles}return t};var Kc={};[uc,vc,Nc,Oc,zc,Vc,qc,Gc,Hc].forEach((function(e){se(Kc,e)}));var Zc={};Zc.getCachedImage=function(e,t,r){var a=this;var n=a.imageCache=a.imageCache||{};var i=n[e];if(i){if(!i.image.complete){i.image.addEventListener("load",r)}return i.image}else{i=n[e]=n[e]||{};var o=i.image=new Image;o.addEventListener("load",r);o.addEventListener("error",(function(){o.error=true}));var s="data:";var l=e.substring(0,s.length).toLowerCase()===s;if(!l){t=t==="null"?null:t;o.crossOrigin=t}o.src=e;return o}};var $c={};$c.registerBinding=function(e,t,r,a){var n=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(e)){var i=[];for(var o=0;o<e.length;o++){var s=e[o];if(s!==undefined){var l=this.binder(s);i.push(l.on.apply(l,n))}}return i}var l=this.binder(e);return l.on.apply(l,n)};$c.binder=function(e){var t=this;var r=t.cy.window();var a=e===r||e===r.document||e===r.document.body||Y(e);if(t.supportsPassiveEvents==null){var n=false;try{var i=Object.defineProperty({},"passive",{get:function e(){n=true;return true}});r.addEventListener("test",null,i)}catch(s){}t.supportsPassiveEvents=n}var o=function r(n,i,o){var s=Array.prototype.slice.call(arguments);if(a&&t.supportsPassiveEvents){s[2]={capture:o!=null?o:false,passive:false,once:false}}t.bindings.push({target:e,args:s});(e.addEventListener||e.on).apply(e,s);return this};return{on:o,addEventListener:o,addListener:o,bind:o}};$c.nodeIsDraggable=function(e){return e&&e.isNode()&&!e.locked()&&e.grabbable()};$c.nodeIsGrabbable=function(e){return this.nodeIsDraggable(e)&&e.interactive()};$c.load=function(){var e=this;var t=e.cy.window();var r=function e(t){return t.selected()};var a=function e(t){var r=t.getRootNode();if(r&&r.nodeType===11&&r.host!==undefined){return r}};var n=function t(r,a,n,i){if(r==null){r=e.cy}for(var o=0;o<a.length;o++){var s=a[o];r.emit({originalEvent:n,type:s,position:i})}};var i=function e(t){return t.shiftKey||t.metaKey||t.ctrlKey};var o=function t(r,a){var n=true;if(e.cy.hasCompoundNodes()&&r&&r.pannable()){for(var i=0;a&&i<a.length;i++){var r=a[i];if(r.isNode()&&r.isParent()&&!r.pannable()){n=false;break}}}else{n=true}return n};var s=function e(t){t[0]._private.grabbed=true};var l=function e(t){t[0]._private.grabbed=false};var u=function e(t){t[0]._private.rscratch.inDragLayer=true};var v=function e(t){t[0]._private.rscratch.inDragLayer=false};var f=function e(t){t[0]._private.rscratch.isGrabTarget=true};var c=function e(t){t[0]._private.rscratch.isGrabTarget=false};var d=function e(t,r){var a=r.addToList;var n=a.has(t);if(!n&&t.grabbable()&&!t.locked()){a.merge(t);s(t)}};var h=function e(t,r){if(!t.cy().hasCompoundNodes()){return}if(r.inDragLayer==null&&r.addToList==null){return}var a=t.descendants();if(r.inDragLayer){a.forEach(u);a.connectedEdges().forEach(u)}if(r.addToList){d(a,r)}};var p=function t(r,a){a=a||{};var n=r.cy().hasCompoundNodes();if(a.inDragLayer){r.forEach(u);r.neighborhood().stdFilter((function(e){return!n||e.isEdge()})).forEach(u)}if(a.addToList){r.forEach((function(e){d(e,a)}))}h(r,a);m(r,{inDragLayer:a.inDragLayer});e.updateCachedGrabbedEles()};var g=p;var y=function t(r){if(!r){return}e.getCachedZSortedEles().forEach((function(e){l(e);v(e);c(e)}));e.updateCachedGrabbedEles()};var m=function e(t,r){if(r.inDragLayer==null&&r.addToList==null){return}if(!t.cy().hasCompoundNodes()){return}var a=t.ancestors().orphans();if(a.same(t)){return}var n=a.descendants().spawnSelf().merge(a).unmerge(t).unmerge(t.descendants());var i=n.connectedEdges();if(r.inDragLayer){i.forEach(u);n.forEach(u)}if(r.addToList){n.forEach((function(e){d(e,r)}))}};var b=function e(){if(document.activeElement!=null&&document.activeElement.blur!=null){document.activeElement.blur()}};var x=typeof MutationObserver!=="undefined";var w=typeof ResizeObserver!=="undefined";if(x){e.removeObserver=new MutationObserver((function(t){for(var r=0;r<t.length;r++){var a=t[r];var n=a.removedNodes;if(n){for(var i=0;i<n.length;i++){var o=n[i];if(o===e.container){e.destroy();break}}}}}));if(e.container.parentNode){e.removeObserver.observe(e.container.parentNode,{childList:true})}}else{e.registerBinding(e.container,"DOMNodeRemoved",(function(t){e.destroy()}))}var E=st((function(){e.cy.resize()}),100);if(x){e.styleObserver=new MutationObserver(E);e.styleObserver.observe(e.container,{attributes:true})}e.registerBinding(t,"resize",E);if(w){e.resizeObserver=new ResizeObserver(E);e.resizeObserver.observe(e.container)}var T=function e(t,r){while(t!=null){r(t);t=t.parentNode}};var k=function t(){e.invalidateContainerClientCoordsCache()};T(e.container,(function(t){e.registerBinding(t,"transitionend",k);e.registerBinding(t,"animationend",k);e.registerBinding(t,"scroll",k)}));e.registerBinding(e.container,"contextmenu",(function(e){e.preventDefault()}));var C=function t(){return e.selection[4]!==0};var P=function t(r){var a=e.findContainerClientCoords();var n=a[0];var i=a[1];var o=a[2];var s=a[3];var l=r.touches?r.touches:[r];var u=false;for(var v=0;v<l.length;v++){var f=l[v];if(n<=f.clientX&&f.clientX<=n+o&&i<=f.clientY&&f.clientY<=i+s){u=true;break}}if(!u){return false}var c=e.container;var d=r.target;var h=d.parentNode;var p=false;while(h){if(h===c){p=true;break}h=h.parentNode}if(!p){return false}return true};e.registerBinding(e.container,"mousedown",(function t(r){if(!P(r)){return}if(e.hoverData.which===1&&r.which!==1){return}r.preventDefault();b();e.hoverData.capture=true;e.hoverData.which=r.which;var a=e.cy;var i=[r.clientX,r.clientY];var o=e.projectIntoViewport(i[0],i[1]);var s=e.selection;var l=e.findNearestElements(o[0],o[1],true,false);var u=l[0];var v=e.dragData.possibleDragElements;e.hoverData.mdownPos=o;e.hoverData.mdownGPos=i;var c=function t(){e.hoverData.tapholdCancelled=false;clearTimeout(e.hoverData.tapholdTimeout);e.hoverData.tapholdTimeout=setTimeout((function(){if(e.hoverData.tapholdCancelled){return}else{var t=e.hoverData.down;if(t){t.emit({originalEvent:r,type:"taphold",position:{x:o[0],y:o[1]}})}else{a.emit({originalEvent:r,type:"taphold",position:{x:o[0],y:o[1]}})}}}),e.tapholdDuration)};if(r.which==3){e.hoverData.cxtStarted=true;var d={originalEvent:r,type:"cxttapstart",position:{x:o[0],y:o[1]}};if(u){u.activate();u.emit(d);e.hoverData.down=u}else{a.emit(d)}e.hoverData.downTime=(new Date).getTime();e.hoverData.cxtDragged=false}else if(r.which==1){if(u){u.activate()}{if(u!=null){if(e.nodeIsGrabbable(u)){var h=function e(t){return{originalEvent:r,type:t,position:{x:o[0],y:o[1]}}};var y=function e(t){t.emit(h("grab"))};f(u);if(!u.selected()){v=e.dragData.possibleDragElements=a.collection();g(u,{addToList:v});u.emit(h("grabon")).emit(h("grab"))}else{v=e.dragData.possibleDragElements=a.collection();var m=a.$((function(t){return t.isNode()&&t.selected()&&e.nodeIsGrabbable(t)}));p(m,{addToList:v});u.emit(h("grabon"));m.forEach(y)}e.redrawHint("eles",true);e.redrawHint("drag",true)}}e.hoverData.down=u;e.hoverData.downs=l;e.hoverData.downTime=(new Date).getTime()}n(u,["mousedown","tapstart","vmousedown"],r,{x:o[0],y:o[1]});if(u==null){s[4]=1;e.data.bgActivePosistion={x:o[0],y:o[1]};e.redrawHint("select",true);e.redraw()}else if(u.pannable()){s[4]=1}c()}s[0]=s[2]=o[0];s[1]=s[3]=o[1]}),false);var S=a(e.container);e.registerBinding([t,S],"mousemove",(function t(r){var a=e.hoverData.capture;if(!a&&!P(r)){return}var s=false;var l=e.cy;var u=l.zoom();var v=[r.clientX,r.clientY];var f=e.projectIntoViewport(v[0],v[1]);var c=e.hoverData.mdownPos;var d=e.hoverData.mdownGPos;var h=e.selection;var g=null;if(!e.hoverData.draggingEles&&!e.hoverData.dragging&&!e.hoverData.selecting){g=e.findNearestElement(f[0],f[1],true,false)}var m=e.hoverData.last;var b=e.hoverData.down;var x=[f[0]-h[2],f[1]-h[3]];var w=e.dragData.possibleDragElements;var E;if(d){var T=v[0]-d[0];var k=T*T;var C=v[1]-d[1];var S=C*C;var D=k+S;e.hoverData.isOverThresholdDrag=E=D>=e.desktopTapThreshold2}var B=i(r);if(E){e.hoverData.tapholdCancelled=true}var A=function t(){var r=e.hoverData.dragDelta=e.hoverData.dragDelta||[];if(r.length===0){r.push(x[0]);r.push(x[1])}else{r[0]+=x[0];r[1]+=x[1]}};s=true;n(g,["mousemove","vmousemove","tapdrag"],r,{x:f[0],y:f[1]});var _=function t(){e.data.bgActivePosistion=undefined;if(!e.hoverData.selecting){l.emit({originalEvent:r,type:"boxstart",position:{x:f[0],y:f[1]}})}h[4]=1;e.hoverData.selecting=true;e.redrawHint("select",true);e.redraw()};if(e.hoverData.which===3){if(E){var M={originalEvent:r,type:"cxtdrag",position:{x:f[0],y:f[1]}};if(b){b.emit(M)}else{l.emit(M)}e.hoverData.cxtDragged=true;if(!e.hoverData.cxtOver||g!==e.hoverData.cxtOver){if(e.hoverData.cxtOver){e.hoverData.cxtOver.emit({originalEvent:r,type:"cxtdragout",position:{x:f[0],y:f[1]}})}e.hoverData.cxtOver=g;if(g){g.emit({originalEvent:r,type:"cxtdragover",position:{x:f[0],y:f[1]}})}}}}else if(e.hoverData.dragging){s=true;if(l.panningEnabled()&&l.userPanningEnabled()){var R;if(e.hoverData.justStartedPan){var N=e.hoverData.mdownPos;R={x:(f[0]-N[0])*u,y:(f[1]-N[1])*u};e.hoverData.justStartedPan=false}else{R={x:x[0]*u,y:x[1]*u}}l.panBy(R);l.emit("dragpan");e.hoverData.dragged=true}f=e.projectIntoViewport(r.clientX,r.clientY)}else if(h[4]==1&&(b==null||b.pannable())){if(E){if(!e.hoverData.dragging&&l.boxSelectionEnabled()&&(B||!l.panningEnabled()||!l.userPanningEnabled())){_()}else if(!e.hoverData.selecting&&l.panningEnabled()&&l.userPanningEnabled()){var L=o(b,e.hoverData.downs);if(L){e.hoverData.dragging=true;e.hoverData.justStartedPan=true;h[4]=0;e.data.bgActivePosistion=Dr(c);e.redrawHint("select",true);e.redraw()}}if(b&&b.pannable()&&b.active()){b.unactivate()}}}else{if(b&&b.pannable()&&b.active()){b.unactivate()}if((!b||!b.grabbed())&&g!=m){if(m){n(m,["mouseout","tapdragout"],r,{x:f[0],y:f[1]})}if(g){n(g,["mouseover","tapdragover"],r,{x:f[0],y:f[1]})}e.hoverData.last=g}if(b){if(E){if(l.boxSelectionEnabled()&&B){if(b&&b.grabbed()){y(w);b.emit("freeon");w.emit("free");if(e.dragData.didDrag){b.emit("dragfreeon");w.emit("dragfree")}}_()}else if(b&&b.grabbed()&&e.nodeIsDraggable(b)){var O=!e.dragData.didDrag;if(O){e.redrawHint("eles",true)}e.dragData.didDrag=true;if(!e.hoverData.draggingEles){p(w,{inDragLayer:true})}var z={x:0,y:0};if(I(x[0])&&I(x[1])){z.x+=x[0];z.y+=x[1];if(O){var F=e.hoverData.dragDelta;if(F&&I(F[0])&&I(F[1])){z.x+=F[0];z.y+=F[1]}}}e.hoverData.draggingEles=true;w.silentShift(z).emit("position drag");e.redrawHint("drag",true);e.redraw()}}else{A()}}s=true}h[2]=f[0];h[3]=f[1];if(s){if(r.stopPropagation)r.stopPropagation();if(r.preventDefault)r.preventDefault();return false}}),false);var D,B,A;e.registerBinding(t,"mouseup",(function t(a){if(e.hoverData.which===1&&a.which!==1&&e.hoverData.capture){return}var o=e.hoverData.capture;if(!o){return}e.hoverData.capture=false;var s=e.cy;var l=e.projectIntoViewport(a.clientX,a.clientY);var u=e.selection;var v=e.findNearestElement(l[0],l[1],true,false);var f=e.dragData.possibleDragElements;var c=e.hoverData.down;var d=i(a);if(e.data.bgActivePosistion){e.redrawHint("select",true);e.redraw()}e.hoverData.tapholdCancelled=true;e.data.bgActivePosistion=undefined;if(c){c.unactivate()}if(e.hoverData.which===3){var h={originalEvent:a,type:"cxttapend",position:{x:l[0],y:l[1]}};if(c){c.emit(h)}else{s.emit(h)}if(!e.hoverData.cxtDragged){var p={originalEvent:a,type:"cxttap",position:{x:l[0],y:l[1]}};if(c){c.emit(p)}else{s.emit(p)}}e.hoverData.cxtDragged=false;e.hoverData.which=null}else if(e.hoverData.which===1){n(v,["mouseup","tapend","vmouseup"],a,{x:l[0],y:l[1]});if(!e.dragData.didDrag&&!e.hoverData.dragged&&!e.hoverData.selecting&&!e.hoverData.isOverThresholdDrag){n(c,["click","tap","vclick"],a,{x:l[0],y:l[1]});B=false;if(a.timeStamp-A<=s.multiClickDebounceTime()){D&&clearTimeout(D);B=true;A=null;n(c,["dblclick","dbltap","vdblclick"],a,{x:l[0],y:l[1]})}else{D=setTimeout((function(){if(B)return;n(c,["oneclick","onetap","voneclick"],a,{x:l[0],y:l[1]})}),s.multiClickDebounceTime());A=a.timeStamp}}if(c==null&&!e.dragData.didDrag&&!e.hoverData.selecting&&!e.hoverData.dragged&&!i(a)){s.$(r).unselect(["tapunselect"]);if(f.length>0){e.redrawHint("eles",true)}e.dragData.possibleDragElements=f=s.collection()}if(v==c&&!e.dragData.didDrag&&!e.hoverData.selecting){if(v!=null&&v._private.selectable){if(e.hoverData.dragging);else if(s.selectionType()==="additive"||d){if(v.selected()){v.unselect(["tapunselect"])}else{v.select(["tapselect"])}}else{if(!d){s.$(r).unmerge(v).unselect(["tapunselect"]);v.select(["tapselect"])}}e.redrawHint("eles",true)}}if(e.hoverData.selecting){var g=s.collection(e.getAllInBox(u[0],u[1],u[2],u[3]));e.redrawHint("select",true);if(g.length>0){e.redrawHint("eles",true)}s.emit({type:"boxend",originalEvent:a,position:{x:l[0],y:l[1]}});var m=function e(t){return t.selectable()&&!t.selected()};if(s.selectionType()==="additive"){g.emit("box").stdFilter(m).select().emit("boxselect")}else{if(!d){s.$(r).unmerge(g).unselect()}g.emit("box").stdFilter(m).select().emit("boxselect")}e.redraw()}if(e.hoverData.dragging){e.hoverData.dragging=false;e.redrawHint("select",true);e.redrawHint("eles",true);e.redraw()}if(!u[4]){e.redrawHint("drag",true);e.redrawHint("eles",true);var b=c&&c.grabbed();y(f);if(b){c.emit("freeon");f.emit("free");if(e.dragData.didDrag){c.emit("dragfreeon");f.emit("dragfree")}}}}u[4]=0;e.hoverData.down=null;e.hoverData.cxtStarted=false;e.hoverData.draggingEles=false;e.hoverData.selecting=false;e.hoverData.isOverThresholdDrag=false;e.dragData.didDrag=false;e.hoverData.dragged=false;e.hoverData.dragDelta=[];e.hoverData.mdownPos=null;e.hoverData.mdownGPos=null;e.hoverData.which=null}),false);var _=function t(r){if(e.scrollingPage){return}var a=e.cy;var n=a.zoom();var i=a.pan();var o=e.projectIntoViewport(r.clientX,r.clientY);var s=[o[0]*n+i.x,o[1]*n+i.y];if(e.hoverData.draggingEles||e.hoverData.dragging||e.hoverData.cxtStarted||C()){r.preventDefault();return}if(a.panningEnabled()&&a.userPanningEnabled()&&a.zoomingEnabled()&&a.userZoomingEnabled()){r.preventDefault();e.data.wheelZooming=true;clearTimeout(e.data.wheelTimeout);e.data.wheelTimeout=setTimeout((function(){e.data.wheelZooming=false;e.redrawHint("eles",true);e.redraw()}),150);var l;if(r.deltaY!=null){l=r.deltaY/-250}else if(r.wheelDeltaY!=null){l=r.wheelDeltaY/1e3}else{l=r.wheelDelta/1e3}l=l*e.wheelSensitivity;var u=r.deltaMode===1;if(u){l*=33}var v=a.zoom()*Math.pow(10,l);if(r.type==="gesturechange"){v=e.gestureStartZoom*r.scale}a.zoom({level:v,renderedPosition:{x:s[0],y:s[1]}});a.emit(r.type==="gesturechange"?"pinchzoom":"scrollzoom")}};e.registerBinding(e.container,"wheel",_,true);e.registerBinding(t,"scroll",(function t(r){e.scrollingPage=true;clearTimeout(e.scrollingPageTimeout);e.scrollingPageTimeout=setTimeout((function(){e.scrollingPage=false}),250)}),true);e.registerBinding(e.container,"gesturestart",(function t(r){e.gestureStartZoom=e.cy.zoom();if(!e.hasTouchStarted){r.preventDefault()}}),true);e.registerBinding(e.container,"gesturechange",(function(t){if(!e.hasTouchStarted){_(t)}}),true);e.registerBinding(e.container,"mouseout",(function t(r){var a=e.projectIntoViewport(r.clientX,r.clientY);e.cy.emit({originalEvent:r,type:"mouseout",position:{x:a[0],y:a[1]}})}),false);e.registerBinding(e.container,"mouseover",(function t(r){var a=e.projectIntoViewport(r.clientX,r.clientY);e.cy.emit({originalEvent:r,type:"mouseover",position:{x:a[0],y:a[1]}})}),false);var M,R,N,L;var O,z;var F,V;var j,X;var Y,q;var W;var U=function e(t,r,a,n){return Math.sqrt((a-t)*(a-t)+(n-r)*(n-r))};var G=function e(t,r,a,n){return(a-t)*(a-t)+(n-r)*(n-r)};var H;e.registerBinding(e.container,"touchstart",H=function t(r){e.hasTouchStarted=true;if(!P(r)){return}b();e.touchData.capture=true;e.data.bgActivePosistion=undefined;var a=e.cy;var i=e.touchData.now;var o=e.touchData.earlier;if(r.touches[0]){var s=e.projectIntoViewport(r.touches[0].clientX,r.touches[0].clientY);i[0]=s[0];i[1]=s[1]}if(r.touches[1]){var s=e.projectIntoViewport(r.touches[1].clientX,r.touches[1].clientY);i[2]=s[0];i[3]=s[1]}if(r.touches[2]){var s=e.projectIntoViewport(r.touches[2].clientX,r.touches[2].clientY);i[4]=s[0];i[5]=s[1]}if(r.touches[1]){e.touchData.singleTouchMoved=true;y(e.dragData.touchDragEles);var l=e.findContainerClientCoords();j=l[0];X=l[1];Y=l[2];q=l[3];M=r.touches[0].clientX-j;R=r.touches[0].clientY-X;N=r.touches[1].clientX-j;L=r.touches[1].clientY-X;W=0<=M&&M<=Y&&0<=N&&N<=Y&&0<=R&&R<=q&&0<=L&&L<=q;var u=a.pan();var v=a.zoom();O=U(M,R,N,L);z=G(M,R,N,L);F=[(M+N)/2,(R+L)/2];V=[(F[0]-u.x)/v,(F[1]-u.y)/v];var c=200;var d=c*c;if(z<d&&!r.touches[2]){var h=e.findNearestElement(i[0],i[1],true,true);var m=e.findNearestElement(i[2],i[3],true,true);if(h&&h.isNode()){h.activate().emit({originalEvent:r,type:"cxttapstart",position:{x:i[0],y:i[1]}});e.touchData.start=h}else if(m&&m.isNode()){m.activate().emit({originalEvent:r,type:"cxttapstart",position:{x:i[0],y:i[1]}});e.touchData.start=m}else{a.emit({originalEvent:r,type:"cxttapstart",position:{x:i[0],y:i[1]}})}if(e.touchData.start){e.touchData.start._private.grabbed=false}e.touchData.cxt=true;e.touchData.cxtDragged=false;e.data.bgActivePosistion=undefined;e.redraw();return}}if(r.touches[2]){if(a.boxSelectionEnabled()){r.preventDefault()}}else if(r.touches[1]);else if(r.touches[0]){var x=e.findNearestElements(i[0],i[1],true,true);var w=x[0];if(w!=null){w.activate();e.touchData.start=w;e.touchData.starts=x;if(e.nodeIsGrabbable(w)){var E=e.dragData.touchDragEles=a.collection();var T=null;e.redrawHint("eles",true);e.redrawHint("drag",true);if(w.selected()){T=a.$((function(t){return t.selected()&&e.nodeIsGrabbable(t)}));p(T,{addToList:E})}else{g(w,{addToList:E})}f(w);var k=function e(t){return{originalEvent:r,type:t,position:{x:i[0],y:i[1]}}};w.emit(k("grabon"));if(T){T.forEach((function(e){e.emit(k("grab"))}))}else{w.emit(k("grab"))}}}n(w,["touchstart","tapstart","vmousedown"],r,{x:i[0],y:i[1]});if(w==null){e.data.bgActivePosistion={x:s[0],y:s[1]};e.redrawHint("select",true);e.redraw()}e.touchData.singleTouchMoved=false;e.touchData.singleTouchStartTime=+new Date;clearTimeout(e.touchData.tapholdTimeout);e.touchData.tapholdTimeout=setTimeout((function(){if(e.touchData.singleTouchMoved===false&&!e.pinching&&!e.touchData.selecting){n(e.touchData.start,["taphold"],r,{x:i[0],y:i[1]})}}),e.tapholdDuration)}if(r.touches.length>=1){var C=e.touchData.startPosition=[null,null,null,null,null,null];for(var S=0;S<i.length;S++){C[S]=o[S]=i[S]}var D=r.touches[0];e.touchData.startGPosition=[D.clientX,D.clientY]}},false);var K;e.registerBinding(t,"touchmove",K=function t(r){var a=e.touchData.capture;if(!a&&!P(r)){return}var i=e.selection;var s=e.cy;var l=e.touchData.now;var u=e.touchData.earlier;var v=s.zoom();if(r.touches[0]){var f=e.projectIntoViewport(r.touches[0].clientX,r.touches[0].clientY);l[0]=f[0];l[1]=f[1]}if(r.touches[1]){var f=e.projectIntoViewport(r.touches[1].clientX,r.touches[1].clientY);l[2]=f[0];l[3]=f[1]}if(r.touches[2]){var f=e.projectIntoViewport(r.touches[2].clientX,r.touches[2].clientY);l[4]=f[0];l[5]=f[1]}var c=e.touchData.startGPosition;var d;if(a&&r.touches[0]&&c){var h=[];for(var g=0;g<l.length;g++){h[g]=l[g]-u[g]}var m=r.touches[0].clientX-c[0];var b=m*m;var x=r.touches[0].clientY-c[1];var w=x*x;var E=b+w;d=E>=e.touchTapThreshold2}if(a&&e.touchData.cxt){r.preventDefault();var T=r.touches[0].clientX-j,k=r.touches[0].clientY-X;var C=r.touches[1].clientX-j,S=r.touches[1].clientY-X;var D=G(T,k,C,S);var B=D/z;var A=150;var _=A*A;var F=1.5;var Y=F*F;if(B>=Y||D>=_){e.touchData.cxt=false;e.data.bgActivePosistion=undefined;e.redrawHint("select",true);var q={originalEvent:r,type:"cxttapend",position:{x:l[0],y:l[1]}};if(e.touchData.start){e.touchData.start.unactivate().emit(q);e.touchData.start=null}else{s.emit(q)}}}if(a&&e.touchData.cxt){var q={originalEvent:r,type:"cxtdrag",position:{x:l[0],y:l[1]}};e.data.bgActivePosistion=undefined;e.redrawHint("select",true);if(e.touchData.start){e.touchData.start.emit(q)}else{s.emit(q)}if(e.touchData.start){e.touchData.start._private.grabbed=false}e.touchData.cxtDragged=true;var H=e.findNearestElement(l[0],l[1],true,true);if(!e.touchData.cxtOver||H!==e.touchData.cxtOver){if(e.touchData.cxtOver){e.touchData.cxtOver.emit({originalEvent:r,type:"cxtdragout",position:{x:l[0],y:l[1]}})}e.touchData.cxtOver=H;if(H){H.emit({originalEvent:r,type:"cxtdragover",position:{x:l[0],y:l[1]}})}}}else if(a&&r.touches[2]&&s.boxSelectionEnabled()){r.preventDefault();e.data.bgActivePosistion=undefined;this.lastThreeTouch=+new Date;if(!e.touchData.selecting){s.emit({originalEvent:r,type:"boxstart",position:{x:l[0],y:l[1]}})}e.touchData.selecting=true;e.touchData.didSelect=true;i[4]=1;if(!i||i.length===0||i[0]===undefined){i[0]=(l[0]+l[2]+l[4])/3;i[1]=(l[1]+l[3]+l[5])/3;i[2]=(l[0]+l[2]+l[4])/3+1;i[3]=(l[1]+l[3]+l[5])/3+1}else{i[2]=(l[0]+l[2]+l[4])/3;i[3]=(l[1]+l[3]+l[5])/3}e.redrawHint("select",true);e.redraw()}else if(a&&r.touches[1]&&!e.touchData.didSelect&&s.zoomingEnabled()&&s.panningEnabled()&&s.userZoomingEnabled()&&s.userPanningEnabled()){r.preventDefault();e.data.bgActivePosistion=undefined;e.redrawHint("select",true);var K=e.dragData.touchDragEles;if(K){e.redrawHint("drag",true);for(var Z=0;Z<K.length;Z++){var $=K[Z]._private;$.grabbed=false;$.rscratch.inDragLayer=false}}var Q=e.touchData.start;var T=r.touches[0].clientX-j,k=r.touches[0].clientY-X;var C=r.touches[1].clientX-j,S=r.touches[1].clientY-X;var J=U(T,k,C,S);var ee=J/O;if(W){var te=T-M;var re=k-R;var ae=C-N;var ne=S-L;var ie=(te+ae)/2;var oe=(re+ne)/2;var se=s.zoom();var le=se*ee;var ue=s.pan();var ve=V[0]*se+ue.x;var fe=V[1]*se+ue.y;var ce={x:-le/se*(ve-ue.x-ie)+ve,y:-le/se*(fe-ue.y-oe)+fe};if(Q&&Q.active()){var K=e.dragData.touchDragEles;y(K);e.redrawHint("drag",true);e.redrawHint("eles",true);Q.unactivate().emit("freeon");K.emit("free");if(e.dragData.didDrag){Q.emit("dragfreeon");K.emit("dragfree")}}s.viewport({zoom:le,pan:ce,cancelOnFailedZoom:true});s.emit("pinchzoom");O=J;M=T;R=k;N=C;L=S;e.pinching=true}if(r.touches[0]){var f=e.projectIntoViewport(r.touches[0].clientX,r.touches[0].clientY);l[0]=f[0];l[1]=f[1]}if(r.touches[1]){var f=e.projectIntoViewport(r.touches[1].clientX,r.touches[1].clientY);l[2]=f[0];l[3]=f[1]}if(r.touches[2]){var f=e.projectIntoViewport(r.touches[2].clientX,r.touches[2].clientY);l[4]=f[0];l[5]=f[1]}}else if(r.touches[0]&&!e.touchData.didSelect){var de=e.touchData.start;var he=e.touchData.last;var H;if(!e.hoverData.draggingEles&&!e.swipePanning){H=e.findNearestElement(l[0],l[1],true,true)}if(a&&de!=null){r.preventDefault()}if(a&&de!=null&&e.nodeIsDraggable(de)){if(d){var K=e.dragData.touchDragEles;var pe=!e.dragData.didDrag;if(pe){p(K,{inDragLayer:true})}e.dragData.didDrag=true;var ge={x:0,y:0};if(I(h[0])&&I(h[1])){ge.x+=h[0];ge.y+=h[1];if(pe){e.redrawHint("eles",true);var ye=e.touchData.dragDelta;if(ye&&I(ye[0])&&I(ye[1])){ge.x+=ye[0];ge.y+=ye[1]}}}e.hoverData.draggingEles=true;K.silentShift(ge).emit("position drag");e.redrawHint("drag",true);if(e.touchData.startPosition[0]==u[0]&&e.touchData.startPosition[1]==u[1]){e.redrawHint("eles",true)}e.redraw()}else{var ye=e.touchData.dragDelta=e.touchData.dragDelta||[];if(ye.length===0){ye.push(h[0]);ye.push(h[1])}else{ye[0]+=h[0];ye[1]+=h[1]}}}{n(de||H,["touchmove","tapdrag","vmousemove"],r,{x:l[0],y:l[1]});if((!de||!de.grabbed())&&H!=he){if(he){he.emit({originalEvent:r,type:"tapdragout",position:{x:l[0],y:l[1]}})}if(H){H.emit({originalEvent:r,type:"tapdragover",position:{x:l[0],y:l[1]}})}}e.touchData.last=H}if(a){for(var Z=0;Z<l.length;Z++){if(l[Z]&&e.touchData.startPosition[Z]&&d){e.touchData.singleTouchMoved=true}}}if(a&&(de==null||de.pannable())&&s.panningEnabled()&&s.userPanningEnabled()){var me=o(de,e.touchData.starts);if(me){r.preventDefault();if(!e.data.bgActivePosistion){e.data.bgActivePosistion=Dr(e.touchData.startPosition)}if(e.swipePanning){s.panBy({x:h[0]*v,y:h[1]*v});s.emit("dragpan")}else if(d){e.swipePanning=true;s.panBy({x:m*v,y:x*v});s.emit("dragpan");if(de){de.unactivate();e.redrawHint("select",true);e.touchData.start=null}}}var f=e.projectIntoViewport(r.touches[0].clientX,r.touches[0].clientY);l[0]=f[0];l[1]=f[1]}}for(var g=0;g<l.length;g++){u[g]=l[g]}if(a&&r.touches.length>0&&!e.hoverData.draggingEles&&!e.swipePanning&&e.data.bgActivePosistion!=null){e.data.bgActivePosistion=undefined;e.redrawHint("select",true);e.redraw()}},false);var Z;e.registerBinding(t,"touchcancel",Z=function t(r){var a=e.touchData.start;e.touchData.capture=false;if(a){a.unactivate()}});var $,Q,J,ee;e.registerBinding(t,"touchend",$=function t(a){var i=e.touchData.start;var o=e.touchData.capture;if(o){if(a.touches.length===0){e.touchData.capture=false}a.preventDefault()}else{return}var s=e.selection;e.swipePanning=false;e.hoverData.draggingEles=false;var l=e.cy;var u=l.zoom();var v=e.touchData.now;var f=e.touchData.earlier;if(a.touches[0]){var c=e.projectIntoViewport(a.touches[0].clientX,a.touches[0].clientY);v[0]=c[0];v[1]=c[1]}if(a.touches[1]){var c=e.projectIntoViewport(a.touches[1].clientX,a.touches[1].clientY);v[2]=c[0];v[3]=c[1]}if(a.touches[2]){var c=e.projectIntoViewport(a.touches[2].clientX,a.touches[2].clientY);v[4]=c[0];v[5]=c[1]}if(i){i.unactivate()}var d;if(e.touchData.cxt){d={originalEvent:a,type:"cxttapend",position:{x:v[0],y:v[1]}};if(i){i.emit(d)}else{l.emit(d)}if(!e.touchData.cxtDragged){var h={originalEvent:a,type:"cxttap",position:{x:v[0],y:v[1]}};if(i){i.emit(h)}else{l.emit(h)}}if(e.touchData.start){e.touchData.start._private.grabbed=false}e.touchData.cxt=false;e.touchData.start=null;e.redraw();return}if(!a.touches[2]&&l.boxSelectionEnabled()&&e.touchData.selecting){e.touchData.selecting=false;var p=l.collection(e.getAllInBox(s[0],s[1],s[2],s[3]));s[0]=undefined;s[1]=undefined;s[2]=undefined;s[3]=undefined;s[4]=0;e.redrawHint("select",true);l.emit({type:"boxend",originalEvent:a,position:{x:v[0],y:v[1]}});var g=function e(t){return t.selectable()&&!t.selected()};p.emit("box").stdFilter(g).select().emit("boxselect");if(p.nonempty()){e.redrawHint("eles",true)}e.redraw()}if(i!=null){i.unactivate()}if(a.touches[2]){e.data.bgActivePosistion=undefined;e.redrawHint("select",true)}else if(a.touches[1]);else if(a.touches[0]);else if(!a.touches[0]){e.data.bgActivePosistion=undefined;e.redrawHint("select",true);var m=e.dragData.touchDragEles;if(i!=null){var b=i._private.grabbed;y(m);e.redrawHint("drag",true);e.redrawHint("eles",true);if(b){i.emit("freeon");m.emit("free");if(e.dragData.didDrag){i.emit("dragfreeon");m.emit("dragfree")}}n(i,["touchend","tapend","vmouseup","tapdragout"],a,{x:v[0],y:v[1]});i.unactivate();e.touchData.start=null}else{var x=e.findNearestElement(v[0],v[1],true,true);n(x,["touchend","tapend","vmouseup","tapdragout"],a,{x:v[0],y:v[1]})}var w=e.touchData.startPosition[0]-v[0];var E=w*w;var T=e.touchData.startPosition[1]-v[1];var k=T*T;var C=E+k;var P=C*u*u;if(!e.touchData.singleTouchMoved){if(!i){l.$(":selected").unselect(["tapunselect"])}n(i,["tap","vclick"],a,{x:v[0],y:v[1]});Q=false;if(a.timeStamp-ee<=l.multiClickDebounceTime()){J&&clearTimeout(J);Q=true;ee=null;n(i,["dbltap","vdblclick"],a,{x:v[0],y:v[1]})}else{J=setTimeout((function(){if(Q)return;n(i,["onetap","voneclick"],a,{x:v[0],y:v[1]})}),l.multiClickDebounceTime());ee=a.timeStamp}}if(i!=null&&!e.dragData.didDrag&&i._private.selectable&&P<e.touchTapThreshold2&&!e.pinching){if(l.selectionType()==="single"){l.$(r).unmerge(i).unselect(["tapunselect"]);i.select(["tapselect"])}else{if(i.selected()){i.unselect(["tapunselect"])}else{i.select(["tapselect"])}}e.redrawHint("eles",true)}e.touchData.singleTouchMoved=true}for(var S=0;S<v.length;S++){f[S]=v[S]}e.dragData.didDrag=false;if(a.touches.length===0){e.touchData.dragDelta=[];e.touchData.startPosition=[null,null,null,null,null,null];e.touchData.startGPosition=null;e.touchData.didSelect=false}if(a.touches.length<2){if(a.touches.length===1){e.touchData.startGPosition=[a.touches[0].clientX,a.touches[0].clientY]}e.pinching=false;e.redrawHint("eles",true);e.redraw()}},false);if(typeof TouchEvent==="undefined"){var te=[];var re=function e(t){return{clientX:t.clientX,clientY:t.clientY,force:1,identifier:t.pointerId,pageX:t.pageX,pageY:t.pageY,radiusX:t.width/2,radiusY:t.height/2,screenX:t.screenX,screenY:t.screenY,target:t.target}};var ae=function e(t){return{event:t,touch:re(t)}};var ne=function e(t){te.push(ae(t))};var ie=function e(t){for(var r=0;r<te.length;r++){var a=te[r];if(a.event.pointerId===t.pointerId){te.splice(r,1);return}}};var oe=function e(t){var r=te.filter((function(e){return e.event.pointerId===t.pointerId}))[0];r.event=t;r.touch=re(t)};var se=function e(t){t.touches=te.map((function(e){return e.touch}))};var le=function e(t){return t.pointerType==="mouse"||t.pointerType===4};e.registerBinding(e.container,"pointerdown",(function(e){if(le(e)){return}e.preventDefault();ne(e);se(e);H(e)}));e.registerBinding(e.container,"pointerup",(function(e){if(le(e)){return}ie(e);se(e);$(e)}));e.registerBinding(e.container,"pointercancel",(function(e){if(le(e)){return}ie(e);se(e);Z(e)}));e.registerBinding(e.container,"pointermove",(function(e){if(le(e)){return}e.preventDefault();oe(e);se(e);K(e)}))}};var Qc={};Qc.generatePolygon=function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,draw:function e(t,r,a,n,i,o){this.renderer.nodeShapeImpl("polygon",t,r,a,n,i,this.points)},intersectLine:function e(t,r,a,n,i,o,s,l){return xa(i,o,this.points,t,r,a/2,n/2,s)},checkPoint:function e(t,r,a,n,i,o,s,l){return fa(t,r,this.points,o,s,n,i,[0,-1],a)}}};Qc.generateEllipse=function(){return this.nodeShapes["ellipse"]={renderer:this,name:"ellipse",draw:function e(t,r,a,n,i,o){this.renderer.nodeShapeImpl(this.name,t,r,a,n,i)},intersectLine:function e(t,r,a,n,i,o,s,l){return pa(i,o,t,r,a/2+s,n/2+s)},checkPoint:function e(t,r,a,n,i,o,s,l){return ga(t,r,n,i,o,s,a)}}};Qc.generateRoundPolygon=function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,getOrCreateCorners:function e(r,a,n,i,o,s,l){if(s[l]!==undefined&&s[l+"-cx"]===r&&s[l+"-cy"]===a){return s[l]}s[l]=new Array(t.length/2);s[l+"-cx"]=r;s[l+"-cy"]=a;var u=n/2;var v=i/2;o=o==="auto"?Sa(n,i):o;var f=new Array(t.length/2);for(var c=0;c<t.length/2;c++){f[c]={x:r+u*t[c*2],y:a+v*t[c*2+1]}}var d,h,p,g,y=f.length;h=f[y-1];for(d=0;d<y;d++){p=f[d%y];g=f[(d+1)%y];s[l][d]=Rc(h,p,g,o);h=p;p=g}return s[l]},draw:function e(t,r,a,n,i,o,s){this.renderer.nodeShapeImpl("round-polygon",t,r,a,n,i,this.points,this.getOrCreateCorners(r,a,n,i,o,s,"drawCorners"))},intersectLine:function e(t,r,a,n,i,o,s,l,u){return wa(i,o,this.points,t,r,a,n,s,this.getOrCreateCorners(t,r,a,n,l,u,"corners"))},checkPoint:function e(t,r,a,n,i,o,s,l,u){return ca(t,r,this.points,o,s,n,i,this.getOrCreateCorners(o,s,n,i,l,u,"corners"))}}};Qc.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes["roundrectangle"]={renderer:this,name:"round-rectangle",points:Ta(4,0),draw:function e(t,r,a,n,i,o){this.renderer.nodeShapeImpl(this.name,t,r,a,n,i,this.points,o)},intersectLine:function e(t,r,a,n,i,o,s,l){return aa(i,o,t,r,a,n,s,l)},checkPoint:function e(t,r,a,n,i,o,s,l){var u=n/2;var v=i/2;l=l==="auto"?Pa(n,i):l;l=Math.min(u,v,l);var f=l*2;if(fa(t,r,this.points,o,s,n,i-f,[0,-1],a)){return true}if(fa(t,r,this.points,o,s,n-f,i,[0,-1],a)){return true}if(ga(t,r,f,f,o-u+l,s-v+l,a)){return true}if(ga(t,r,f,f,o+u-l,s-v+l,a)){return true}if(ga(t,r,f,f,o+u-l,s+v-l,a)){return true}if(ga(t,r,f,f,o-u+l,s+v-l,a)){return true}return false}}};Qc.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes["cutrectangle"]={renderer:this,name:"cut-rectangle",cornerLength:Da(),points:Ta(4,0),draw:function e(t,r,a,n,i,o){this.renderer.nodeShapeImpl(this.name,t,r,a,n,i,null,o)},generateCutTrianglePts:function e(t,r,a,n,i){var o=i==="auto"?this.cornerLength:i;var s=r/2;var l=t/2;var u=a-l;var v=a+l;var f=n-s;var c=n+s;return{topLeft:[u,f+o,u+o,f,u+o,f+o],topRight:[v-o,f,v,f+o,v-o,f+o],bottomRight:[v,c-o,v-o,c,v-o,c-o],bottomLeft:[u+o,c,u,c-o,u+o,c-o]}},intersectLine:function e(t,r,a,n,i,o,s,l){var u=this.generateCutTrianglePts(a+2*s,n+2*s,t,r,l);var v=[].concat.apply([],[u.topLeft.splice(0,4),u.topRight.splice(0,4),u.bottomRight.splice(0,4),u.bottomLeft.splice(0,4)]);return xa(i,o,v,t,r)},checkPoint:function e(t,r,a,n,i,o,s,l){var u=l==="auto"?this.cornerLength:l;if(fa(t,r,this.points,o,s,n,i-2*u,[0,-1],a)){return true}if(fa(t,r,this.points,o,s,n-2*u,i,[0,-1],a)){return true}var v=this.generateCutTrianglePts(n,i,o,s);return va(t,r,v.topLeft)||va(t,r,v.topRight)||va(t,r,v.bottomRight)||va(t,r,v.bottomLeft)}}};Qc.generateBarrel=function(){return this.nodeShapes["barrel"]={renderer:this,name:"barrel",points:Ta(4,0),draw:function e(t,r,a,n,i,o){this.renderer.nodeShapeImpl(this.name,t,r,a,n,i)},intersectLine:function e(t,r,a,n,i,o,s,l){var u=.15;var v=.5;var f=.85;var c=this.generateBarrelBezierPts(a+2*s,n+2*s,t,r);var d=function e(t){var r=jr({x:t[0],y:t[1]},{x:t[2],y:t[3]},{x:t[4],y:t[5]},u);var a=jr({x:t[0],y:t[1]},{x:t[2],y:t[3]},{x:t[4],y:t[5]},v);var n=jr({x:t[0],y:t[1]},{x:t[2],y:t[3]},{x:t[4],y:t[5]},f);return[t[0],t[1],r.x,r.y,a.x,a.y,n.x,n.y,t[4],t[5]]};var h=[].concat(d(c.topLeft),d(c.topRight),d(c.bottomRight),d(c.bottomLeft));return xa(i,o,h,t,r)},generateBarrelBezierPts:function e(t,r,a,n){var i=r/2;var o=t/2;var s=a-o;var l=a+o;var u=n-i;var v=n+i;var f=Aa(t,r);var c=f.heightOffset;var d=f.widthOffset;var h=f.ctrlPtOffsetPct*t;var p={topLeft:[s,u+c,s+h,u,s+d,u],topRight:[l-d,u,l-h,u,l,u+c],bottomRight:[l,v-c,l-h,v,l-d,v],bottomLeft:[s+d,v,s+h,v,s,v-c]};p.topLeft.isTop=true;p.topRight.isTop=true;p.bottomLeft.isBottom=true;p.bottomRight.isBottom=true;return p},checkPoint:function e(t,r,a,n,i,o,s,l){var u=Aa(n,i);var v=u.heightOffset;var f=u.widthOffset;if(fa(t,r,this.points,o,s,n,i-2*v,[0,-1],a)){return true}if(fa(t,r,this.points,o,s,n-2*f,i,[0,-1],a)){return true}var c=this.generateBarrelBezierPts(n,i,o,s);var d=function e(t,r,a){var n=a[4];var i=a[2];var o=a[0];var s=a[5];var l=a[1];var u=Math.min(n,o);var v=Math.max(n,o);var f=Math.min(s,l);var c=Math.max(s,l);if(u<=t&&t<=v&&f<=r&&r<=c){var d=Ba(n,i,o);var h=oa(d[0],d[1],d[2],t);var p=h.filter((function(e){return 0<=e&&e<=1}));if(p.length>0){return p[0]}}return null};var h=Object.keys(c);for(var p=0;p<h.length;p++){var g=h[p];var y=c[g];var m=d(t,r,y);if(m==null){continue}var b=y[5];var x=y[3];var w=y[1];var E=Vr(b,x,w,m);if(y.isTop&&E<=r){return true}if(y.isBottom&&r<=E){return true}}return false}}};Qc.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes["bottomroundrectangle"]={renderer:this,name:"bottom-round-rectangle",points:Ta(4,0),draw:function e(t,r,a,n,i,o){this.renderer.nodeShapeImpl(this.name,t,r,a,n,i,this.points,o)},intersectLine:function e(t,r,a,n,i,o,s,l){var u=t-(a/2+s);var v=r-(n/2+s);var f=v;var c=t+(a/2+s);var d=ba(i,o,t,r,u,v,c,f,false);if(d.length>0){return d}return aa(i,o,t,r,a,n,s,l)},checkPoint:function e(t,r,a,n,i,o,s,l){l=l==="auto"?Pa(n,i):l;var u=2*l;if(fa(t,r,this.points,o,s,n,i-u,[0,-1],a)){return true}if(fa(t,r,this.points,o,s,n-u,i,[0,-1],a)){return true}var v=n/2+2*a;var f=i/2+2*a;var c=[o-v,s-f,o-v,s,o+v,s,o+v,s-f];if(va(t,r,c)){return true}if(ga(t,r,u,u,o+n/2-l,s+i/2-l,a)){return true}if(ga(t,r,u,u,o-n/2+l,s+i/2-l,a)){return true}return false}}};Qc.registerNodeShapes=function(){var e=this.nodeShapes={};var t=this;this.generateEllipse();this.generatePolygon("triangle",Ta(3,0));this.generateRoundPolygon("round-triangle",Ta(3,0));this.generatePolygon("rectangle",Ta(4,0));e["square"]=e["rectangle"];this.generateRoundRectangle();this.generateCutRectangle();this.generateBarrel();this.generateBottomRoundrectangle();{var r=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",r);this.generateRoundPolygon("round-diamond",r)}this.generatePolygon("pentagon",Ta(5,0));this.generateRoundPolygon("round-pentagon",Ta(5,0));this.generatePolygon("hexagon",Ta(6,0));this.generateRoundPolygon("round-hexagon",Ta(6,0));this.generatePolygon("heptagon",Ta(7,0));this.generateRoundPolygon("round-heptagon",Ta(7,0));this.generatePolygon("octagon",Ta(8,0));this.generateRoundPolygon("round-octagon",Ta(8,0));var a=new Array(20);{var n=Ca(5,0);var i=Ca(5,Math.PI/5);var o=.5*(3-Math.sqrt(5));o*=1.57;for(var s=0;s<i.length/2;s++){i[s*2]*=o;i[s*2+1]*=o}for(var s=0;s<20/4;s++){a[s*4]=n[s*2];a[s*4+1]=n[s*2+1];a[s*4+2]=i[s*2];a[s*4+3]=i[s*2+1]}}a=ka(a);this.generatePolygon("star",a);this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]);this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]);this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]);this.nodeShapes["concavehexagon"]=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);{var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l);this.generateRoundPolygon("round-tag",l)}e.makePolygon=function(e){var r=e.join("$");var a="polygon-"+r;var n;if(n=this[a]){return n}return t.generatePolygon(a,e)}};var Jc={};Jc.timeToRender=function(){return this.redrawTotalTime/this.redrawCount};Jc.redraw=function(e){e=e||Xt();var t=this;if(t.averageRedrawTime===undefined){t.averageRedrawTime=0}if(t.lastRedrawTime===undefined){t.lastRedrawTime=0}if(t.lastDrawTime===undefined){t.lastDrawTime=0}t.requestedFrame=true;t.renderOptions=e};Jc.beforeRender=function(e,t){if(this.destroyed){return}if(t==null){Rt("Priority is not optional for beforeRender")}var r=this.beforeRenderCallbacks;r.push({fn:e,priority:t});r.sort((function(e,t){return t.priority-e.priority}))};var ed=function e(t,r,a){var n=t.beforeRenderCallbacks;for(var i=0;i<n.length;i++){n[i].fn(r,a)}};Jc.startRenderLoop=function(){var e=this;var t=e.cy;if(e.renderLoopStarted){return}else{e.renderLoopStarted=true}var r=function a(n){if(e.destroyed){return}if(t.batching());else if(e.requestedFrame&&!e.skipFrame){ed(e,true,n);var i=ct();e.render(e.renderOptions);var o=e.lastDrawTime=ct();if(e.averageRedrawTime===undefined){e.averageRedrawTime=o-i}if(e.redrawCount===undefined){e.redrawCount=0}e.redrawCount++;if(e.redrawTotalTime===undefined){e.redrawTotalTime=0}var s=o-i;e.redrawTotalTime+=s;e.lastRedrawTime=s;e.averageRedrawTime=e.averageRedrawTime/2+s/2;e.requestedFrame=false}else{ed(e,false,n)}e.skipFrame=false;ft(r)};ft(r)};var td=function e(t){this.init(t)};var rd=td;var ad=rd.prototype;ad.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"];ad.init=function(e){var t=this;t.options=e;t.cy=e.cy;var r=t.container=e.cy.container();var a=t.cy.window();if(a){var n=a.document;var i=n.head;var o="__________cytoscape_stylesheet";var s="__________cytoscape_container";var l=n.getElementById(o)!=null;if(r.className.indexOf(s)<0){r.className=(r.className||"")+" "+s}if(!l){var u=n.createElement("style");u.id=o;u.textContent="."+s+" { position: relative; }";i.insertBefore(u,i.children[0])}var v=a.getComputedStyle(r);var f=v.getPropertyValue("position");if(f==="static"){Lt("A Cytoscape container has style position:static and so can not use UI extensions properly")}}t.selection=[undefined,undefined,undefined,undefined,0];t.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95];t.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:false,initialPan:[null,null],capture:false};t.dragData={possibleDragElements:[]};t.touchData={start:null,capture:false,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:true,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]};t.redraws=0;t.showFps=e.showFps;t.debug=e.debug;t.webgl=e.webgl;t.hideEdgesOnViewport=e.hideEdgesOnViewport;t.textureOnViewport=e.textureOnViewport;t.wheelSensitivity=e.wheelSensitivity;t.motionBlurEnabled=e.motionBlur;t.forcedPixelRatio=I(e.pixelRatio)?e.pixelRatio:null;t.motionBlur=e.motionBlur;t.motionBlurOpacity=e.motionBlurOpacity;t.motionBlurTransparency=1-t.motionBlurOpacity;t.motionBlurPxRatio=1;t.mbPxRBlurry=1;t.minMbLowQualFrames=4;t.fullQualityMb=false;t.clearedForMotionBlur=[];t.desktopTapThreshold=e.desktopTapThreshold;t.desktopTapThreshold2=e.desktopTapThreshold*e.desktopTapThreshold;t.touchTapThreshold=e.touchTapThreshold;t.touchTapThreshold2=e.touchTapThreshold*e.touchTapThreshold;t.tapholdDuration=500;t.bindings=[];t.beforeRenderCallbacks=[];t.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100};t.registerNodeShapes();t.registerArrowShapes();t.registerCalculationListeners()};ad.notify=function(e,t){var r=this;var a=r.cy;if(this.destroyed){return}if(e==="init"){r.load();return}if(e==="destroy"){r.destroy();return}if(e==="add"||e==="remove"||e==="move"&&a.hasCompoundNodes()||e==="load"||e==="zorder"||e==="mount"){r.invalidateCachedZSortedEles()}if(e==="viewport"){r.redrawHint("select",true)}if(e==="gc"){r.redrawHint("gc",true)}if(e==="load"||e==="resize"||e==="mount"){r.invalidateContainerClientCoordsCache();r.matchCanvasSize(r.container)}r.redrawHint("eles",true);r.redrawHint("drag",true);this.startRenderLoop();this.redraw()};ad.destroy=function(){var e=this;e.destroyed=true;e.cy.stopAnimationLoop();for(var t=0;t<e.bindings.length;t++){var r=e.bindings[t];var a=r;var n=a.target;(n.off||n.removeEventListener).apply(n,a.args)}e.bindings=[];e.beforeRenderCallbacks=[];e.onUpdateEleCalcsFns=[];if(e.removeObserver){e.removeObserver.disconnect()}if(e.styleObserver){e.styleObserver.disconnect()}if(e.resizeObserver){e.resizeObserver.disconnect()}if(e.labelCalcDiv){try{document.body.removeChild(e.labelCalcDiv)}catch(i){}}};ad.isHeadless=function(){return false};[lc,Kc,Zc,$c,Qc,Jc].forEach((function(e){se(ad,e)}));var nd=1e3/60;var id={setupDequeueing:function e(t){return function e(){var r=this;var a=this.renderer;if(r.dequeueingSetup){return}else{r.dequeueingSetup=true}var n=st((function(){a.redrawHint("eles",true);a.redrawHint("drag",true);a.redraw()}),t.deqRedrawThreshold);var i=function e(i,o){var s=ct();var l=a.averageRedrawTime;var u=a.lastRedrawTime;var v=[];var f=a.cy.extent();var c=a.getPixelRatio();if(!i){a.flushRenderedStyleQueue()}while(true){var d=ct();var h=d-s;var p=d-o;if(u<nd){var g=nd-(i?l:0);if(p>=t.deqFastCost*g){break}}else{if(i){if(h>=t.deqCost*u||h>=t.deqAvgCost*l){break}}else if(p>=t.deqNoDrawCost*nd){break}}var y=t.deq(r,c,f);if(y.length>0){for(var m=0;m<y.length;m++){v.push(y[m])}}else{break}}if(v.length>0){t.onDeqd(r,v);if(!i&&t.shouldRedraw(r,v,c,f)){n()}}};var o=t.priority||It;a.beforeRender(i,o(r))}}};var od=function(){function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:_t;o(this,e);this.idsByKey=new Zt;this.keyForId=new Zt;this.cachesByLvl=new Zt;this.lvls=[];this.getKey=t;this.doesEleInvalidateKey=r}return l(e,[{key:"getIdsFor",value:function e(t){if(t==null){Rt("Can not get id list for null key")}var r=this.idsByKey;var a=this.idsByKey.get(t);if(!a){a=new Jt;r.set(t,a)}return a}},{key:"addIdForKey",value:function e(t,r){if(t!=null){this.getIdsFor(t).add(r)}}},{key:"deleteIdForKey",value:function e(t,r){if(t!=null){this.getIdsFor(t)["delete"](r)}}},{key:"getNumberOfIdsForKey",value:function e(t){if(t==null){return 0}else{return this.getIdsFor(t).size}}},{key:"updateKeyMappingFor",value:function e(t){var r=t.id();var a=this.keyForId.get(r);var n=this.getKey(t);this.deleteIdForKey(a,r);this.addIdForKey(n,r);this.keyForId.set(r,n)}},{key:"deleteKeyMappingFor",value:function e(t){var r=t.id();var a=this.keyForId.get(r);this.deleteIdForKey(a,r);this.keyForId["delete"](r)}},{key:"keyHasChangedFor",value:function e(t){var r=t.id();var a=this.keyForId.get(r);var n=this.getKey(t);return a!==n}},{key:"isInvalid",value:function e(t){return this.keyHasChangedFor(t)||this.doesEleInvalidateKey(t)}},{key:"getCachesAt",value:function e(t){var r=this.cachesByLvl,a=this.lvls;var n=r.get(t);if(!n){n=new Zt;r.set(t,n);a.push(t)}return n}},{key:"getCache",value:function e(t,r){return this.getCachesAt(r).get(t)}},{key:"get",value:function e(t,r){var a=this.getKey(t);var n=this.getCache(a,r);if(n!=null){this.updateKeyMappingFor(t)}return n}},{key:"getForCachedKey",value:function e(t,r){var a=this.keyForId.get(t.id());var n=this.getCache(a,r);return n}},{key:"hasCache",value:function e(t,r){return this.getCachesAt(r).has(t)}},{key:"has",value:function e(t,r){var a=this.getKey(t);return this.hasCache(a,r)}},{key:"setCache",value:function e(t,r,a){a.key=t;this.getCachesAt(r).set(t,a)}},{key:"set",value:function e(t,r,a){var n=this.getKey(t);this.setCache(n,r,a);this.updateKeyMappingFor(t)}},{key:"deleteCache",value:function e(t,r){this.getCachesAt(r)["delete"](t)}},{key:"delete",value:function e(t,r){var a=this.getKey(t);this.deleteCache(a,r)}},{key:"invalidateKey",value:function e(t){var r=this;this.lvls.forEach((function(e){return r.deleteCache(t,e)}))}},{key:"invalidate",value:function e(t){var r=t.id();var a=this.keyForId.get(r);this.deleteKeyMappingFor(t);var n=this.doesEleInvalidateKey(t);if(n){this.invalidateKey(a)}return n||this.getNumberOfIdsForKey(a)===0}}])}();var sd=25;var ld=50;var ud=-4;var vd=3;var fd=7.99;var cd=8;var dd=1024;var hd=1024;var pd=1024;var gd=.2;var yd=.8;var md=10;var bd=.15;var xd=.1;var wd=.9;var Ed=.9;var Td=100;var kd=1;var Cd={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"};var Pd=Yt({getKey:null,doesEleInvalidateKey:_t,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:At,allowEdgeTxrCaching:true,allowParentTxrCaching:true});var Sd=function e(t,r){var a=this;a.renderer=t;a.onDequeues=[];var n=Pd(r);se(a,n);a.lookup=new od(n.getKey,n.doesEleInvalidateKey);a.setupDequeueing()};var Dd=Sd.prototype;Dd.reasons=Cd;Dd.getTextureQueue=function(e){var t=this;t.eleImgCaches=t.eleImgCaches||{};return t.eleImgCaches[e]=t.eleImgCaches[e]||[]};Dd.getRetiredTextureQueue=function(e){var t=this;var r=t.eleImgCaches.retired=t.eleImgCaches.retired||{};var a=r[e]=r[e]||[];return a};Dd.getElementQueue=function(){var e=this;var t=e.eleCacheQueue=e.eleCacheQueue||new fr((function(e,t){return t.reqs-e.reqs}));return t};Dd.getElementKeyToQueue=function(){var e=this;var t=e.eleKeyToCacheQueue=e.eleKeyToCacheQueue||{};return t};Dd.getElement=function(e,t,r,a,n){var i=this;var o=this.renderer;var s=o.cy.zoom();var l=this.lookup;if(!t||t.w===0||t.h===0||isNaN(t.w)||isNaN(t.h)||!e.visible()||e.removed()){return null}if(!i.allowEdgeTxrCaching&&e.isEdge()||!i.allowParentTxrCaching&&e.isParent()){return null}if(a==null){a=Math.ceil(Nr(s*r))}if(a<ud){a=ud}else if(s>=fd||a>vd){return null}var u=Math.pow(2,a);var v=t.h*u;var f=t.w*u;var c=o.eleTextBiggerThanMin(e,u);if(!this.isVisible(e,c)){return null}var d=l.get(e,a);if(d&&d.invalidated){d.invalidated=false;d.texture.invalidatedWidth-=d.width}if(d){return d}var h;if(v<=sd){h=sd}else if(v<=ld){h=ld}else{h=Math.ceil(v/ld)*ld}if(v>pd||f>hd){return null}var p=i.getTextureQueue(h);var g=p[p.length-2];var y=function e(){return i.recycleTexture(h,f)||i.addTexture(h,f)};if(!g){g=p[p.length-1]}if(!g){g=y()}if(g.width-g.usedWidth<f){g=y()}var m=function e(t){return t&&t.scaledLabelShown===c};var b=n&&n===Cd.dequeue;var x=n&&n===Cd.highQuality;var w=n&&n===Cd.downscale;var E;for(var T=a+1;T<=vd;T++){var k=l.get(e,T);if(k){E=k;break}}var C=E&&E.level===a+1?E:null;var P=function e(){g.context.drawImage(C.texture.canvas,C.x,0,C.width,C.height,g.usedWidth,0,f,v)};g.context.setTransform(1,0,0,1,0,0);g.context.clearRect(g.usedWidth,0,f,h);if(m(C)){P()}else if(m(E)){if(x){for(var S=E.level;S>a;S--){C=i.getElement(e,t,r,S,Cd.downscale)}P()}else{i.queueElement(e,E.level-1);return E}}else{var D;if(!b&&!x&&!w){for(var B=a-1;B>=ud;B--){var A=l.get(e,B);if(A){D=A;break}}}if(m(D)){i.queueElement(e,a);return D}g.context.translate(g.usedWidth,0);g.context.scale(u,u);this.drawElement(g.context,e,t,c,false);g.context.scale(1/u,1/u);g.context.translate(-g.usedWidth,0)}d={x:g.usedWidth,texture:g,level:a,scale:u,width:f,height:v,scaledLabelShown:c};g.usedWidth+=Math.ceil(f+cd);g.eleCaches.push(d);l.set(e,a,d);i.checkTextureFullness(g);return d};Dd.invalidateElements=function(e){for(var t=0;t<e.length;t++){this.invalidateElement(e[t])}};Dd.invalidateElement=function(e){var t=this;var r=t.lookup;var a=[];var n=r.isInvalid(e);if(!n){return}for(var i=ud;i<=vd;i++){var o=r.getForCachedKey(e,i);if(o){a.push(o)}}var s=r.invalidate(e);if(s){for(var l=0;l<a.length;l++){var u=a[l];var v=u.texture;v.invalidatedWidth+=u.width;u.invalidated=true;t.checkTextureUtility(v)}}t.removeFromQueue(e)};Dd.checkTextureUtility=function(e){if(e.invalidatedWidth>=gd*e.width){this.retireTexture(e)}};Dd.checkTextureFullness=function(e){var t=this;var r=t.getTextureQueue(e.height);if(e.usedWidth/e.width>yd&&e.fullnessChecks>=md){qt(r,e)}else{e.fullnessChecks++}};Dd.retireTexture=function(e){var t=this;var r=e.height;var a=t.getTextureQueue(r);var n=this.lookup;qt(a,e);e.retired=true;var i=e.eleCaches;for(var o=0;o<i.length;o++){var s=i[o];n.deleteCache(s.key,s.level)}Wt(i);var l=t.getRetiredTextureQueue(r);l.push(e)};Dd.addTexture=function(e,t){var r=this;var a=r.getTextureQueue(e);var n={};a.push(n);n.eleCaches=[];n.height=e;n.width=Math.max(dd,t);n.usedWidth=0;n.invalidatedWidth=0;n.fullnessChecks=0;n.canvas=r.renderer.makeOffscreenCanvas(n.width,n.height);n.context=n.canvas.getContext("2d");return n};Dd.recycleTexture=function(e,t){var r=this;var a=r.getTextureQueue(e);var n=r.getRetiredTextureQueue(e);for(var i=0;i<n.length;i++){var o=n[i];if(o.width>=t){o.retired=false;o.usedWidth=0;o.invalidatedWidth=0;o.fullnessChecks=0;Wt(o.eleCaches);o.context.setTransform(1,0,0,1,0,0);o.context.clearRect(0,0,o.width,o.height);qt(n,o);a.push(o);return o}}};Dd.queueElement=function(e,t){var r=this;var a=r.getElementQueue();var n=r.getElementKeyToQueue();var i=this.getKey(e);var o=n[i];if(o){o.level=Math.max(o.level,t);o.eles.merge(e);o.reqs++;a.updateItem(o)}else{var s={eles:e.spawn().merge(e),level:t,reqs:1,key:i};a.push(s);n[i]=s}};Dd.dequeue=function(e){var t=this;var r=t.getElementQueue();var a=t.getElementKeyToQueue();var n=[];var i=t.lookup;for(var o=0;o<kd;o++){if(r.size()>0){var s=r.pop();var l=s.key;var u=s.eles[0];var v=i.hasCache(u,s.level);a[l]=null;if(v){continue}n.push(s);var f=t.getBoundingBox(u);t.getElement(u,f,e,s.level,Cd.dequeue)}else{break}}return n};Dd.removeFromQueue=function(e){var t=this;var r=t.getElementQueue();var a=t.getElementKeyToQueue();var n=this.getKey(e);var i=a[n];if(i!=null){if(i.eles.length===1){i.reqs=Bt;r.updateItem(i);r.pop();a[n]=null}else{i.eles.unmerge(e)}}};Dd.onDequeue=function(e){this.onDequeues.push(e)};Dd.offDequeue=function(e){qt(this.onDequeues,e)};Dd.setupDequeueing=id.setupDequeueing({deqRedrawThreshold:Td,deqCost:bd,deqAvgCost:xd,deqNoDrawCost:wd,deqFastCost:Ed,deq:function e(t,r,a){return t.dequeue(r,a)},onDeqd:function e(t,r){for(var a=0;a<t.onDequeues.length;a++){var n=t.onDequeues[a];n(r)}},shouldRedraw:function e(t,r,a,n){for(var i=0;i<r.length;i++){var o=r[i].eles;for(var s=0;s<o.length;s++){var l=o[s].boundingBox();if(Jr(l,n)){return true}}}return false},priority:function e(t){return t.renderer.beforeRenderPriorities.eleTxrDeq}});var Bd=1;var Ad=-4;var _d=2;var Md=3.99;var Id=50;var Rd=50;var Nd=.15;var Ld=.1;var Od=.9;var zd=.9;var Fd=1;var Vd=250;var jd=4e3*4e3;var Xd=32767;var Yd=true;var qd=function e(t){var r=this;var a=r.renderer=t;var n=a.cy;r.layersByLevel={};r.firstGet=true;r.lastInvalidationTime=ct()-2*Vd;r.skipping=false;r.eleTxrDeqs=n.collection();r.scheduleElementRefinement=st((function(){r.refineElementTextures(r.eleTxrDeqs);r.eleTxrDeqs.unmerge(r.eleTxrDeqs)}),Rd);a.beforeRender((function(e,t){if(t-r.lastInvalidationTime<=Vd){r.skipping=true}else{r.skipping=false}}),a.beforeRenderPriorities.lyrTxrSkip);var i=function e(t,r){return r.reqs-t.reqs};r.layersQueue=new fr(i);r.setupDequeueing()};var Wd=qd.prototype;var Ud=0;var Gd=Math.pow(2,53)-1;Wd.makeLayer=function(e,t){var r=Math.pow(2,t);var a=Math.ceil(e.w*r);var n=Math.ceil(e.h*r);var i=this.renderer.makeOffscreenCanvas(a,n);var o={id:Ud=++Ud%Gd,bb:e,level:t,width:a,height:n,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0};var s=o.context;var l=-o.bb.x1;var u=-o.bb.y1;s.scale(r,r);s.translate(l,u);return o};Wd.getLayers=function(e,t,r){var a=this;var n=a.renderer;var i=n.cy;var o=i.zoom();var s=a.firstGet;a.firstGet=false;if(r==null){r=Math.ceil(Nr(o*t));if(r<Ad){r=Ad}else if(o>=Md||r>_d){return null}}a.validateLayersElesOrdering(r,e);var l=a.layersByLevel;var u=Math.pow(2,r);var v=l[r]=l[r]||[];var f;var c=a.levelIsComplete(r,e);var d;var h=function t(){var n=function t(r){a.validateLayersElesOrdering(r,e);if(a.levelIsComplete(r,e)){d=l[r];return true}};var i=function e(t){if(d){return}for(var a=r+t;Ad<=a&&a<=_d;a+=t){if(n(a)){break}}};i(1);i(-1);for(var o=v.length-1;o>=0;o--){var s=v[o];if(s.invalid){qt(v,s)}}};if(!c){h()}else{return v}var p=function t(){if(!f){f=qr();for(var r=0;r<e.length;r++){Hr(f,e[r].boundingBox())}}return f};var g=function e(t){t=t||{};var n=t.after;p();var i=Math.ceil(f.w*u);var o=Math.ceil(f.h*u);if(i>Xd||o>Xd){return null}var s=i*o;if(s>jd){return null}var l=a.makeLayer(f,r);if(n!=null){var c=v.indexOf(n)+1;v.splice(c,0,l)}else if(t.insert===undefined||t.insert){v.unshift(l)}return l};if(a.skipping&&!s){return null}var y=null;var m=e.length/Bd;var b=!s;for(var x=0;x<e.length;x++){var w=e[x];var E=w._private.rscratch;var T=E.imgLayerCaches=E.imgLayerCaches||{};var k=T[r];if(k){y=k;continue}if(!y||y.eles.length>=m||!ra(y.bb,w.boundingBox())){y=g({insert:true,after:y});if(!y){return null}}if(d||b){a.queueLayer(y,w)}else{a.drawEleInLayer(y,w,r,t)}y.eles.push(w);T[r]=y}if(d){return d}if(b){return null}return v};Wd.getEleLevelForLayerLevel=function(e,t){return e};Wd.drawEleInLayer=function(e,t,r,a){var n=this;var i=this.renderer;var o=e.context;var s=t.boundingBox();if(s.w===0||s.h===0||!t.visible()){return}r=n.getEleLevelForLayerLevel(r,a);{i.setImgSmoothing(o,false)}{i.drawCachedElement(o,t,null,null,r,Yd)}{i.setImgSmoothing(o,true)}};Wd.levelIsComplete=function(e,t){var r=this;var a=r.layersByLevel[e];if(!a||a.length===0){return false}var n=0;for(var i=0;i<a.length;i++){var o=a[i];if(o.reqs>0){return false}if(o.invalid){return false}n+=o.eles.length}if(n!==t.length){return false}return true};Wd.validateLayersElesOrdering=function(e,t){var r=this.layersByLevel[e];if(!r){return}for(var a=0;a<r.length;a++){var n=r[a];var i=-1;for(var o=0;o<t.length;o++){if(n.eles[0]===t[o]){i=o;break}}if(i<0){this.invalidateLayer(n);continue}var s=i;for(var o=0;o<n.eles.length;o++){if(n.eles[o]!==t[s+o]){this.invalidateLayer(n);break}}}};Wd.updateElementsInLayers=function(e,t){var r=this;var a=O(e[0]);for(var n=0;n<e.length;n++){var i=a?null:e[n];var o=a?e[n]:e[n].ele;var s=o._private.rscratch;var l=s.imgLayerCaches=s.imgLayerCaches||{};for(var u=Ad;u<=_d;u++){var v=l[u];if(!v){continue}if(i&&r.getEleLevelForLayerLevel(v.level)!==i.level){continue}t(v,o,i)}}};Wd.haveLayers=function(){var e=this;var t=false;for(var r=Ad;r<=_d;r++){var a=e.layersByLevel[r];if(a&&a.length>0){t=true;break}}return t};Wd.invalidateElements=function(e){var t=this;if(e.length===0){return}t.lastInvalidationTime=ct();if(e.length===0||!t.haveLayers()){return}t.updateElementsInLayers(e,(function e(r,a,n){t.invalidateLayer(r)}))};Wd.invalidateLayer=function(e){this.lastInvalidationTime=ct();if(e.invalid){return}var t=e.level;var r=e.eles;var a=this.layersByLevel[t];qt(a,e);e.elesQueue=[];e.invalid=true;if(e.replacement){e.replacement.invalid=true}for(var n=0;n<r.length;n++){var i=r[n]._private.rscratch.imgLayerCaches;if(i){i[t]=null}}};Wd.refineElementTextures=function(e){var t=this;t.updateElementsInLayers(e,(function e(r,a,n){var i=r.replacement;if(!i){i=r.replacement=t.makeLayer(r.bb,r.level);i.replaces=r;i.eles=r.eles}if(!i.reqs){for(var o=0;o<i.eles.length;o++){t.queueLayer(i,i.eles[o])}}}))};Wd.enqueueElementRefinement=function(e){this.eleTxrDeqs.merge(e);this.scheduleElementRefinement()};Wd.queueLayer=function(e,t){var r=this;var a=r.layersQueue;var n=e.elesQueue;var i=n.hasId=n.hasId||{};if(e.replacement){return}if(t){if(i[t.id()]){return}n.push(t);i[t.id()]=true}if(e.reqs){e.reqs++;a.updateItem(e)}else{e.reqs=1;a.push(e)}};Wd.dequeue=function(e){var t=this;var r=t.layersQueue;var a=[];var n=0;while(n<Fd){if(r.size()===0){break}var i=r.peek();if(i.replacement){r.pop();continue}if(i.replaces&&i!==i.replaces.replacement){r.pop();continue}if(i.invalid){r.pop();continue}var o=i.elesQueue.shift();if(o){t.drawEleInLayer(i,o,i.level,e);n++}if(a.length===0){a.push(true)}if(i.elesQueue.length===0){r.pop();i.reqs=0;if(i.replaces){t.applyLayerReplacement(i)}t.requestRedraw()}}return a};Wd.applyLayerReplacement=function(e){var t=this;var r=t.layersByLevel[e.level];var a=e.replaces;var n=r.indexOf(a);if(n<0||a.invalid){return}r[n]=e;for(var i=0;i<e.eles.length;i++){var o=e.eles[i]._private;var s=o.imgLayerCaches=o.imgLayerCaches||{};if(s){s[e.level]=e}}t.requestRedraw()};Wd.requestRedraw=st((function(){var e=this.renderer;e.redrawHint("eles",true);e.redrawHint("drag",true);e.redraw()}),100);Wd.setupDequeueing=id.setupDequeueing({deqRedrawThreshold:Id,deqCost:Nd,deqAvgCost:Ld,deqNoDrawCost:Od,deqFastCost:zd,deq:function e(t,r){return t.dequeue(r)},onDeqd:It,shouldRedraw:At,priority:function e(t){return t.renderer.beforeRenderPriorities.lyrTxrDeq}});var Hd={};var Kd;function Zd(e,t){for(var r=0;r<t.length;r++){var a=t[r];e.lineTo(a.x,a.y)}}function $d(e,t,r){var a;for(var n=0;n<t.length;n++){var i=t[n];if(n===0){a=i}e.lineTo(i.x,i.y)}e.quadraticCurveTo(r.x,r.y,a.x,a.y)}function Qd(e,t,r){if(e.beginPath){e.beginPath()}var a=t;for(var n=0;n<a.length;n++){var i=a[n];e.lineTo(i.x,i.y)}var o=r;var s=r[0];e.moveTo(s.x,s.y);for(var n=1;n<o.length;n++){var i=o[n];e.lineTo(i.x,i.y)}if(e.closePath){e.closePath()}}function Jd(e,t,r,a,n){if(e.beginPath){e.beginPath()}e.arc(r,a,n,0,Math.PI*2,false);var i=t;var o=i[0];e.moveTo(o.x,o.y);for(var s=0;s<i.length;s++){var l=i[s];e.lineTo(l.x,l.y)}if(e.closePath){e.closePath()}}function eh(e,t,r,a){e.arc(t,r,a,0,Math.PI*2,false)}Hd.arrowShapeImpl=function(e){return(Kd||(Kd={polygon:Zd,"triangle-backcurve":$d,"triangle-tee":Qd,"circle-triangle":Jd,"triangle-cross":Qd,circle:eh}))[e]};var th={};th.drawElement=function(e,t,r,a,n,i){var o=this;if(t.isNode()){o.drawNode(e,t,r,a,n,i)}else{o.drawEdge(e,t,r,a,n,i)}};th.drawElementOverlay=function(e,t){var r=this;if(t.isNode()){r.drawNodeOverlay(e,t)}else{r.drawEdgeOverlay(e,t)}};th.drawElementUnderlay=function(e,t){var r=this;if(t.isNode()){r.drawNodeUnderlay(e,t)}else{r.drawEdgeUnderlay(e,t)}};th.drawCachedElementPortion=function(e,t,r,a,n,i,o,s){var l=this;var u=r.getBoundingBox(t);if(u.w===0||u.h===0){return}var v=r.getElement(t,u,a,n,i);if(v!=null){var f=s(l,t);if(f===0){return}var c=o(l,t);var d=u.x1,h=u.y1,p=u.w,g=u.h;var y,m,b,x,w;if(c!==0){var E=r.getRotationPoint(t);b=E.x;x=E.y;e.translate(b,x);e.rotate(c);w=l.getImgSmoothing(e);if(!w){l.setImgSmoothing(e,true)}var T=r.getRotationOffset(t);y=T.x;m=T.y}else{y=d;m=h}var k;if(f!==1){k=e.globalAlpha;e.globalAlpha=k*f}e.drawImage(v.texture.canvas,v.x,0,v.width,v.height,y,m,p,g);if(f!==1){e.globalAlpha=k}if(c!==0){e.rotate(-c);e.translate(-b,-x);if(!w){l.setImgSmoothing(e,false)}}}else{r.drawElement(e,t)}};var rh=function e(){return 0};var ah=function e(t,r){return t.getTextAngle(r,null)};var nh=function e(t,r){return t.getTextAngle(r,"source")};var ih=function e(t,r){return t.getTextAngle(r,"target")};var oh=function e(t,r){return r.effectiveOpacity()};var sh=function e(t,r){return r.pstyle("text-opacity").pfValue*r.effectiveOpacity()};th.drawCachedElement=function(e,t,r,a,n,i){var o=this;var s=o.data,l=s.eleTxrCache,u=s.lblTxrCache,v=s.slbTxrCache,f=s.tlbTxrCache;var c=t.boundingBox();var d=i===true?l.reasons.highQuality:null;if(c.w===0||c.h===0||!t.visible()){return}if(!a||Jr(c,a)){var h=t.isEdge();var p=t.element()._private.rscratch.badLine;o.drawElementUnderlay(e,t);o.drawCachedElementPortion(e,t,l,r,n,d,rh,oh);if(!h||!p){o.drawCachedElementPortion(e,t,u,r,n,d,ah,sh)}if(h&&!p){o.drawCachedElementPortion(e,t,v,r,n,d,nh,sh);o.drawCachedElementPortion(e,t,f,r,n,d,ih,sh)}o.drawElementOverlay(e,t)}};th.drawElements=function(e,t){var r=this;for(var a=0;a<t.length;a++){var n=t[a];r.drawElement(e,n)}};th.drawCachedElements=function(e,t,r,a){var n=this;for(var i=0;i<t.length;i++){var o=t[i];n.drawCachedElement(e,o,r,a)}};th.drawCachedNodes=function(e,t,r,a){var n=this;for(var i=0;i<t.length;i++){var o=t[i];if(!o.isNode()){continue}n.drawCachedElement(e,o,r,a)}};th.drawLayeredElements=function(e,t,r,a){var n=this;var i=n.data.lyrTxrCache.getLayers(t,r);if(i){for(var o=0;o<i.length;o++){var s=i[o];var l=s.bb;if(l.w===0||l.h===0){continue}e.drawImage(s.canvas,l.x1,l.y1,l.w,l.h)}}else{n.drawCachedElements(e,t,r,a)}};var lh={};lh.drawEdge=function(e,t,r){var a=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;var n=arguments.length>4&&arguments[4]!==undefined?arguments[4]:true;var i=arguments.length>5&&arguments[5]!==undefined?arguments[5]:true;var o=this;var s=t._private.rscratch;if(i&&!t.visible()){return}if(s.badLine||s.allpts==null||isNaN(s.allpts[0])){return}var l;if(r){l=r;e.translate(-l.x1,-l.y1)}var u=i?t.pstyle("opacity").value:1;var v=i?t.pstyle("line-opacity").value:1;var f=t.pstyle("curve-style").value;var c=t.pstyle("line-style").value;var d=t.pstyle("width").pfValue;var h=t.pstyle("line-cap").value;var p=t.pstyle("line-outline-width").value;var g=t.pstyle("line-outline-color").value;var y=u*v;var m=u*v;var b=function r(){var a=arguments.length>0&&arguments[0]!==undefined?arguments[0]:y;if(f==="straight-triangle"){o.eleStrokeStyle(e,t,a);o.drawEdgeTrianglePath(t,e,s.allpts)}else{e.lineWidth=d;e.lineCap=h;o.eleStrokeStyle(e,t,a);o.drawEdgePath(t,e,s.allpts,c);e.lineCap="butt"}};var x=function r(){var a=arguments.length>0&&arguments[0]!==undefined?arguments[0]:y;e.lineWidth=d+p;e.lineCap=h;if(p>0){o.colorStrokeStyle(e,g[0],g[1],g[2],a)}else{e.lineCap="butt";return}if(f==="straight-triangle"){o.drawEdgeTrianglePath(t,e,s.allpts)}else{o.drawEdgePath(t,e,s.allpts,c);e.lineCap="butt"}};var w=function r(){if(!n){return}o.drawEdgeOverlay(e,t)};var E=function r(){if(!n){return}o.drawEdgeUnderlay(e,t)};var T=function r(){var a=arguments.length>0&&arguments[0]!==undefined?arguments[0]:m;o.drawArrowheads(e,t,a)};var k=function r(){o.drawElementText(e,t,null,a)};e.lineJoin="round";var C=t.pstyle("ghost").value==="yes";if(C){var P=t.pstyle("ghost-offset-x").pfValue;var S=t.pstyle("ghost-offset-y").pfValue;var D=t.pstyle("ghost-opacity").value;var B=y*D;e.translate(P,S);b(B);T(B);e.translate(-P,-S)}else{x()}E();b();T();w();k();if(r){e.translate(l.x1,l.y1)}};var uh=function e(t){if(!["overlay","underlay"].includes(t)){throw new Error("Invalid state")}return function(e,r){if(!r.visible()){return}var a=r.pstyle("".concat(t,"-opacity")).value;if(a===0){return}var n=this;var i=n.usePaths();var o=r._private.rscratch;var s=r.pstyle("".concat(t,"-padding")).pfValue;var l=2*s;var u=r.pstyle("".concat(t,"-color")).value;e.lineWidth=l;if(o.edgeType==="self"&&!i){e.lineCap="butt"}else{e.lineCap="round"}n.colorStrokeStyle(e,u[0],u[1],u[2],a);n.drawEdgePath(r,e,o.allpts,"solid")}};lh.drawEdgeOverlay=uh("overlay");lh.drawEdgeUnderlay=uh("underlay");lh.drawEdgePath=function(e,t,r,a){var n=e._private.rscratch;var i=t;var o;var s=false;var l=this.usePaths();var v=e.pstyle("line-dash-pattern").pfValue;var f=e.pstyle("line-dash-offset").pfValue;if(l){var c=r.join("$");var d=n.pathCacheKey&&n.pathCacheKey===c;if(d){o=t=n.pathCache;s=true}else{o=t=new Path2D;n.pathCacheKey=c;n.pathCache=o}}if(i.setLineDash){switch(a){case"dotted":i.setLineDash([1,1]);break;case"dashed":i.setLineDash(v);i.lineDashOffset=f;break;case"solid":i.setLineDash([]);break}}if(!s&&!n.badLine){if(t.beginPath){t.beginPath()}t.moveTo(r[0],r[1]);switch(n.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var h=2;h+3<r.length;h+=4){t.quadraticCurveTo(r[h],r[h+1],r[h+2],r[h+3])}break;case"straight":case"haystack":for(var p=2;p+1<r.length;p+=2){t.lineTo(r[p],r[p+1])}break;case"segments":if(n.isRound){var g=u(n.roundCorners),y;try{for(g.s();!(y=g.n()).done;){var m=y.value;Ic(t,m)}}catch(x){g.e(x)}finally{g.f()}t.lineTo(r[r.length-2],r[r.length-1])}else{for(var b=2;b+1<r.length;b+=2){t.lineTo(r[b],r[b+1])}}break}}t=i;if(l){t.stroke(o)}else{t.stroke()}if(t.setLineDash){t.setLineDash([])}};lh.drawEdgeTrianglePath=function(e,t,r){t.fillStyle=t.strokeStyle;var a=e.pstyle("width").pfValue;for(var n=0;n+1<r.length;n+=2){var i=[r[n+2]-r[n],r[n+3]-r[n+1]];var o=Math.sqrt(i[0]*i[0]+i[1]*i[1]);var s=[i[1]/o,-i[0]/o];var l=[s[0]*a/2,s[1]*a/2];t.beginPath();t.moveTo(r[n]-l[0],r[n+1]-l[1]);t.lineTo(r[n]+l[0],r[n+1]+l[1]);t.lineTo(r[n+2],r[n+3]);t.closePath();t.fill()}};lh.drawArrowheads=function(e,t,r){var a=t._private.rscratch;var n=a.edgeType==="haystack";if(!n){this.drawArrowhead(e,t,"source",a.arrowStartX,a.arrowStartY,a.srcArrowAngle,r)}this.drawArrowhead(e,t,"mid-target",a.midX,a.midY,a.midtgtArrowAngle,r);this.drawArrowhead(e,t,"mid-source",a.midX,a.midY,a.midsrcArrowAngle,r);if(!n){this.drawArrowhead(e,t,"target",a.arrowEndX,a.arrowEndY,a.tgtArrowAngle,r)}};lh.drawArrowhead=function(e,t,r,a,n,i,o){if(isNaN(a)||a==null||isNaN(n)||n==null||isNaN(i)||i==null){return}var s=this;var l=t.pstyle(r+"-arrow-shape").value;if(l==="none"){return}var u=t.pstyle(r+"-arrow-fill").value==="hollow"?"both":"filled";var v=t.pstyle(r+"-arrow-fill").value;var f=t.pstyle("width").pfValue;var c=t.pstyle(r+"-arrow-width");var d=c.value==="match-line"?f:c.pfValue;if(c.units==="%")d*=f;var h=t.pstyle("opacity").value;if(o===undefined){o=h}var p=e.globalCompositeOperation;if(o!==1||v==="hollow"){e.globalCompositeOperation="destination-out";s.colorFillStyle(e,255,255,255,1);s.colorStrokeStyle(e,255,255,255,1);s.drawArrowShape(t,e,u,f,l,d,a,n,i);e.globalCompositeOperation=p}var g=t.pstyle(r+"-arrow-color").value;s.colorFillStyle(e,g[0],g[1],g[2],o);s.colorStrokeStyle(e,g[0],g[1],g[2],o);s.drawArrowShape(t,e,v,f,l,d,a,n,i)};lh.drawArrowShape=function(e,t,r,a,n,i,o,s,l){var u=this;var v=this.usePaths()&&n!=="triangle-cross";var f=false;var c;var d=t;var h={x:o,y:s};var p=e.pstyle("arrow-scale").value;var g=this.getArrowWidth(a,p);var y=u.arrowShapes[n];if(v){var m=u.arrowPathCache=u.arrowPathCache||[];var b=Tt(n);var x=m[b];if(x!=null){c=t=x;f=true}else{c=t=new Path2D;m[b]=c}}if(!f){if(t.beginPath){t.beginPath()}if(v){y.draw(t,1,0,{x:0,y:0},1)}else{y.draw(t,g,l,h,a)}if(t.closePath){t.closePath()}}t=d;if(v){t.translate(o,s);t.rotate(l);t.scale(g,g)}if(r==="filled"||r==="both"){if(v){t.fill(c)}else{t.fill()}}if(r==="hollow"||r==="both"){t.lineWidth=i/(v?g:1);t.lineJoin="miter";if(v){t.stroke(c)}else{t.stroke()}}if(v){t.scale(1/g,1/g);t.rotate(-l);t.translate(-o,-s)}};var vh={};vh.safeDrawImage=function(e,t,r,a,n,i,o,s,l,u){if(n<=0||i<=0||l<=0||u<=0){return}try{e.drawImage(t,r,a,n,i,o,s,l,u)}catch(v){Lt(v)}};vh.drawInscribedImage=function(e,t,r,a,n){var i=this;var o=r.position();var s=o.x;var l=o.y;var u=r.cy().style();var v=u.getIndexedStyle.bind(u);var f=v(r,"background-fit","value",a);var c=v(r,"background-repeat","value",a);var d=r.width();var h=r.height();var p=r.padding()*2;var g=d+(v(r,"background-width-relative-to","value",a)==="inner"?0:p);var y=h+(v(r,"background-height-relative-to","value",a)==="inner"?0:p);var m=r._private.rscratch;var b=v(r,"background-clip","value",a);var x=b==="node";var w=v(r,"background-image-opacity","value",a)*n;var E=v(r,"background-image-smoothing","value",a);var T=r.pstyle("corner-radius").value;if(T!=="auto")T=r.pstyle("corner-radius").pfValue;var k=t.width||t.cachedW;var C=t.height||t.cachedH;if(null==k||null==C){document.body.appendChild(t);k=t.cachedW=t.width||t.offsetWidth;C=t.cachedH=t.height||t.offsetHeight;document.body.removeChild(t)}var P=k;var S=C;if(v(r,"background-width","value",a)!=="auto"){if(v(r,"background-width","units",a)==="%"){P=v(r,"background-width","pfValue",a)*g}else{P=v(r,"background-width","pfValue",a)}}if(v(r,"background-height","value",a)!=="auto"){if(v(r,"background-height","units",a)==="%"){S=v(r,"background-height","pfValue",a)*y}else{S=v(r,"background-height","pfValue",a)}}if(P===0||S===0){return}if(f==="contain"){var D=Math.min(g/P,y/S);P*=D;S*=D}else if(f==="cover"){var D=Math.max(g/P,y/S);P*=D;S*=D}var B=s-g/2;var A=v(r,"background-position-x","units",a);var _=v(r,"background-position-x","pfValue",a);if(A==="%"){B+=(g-P)*_}else{B+=_}var M=v(r,"background-offset-x","units",a);var I=v(r,"background-offset-x","pfValue",a);if(M==="%"){B+=(g-P)*I}else{B+=I}var R=l-y/2;var N=v(r,"background-position-y","units",a);var L=v(r,"background-position-y","pfValue",a);if(N==="%"){R+=(y-S)*L}else{R+=L}var O=v(r,"background-offset-y","units",a);var z=v(r,"background-offset-y","pfValue",a);if(O==="%"){R+=(y-S)*z}else{R+=z}if(m.pathCache){B-=s;R-=l;s=0;l=0}var F=e.globalAlpha;e.globalAlpha=w;var V=i.getImgSmoothing(e);var j=false;if(E==="no"&&V){i.setImgSmoothing(e,false);j=true}else if(E==="yes"&&!V){i.setImgSmoothing(e,true);j=true}if(c==="no-repeat"){if(x){e.save();if(m.pathCache){e.clip(m.pathCache)}else{i.nodeShapes[i.getNodeShape(r)].draw(e,s,l,g,y,T,m);e.clip()}}i.safeDrawImage(e,t,0,0,k,C,B,R,P,S);if(x){e.restore()}}else{var X=e.createPattern(t,c);e.fillStyle=X;i.nodeShapes[i.getNodeShape(r)].draw(e,s,l,g,y,T,m);e.translate(B,R);e.fill();e.translate(-B,-R)}e.globalAlpha=F;if(j){i.setImgSmoothing(e,V)}};var fh={};fh.eleTextBiggerThanMin=function(e,t){if(!t){var r=e.cy().zoom();var a=this.getPixelRatio();var n=Math.ceil(Nr(r*a));t=Math.pow(2,n)}var i=e.pstyle("font-size").pfValue*t;var o=e.pstyle("min-zoomed-font-size").pfValue;if(i<o){return false}return true};fh.drawElementText=function(e,t,r,a,n){var i=arguments.length>5&&arguments[5]!==undefined?arguments[5]:true;var o=this;if(a==null){if(i&&!o.eleTextBiggerThanMin(t)){return}}else if(a===false){return}if(t.isNode()){var s=t.pstyle("label");if(!s||!s.value){return}var l=o.getLabelJustification(t);e.textAlign=l;e.textBaseline="bottom"}else{var u=t.element()._private.rscratch.badLine;var v=t.pstyle("label");var f=t.pstyle("source-label");var c=t.pstyle("target-label");if(u||(!v||!v.value)&&(!f||!f.value)&&(!c||!c.value)){return}e.textAlign="center";e.textBaseline="bottom"}var d=!r;var h;if(r){h=r;e.translate(-h.x1,-h.y1)}if(n==null){o.drawText(e,t,null,d,i);if(t.isEdge()){o.drawText(e,t,"source",d,i);o.drawText(e,t,"target",d,i)}}else{o.drawText(e,t,n,d,i)}if(r){e.translate(h.x1,h.y1)}};fh.getFontCache=function(e){var t;this.fontCaches=this.fontCaches||[];for(var r=0;r<this.fontCaches.length;r++){t=this.fontCaches[r];if(t.context===e){return t}}t={context:e};this.fontCaches.push(t);return t};fh.setupTextStyle=function(e,t){var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:true;var a=t.pstyle("font-style").strValue;var n=t.pstyle("font-size").pfValue+"px";var i=t.pstyle("font-family").strValue;var o=t.pstyle("font-weight").strValue;var s=r?t.effectiveOpacity()*t.pstyle("text-opacity").value:1;var l=t.pstyle("text-outline-opacity").value*s;var u=t.pstyle("color").value;var v=t.pstyle("text-outline-color").value;e.font=a+" "+o+" "+n+" "+i;e.lineJoin="round";this.colorFillStyle(e,u[0],u[1],u[2],s);this.colorStrokeStyle(e,v[0],v[1],v[2],l)};function ch(e,t,r,a,n){var i=arguments.length>5&&arguments[5]!==undefined?arguments[5]:5;var o=arguments.length>6?arguments[6]:undefined;e.beginPath();e.moveTo(t+i,r);e.lineTo(t+a-i,r);e.quadraticCurveTo(t+a,r,t+a,r+i);e.lineTo(t+a,r+n-i);e.quadraticCurveTo(t+a,r+n,t+a-i,r+n);e.lineTo(t+i,r+n);e.quadraticCurveTo(t,r+n,t,r+n-i);e.lineTo(t,r+i);e.quadraticCurveTo(t,r,t+i,r);e.closePath();if(o)e.stroke();else e.fill()}fh.getTextAngle=function(e,t){var r;var a=e._private;var n=a.rscratch;var i=t?t+"-":"";var o=e.pstyle(i+"text-rotation");if(o.strValue==="autorotate"){var s=Gt(n,"labelAngle",t);r=e.isEdge()?s:0}else if(o.strValue==="none"){r=0}else{r=o.pfValue}return r};fh.drawText=function(e,t,r){var a=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;var n=arguments.length>4&&arguments[4]!==undefined?arguments[4]:true;var i=t._private;var o=i.rscratch;var s=n?t.effectiveOpacity():1;if(n&&(s===0||t.pstyle("text-opacity").value===0)){return}if(r==="main"){r=null}var l=Gt(o,"labelX",r);var u=Gt(o,"labelY",r);var v,f;var c=this.getLabelText(t,r);if(c!=null&&c!==""&&!isNaN(l)&&!isNaN(u)){this.setupTextStyle(e,t,n);var d=r?r+"-":"";var h=Gt(o,"labelWidth",r);var p=Gt(o,"labelHeight",r);var g=t.pstyle(d+"text-margin-x").pfValue;var y=t.pstyle(d+"text-margin-y").pfValue;var m=t.isEdge();var b=t.pstyle("text-halign").value;var x=t.pstyle("text-valign").value;if(m){b="center";x="center"}l+=g;u+=y;var w;if(!a){w=0}else{w=this.getTextAngle(t,r)}if(w!==0){v=l;f=u;e.translate(v,f);e.rotate(w);l=0;u=0}switch(x){case"top":break;case"center":u+=p/2;break;case"bottom":u+=p;break}var E=t.pstyle("text-background-opacity").value;var T=t.pstyle("text-border-opacity").value;var k=t.pstyle("text-border-width").pfValue;var C=t.pstyle("text-background-padding").pfValue;var P=t.pstyle("text-background-shape").strValue;var S=P.indexOf("round")===0;var D=2;if(E>0||k>0&&T>0){var B=l-C;switch(b){case"left":B-=h;break;case"center":B-=h/2;break}var A=u-p-C;var _=h+2*C;var M=p+2*C;if(E>0){var I=e.fillStyle;var R=t.pstyle("text-background-color").value;e.fillStyle="rgba("+R[0]+","+R[1]+","+R[2]+","+E*s+")";if(S){ch(e,B,A,_,M,D)}else{e.fillRect(B,A,_,M)}e.fillStyle=I}if(k>0&&T>0){var N=e.strokeStyle;var L=e.lineWidth;var O=t.pstyle("text-border-color").value;var z=t.pstyle("text-border-style").value;e.strokeStyle="rgba("+O[0]+","+O[1]+","+O[2]+","+T*s+")";e.lineWidth=k;if(e.setLineDash){switch(z){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"double":e.lineWidth=k/4;e.setLineDash([]);break;case"solid":e.setLineDash([]);break}}if(S){ch(e,B,A,_,M,D,"stroke")}else{e.strokeRect(B,A,_,M)}if(z==="double"){var F=k/2;if(S){ch(e,B+F,A+F,_-F*2,M-F*2,D,"stroke")}else{e.strokeRect(B+F,A+F,_-F*2,M-F*2)}}if(e.setLineDash){e.setLineDash([])}e.lineWidth=L;e.strokeStyle=N}}var V=2*t.pstyle("text-outline-width").pfValue;if(V>0){e.lineWidth=V}if(t.pstyle("text-wrap").value==="wrap"){var j=Gt(o,"labelWrapCachedLines",r);var X=Gt(o,"labelLineHeight",r);var Y=h/2;var q=this.getLabelJustification(t);if(q==="auto");else if(b==="left"){if(q==="left"){l+=-h}else if(q==="center"){l+=-Y}}else if(b==="center"){if(q==="left"){l+=-Y}else if(q==="right"){l+=Y}}else if(b==="right"){if(q==="center"){l+=Y}else if(q==="right"){l+=h}}switch(x){case"top":u-=(j.length-1)*X;break;case"center":case"bottom":u-=(j.length-1)*X;break}for(var W=0;W<j.length;W++){if(V>0){e.strokeText(j[W],l,u)}e.fillText(j[W],l,u);u+=X}}else{if(V>0){e.strokeText(c,l,u)}e.fillText(c,l,u)}if(w!==0){e.rotate(-w);e.translate(-v,-f)}}};var dh={};dh.drawNode=function(e,t,r){var a=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;var n=arguments.length>4&&arguments[4]!==undefined?arguments[4]:true;var i=arguments.length>5&&arguments[5]!==undefined?arguments[5]:true;var o=this;var s,l;var u=t._private;var v=u.rscratch;var f=t.position();if(!I(f.x)||!I(f.y)){return}if(i&&!t.visible()){return}var c=i?t.effectiveOpacity():1;var d=o.usePaths();var h;var p=false;var g=t.padding();s=t.width()+2*g;l=t.height()+2*g;var y;if(r){y=r;e.translate(-y.x1,-y.y1)}var m=t.pstyle("background-image");var b=m.value;var x=new Array(b.length);var w=new Array(b.length);var E=0;for(var T=0;T<b.length;T++){var k=b[T];var C=x[T]=k!=null&&k!=="none";if(C){var P=t.cy().style().getIndexedStyle(t,"background-image-crossorigin","value",T);E++;w[T]=o.getCachedImage(k,P,(function(){u.backgroundTimestamp=Date.now();t.emitAndNotify("background")}))}}var S=t.pstyle("background-blacken").value;var D=t.pstyle("border-width").pfValue;var B=t.pstyle("background-opacity").value*c;var A=t.pstyle("border-color").value;var _=t.pstyle("border-style").value;var M=t.pstyle("border-join").value;var R=t.pstyle("border-cap").value;var N=t.pstyle("border-position").value;var L=t.pstyle("border-dash-pattern").pfValue;var O=t.pstyle("border-dash-offset").pfValue;var z=t.pstyle("border-opacity").value*c;var F=t.pstyle("outline-width").pfValue;var V=t.pstyle("outline-color").value;var j=t.pstyle("outline-style").value;var X=t.pstyle("outline-opacity").value*c;var Y=t.pstyle("outline-offset").value;var q=t.pstyle("corner-radius").value;if(q!=="auto")q=t.pstyle("corner-radius").pfValue;var W=function r(){var a=arguments.length>0&&arguments[0]!==undefined?arguments[0]:B;o.eleFillStyle(e,t,a)};var U=function t(){var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:z;o.colorStrokeStyle(e,A[0],A[1],A[2],r)};var G=function t(){var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:X;o.colorStrokeStyle(e,V[0],V[1],V[2],r)};var H=function e(t,r,a,n){var i=o.nodePathCache=o.nodePathCache||[];var s=kt(a==="polygon"?a+","+n.join(","):a,""+r,""+t,""+q);var l=i[s];var u;var f=false;if(l!=null){u=l;f=true;v.pathCache=u}else{u=new Path2D;i[s]=v.pathCache=u}return{path:u,cacheHit:f}};var K=t.pstyle("shape").strValue;var Z=t.pstyle("shape-polygon-points").pfValue;if(d){e.translate(f.x,f.y);var $=H(s,l,K,Z);h=$.path;p=$.cacheHit}var Q=function r(){if(!p){var a=f;if(d){a={x:0,y:0}}o.nodeShapes[o.getNodeShape(t)].draw(h||e,a.x,a.y,s,l,q,v)}if(d){e.fill(h)}else{e.fill()}};var J=function r(){var a=arguments.length>0&&arguments[0]!==undefined?arguments[0]:c;var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;var i=u.backgrounding;var s=0;for(var l=0;l<w.length;l++){var v=t.cy().style().getIndexedStyle(t,"background-image-containment","value",l);if(n&&v==="over"||!n&&v==="inside"){s++;continue}if(x[l]&&w[l].complete&&!w[l].error){s++;o.drawInscribedImage(e,w[l],t,l,a)}}u.backgrounding=!(s===E);if(i!==u.backgrounding){t.updateStyle(false)}};var ee=function r(){var a=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:c;if(o.hasPie(t)){o.drawPie(e,t,n);if(a){if(!d){o.nodeShapes[o.getNodeShape(t)].draw(e,f.x,f.y,s,l,q,v)}}}};var te=function t(){var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:c;var a=(S>0?S:-S)*r;var n=S>0?0:255;if(S!==0){o.colorFillStyle(e,n,n,n,a);if(d){e.fill(h)}else{e.fill()}}};var re=function t(){if(D>0){e.lineWidth=D;e.lineCap=R;e.lineJoin=M;if(e.setLineDash){switch(_){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash(L);e.lineDashOffset=O;break;case"solid":case"double":e.setLineDash([]);break}}if(N!=="center"){e.save();e.lineWidth*=2;if(N==="inside"){d?e.clip(h):e.clip()}else{var r=new Path2D;r.rect(-s/2-D,-l/2-D,s+2*D,l+2*D);r.addPath(h);e.clip(r,"evenodd")}d?e.stroke(h):e.stroke();e.restore()}else{d?e.stroke(h):e.stroke()}if(_==="double"){e.lineWidth=D/3;var a=e.globalCompositeOperation;e.globalCompositeOperation="destination-out";if(d){e.stroke(h)}else{e.stroke()}e.globalCompositeOperation=a}if(e.setLineDash){e.setLineDash([])}}};var ae=function r(){if(F>0){e.lineWidth=F;e.lineCap="butt";if(e.setLineDash){switch(j){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"solid":case"double":e.setLineDash([]);break}}var a=f;if(d){a={x:0,y:0}}var n=o.getNodeShape(t);var i=D;if(N==="inside")i=0;if(N==="outside")i*=2;var u=(s+i+(F+Y))/s;var v=(l+i+(F+Y))/l;var c=s*u;var h=l*v;var p=o.nodeShapes[n].points;var g;if(d){var y=H(c,h,n,p);g=y.path}if(n==="ellipse"){o.drawEllipsePath(g||e,a.x,a.y,c,h)}else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(n)){var m=0;var b=0;var x=0;if(n==="round-diamond"){m=(i+Y+F)*1.4}else if(n==="round-heptagon"){m=(i+Y+F)*1.075;x=-(i/2+Y+F)/35}else if(n==="round-hexagon"){m=(i+Y+F)*1.12}else if(n==="round-pentagon"){m=(i+Y+F)*1.13;x=-(i/2+Y+F)/15}else if(n==="round-tag"){m=(i+Y+F)*1.12;b=(i/2+F+Y)*.07}else if(n==="round-triangle"){m=(i+Y+F)*(Math.PI/2);x=-(i+Y/2+F)/Math.PI}if(m!==0){u=(s+m)/s;c=s*u;if(!["round-hexagon","round-tag"].includes(n)){v=(l+m)/l;h=l*v}}q=q==="auto"?Sa(c,h):q;var w=c/2;var E=h/2;var T=q+(i+F+Y)/2;var k=new Array(p.length/2);var C=new Array(p.length/2);for(var P=0;P<p.length/2;P++){k[P]={x:a.x+b+w*p[P*2],y:a.y+x+E*p[P*2+1]}}var S,B,A,_,M=k.length;B=k[M-1];for(S=0;S<M;S++){A=k[S%M];_=k[(S+1)%M];C[S]=Rc(B,A,_,T);B=A;A=_}o.drawRoundPolygonPath(g||e,a.x+b,a.y+x,s*u,l*v,p,C)}else if(["roundrectangle","round-rectangle"].includes(n)){q=q==="auto"?Pa(c,h):q;o.drawRoundRectanglePath(g||e,a.x,a.y,c,h,q+(i+F+Y)/2)}else if(["cutrectangle","cut-rectangle"].includes(n)){q=q==="auto"?Da():q;o.drawCutRectanglePath(g||e,a.x,a.y,c,h,null,q+(i+F+Y)/4)}else if(["bottomroundrectangle","bottom-round-rectangle"].includes(n)){q=q==="auto"?Pa(c,h):q;o.drawBottomRoundRectanglePath(g||e,a.x,a.y,c,h,q+(i+F+Y)/2)}else if(n==="barrel"){o.drawBarrelPath(g||e,a.x,a.y,c,h)}else if(n.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(n)){var I=(i+F+Y)/s;p=da(ha(p,I));o.drawPolygonPath(g||e,a.x,a.y,s,l,p)}else{var R=(i+F+Y)/s;p=da(ha(p,-R));o.drawPolygonPath(g||e,a.x,a.y,s,l,p)}if(d){e.stroke(g)}else{e.stroke()}if(j==="double"){e.lineWidth=i/3;var L=e.globalCompositeOperation;e.globalCompositeOperation="destination-out";if(d){e.stroke(g)}else{e.stroke()}e.globalCompositeOperation=L}if(e.setLineDash){e.setLineDash([])}}};var ne=function r(){if(n){o.drawNodeOverlay(e,t,f,s,l)}};var ie=function r(){if(n){o.drawNodeUnderlay(e,t,f,s,l)}};var oe=function r(){o.drawElementText(e,t,null,a)};var se=t.pstyle("ghost").value==="yes";if(se){var le=t.pstyle("ghost-offset-x").pfValue;var ue=t.pstyle("ghost-offset-y").pfValue;var ve=t.pstyle("ghost-opacity").value;var fe=ve*c;e.translate(le,ue);G();ae();W(ve*B);Q();J(fe,true);U(ve*z);re();ee(S!==0||D!==0);J(fe,false);te(fe);e.translate(-le,-ue)}if(d){e.translate(-f.x,-f.y)}ie();if(d){e.translate(f.x,f.y)}G();ae();W();Q();J(c,true);U();re();ee(S!==0||D!==0);J(c,false);te();if(d){e.translate(-f.x,-f.y)}oe();ne();if(r){e.translate(y.x1,y.y1)}};var hh=function e(t){if(!["overlay","underlay"].includes(t)){throw new Error("Invalid state")}return function(e,r,a,n,i){var o=this;if(!r.visible()){return}var s=r.pstyle("".concat(t,"-padding")).pfValue;var l=r.pstyle("".concat(t,"-opacity")).value;var u=r.pstyle("".concat(t,"-color")).value;var v=r.pstyle("".concat(t,"-shape")).value;var f=r.pstyle("".concat(t,"-corner-radius")).value;if(l>0){a=a||r.position();if(n==null||i==null){var c=r.padding();n=r.width()+2*c;i=r.height()+2*c}o.colorFillStyle(e,u[0],u[1],u[2],l);o.nodeShapes[v].draw(e,a.x,a.y,n+s*2,i+s*2,f);e.fill()}}};dh.drawNodeOverlay=hh("overlay");dh.drawNodeUnderlay=hh("underlay");dh.hasPie=function(e){e=e[0];return e._private.hasPie};dh.drawPie=function(e,t,r,a){t=t[0];a=a||t.position();var n=t.cy().style();var i=t.pstyle("pie-size");var o=a.x;var s=a.y;var l=t.width();var u=t.height();var v=Math.min(l,u)/2;var f=0;var c=this.usePaths();if(c){o=0;s=0}if(i.units==="%"){v=v*i.pfValue}else if(i.pfValue!==undefined){v=i.pfValue/2}for(var d=1;d<=n.pieBackgroundN;d++){var h=t.pstyle("pie-"+d+"-background-size").value;var p=t.pstyle("pie-"+d+"-background-color").value;var g=t.pstyle("pie-"+d+"-background-opacity").value*r;var y=h/100;if(y+f>1){y=1-f}var m=1.5*Math.PI+2*Math.PI*f;var b=2*Math.PI*y;var x=m+b;if(h===0||f>=1||f+y>1){continue}e.beginPath();e.moveTo(o,s);e.arc(o,s,v,m,x);e.closePath();this.colorFillStyle(e,p[0],p[1],p[2],g);e.fill();f+=y}};var ph={};var gh=100;ph.getPixelRatio=function(){var e=this.data.contexts[0];if(this.forcedPixelRatio!=null){return this.forcedPixelRatio}var t=this.cy.window();var r=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(t.devicePixelRatio||1)/r};ph.paintCache=function(e){var t=this.paintCaches=this.paintCaches||[];var r=true;var a;for(var n=0;n<t.length;n++){a=t[n];if(a.context===e){r=false;break}}if(r){a={context:e};t.push(a)}return a};ph.createGradientStyleFor=function(e,t,r,a,n){var i;var o=this.usePaths();var s=r.pstyle(t+"-gradient-stop-colors").value,l=r.pstyle(t+"-gradient-stop-positions").pfValue;if(a==="radial-gradient"){if(r.isEdge()){var u=r.sourceEndpoint(),v=r.targetEndpoint(),f=r.midpoint();var c=Or(u,f);var d=Or(v,f);i=e.createRadialGradient(f.x,f.y,0,f.x,f.y,Math.max(c,d))}else{var h=o?{x:0,y:0}:r.position(),p=r.paddedWidth(),g=r.paddedHeight();i=e.createRadialGradient(h.x,h.y,0,h.x,h.y,Math.max(p,g))}}else{if(r.isEdge()){var y=r.sourceEndpoint(),m=r.targetEndpoint();i=e.createLinearGradient(y.x,y.y,m.x,m.y)}else{var b=o?{x:0,y:0}:r.position(),x=r.paddedWidth(),w=r.paddedHeight(),E=x/2,T=w/2;var k=r.pstyle("background-gradient-direction").value;switch(k){case"to-bottom":i=e.createLinearGradient(b.x,b.y-T,b.x,b.y+T);break;case"to-top":i=e.createLinearGradient(b.x,b.y+T,b.x,b.y-T);break;case"to-left":i=e.createLinearGradient(b.x+E,b.y,b.x-E,b.y);break;case"to-right":i=e.createLinearGradient(b.x-E,b.y,b.x+E,b.y);break;case"to-bottom-right":case"to-right-bottom":i=e.createLinearGradient(b.x-E,b.y-T,b.x+E,b.y+T);break;case"to-top-right":case"to-right-top":i=e.createLinearGradient(b.x-E,b.y+T,b.x+E,b.y-T);break;case"to-bottom-left":case"to-left-bottom":i=e.createLinearGradient(b.x+E,b.y-T,b.x-E,b.y+T);break;case"to-top-left":case"to-left-top":i=e.createLinearGradient(b.x+E,b.y+T,b.x-E,b.y-T);break}}}if(!i)return null;var C=l.length===s.length;var P=s.length;for(var S=0;S<P;S++){i.addColorStop(C?l[S]:S/(P-1),"rgba("+s[S][0]+","+s[S][1]+","+s[S][2]+","+n+")")}return i};ph.gradientFillStyle=function(e,t,r,a){var n=this.createGradientStyleFor(e,"background",t,r,a);if(!n)return null;e.fillStyle=n};ph.colorFillStyle=function(e,t,r,a,n){e.fillStyle="rgba("+t+","+r+","+a+","+n+")"};ph.eleFillStyle=function(e,t,r){var a=t.pstyle("background-fill").value;if(a==="linear-gradient"||a==="radial-gradient"){this.gradientFillStyle(e,t,a,r)}else{var n=t.pstyle("background-color").value;this.colorFillStyle(e,n[0],n[1],n[2],r)}};ph.gradientStrokeStyle=function(e,t,r,a){var n=this.createGradientStyleFor(e,"line",t,r,a);if(!n)return null;e.strokeStyle=n};ph.colorStrokeStyle=function(e,t,r,a,n){e.strokeStyle="rgba("+t+","+r+","+a+","+n+")"};ph.eleStrokeStyle=function(e,t,r){var a=t.pstyle("line-fill").value;if(a==="linear-gradient"||a==="radial-gradient"){this.gradientStrokeStyle(e,t,a,r)}else{var n=t.pstyle("line-color").value;this.colorStrokeStyle(e,n[0],n[1],n[2],r)}};ph.matchCanvasSize=function(e){var t=this;var r=t.data;var a=t.findContainerClientCoords();var n=a[2];var i=a[3];var o=t.getPixelRatio();var s=t.motionBlurPxRatio;if(e===t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_NODE]||e===t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_DRAG]){o=s}var l=n*o;var u=i*o;var v;if(l===t.canvasWidth&&u===t.canvasHeight){return}t.fontCaches=null;var f=r.canvasContainer;f.style.width=n+"px";f.style.height=i+"px";for(var c=0;c<t.CANVAS_LAYERS;c++){v=r.canvases[c];v.width=l;v.height=u;v.style.width=n+"px";v.style.height=i+"px"}for(var c=0;c<t.BUFFER_COUNT;c++){v=r.bufferCanvases[c];v.width=l;v.height=u;v.style.width=n+"px";v.style.height=i+"px"}t.textureMult=1;if(o<=1){v=r.bufferCanvases[t.TEXTURE_BUFFER];t.textureMult=2;v.width=l*t.textureMult;v.height=u*t.textureMult}t.canvasWidth=l;t.canvasHeight=u;t.pixelRatio=o};ph.renderTo=function(e,t,r,a){this.render({forcedContext:e,forcedZoom:t,forcedPan:r,drawAllLayers:true,forcedPxRatio:a})};ph.clearCanvas=function(){var e=this;var t=e.data;function r(t){t.clearRect(0,0,e.canvasWidth,e.canvasHeight)}r(t.contexts[e.NODE]);r(t.contexts[e.DRAG])};ph.render=function(e){var t=this;e=e||Xt();var r=t.cy;var a=e.forcedContext;var n=e.drawAllLayers;var i=e.drawOnlyNodeLayer;var o=e.forcedZoom;var s=e.forcedPan;var l=e.forcedPxRatio===undefined?this.getPixelRatio():e.forcedPxRatio;var u=t.data;var v=u.canvasNeedsRedraw;var f=t.textureOnViewport&&!a&&(t.pinching||t.hoverData.dragging||t.swipePanning||t.data.wheelZooming);var c=e.motionBlur!==undefined?e.motionBlur:t.motionBlur;var d=t.motionBlurPxRatio;var h=r.hasCompoundNodes();var p=t.hoverData.draggingEles;var g=t.hoverData.selecting||t.touchData.selecting?true:false;c=c&&!a&&t.motionBlurEnabled&&!g;var y=c;if(!a){if(t.prevPxRatio!==l){t.invalidateContainerClientCoordsCache();t.matchCanvasSize(t.container);t.redrawHint("eles",true);t.redrawHint("drag",true)}t.prevPxRatio=l}if(!a&&t.motionBlurTimeout){clearTimeout(t.motionBlurTimeout)}if(c){if(t.mbFrames==null){t.mbFrames=0}t.mbFrames++;if(t.mbFrames<3){y=false}if(t.mbFrames>t.minMbLowQualFrames){t.motionBlurPxRatio=t.mbPxRBlurry}}if(t.clearingMotionBlur){t.motionBlurPxRatio=1}if(t.textureDrawLastFrame&&!f){v[t.NODE]=true;v[t.SELECT_BOX]=true}var m=r.style();var b=r.zoom();var x=o!==undefined?o:b;var w=r.pan();var E={x:w.x,y:w.y};var T={zoom:b,pan:{x:w.x,y:w.y}};var k=t.prevViewport;var C=k===undefined||T.zoom!==k.zoom||T.pan.x!==k.pan.x||T.pan.y!==k.pan.y;if(!C&&!(p&&!h)){t.motionBlurPxRatio=1}if(s){E=s}x*=l;E.x*=l;E.y*=l;var P=t.getCachedZSortedEles();function S(e,r,a,n,i){var o=e.globalCompositeOperation;e.globalCompositeOperation="destination-out";t.colorFillStyle(e,255,255,255,t.motionBlurTransparency);e.fillRect(r,a,n,i);e.globalCompositeOperation=o}function D(e,r){var i,l,v,f;if(!t.clearingMotionBlur&&(e===u.bufferContexts[t.MOTIONBLUR_BUFFER_NODE]||e===u.bufferContexts[t.MOTIONBLUR_BUFFER_DRAG])){i={x:w.x*d,y:w.y*d};l=b*d;v=t.canvasWidth*d;f=t.canvasHeight*d}else{i=E;l=x;v=t.canvasWidth;f=t.canvasHeight}e.setTransform(1,0,0,1,0,0);if(r==="motionBlur"){S(e,0,0,v,f)}else if(!a&&(r===undefined||r)){e.clearRect(0,0,v,f)}if(!n){e.translate(i.x,i.y);e.scale(l,l)}if(s){e.translate(s.x,s.y)}if(o){e.scale(o,o)}}if(!f){t.textureDrawLastFrame=false}if(f){t.textureDrawLastFrame=true;if(!t.textureCache){t.textureCache={};t.textureCache.bb=r.mutableElements().boundingBox();t.textureCache.texture=t.data.bufferCanvases[t.TEXTURE_BUFFER];var B=t.data.bufferContexts[t.TEXTURE_BUFFER];B.setTransform(1,0,0,1,0,0);B.clearRect(0,0,t.canvasWidth*t.textureMult,t.canvasHeight*t.textureMult);t.render({forcedContext:B,drawOnlyNodeLayer:true,forcedPxRatio:l*t.textureMult});var T=t.textureCache.viewport={zoom:r.zoom(),pan:r.pan(),width:t.canvasWidth,height:t.canvasHeight};T.mpan={x:(0-T.pan.x)/T.zoom,y:(0-T.pan.y)/T.zoom}}v[t.DRAG]=false;v[t.NODE]=false;var A=u.contexts[t.NODE];var _=t.textureCache.texture;var T=t.textureCache.viewport;A.setTransform(1,0,0,1,0,0);if(c){S(A,0,0,T.width,T.height)}else{A.clearRect(0,0,T.width,T.height)}var M=m.core("outside-texture-bg-color").value;var I=m.core("outside-texture-bg-opacity").value;t.colorFillStyle(A,M[0],M[1],M[2],I);A.fillRect(0,0,T.width,T.height);var b=r.zoom();D(A,false);A.clearRect(T.mpan.x,T.mpan.y,T.width/T.zoom/l,T.height/T.zoom/l);A.drawImage(_,T.mpan.x,T.mpan.y,T.width/T.zoom/l,T.height/T.zoom/l)}else if(t.textureOnViewport&&!a){t.textureCache=null}var R=r.extent();var N=t.pinching||t.hoverData.dragging||t.swipePanning||t.data.wheelZooming||t.hoverData.draggingEles||t.cy.animated();var L=t.hideEdgesOnViewport&&N;var O=[];O[t.NODE]=!v[t.NODE]&&c&&!t.clearedForMotionBlur[t.NODE]||t.clearingMotionBlur;if(O[t.NODE]){t.clearedForMotionBlur[t.NODE]=true}O[t.DRAG]=!v[t.DRAG]&&c&&!t.clearedForMotionBlur[t.DRAG]||t.clearingMotionBlur;if(O[t.DRAG]){t.clearedForMotionBlur[t.DRAG]=true}if(v[t.NODE]||n||i||O[t.NODE]){var z=c&&!O[t.NODE]&&d!==1;var A=a||(z?t.data.bufferContexts[t.MOTIONBLUR_BUFFER_NODE]:u.contexts[t.NODE]);var F=c&&!z?"motionBlur":undefined;D(A,F);if(L){t.drawCachedNodes(A,P.nondrag,l,R)}else{t.drawLayeredElements(A,P.nondrag,l,R)}if(t.debug){t.drawDebugPoints(A,P.nondrag)}if(!n&&!c){v[t.NODE]=false}}if(!i&&(v[t.DRAG]||n||O[t.DRAG])){var z=c&&!O[t.DRAG]&&d!==1;var A=a||(z?t.data.bufferContexts[t.MOTIONBLUR_BUFFER_DRAG]:u.contexts[t.DRAG]);D(A,c&&!z?"motionBlur":undefined);if(L){t.drawCachedNodes(A,P.drag,l,R)}else{t.drawCachedElements(A,P.drag,l,R)}if(t.debug){t.drawDebugPoints(A,P.drag)}if(!n&&!c){v[t.DRAG]=false}}this.drawSelectionRectangle(e,D);if(c&&d!==1){var V=u.contexts[t.NODE];var j=t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_NODE];var X=u.contexts[t.DRAG];var Y=t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_DRAG];var q=function e(r,a,n){r.setTransform(1,0,0,1,0,0);if(n||!y){r.clearRect(0,0,t.canvasWidth,t.canvasHeight)}else{S(r,0,0,t.canvasWidth,t.canvasHeight)}var i=d;r.drawImage(a,0,0,t.canvasWidth*i,t.canvasHeight*i,0,0,t.canvasWidth,t.canvasHeight)};if(v[t.NODE]||O[t.NODE]){q(V,j,O[t.NODE]);v[t.NODE]=false}if(v[t.DRAG]||O[t.DRAG]){q(X,Y,O[t.DRAG]);v[t.DRAG]=false}}t.prevViewport=T;if(t.clearingMotionBlur){t.clearingMotionBlur=false;t.motionBlurCleared=true;t.motionBlur=true}if(c){t.motionBlurTimeout=setTimeout((function(){t.motionBlurTimeout=null;t.clearedForMotionBlur[t.NODE]=false;t.clearedForMotionBlur[t.DRAG]=false;t.motionBlur=false;t.clearingMotionBlur=!f;t.mbFrames=0;v[t.NODE]=true;v[t.DRAG]=true;t.redraw()}),gh)}if(!a){r.emit("render")}};var yh;ph.drawSelectionRectangle=function(e,t){var r=this;var a=r.cy;var n=r.data;var i=a.style();var o=e.drawOnlyNodeLayer;var s=e.drawAllLayers;var l=n.canvasNeedsRedraw;var u=e.forcedContext;if(r.showFps||!o&&l[r.SELECT_BOX]&&!s){var v=u||n.contexts[r.SELECT_BOX];t(v);if(r.selection[4]==1&&(r.hoverData.selecting||r.touchData.selecting)){var f=r.cy.zoom();var c=i.core("selection-box-border-width").value/f;v.lineWidth=c;v.fillStyle="rgba("+i.core("selection-box-color").value[0]+","+i.core("selection-box-color").value[1]+","+i.core("selection-box-color").value[2]+","+i.core("selection-box-opacity").value+")";v.fillRect(r.selection[0],r.selection[1],r.selection[2]-r.selection[0],r.selection[3]-r.selection[1]);if(c>0){v.strokeStyle="rgba("+i.core("selection-box-border-color").value[0]+","+i.core("selection-box-border-color").value[1]+","+i.core("selection-box-border-color").value[2]+","+i.core("selection-box-opacity").value+")";v.strokeRect(r.selection[0],r.selection[1],r.selection[2]-r.selection[0],r.selection[3]-r.selection[1])}}if(n.bgActivePosistion&&!r.hoverData.selecting){var f=r.cy.zoom();var d=n.bgActivePosistion;v.fillStyle="rgba("+i.core("active-bg-color").value[0]+","+i.core("active-bg-color").value[1]+","+i.core("active-bg-color").value[2]+","+i.core("active-bg-opacity").value+")";v.beginPath();v.arc(d.x,d.y,i.core("active-bg-size").pfValue/f,0,2*Math.PI);v.fill()}var h=r.lastRedrawTime;if(r.showFps&&h){h=Math.round(h);var p=Math.round(1e3/h);var g="1 frame = "+h+" ms = "+p+" fps";v.setTransform(1,0,0,1,0,0);v.fillStyle="rgba(255, 0, 0, 0.75)";v.strokeStyle="rgba(255, 0, 0, 0.75)";v.font="30px Arial";if(!yh){var y=v.measureText(g);yh=y.actualBoundingBoxAscent}v.fillText(g,0,yh);var m=60;v.strokeRect(0,yh+10,250,20);v.fillRect(0,yh+10,250*Math.min(p/m,1),20)}if(!s){l[r.SELECT_BOX]=false}}};function mh(e,t,r){var a=e.createShader(t);e.shaderSource(a,r);e.compileShader(a);if(!e.getShaderParameter(a,e.COMPILE_STATUS)){throw new Error(e.getShaderInfoLog(a))}return a}function bh(e,t,r){var a=mh(e,e.VERTEX_SHADER,t);var n=mh(e,e.FRAGMENT_SHADER,r);var i=e.createProgram();e.attachShader(i,a);e.attachShader(i,n);e.linkProgram(i);if(!e.getProgramParameter(i,e.LINK_STATUS)){throw new Error("Could not initialize shaders")}return i}function xh(e,t,r){if(r===undefined){r=t}var a=e.makeOffscreenCanvas(t,r);var n=a.context=a.getContext("2d");a.clear=function(){return n.clearRect(0,0,a.width,a.height)};a.clear();return a}function wh(e){var t=e.pixelRatio;var r=e.cy.zoom();var a=e.cy.pan();return{zoom:r*t,pan:{x:a.x*t,y:a.y*t}}}function Eh(e,t,r,a,n){var i=a*r+t.x;var o=n*r+t.y;o=Math.round(e.canvasHeight-o);return[i,o]}function Th(e,t,r){var a=e[0]/255;var n=e[1]/255;var i=e[2]/255;var o=t;var s=r||new Array(4);s[0]=a*o;s[1]=n*o;s[2]=i*o;s[3]=o;return s}function kh(e,t){var r=t||new Array(4);r[0]=(e>>0&255)/255;r[1]=(e>>8&255)/255;r[2]=(e>>16&255)/255;r[3]=(e>>24&255)/255;return r}function Ch(e){return e[0]+(e[1]<<8)+(e[2]<<16)+(e[3]<<24)}function Ph(e,t){var r=e.createTexture();r.buffer=function(t){e.bindTexture(e.TEXTURE_2D,r);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR_MIPMAP_NEAREST);e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,true);e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t);e.generateMipmap(e.TEXTURE_2D);e.bindTexture(e.TEXTURE_2D,null)};r.deleteTexture=function(){e.deleteTexture(r)};return r}function Sh(e,t){switch(t){case"float":return[1,e.FLOAT,4];case"vec2":return[2,e.FLOAT,4];case"vec3":return[3,e.FLOAT,4];case"vec4":return[4,e.FLOAT,4];case"int":return[1,e.INT,4];case"ivec2":return[2,e.INT,4]}}function Dh(e,t,r){switch(t){case e.FLOAT:return new Float32Array(r);case e.INT:return new Int32Array(r)}}function Bh(e,t,r,a,n,i){switch(t){case e.FLOAT:return new Float32Array(r.buffer,i*a,n);case e.INT:return new Int32Array(r.buffer,i*a,n)}}function Ah(e,t,r,a){var n=Sh(e,t),i=p(n,2),o=i[0],s=i[1];var l=Dh(e,s,a);var u=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,u);e.bufferData(e.ARRAY_BUFFER,l,e.STATIC_DRAW);if(s===e.FLOAT){e.vertexAttribPointer(r,o,s,false,0,0)}else if(s===e.INT){e.vertexAttribIPointer(r,o,s,0,0)}e.enableVertexAttribArray(r);e.bindBuffer(e.ARRAY_BUFFER,null);return u}function _h(e,t,r,a){var n=Sh(e,r),i=p(n,3),o=i[0],s=i[1],l=i[2];var u=Dh(e,s,t*o);var v=o*l;var f=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,f);e.bufferData(e.ARRAY_BUFFER,t*v,e.DYNAMIC_DRAW);e.enableVertexAttribArray(a);if(s===e.FLOAT){e.vertexAttribPointer(a,o,s,false,v,0)}else if(s===e.INT){e.vertexAttribIPointer(a,o,s,v,0)}e.vertexAttribDivisor(a,1);e.bindBuffer(e.ARRAY_BUFFER,null);var c=new Array(t);for(var d=0;d<t;d++){c[d]=Bh(e,s,u,v,o,d)}f.dataArray=u;f.stride=v;f.size=o;f.getView=function(e){return c[e]};f.setPoint=function(e,t,r){var a=c[e];a[0]=t;a[1]=r};f.bufferSubData=function(t){e.bindBuffer(e.ARRAY_BUFFER,f);if(t){e.bufferSubData(e.ARRAY_BUFFER,0,u,0,t*o)}else{e.bufferSubData(e.ARRAY_BUFFER,0,u)}};return f}function Mh(e,t,r){var a=9;var n=new Float32Array(t*a);var i=new Array(t);for(var o=0;o<t;o++){var s=o*a*4;i[o]=new Float32Array(n.buffer,s,a)}var l=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,l);e.bufferData(e.ARRAY_BUFFER,n.byteLength,e.DYNAMIC_DRAW);for(var u=0;u<3;u++){var v=r+u;e.enableVertexAttribArray(v);e.vertexAttribPointer(v,3,e.FLOAT,false,3*12,u*12);e.vertexAttribDivisor(v,1)}e.bindBuffer(e.ARRAY_BUFFER,null);l.getMatrixView=function(e){return i[e]};l.setData=function(e,t){i[t].set(e,0)};l.bufferSubData=function(){e.bindBuffer(e.ARRAY_BUFFER,l);e.bufferSubData(e.ARRAY_BUFFER,0,n)};return l}function Ih(e){var t=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,t);var r=e.createTexture();e.bindTexture(e.TEXTURE_2D,r);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE);e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,r,0);e.bindFramebuffer(e.FRAMEBUFFER,null);t.setFramebufferAttachmentSizes=function(t,a){e.bindTexture(e.TEXTURE_2D,r);e.texImage2D(e.TEXTURE_2D,0,e.RGBA,t,a,0,e.RGBA,e.UNSIGNED_BYTE,null)};return t}var Rh=typeof Float32Array!=="undefined"?Float32Array:Array;if(!Math.hypot)Math.hypot=function(){var e=0,t=arguments.length;while(t--){e+=arguments[t]*arguments[t]}return Math.sqrt(e)};function Nh(){var e=new Rh(9);if(Rh!=Float32Array){e[1]=0;e[2]=0;e[3]=0;e[5]=0;e[6]=0;e[7]=0}e[0]=1;e[4]=1;e[8]=1;return e}function Lh(e){e[0]=1;e[1]=0;e[2]=0;e[3]=0;e[4]=1;e[5]=0;e[6]=0;e[7]=0;e[8]=1;return e}function Oh(e,t,r){var a=t[0],n=t[1],i=t[2];var o=t[3],s=t[4],l=t[5];var u=t[6],v=t[7],f=t[8];var c=r[0],d=r[1],h=r[2];var p=r[3],g=r[4],y=r[5];var m=r[6],b=r[7],x=r[8];e[0]=c*a+d*o+h*u;e[1]=c*n+d*s+h*v;e[2]=c*i+d*l+h*f;e[3]=p*a+g*o+y*u;e[4]=p*n+g*s+y*v;e[5]=p*i+g*l+y*f;e[6]=m*a+b*o+x*u;e[7]=m*n+b*s+x*v;e[8]=m*i+b*l+x*f;return e}function zh(e,t,r){var a=t[0],n=t[1],i=t[2],o=t[3],s=t[4],l=t[5],u=t[6],v=t[7],f=t[8],c=r[0],d=r[1];e[0]=a;e[1]=n;e[2]=i;e[3]=o;e[4]=s;e[5]=l;e[6]=c*a+d*o+u;e[7]=c*n+d*s+v;e[8]=c*i+d*l+f;return e}function Fh(e,t,r){var a=t[0],n=t[1],i=t[2],o=t[3],s=t[4],l=t[5],u=t[6],v=t[7],f=t[8],c=Math.sin(r),d=Math.cos(r);e[0]=d*a+c*o;e[1]=d*n+c*s;e[2]=d*i+c*l;e[3]=d*o-c*a;e[4]=d*s-c*n;e[5]=d*l-c*i;e[6]=u;e[7]=v;e[8]=f;return e}function Vh(e,t,r){var a=r[0],n=r[1];e[0]=a*t[0];e[1]=a*t[1];e[2]=a*t[2];e[3]=n*t[3];e[4]=n*t[4];e[5]=n*t[5];e[6]=t[6];e[7]=t[7];e[8]=t[8];return e}function jh(e,t,r){e[0]=2/t;e[1]=0;e[2]=0;e[3]=0;e[4]=-2/r;e[5]=0;e[6]=-1;e[7]=1;e[8]=1;return e}var Xh={SCREEN:{name:"screen",screen:true},PICKING:{name:"picking",picking:true}};var Yh=Yt({texRows:24});var qh=Yt({collection:"default",getKey:null,drawElement:null,getBoundingBox:null,getRotation:null,getRotationPoint:null,getRotationOffset:null,isVisible:function e(){return true},getPadding:0});var Wh=function(){function e(t,r,a,n){o(this,e);this.debugID=Math.floor(Math.random()*1e4);this.r=t;this.texSize=r;this.texRows=a;this.texHeight=Math.floor(r/a);this.enableWrapping=true;this.locked=false;this.texture=null;this.needsBuffer=true;this.freePointer={x:0,row:0};this.keyToLocation=new Map;this.canvas=n(t,r,r);this.scratch=n(t,r,this.texHeight,"scratch")}return l(e,[{key:"lock",value:function e(){this.locked=true}},{key:"getKeys",value:function e(){return new Set(this.keyToLocation.keys())}},{key:"getScale",value:function e(t){var r=t.w,a=t.h;var n=this.texHeight,i=this.texSize;var o=n/a;var s=r*o;var l=a*o;if(s>i){o=i/r;s=r*o;l=a*o}return{scale:o,texW:s,texH:l}}},{key:"draw",value:function e(t,r,a){var n=this;if(this.locked)throw new Error("can't draw, atlas is locked");var i=this.texSize,o=this.texRows,s=this.texHeight;var l=this.getScale(r),u=l.scale,v=l.texW,f=l.texH;var c=[null,null];var d=function e(t,n){if(a&&n){var i=n.context;var o=t.x,l=t.row;var v=o;var f=s*l;i.save();i.translate(v,f);i.scale(u,u);a(i,r);i.restore()}};var h=function e(){d(n.freePointer,n.canvas);c[0]={x:n.freePointer.x,y:n.freePointer.row*s,w:v,h:f};c[1]={x:n.freePointer.x+v,y:n.freePointer.row*s,w:0,h:f};n.freePointer.x+=v;if(n.freePointer.x==i){n.freePointer.x=0;n.freePointer.row++}};var p=function e(){var t=n.scratch,r=n.canvas;t.clear();d({x:0,row:0},t);var a=i-n.freePointer.x;var o=v-a;var l=s;{var u=n.freePointer.x;var h=n.freePointer.row*s;var p=a;r.context.drawImage(t,0,0,p,l,u,h,p,l);c[0]={x:u,y:h,w:p,h:f}}{var g=a;var y=(n.freePointer.row+1)*s;var m=o;if(r){r.context.drawImage(t,g,0,m,l,0,y,m,l)}c[1]={x:0,y,w:m,h:f}}n.freePointer.x=o;n.freePointer.row++};var g=function e(){n.freePointer.x=0;n.freePointer.row++};if(this.freePointer.x+v<=i){h()}else if(this.freePointer.row>=o-1){return false}else if(this.freePointer.x===i){g();h()}else if(this.enableWrapping){p()}else{g();h()}this.keyToLocation.set(t,c);this.needsBuffer=true;return c}},{key:"getOffsets",value:function e(t){return this.keyToLocation.get(t)}},{key:"isEmpty",value:function e(){return this.freePointer.x===0&&this.freePointer.row===0}},{key:"canFit",value:function e(t){if(this.locked)return false;var r=this.texSize,a=this.texRows;var n=this.getScale(t),i=n.texW;if(this.freePointer.x+i>r){return this.freePointer.row<a-1}return true}},{key:"bufferIfNeeded",value:function e(t){if(!this.texture){this.texture=Ph(t,this.debugID)}if(this.needsBuffer){this.texture.buffer(this.canvas);this.needsBuffer=false;if(this.locked){this.canvas=null;this.scratch=null}}}},{key:"dispose",value:function e(){if(this.texture){this.texture.deleteTexture();this.texture=null}this.canvas=null;this.scratch=null;this.locked=true}}])}();var Uh=function(){function e(t,r,a,n){o(this,e);this.r=t;this.texSize=r;this.texRows=a;this.createTextureCanvas=n;this.atlases=[];this.styleKeyToAtlas=new Map;this.markedKeys=new Set}return l(e,[{key:"getKeys",value:function e(){return new Set(this.styleKeyToAtlas.keys())}},{key:"_createAtlas",value:function e(){var t=this.r,r=this.texSize,a=this.texRows,n=this.createTextureCanvas;return new Wh(t,r,a,n)}},{key:"_getScratchCanvas",value:function e(){if(!this.scratch){var t=this.r,r=this.texSize,a=this.texRows,n=this.createTextureCanvas;var i=Math.floor(r/a);this.scratch=n(t,r,i,"scratch")}return this.scratch}},{key:"draw",value:function e(t,r,a){var n=this.styleKeyToAtlas.get(t);if(!n){n=this.atlases[this.atlases.length-1];if(!n||!n.canFit(r)){if(n)n.lock();n=this._createAtlas();this.atlases.push(n)}n.draw(t,r,a);this.styleKeyToAtlas.set(t,n)}return n}},{key:"getAtlas",value:function e(t){return this.styleKeyToAtlas.get(t)}},{key:"hasAtlas",value:function e(t){return this.styleKeyToAtlas.has(t)}},{key:"markKeyForGC",value:function e(t){this.markedKeys.add(t)}},{key:"gc",value:function e(){var t=this;var r=this.markedKeys;if(r.size===0){console.log("nothing to garbage collect");return}var a=[];var n=new Map;var i=null;var o=u(this.atlases),s;try{var l=function e(){var o=s.value;var l=o.getKeys();var v=Gh(r,l);if(v.size===0){a.push(o);l.forEach((function(e){return n.set(e,o)}));return 1}if(!i){i=t._createAtlas();a.push(i)}var f=u(l),c;try{for(f.s();!(c=f.n()).done;){var d=c.value;if(!v.has(d)){var h=o.getOffsets(d),g=p(h,2),y=g[0],m=g[1];if(!i.canFit({w:y.w+m.w,h:y.h})){i.lock();i=t._createAtlas();a.push(i)}t._copyTextureToNewAtlas(d,o,i);n.set(d,i)}}}catch(b){f.e(b)}finally{f.f()}o.dispose()};for(o.s();!(s=o.n()).done;){if(l())continue}}catch(v){o.e(v)}finally{o.f()}this.atlases=a;this.styleKeyToAtlas=n;this.markedKeys=new Set}},{key:"_copyTextureToNewAtlas",value:function e(t,r,a){var n=r.getOffsets(t),i=p(n,2),o=i[0],s=i[1];if(s.w===0){a.draw(t,o,(function(e){e.drawImage(r.canvas,o.x,o.y,o.w,o.h,0,0,o.w,o.h)}))}else{var l=this._getScratchCanvas();l.clear();l.context.drawImage(r.canvas,o.x,o.y,o.w,o.h,0,0,o.w,o.h);l.context.drawImage(r.canvas,s.x,s.y,s.w,s.h,o.w,0,s.w,s.h);var u=o.w+s.w;var v=o.h;a.draw(t,{w:u,h:v},(function(e){e.drawImage(l,0,0,u,v,0,0,u,v)}))}}},{key:"getCounts",value:function e(){return{keyCount:this.styleKeyToAtlas.size,atlasCount:new Set(this.styleKeyToAtlas.values()).size}}}])}();function Gh(e,t){if(e.intersection)return e.intersection(t);else return new Set(g(e).filter((function(e){return t.has(e)})))}var Hh=function(){function e(t,r){o(this,e);this.r=t;this.globalOptions=r;this.atlasSize=r.webglTexSize;this.maxAtlasesPerBatch=r.webglTexPerBatch;this.renderTypes=new Map;this.collections=new Map;this.typeAndIdToKey=new Map;this.batchAtlases=[]}return l(e,[{key:"getAtlasSize",value:function e(){return this.atlasSize}},{key:"getMaxAtlasesPerBatch",value:function e(){return this.maxAtlasesPerBatch}},{key:"addAtlasCollection",value:function e(t,r){var a=this.globalOptions,n=a.webglTexSize,i=a.createTextureCanvas;var o=r.texRows;var s=this._cacheScratchCanvas(i);var l=new Uh(this.r,n,o,s);this.collections.set(t,l)}},{key:"addRenderType",value:function e(t,r){var a=r.collection;if(!this.collections.has(a))throw new Error("invalid atlas collection name '".concat(a,"'"));var n=this.collections.get(a);var i=se({type:t,atlasCollection:n},r);this.renderTypes.set(t,i)}},{key:"getRenderTypeOpts",value:function e(t){return this.renderTypes.get(t)}},{key:"getAtlasCollection",value:function e(t){return this.collections.get(t)}},{key:"_cacheScratchCanvas",value:function e(t){var r=-1;var a=-1;var n=null;return function(e,i,o,s){if(s){if(!n||i!=r||o!=a){r=i;a=o;n=t(e,i,o)}return n}else{return t(e,i,o)}}}},{key:"_key",value:function e(t,r){return"".concat(t,"-").concat(r)}},{key:"invalidate",value:function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},a=r.forceRedraw,n=a===undefined?false:a,i=r.filterEle,o=i===undefined?function(){return true}:i,s=r.filterType,l=s===undefined?function(){return true}:s;var v=false;var f=false;var c=u(t),d;try{for(c.s();!(d=c.n()).done;){var h=d.value;if(o(h)){var p=u(this.renderTypes.values()),g;try{for(p.s();!(g=p.n()).done;){var y=g.value;var m=y.type;if(l(m)){var b=y.getKey(h);var x=this.collections.get(y.collection);if(n){x.markKeyForGC(b);f=true}else{var w=y.getID?y.getID(h):h.id();var E=this._key(m,w);var T=this.typeAndIdToKey.get(E);if(T!==undefined&&T!==b){this.typeAndIdToKey["delete"](E);x.markKeyForGC(T);v=true}}}}}catch(k){p.e(k)}finally{p.f()}}}}catch(k){c.e(k)}finally{c.f()}if(f){this.gc();v=false}return v}},{key:"gc",value:function e(){var t=u(this.collections.values()),r;try{for(t.s();!(r=t.n()).done;){var a=r.value;a.gc()}}catch(n){t.e(n)}finally{t.f()}}},{key:"getOrCreateAtlas",value:function e(t,r,a){var n=this.renderTypes.get(r);var i=n.getKey(t);if(!a)a=n.getBoundingBox(t);var o=this.collections.get(n.collection);var s=false;var l=o.draw(i,a,(function(e){n.drawElement(e,t,a,true,true);s=true}));if(s){var u=n.getID?n.getID(t):t.id();var v=this._key(r,u);this.typeAndIdToKey.set(v,i)}return l}},{key:"startBatch",value:function e(){this.batchAtlases=[]}},{key:"getAtlasCount",value:function e(){return this.batchAtlases.length}},{key:"getAtlases",value:function e(){return this.batchAtlases}},{key:"canAddToCurrentBatch",value:function e(t,r){if(this.batchAtlases.length===this.maxAtlasesPerBatch){var a=this.renderTypes.get(r);var n=a.getKey(t);var i=this.collections.get(a.collection);var o=i.getAtlas(n);return Boolean(o)&&this.batchAtlases.includes(o)}return true}},{key:"getAtlasIndexForBatch",value:function e(t){var r=this.batchAtlases.indexOf(t);if(r<0){if(this.batchAtlases.length===this.maxAtlasesPerBatch){return}this.batchAtlases.push(t);r=this.batchAtlases.length-1}return r}},{key:"getIndexArray",value:function e(){return Array.from({length:this.maxAtlasesPerBatch},(function(e,t){return t}))}},{key:"getAtlasInfo",value:function e(t,r){var a=this.renderTypes.get(r);var n=a.getBoundingBox(t);var i=this.getOrCreateAtlas(t,r,n);var o=this.getAtlasIndexForBatch(i);if(o===undefined){return undefined}var s=a.getKey(t);var l=i.getOffsets(s),u=p(l,2),v=u[0],f=u[1];return{index:o,tex1:v,tex2:f,bb:n}}},{key:"setTransformMatrix",value:function e(t,r,a,n){var i=arguments.length>4&&arguments[4]!==undefined?arguments[4]:true;var o=this.getRenderTypeOpts(a);var s=o.getPadding?o.getPadding(t):0;if(n){var l=n.bb,u=n.tex1,v=n.tex2;var f=u.w/(u.w+v.w);if(!i){f=1-f}var c=this.getAdjustedBB(l,s,i,f);this._applyTransformMatrix(r,c,o,t)}else{var d=o.getBoundingBox(t);var h=this.getAdjustedBB(d,s,true,1);this._applyTransformMatrix(r,h,o,t)}}},{key:"_applyTransformMatrix",value:function e(t,r,a,n){var i,o;Lh(t);var s=a.getRotation?a.getRotation(n):0;if(s!==0){var l=a.getRotationPoint(n),u=l.x,v=l.y;zh(t,t,[u,v]);Fh(t,t,s);var f=a.getRotationOffset(n);i=f.x+r.xOffset;o=f.y}else{i=r.x1;o=r.y1}zh(t,t,[i,o]);Vh(t,t,[r.w,r.h])}},{key:"getAdjustedBB",value:function e(t,r,a,n){var i=t.x1,o=t.y1,s=t.w,l=t.h;if(r){i-=r;o-=r;s+=2*r;l+=2*r}var u=0;var v=s*n;if(a&&n<1){s=v}else if(!a&&n<1){u=s-v;i+=u;s=v}return{x1:i,y1:o,w:s,h:l,xOffset:u}}},{key:"getDebugInfo",value:function e(){var t=[];var r=u(this.collections),a;try{for(r.s();!(a=r.n()).done;){var n=p(a.value,2),i=n[0],o=n[1];var s=o.getCounts(),l=s.keyCount,v=s.atlasCount;t.push({type:i,keyCount:l,atlasCount:v})}}catch(f){r.e(f)}finally{r.f()}return t}}])}();var Kh=0;var Zh=1;var $h=2;var Qh=3;var Jh=4;var ep=function(){function e(t,r,a){o(this,e);this.r=t;this.gl=r;this.maxInstances=a.webglBatchSize;this.atlasSize=a.webglTexSize;this.bgColor=a.bgColor;this.debug=a.webglDebug;this.batchDebugInfo=[];a.enableWrapping=true;a.createTextureCanvas=xh;this.atlasManager=new Hh(t,a);this.program=this.createShaderProgram(Xh.SCREEN);this.pickingProgram=this.createShaderProgram(Xh.PICKING);this.vao=this.createVAO()}return l(e,[{key:"addAtlasCollection",value:function e(t,r){this.atlasManager.addAtlasCollection(t,r)}},{key:"addAtlasRenderType",value:function e(t,r){this.atlasManager.addRenderType(t,r)}},{key:"invalidate",value:function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},a=r.type;var n=this.atlasManager;if(a){return n.invalidate(t,{filterType:function e(t){return t===a},forceRedraw:true})}else{return n.invalidate(t)}}},{key:"gc",value:function e(){this.atlasManager.gc()}},{key:"createShaderProgram",value:function e(t){var r=this.gl;var a="#version 300 es\n      precision highp float;\n\n      uniform mat3 uPanZoomMatrix;\n      uniform int  uAtlasSize;\n      \n      // instanced\n      in vec2 aPosition; \n\n      in mat3 aTransform;\n\n      // what are we rendering?\n      in int aVertType;\n\n      // for picking\n      in vec4 aIndex;\n      \n      // For textures\n      in int aAtlasId; // which shader unit/atlas to use\n      in vec4 aTex; // x/y/w/h of texture in atlas\n\n      // for edges\n      in vec4 aPointAPointB;\n      in vec4 aPointCPointD;\n      in float aLineWidth;\n      in vec4 aColor;\n\n      out vec2 vTexCoord;\n      out vec4 vColor;\n      flat out int vAtlasId;\n      flat out vec4 vIndex;\n      flat out int vVertType;\n\n      void main(void) {\n        int vid = gl_VertexID;\n        vec2 position = aPosition;\n\n        if(aVertType == ".concat(Kh,") {\n          float texX = aTex.x;\n          float texY = aTex.y;\n          float texW = aTex.z;\n          float texH = aTex.w;\n\n          int vid = gl_VertexID;\n\n          if(vid == 1 || vid == 2 || vid == 4) {\n            texX += texW;\n          }\n          if(vid == 2 || vid == 4 || vid == 5) {\n            texY += texH;\n          }\n\n          float d = float(uAtlasSize);\n          vTexCoord = vec2(texX / d, texY / d); // tex coords must be between 0 and 1\n\n          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);\n        }\n        else if(aVertType == ").concat(Jh,") {\n          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);\n          vColor = aColor;\n        }\n        else if(aVertType == ").concat(Zh,") {\n          vec2 source = aPointAPointB.xy;\n          vec2 target = aPointAPointB.zw;\n\n          // adjust the geometry so that the line is centered on the edge\n          position.y = position.y - 0.5;\n\n          vec2 xBasis = target - source;\n          vec2 yBasis = normalize(vec2(-xBasis.y, xBasis.x));\n          vec2 point = source + xBasis * position.x + yBasis * aLineWidth * position.y;\n\n          gl_Position = vec4(uPanZoomMatrix * vec3(point, 1.0), 1.0);\n          vColor = aColor;\n        } \n        else if(aVertType == ").concat($h,") {\n          vec2 pointA = aPointAPointB.xy;\n          vec2 pointB = aPointAPointB.zw;\n          vec2 pointC = aPointCPointD.xy;\n          vec2 pointD = aPointCPointD.zw;\n\n          // adjust the geometry so that the line is centered on the edge\n          position.y = position.y - 0.5;\n\n          vec2 p0 = pointA;\n          vec2 p1 = pointB;\n          vec2 p2 = pointC;\n          vec2 pos = position;\n          if(position.x == 1.0) {\n            p0 = pointD;\n            p1 = pointC;\n            p2 = pointB;\n            pos = vec2(0.0, -position.y);\n          }\n\n          vec2 p01 = p1 - p0;\n          vec2 p12 = p2 - p1;\n          vec2 p21 = p1 - p2;\n\n          // Find the normal vector.\n          vec2 tangent = normalize(normalize(p12) + normalize(p01));\n          vec2 normal = vec2(-tangent.y, tangent.x);\n\n          // Find the vector perpendicular to p0 -> p1.\n          vec2 p01Norm = normalize(vec2(-p01.y, p01.x));\n\n          // Determine the bend direction.\n          float sigma = sign(dot(p01 + p21, normal));\n          float width = aLineWidth;\n\n          if(sign(pos.y) == -sigma) {\n            // This is an intersecting vertex. Adjust the position so that there's no overlap.\n            vec2 point = 0.5 * width * normal * -sigma / dot(normal, p01Norm);\n            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);\n          } else {\n            // This is a non-intersecting vertex. Treat it like a mitre join.\n            vec2 point = 0.5 * width * normal * sigma * dot(normal, p01Norm);\n            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);\n          }\n\n          vColor = aColor;\n        } \n        else if(aVertType == ").concat(Qh," && vid < 3) {\n          // massage the first triangle into an edge arrow\n          if(vid == 0)\n            position = vec2(-0.15, -0.3);\n          if(vid == 1)\n            position = vec2( 0.0,   0.0);\n          if(vid == 2)\n            position = vec2( 0.15, -0.3);\n\n          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);\n          vColor = aColor;\n        }\n        else {\n          gl_Position = vec4(2.0, 0.0, 0.0, 1.0); // discard vertex by putting it outside webgl clip space\n        }\n\n        vAtlasId = aAtlasId;\n        vIndex = aIndex;\n        vVertType = aVertType;\n      }\n    ");var n=this.atlasManager.getIndexArray();var i="#version 300 es\n      precision highp float;\n\n      // define texture unit for each node in the batch\n      ".concat(n.map((function(e){return"uniform sampler2D uTexture".concat(e,";")})).join("\n\t"),"\n\n      uniform vec4 uBGColor;\n\n      in vec2 vTexCoord;\n      in vec4 vColor;\n      flat in int vAtlasId;\n      flat in vec4 vIndex;\n      flat in int vVertType;\n\n      out vec4 outColor;\n\n      void main(void) {\n        if(vVertType == ").concat(Kh,") {\n          ").concat(n.map((function(e){return"if(vAtlasId == ".concat(e,") outColor = texture(uTexture").concat(e,", vTexCoord);")})).join("\n\telse "),"\n        } else if(vVertType == ").concat(Qh,") {\n          // blend arrow color with background (using premultiplied alpha)\n          outColor.rgb = vColor.rgb + (uBGColor.rgb * (1.0 - vColor.a)); \n          outColor.a = 1.0; // make opaque, masks out line under arrow\n        } else {\n          outColor = vColor;\n        }\n\n        ").concat(t.picking?"if(outColor.a == 0.0) discard;\n             else outColor = vIndex;":"","\n      }\n    ");var o=bh(r,a,i);o.aPosition=r.getAttribLocation(o,"aPosition");o.aIndex=r.getAttribLocation(o,"aIndex");o.aVertType=r.getAttribLocation(o,"aVertType");o.aTransform=r.getAttribLocation(o,"aTransform");o.aAtlasId=r.getAttribLocation(o,"aAtlasId");o.aTex=r.getAttribLocation(o,"aTex");o.aPointAPointB=r.getAttribLocation(o,"aPointAPointB");o.aPointCPointD=r.getAttribLocation(o,"aPointCPointD");o.aLineWidth=r.getAttribLocation(o,"aLineWidth");o.aColor=r.getAttribLocation(o,"aColor");o.uPanZoomMatrix=r.getUniformLocation(o,"uPanZoomMatrix");o.uAtlasSize=r.getUniformLocation(o,"uAtlasSize");o.uBGColor=r.getUniformLocation(o,"uBGColor");o.uTextures=[];for(var s=0;s<this.atlasManager.getMaxAtlasesPerBatch();s++){o.uTextures.push(r.getUniformLocation(o,"uTexture".concat(s)))}return o}},{key:"createVAO",value:function e(){var t=[0,0,1,0,1,1,0,0,1,1,0,1];this.vertexCount=t.length/2;var r=this.maxInstances;var a=this.gl,n=this.program;var i=a.createVertexArray();a.bindVertexArray(i);Ah(a,"vec2",n.aPosition,t);this.transformBuffer=Mh(a,r,n.aTransform);this.indexBuffer=_h(a,r,"vec4",n.aIndex);this.vertTypeBuffer=_h(a,r,"int",n.aVertType);this.atlasIdBuffer=_h(a,r,"int",n.aAtlasId);this.texBuffer=_h(a,r,"vec4",n.aTex);this.pointAPointBBuffer=_h(a,r,"vec4",n.aPointAPointB);this.pointCPointDBuffer=_h(a,r,"vec4",n.aPointCPointD);this.lineWidthBuffer=_h(a,r,"float",n.aLineWidth);this.colorBuffer=_h(a,r,"vec4",n.aColor);a.bindVertexArray(null);return i}},{key:"buffers",get:function e(){var t=this;if(!this._buffers){this._buffers=Object.keys(this).filter((function(e){return e.endsWith("Buffer")})).map((function(e){return t[e]}))}return this._buffers}},{key:"startFrame",value:function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:Xh.SCREEN;this.panZoomMatrix=t;this.renderTarget=r;this.batchDebugInfo=[];this.wrappedCount=0;this.rectangleCount=0;this.startBatch()}},{key:"startBatch",value:function e(){this.instanceCount=0;this.atlasManager.startBatch()}},{key:"endFrame",value:function e(){this.endBatch()}},{key:"getTempMatrix",value:function e(){return this.tempMatrix=this.tempMatrix||Nh()}},{key:"drawTexture",value:function e(t,r,a){var n=this.atlasManager;if(!t.visible()){return}if(!n.getRenderTypeOpts(a).isVisible(t)){return}if(!n.canAddToCurrentBatch(t,a)){this.endBatch()}if(this.instanceCount+1>=this.maxInstances){this.endBatch()}var i=this.instanceCount;this.vertTypeBuffer.getView(i)[0]=Kh;var o=this.indexBuffer.getView(i);kh(r,o);var s=n.getAtlasInfo(t,a);var l=s.index,u=s.tex1,v=s.tex2;if(v.w>0)this.wrappedCount++;var f=true;for(var c=0,d=[u,v];c<d.length;c++){var h=d[c];if(h.w!=0){var p=this.instanceCount;this.vertTypeBuffer.getView(p)[0]=Kh;var g=this.indexBuffer.getView(p);kh(r,g);var y=this.atlasIdBuffer.getView(p);y[0]=l;var m=this.texBuffer.getView(p);m[0]=h.x;m[1]=h.y;m[2]=h.w;m[3]=h.h;var b=this.transformBuffer.getMatrixView(p);n.setTransformMatrix(t,b,a,s,f);this.instanceCount++}f=false}if(this.instanceCount>=this.maxInstances){this.endBatch()}}},{key:"drawSimpleRectangle",value:function e(t,r,a){if(!t.visible()){return}var n=this.atlasManager;var i=this.instanceCount;this.vertTypeBuffer.getView(i)[0]=Jh;var o=this.indexBuffer.getView(i);kh(r,o);var s=t.pstyle("background-color").value;var l=t.pstyle("background-opacity").value;var u=this.colorBuffer.getView(i);Th(s,l,u);var v=this.transformBuffer.getMatrixView(i);n.setTransformMatrix(t,v,a);this.rectangleCount++;this.instanceCount++;if(this.instanceCount>=this.maxInstances){this.endBatch()}}},{key:"drawEdgeArrow",value:function e(t,r,a){if(!t.visible()){return}var n=t._private.rscratch;var i,o,s;if(a==="source"){i=n.arrowStartX;o=n.arrowStartY;s=n.srcArrowAngle}else{i=n.arrowEndX;o=n.arrowEndY;s=n.tgtArrowAngle}if(isNaN(i)||i==null||isNaN(o)||o==null||isNaN(s)||s==null){return}var l=t.pstyle(a+"-arrow-shape").value;if(l==="none"){return}var u=t.pstyle(a+"-arrow-color").value;var v=t.pstyle("opacity").value;var f=t.pstyle("line-opacity").value;var c=v*f;var d=t.pstyle("width").pfValue;var h=t.pstyle("arrow-scale").value;var p=this.r.getArrowWidth(d,h);var g=this.instanceCount;var y=this.transformBuffer.getMatrixView(g);Lh(y);zh(y,y,[i,o]);Vh(y,y,[p,p]);Fh(y,y,s);this.vertTypeBuffer.getView(g)[0]=Qh;var m=this.indexBuffer.getView(g);kh(r,m);var b=this.colorBuffer.getView(g);Th(u,c,b);this.instanceCount++;if(this.instanceCount>=this.maxInstances){this.endBatch()}}},{key:"drawEdgeLine",value:function e(t,r){if(!t.visible()){return}var a=this.getEdgePoints(t);if(!a){return}var n=t.pstyle("opacity").value;var i=t.pstyle("line-opacity").value;var o=t.pstyle("width").pfValue;var s=t.pstyle("line-color").value;var l=n*i;if(a.length/2+this.instanceCount>this.maxInstances){this.endBatch()}if(a.length==4){var u=this.instanceCount;this.vertTypeBuffer.getView(u)[0]=Zh;var v=this.indexBuffer.getView(u);kh(r,v);var f=this.colorBuffer.getView(u);Th(s,l,f);var c=this.lineWidthBuffer.getView(u);c[0]=o;var d=this.pointAPointBBuffer.getView(u);d[0]=a[0];d[1]=a[1];d[2]=a[2];d[3]=a[3];this.instanceCount++;if(this.instanceCount>=this.maxInstances){this.endBatch()}}else{for(var h=0;h<a.length-2;h+=2){var p=this.instanceCount;this.vertTypeBuffer.getView(p)[0]=$h;var g=this.indexBuffer.getView(p);kh(r,g);var y=this.colorBuffer.getView(p);Th(s,l,y);var m=this.lineWidthBuffer.getView(p);m[0]=o;var b=a[h-2],x=a[h-1];var w=a[h],E=a[h+1];var T=a[h+2],k=a[h+3];var C=a[h+4],P=a[h+5];if(h==0){b=2*w-T+.001;x=2*E-k+.001}if(h==a.length-4){C=2*T-w+.001;P=2*k-E+.001}var S=this.pointAPointBBuffer.getView(p);S[0]=b;S[1]=x;S[2]=w;S[3]=E;var D=this.pointCPointDBuffer.getView(p);D[0]=T;D[1]=k;D[2]=C;D[3]=P;this.instanceCount++;if(this.instanceCount>=this.maxInstances){this.endBatch()}}}}},{key:"getEdgePoints",value:function e(t){var r=t._private.rscratch;if(r.badLine||r.allpts==null||isNaN(r.allpts[0])){return}var a=r.allpts;if(a.length==4){return a}var n=this.getNumSegments(t);return this.getCurveSegmentPoints(a,n)}},{key:"getNumSegments",value:function e(t){var r=15;return Math.min(Math.max(r,5),this.maxInstances)}},{key:"getCurveSegmentPoints",value:function e(t,r){if(t.length==4){return t}var a=Array((r+1)*2);for(var n=0;n<=r;n++){if(n==0){a[0]=t[0];a[1]=t[1]}else if(n==r){a[n*2]=t[t.length-2];a[n*2+1]=t[t.length-1]}else{var i=n/r;this.setCurvePoint(t,i,a,n*2)}}return a}},{key:"setCurvePoint",value:function e(t,r,a,n){if(t.length<=2){a[n]=t[0];a[n+1]=t[1]}else{var i=Array(t.length-2);for(var o=0;o<i.length;o+=2){var s=(1-r)*t[o]+r*t[o+2];var l=(1-r)*t[o+1]+r*t[o+3];i[o]=s;i[o+1]=l}return this.setCurvePoint(i,r,a,n)}}},{key:"endBatch",value:function e(){var t=this.gl,r=this.vao,a=this.vertexCount,n=this.instanceCount;if(n===0)return;var i=this.renderTarget.picking?this.pickingProgram:this.program;t.useProgram(i);t.bindVertexArray(r);var o=u(this.buffers),s;try{for(o.s();!(s=o.n()).done;){var l=s.value;l.bufferSubData(n)}}catch(h){o.e(h)}finally{o.f()}var v=this.atlasManager.getAtlases();for(var f=0;f<v.length;f++){v[f].bufferIfNeeded(t)}for(var c=0;c<v.length;c++){t.activeTexture(t.TEXTURE0+c);t.bindTexture(t.TEXTURE_2D,v[c].texture);t.uniform1i(i.uTextures[c],c)}t.uniformMatrix3fv(i.uPanZoomMatrix,false,this.panZoomMatrix);t.uniform1i(i.uAtlasSize,this.atlasManager.getAtlasSize());var d=Th(this.bgColor,1);t.uniform4fv(i.uBGColor,d);t.drawArraysInstanced(t.TRIANGLES,0,a,n);t.bindVertexArray(null);t.bindTexture(t.TEXTURE_2D,null);if(this.debug){this.batchDebugInfo.push({count:n,atlasCount:v.length})}this.startBatch()}},{key:"getDebugInfo",value:function e(){var t=this.atlasManager.getDebugInfo();var r=t.reduce((function(e,t){return e+t.atlasCount}),0);var a=this.batchDebugInfo;var n=a.reduce((function(e,t){return e+t.count}),0);return{atlasInfo:t,totalAtlases:r,wrappedCount:this.wrappedCount,rectangleCount:this.rectangleCount,batchCount:a.length,batchInfo:a,totalInstances:n}}}])}();function tp(e,t){return"rgba(".concat(e[0],", ").concat(e[1],", ").concat(e[2],", ").concat(t,")")}var rp=function(){function e(t){o(this,e);this.r=t}return l(e,[{key:"getStyleKey",value:function e(t,r){var a=this.getStyle(t,r),n=a.shape,i=a.opacity,o=a.color;if(!n)return null;var s=r.width();var l=r.height();var u=tp(o,i);return Tt("".concat(n,"-").concat(s,"-").concat(l,"-").concat(u))}},{key:"isVisible",value:function e(t,r){var a=r.pstyle("".concat(t,"-opacity")).value;return a>0}},{key:"getStyle",value:function e(t,r){var a=r.pstyle("".concat(t,"-opacity")).value;var n=r.pstyle("".concat(t,"-color")).value;var i=r.pstyle("".concat(t,"-shape")).value;return{opacity:a,color:n,shape:i}}},{key:"getPadding",value:function e(t,r){return r.pstyle("".concat(t,"-padding")).pfValue}},{key:"draw",value:function e(t,r,a,n){if(!this.isVisible(t,a))return;var i=this.r;var o=n.w;var s=n.h;var l=o/2;var u=s/2;var v=this.getStyle(t,a),f=v.shape,c=v.color,d=v.opacity;r.save();r.fillStyle=tp(c,d);if(f==="round-rectangle"||f==="roundrectangle"){i.drawRoundRectanglePath(r,l,u,o,s,"auto")}else if(f==="ellipse"){i.drawEllipsePath(r,l,u,o,s)}r.fill();r.restore()}}])}();var ap={};ap.initWebgl=function(e,t){var r=this;var a=r.data.contexts[r.WEBGL];e.bgColor=np(r);e.webglTexSize=Math.min(e.webglTexSize,a.getParameter(a.MAX_TEXTURE_SIZE));e.webglTexRows=Math.min(e.webglTexRows,54);e.webglTexRowsNodes=Math.min(e.webglTexRowsNodes,54);e.webglBatchSize=Math.min(e.webglBatchSize,16384);e.webglTexPerBatch=Math.min(e.webglTexPerBatch,a.getParameter(a.MAX_TEXTURE_IMAGE_UNITS));r.webglDebug=e.webglDebug;r.webglDebugShowAtlases=e.webglDebugShowAtlases;r.pickingFrameBuffer=Ih(a);r.pickingFrameBuffer.needsDraw=true;var n=function e(t){return function(e){return r.getTextAngle(e,t)}};var i=function e(t){return function(e){var r=e.pstyle(t);return r&&r.value}};r.drawing=new ep(r,a,e);var o=new rp(r);r.drawing.addAtlasCollection("node",Yh({texRows:e.webglTexRowsNodes}));r.drawing.addAtlasCollection("label",Yh({texRows:e.webglTexRows}));r.drawing.addAtlasRenderType("node-body",qh({collection:"node",getKey:t.getStyleKey,getBoundingBox:t.getElementBox,drawElement:t.drawElement}));r.drawing.addAtlasRenderType("label",qh({collection:"label",getKey:t.getLabelKey,getBoundingBox:t.getLabelBox,drawElement:t.drawLabel,getRotation:n(null),getRotationPoint:t.getLabelRotationPoint,getRotationOffset:t.getLabelRotationOffset,isVisible:i("label")}));r.drawing.addAtlasRenderType("node-overlay",qh({collection:"node",getBoundingBox:t.getElementBox,getKey:function e(t){return o.getStyleKey("overlay",t)},drawElement:function e(t,r,a){return o.draw("overlay",t,r,a)},isVisible:function e(t){return o.isVisible("overlay",t)},getPadding:function e(t){return o.getPadding("overlay",t)}}));r.drawing.addAtlasRenderType("node-underlay",qh({collection:"node",getBoundingBox:t.getElementBox,getKey:function e(t){return o.getStyleKey("underlay",t)},drawElement:function e(t,r,a){return o.draw("underlay",t,r,a)},isVisible:function e(t){return o.isVisible("underlay",t)},getPadding:function e(t){return o.getPadding("underlay",t)}}));r.drawing.addAtlasRenderType("edge-source-label",qh({collection:"label",getKey:t.getSourceLabelKey,getBoundingBox:t.getSourceLabelBox,drawElement:t.drawSourceLabel,getRotation:n("source"),getRotationPoint:t.getSourceLabelRotationPoint,getRotationOffset:t.getSourceLabelRotationOffset,isVisible:i("source-label")}));r.drawing.addAtlasRenderType("edge-target-label",qh({collection:"label",getKey:t.getTargetLabelKey,getBoundingBox:t.getTargetLabelBox,drawElement:t.drawTargetLabel,getRotation:n("target"),getRotationPoint:t.getTargetLabelRotationPoint,getRotationOffset:t.getTargetLabelRotationOffset,isVisible:i("target-label")}));var s=st((function(){console.log("garbage collect flag set");r.data.gc=true}),1e4);r.onUpdateEleCalcs((function(e,t){var a=false;if(t&&t.length>0){a|=r.drawing.invalidate(t)}if(a){s()}}));ip(r)};function np(e){var t=e.cy.container();var r=t&&t.style&&t.style.backgroundColor||"white";return ce(r)}function ip(e){{var t=e.render;e.render=function(r){r=r||{};var a=e.cy;if(e.webgl){if(a.zoom()>fd){op(e);t.call(e,r)}else{sp(e);yp(e,r,Xh.SCREEN)}}}}{var r=e.matchCanvasSize;e.matchCanvasSize=function(t){r.call(e,t);e.pickingFrameBuffer.setFramebufferAttachmentSizes(e.canvasWidth,e.canvasHeight);e.pickingFrameBuffer.needsDraw=true}}{e.findNearestElements=function(t,r,a,n){return hp(e,t,r)}}{var a=e.invalidateCachedZSortedEles;e.invalidateCachedZSortedEles=function(){a.call(e);e.pickingFrameBuffer.needsDraw=true}}{var n=e.notify;e.notify=function(t,r){n.call(e,t,r);if(t==="viewport"||t==="bounds"){e.pickingFrameBuffer.needsDraw=true}else if(t==="background"){e.drawing.invalidate(r,{type:"node-body"})}}}}function op(e){var t=e.data.contexts[e.WEBGL];t.clear(t.COLOR_BUFFER_BIT|t.DEPTH_BUFFER_BIT)}function sp(e){var t=function t(r){r.save();r.setTransform(1,0,0,1,0,0);r.clearRect(0,0,e.canvasWidth,e.canvasHeight);r.restore()};t(e.data.contexts[e.NODE]);t(e.data.contexts[e.DRAG])}function lp(e){var t=e.canvasWidth;var r=e.canvasHeight;var a=wh(e),n=a.pan,i=a.zoom;var o=Nh();zh(o,o,[n.x,n.y]);Vh(o,o,[i,i]);var s=Nh();jh(s,t,r);var l=Nh();Oh(l,s,o);return l}function up(e,t){var r=e.canvasWidth;var a=e.canvasHeight;var n=wh(e),i=n.pan,o=n.zoom;t.setTransform(1,0,0,1,0,0);t.clearRect(0,0,r,a);t.translate(i.x,i.y);t.scale(o,o)}function vp(e,t){e.drawSelectionRectangle(t,(function(t){return up(e,t)}))}function fp(e){var t=e.data.contexts[e.NODE];t.save();up(e,t);t.strokeStyle="rgba(0, 0, 0, 0.3)";t.beginPath();t.moveTo(-1e3,0);t.lineTo(1e3,0);t.stroke();t.beginPath();t.moveTo(0,-1e3);t.lineTo(0,1e3);t.stroke();t.restore()}function cp(e){var t=function t(r,a,n){var i=r.atlasManager.getAtlasCollection(a);var o=e.data.contexts[e.NODE];var s=.125;var l=i.atlases;for(var u=0;u<l.length;u++){var v=l[u];var f=v.canvas;if(f){var c=f.width;var d=f.height;var h=c*u;var p=f.height*n;o.save();o.scale(s,s);o.drawImage(f,h,p);o.strokeStyle="black";o.rect(h,p,c,d);o.stroke();o.restore()}}};var r=0;t(e.drawing,"node",r++);t(e.drawing,"label",r++)}function dp(e,t,r,a,n){var i,o,s,l;var u=wh(e),v=u.pan,f=u.zoom;{var c=Eh(e,v,f,t,r),d=p(c,2),h=d[0],g=d[1];var y=6;i=h-y/2;o=g-y/2;s=y;l=y}if(s===0||l===0){return[]}var m=e.data.contexts[e.WEBGL];m.bindFramebuffer(m.FRAMEBUFFER,e.pickingFrameBuffer);if(e.pickingFrameBuffer.needsDraw){m.viewport(0,0,m.canvas.width,m.canvas.height);yp(e,null,Xh.PICKING);e.pickingFrameBuffer.needsDraw=false}var b=s*l;var x=new Uint8Array(b*4);m.readPixels(i,o,s,l,m.RGBA,m.UNSIGNED_BYTE,x);m.bindFramebuffer(m.FRAMEBUFFER,null);var w=new Set;for(var E=0;E<b;E++){var T=x.slice(E*4,E*4+4);var k=Ch(T)-1;if(k>=0){w.add(k)}}return w}function hp(e,t,r){var a=dp(e,t,r);var n=e.getCachedZSortedEles();var i,o;var s=u(a),l;try{for(s.s();!(l=s.n()).done;){var v=l.value;var f=n[v];if(!i&&f.isNode()){i=f}if(!o&&f.isEdge()){o=f}if(i&&o){break}}}catch(c){s.e(c)}finally{s.f()}return[i,o].filter(Boolean)}function pp(e){return e.pstyle("shape").value==="rectangle"&&e.pstyle("background-fill").value==="solid"&&e.pstyle("border-width").pfValue===0&&e.pstyle("background-image").strValue==="none"}function gp(e,t,r){var a=e.drawing;t+=1;if(r.isNode()){a.drawTexture(r,t,"node-underlay");if(pp(r)){a.drawSimpleRectangle(r,t,"node-body")}else{a.drawTexture(r,t,"node-body")}a.drawTexture(r,t,"label");a.drawTexture(r,t,"node-overlay")}else{a.drawEdgeLine(r,t);a.drawEdgeArrow(r,t,"source");a.drawEdgeArrow(r,t,"target");a.drawTexture(r,t,"label");a.drawTexture(r,t,"edge-source-label");a.drawTexture(r,t,"edge-target-label")}}function yp(e,t,r){var a;if(e.webglDebug){a=performance.now()}var n=e.drawing;var i=0;if(r.screen){if(e.data.canvasNeedsRedraw[e.SELECT_BOX]){vp(e,t)}}if(e.data.canvasNeedsRedraw[e.NODE]||r.picking){var o=e.data.contexts[e.WEBGL];if(r.screen){o.clearColor(0,0,0,0);o.enable(o.BLEND);o.blendFunc(o.ONE,o.ONE_MINUS_SRC_ALPHA)}else{o.disable(o.BLEND)}o.clear(o.COLOR_BUFFER_BIT|o.DEPTH_BUFFER_BIT);o.viewport(0,0,o.canvas.width,o.canvas.height);var s=lp(e);var l=e.getCachedZSortedEles();i=l.length;n.startFrame(s,r);if(r.screen){for(var v=0;v<l.nondrag.length;v++){gp(e,v,l.nondrag[v])}for(var f=0;f<l.drag.length;f++){gp(e,f,l.drag[f])}}else if(r.picking){for(var c=0;c<l.length;c++){gp(e,c,l[c])}}n.endFrame();if(r.screen&&e.webglDebugShowAtlases){fp(e);cp(e)}e.data.canvasNeedsRedraw[e.NODE]=false;e.data.canvasNeedsRedraw[e.DRAG]=false}if(e.webglDebug){var d=performance.now();var h=false;var p=Math.ceil(d-a);var g=n.getDebugInfo();var y=["".concat(i," elements"),"".concat(g.totalInstances," instances"),"".concat(g.batchCount," batches"),"".concat(g.totalAtlases," atlases"),"".concat(g.wrappedCount," wrapped textures"),"".concat(g.rectangleCount," simple rectangles")].join(", ");if(h){console.log("WebGL (".concat(r.name,") - time ").concat(p,"ms, ").concat(y))}else{console.log("WebGL (".concat(r.name,") - frame time ").concat(p,"ms"));console.log("Totals:");console.log("  ".concat(y));console.log("Texture Atlases Used:");var m=g.atlasInfo;var b=u(m),x;try{for(b.s();!(x=b.n()).done;){var w=x.value;console.log("  ".concat(w.type,": ").concat(w.keyCount," keys, ").concat(w.atlasCount," atlases"))}}catch(E){b.e(E)}finally{b.f()}console.log("")}}if(e.data.gc){console.log("Garbage Collect!");e.data.gc=false;n.gc()}}var mp={};mp.drawPolygonPath=function(e,t,r,a,n,i){var o=a/2;var s=n/2;if(e.beginPath){e.beginPath()}e.moveTo(t+o*i[0],r+s*i[1]);for(var l=1;l<i.length/2;l++){e.lineTo(t+o*i[l*2],r+s*i[l*2+1])}e.closePath()};mp.drawRoundPolygonPath=function(e,t,r,a,n,i,o){o.forEach((function(t){return Ic(e,t)}));e.closePath()};mp.drawRoundRectanglePath=function(e,t,r,a,n,i){var o=a/2;var s=n/2;var l=i==="auto"?Pa(a,n):Math.min(i,s,o);if(e.beginPath){e.beginPath()}e.moveTo(t,r-s);e.arcTo(t+o,r-s,t+o,r,l);e.arcTo(t+o,r+s,t,r+s,l);e.arcTo(t-o,r+s,t-o,r,l);e.arcTo(t-o,r-s,t,r-s,l);e.lineTo(t,r-s);e.closePath()};mp.drawBottomRoundRectanglePath=function(e,t,r,a,n,i){var o=a/2;var s=n/2;var l=i==="auto"?Pa(a,n):i;if(e.beginPath){e.beginPath()}e.moveTo(t,r-s);e.lineTo(t+o,r-s);e.lineTo(t+o,r);e.arcTo(t+o,r+s,t,r+s,l);e.arcTo(t-o,r+s,t-o,r,l);e.lineTo(t-o,r-s);e.lineTo(t,r-s);e.closePath()};mp.drawCutRectanglePath=function(e,t,r,a,n,i,o){var s=a/2;var l=n/2;var u=o==="auto"?Da():o;if(e.beginPath){e.beginPath()}e.moveTo(t-s+u,r-l);e.lineTo(t+s-u,r-l);e.lineTo(t+s,r-l+u);e.lineTo(t+s,r+l-u);e.lineTo(t+s-u,r+l);e.lineTo(t-s+u,r+l);e.lineTo(t-s,r+l-u);e.lineTo(t-s,r-l+u);e.closePath()};mp.drawBarrelPath=function(e,t,r,a,n){var i=a/2;var o=n/2;var s=t-i;var l=t+i;var u=r-o;var v=r+o;var f=Aa(a,n);var c=f.widthOffset;var d=f.heightOffset;var h=f.ctrlPtOffsetPct*c;if(e.beginPath){e.beginPath()}e.moveTo(s,u+d);e.lineTo(s,v-d);e.quadraticCurveTo(s+h,v,s+c,v);e.lineTo(l-c,v);e.quadraticCurveTo(l-h,v,l,v-d);e.lineTo(l,u+d);e.quadraticCurveTo(l-h,u,l-c,u);e.lineTo(s+c,u);e.quadraticCurveTo(s+h,u,s,u+d);e.closePath()};var bp=Math.sin(0);var xp=Math.cos(0);var wp={};var Ep={};var Tp=Math.PI/40;for(var kp=0*Math.PI;kp<2*Math.PI;kp+=Tp){wp[kp]=Math.sin(kp);Ep[kp]=Math.cos(kp)}mp.drawEllipsePath=function(e,t,r,a,n){if(e.beginPath){e.beginPath()}if(e.ellipse){e.ellipse(t,r,a/2,n/2,0,0,2*Math.PI)}else{var i,o;var s=a/2;var l=n/2;for(var u=0*Math.PI;u<2*Math.PI;u+=Tp){i=t-s*wp[u]*bp+s*Ep[u]*xp;o=r+l*Ep[u]*bp+l*wp[u]*xp;if(u===0){e.moveTo(i,o)}else{e.lineTo(i,o)}}}e.closePath()};var Cp={};Cp.createBuffer=function(e,t){var r=document.createElement("canvas");r.width=e;r.height=t;return[r,r.getContext("2d")]};Cp.bufferCanvasImage=function(e){var t=this.cy;var r=t.mutableElements();var a=r.boundingBox();var n=this.findContainerClientCoords();var i=e.full?Math.ceil(a.w):n[2];var o=e.full?Math.ceil(a.h):n[3];var s=I(e.maxWidth)||I(e.maxHeight);var l=this.getPixelRatio();var u=1;if(e.scale!==undefined){i*=e.scale;o*=e.scale;u=e.scale}else if(s){var v=Infinity;var f=Infinity;if(I(e.maxWidth)){v=u*e.maxWidth/i}if(I(e.maxHeight)){f=u*e.maxHeight/o}u=Math.min(v,f);i*=u;o*=u}if(!s){i*=l;o*=l;u*=l}var c=document.createElement("canvas");c.width=i;c.height=o;c.style.width=i+"px";c.style.height=o+"px";var d=c.getContext("2d");if(i>0&&o>0){d.clearRect(0,0,i,o);d.globalCompositeOperation="source-over";var h=this.getCachedZSortedEles();if(e.full){d.translate(-a.x1*u,-a.y1*u);d.scale(u,u);this.drawElements(d,h);d.scale(1/u,1/u);d.translate(a.x1*u,a.y1*u)}else{var p=t.pan();var g={x:p.x*u,y:p.y*u};u*=t.zoom();d.translate(g.x,g.y);d.scale(u,u);this.drawElements(d,h);d.scale(1/u,1/u);d.translate(-g.x,-g.y)}if(e.bg){d.globalCompositeOperation="destination-over";d.fillStyle=e.bg;d.rect(0,0,i,o);d.fill()}}return c};function Pp(e,t){var r=atob(e);var a=new ArrayBuffer(r.length);var n=new Uint8Array(a);for(var i=0;i<r.length;i++){n[i]=r.charCodeAt(i)}return new Blob([a],{type:t})}function Sp(e){var t=e.indexOf(",");return e.substr(t+1)}function Dp(e,t,r){var a=function a(){return t.toDataURL(r,e.quality)};switch(e.output){case"blob-promise":return new si((function(a,n){try{t.toBlob((function(e){if(e!=null){a(e)}else{n(new Error("`canvas.toBlob()` sent a null value in its callback"))}}),r,e.quality)}catch(i){n(i)}}));case"blob":return Pp(Sp(a()),r);case"base64":return Sp(a());case"base64uri":default:return a()}}Cp.png=function(e){return Dp(e,this.bufferCanvasImage(e),"image/png")};Cp.jpg=function(e){return Dp(e,this.bufferCanvasImage(e),"image/jpeg")};var Bp={};Bp.nodeShapeImpl=function(e,t,r,a,n,i,o,s){switch(e){case"ellipse":return this.drawEllipsePath(t,r,a,n,i);case"polygon":return this.drawPolygonPath(t,r,a,n,i,o);case"round-polygon":return this.drawRoundPolygonPath(t,r,a,n,i,o,s);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(t,r,a,n,i,s);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(t,r,a,n,i,o,s);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(t,r,a,n,i,s);case"barrel":return this.drawBarrelPath(t,r,a,n,i)}};var Ap=Mp;var _p=Mp.prototype;_p.CANVAS_LAYERS=3;_p.SELECT_BOX=0;_p.DRAG=1;_p.NODE=2;_p.WEBGL=3;_p.CANVAS_TYPES=["2d","2d","2d","webgl2"];_p.BUFFER_COUNT=3;_p.TEXTURE_BUFFER=0;_p.MOTIONBLUR_BUFFER_NODE=1;_p.MOTIONBLUR_BUFFER_DRAG=2;function Mp(e){var t=this;var r=t.cy.window();var a=r.document;if(e.webgl){_p.CANVAS_LAYERS=t.CANVAS_LAYERS=4;console.log("webgl rendering enabled")}t.data={canvases:new Array(_p.CANVAS_LAYERS),contexts:new Array(_p.CANVAS_LAYERS),canvasNeedsRedraw:new Array(_p.CANVAS_LAYERS),bufferCanvases:new Array(_p.BUFFER_COUNT),bufferContexts:new Array(_p.CANVAS_LAYERS)};var n="-webkit-tap-highlight-color";var i="rgba(0,0,0,0)";t.data.canvasContainer=a.createElement("div");var o=t.data.canvasContainer.style;t.data.canvasContainer.style[n]=i;o.position="relative";o.zIndex="0";o.overflow="hidden";var s=e.cy.container();s.appendChild(t.data.canvasContainer);s.style[n]=i;var l={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};if(U()){l["-ms-touch-action"]="none";l["touch-action"]="none"}for(var u=0;u<_p.CANVAS_LAYERS;u++){var v=t.data.canvases[u]=a.createElement("canvas");var f=_p.CANVAS_TYPES[u];t.data.contexts[u]=v.getContext(f);if(!t.data.contexts[u]){Rt("Could not create canvas of type "+f)}Object.keys(l).forEach((function(e){v.style[e]=l[e]}));v.style.position="absolute";v.setAttribute("data-id","layer"+u);v.style.zIndex=String(_p.CANVAS_LAYERS-u);t.data.canvasContainer.appendChild(v);t.data.canvasNeedsRedraw[u]=false}t.data.topCanvas=t.data.canvases[0];t.data.canvases[_p.NODE].setAttribute("data-id","layer"+_p.NODE+"-node");t.data.canvases[_p.SELECT_BOX].setAttribute("data-id","layer"+_p.SELECT_BOX+"-selectbox");t.data.canvases[_p.DRAG].setAttribute("data-id","layer"+_p.DRAG+"-drag");if(t.data.canvases[_p.WEBGL]){t.data.canvases[_p.WEBGL].setAttribute("data-id","layer"+_p.WEBGL+"-webgl")}for(var u=0;u<_p.BUFFER_COUNT;u++){t.data.bufferCanvases[u]=a.createElement("canvas");t.data.bufferContexts[u]=t.data.bufferCanvases[u].getContext("2d");t.data.bufferCanvases[u].style.position="absolute";t.data.bufferCanvases[u].setAttribute("data-id","buffer"+u);t.data.bufferCanvases[u].style.zIndex=String(-u-1);t.data.bufferCanvases[u].style.visibility="hidden"}t.pathsEnabled=true;var c=qr();var d=function e(t){return{x:(t.x1+t.x2)/2,y:(t.y1+t.y2)/2}};var h=function e(t){return{x:-t.w/2,y:-t.h/2}};var p=function e(t){var r=t[0]._private;var a=r.oldBackgroundTimestamp===r.backgroundTimestamp;return!a};var g=function e(t){return t[0]._private.nodeKey};var y=function e(t){return t[0]._private.labelStyleKey};var m=function e(t){return t[0]._private.sourceLabelStyleKey};var b=function e(t){return t[0]._private.targetLabelStyleKey};var x=function e(r,a,n,i,o){return t.drawElement(r,a,n,false,false,o)};var w=function e(r,a,n,i,o){return t.drawElementText(r,a,n,i,"main",o)};var E=function e(r,a,n,i,o){return t.drawElementText(r,a,n,i,"source",o)};var T=function e(r,a,n,i,o){return t.drawElementText(r,a,n,i,"target",o)};var k=function e(t){t.boundingBox();return t[0]._private.bodyBounds};var C=function e(t){t.boundingBox();return t[0]._private.labelBounds.main||c};var P=function e(t){t.boundingBox();return t[0]._private.labelBounds.source||c};var S=function e(t){t.boundingBox();return t[0]._private.labelBounds.target||c};var D=function e(t,r){return r};var B=function e(t){return d(k(t))};var A=function e(t,r,a){var n=t?t+"-":"";return{x:r.x+a.pstyle(n+"text-margin-x").pfValue,y:r.y+a.pstyle(n+"text-margin-y").pfValue}};var _=function e(t,r,a){var n=t[0]._private.rscratch;return{x:n[r],y:n[a]}};var M=function e(t){return A("",_(t,"labelX","labelY"),t)};var I=function e(t){return A("source",_(t,"sourceLabelX","sourceLabelY"),t)};var R=function e(t){return A("target",_(t,"targetLabelX","targetLabelY"),t)};var N=function e(t){return h(k(t))};var L=function e(t){return h(P(t))};var O=function e(t){return h(S(t))};var z=function e(t){var r=C(t);var a=h(C(t));if(t.isNode()){switch(t.pstyle("text-halign").value){case"left":a.x=-r.w-(r.leftPad||0);break;case"right":a.x=-(r.rightPad||0);break}switch(t.pstyle("text-valign").value){case"top":a.y=-r.h-(r.topPad||0);break;case"bottom":a.y=-(r.botPad||0);break}}return a};var F=t.data.eleTxrCache=new Sd(t,{getKey:g,doesEleInvalidateKey:p,drawElement:x,getBoundingBox:k,getRotationPoint:B,getRotationOffset:N,allowEdgeTxrCaching:false,allowParentTxrCaching:false});var V=t.data.lblTxrCache=new Sd(t,{getKey:y,drawElement:w,getBoundingBox:C,getRotationPoint:M,getRotationOffset:z,isVisible:D});var j=t.data.slbTxrCache=new Sd(t,{getKey:m,drawElement:E,getBoundingBox:P,getRotationPoint:I,getRotationOffset:L,isVisible:D});var X=t.data.tlbTxrCache=new Sd(t,{getKey:b,drawElement:T,getBoundingBox:S,getRotationPoint:R,getRotationOffset:O,isVisible:D});var Y=t.data.lyrTxrCache=new qd(t);t.onUpdateEleCalcs((function e(t,r){F.invalidateElements(r);V.invalidateElements(r);j.invalidateElements(r);X.invalidateElements(r);Y.invalidateElements(r);for(var a=0;a<r.length;a++){var n=r[a]._private;n.oldBackgroundTimestamp=n.backgroundTimestamp}}));var q=function e(t){for(var r=0;r<t.length;r++){Y.enqueueElementRefinement(t[r].ele)}};F.onDequeue(q);V.onDequeue(q);j.onDequeue(q);X.onDequeue(q);if(e.webgl){t.initWebgl(e,{getStyleKey:g,getLabelKey:y,getSourceLabelKey:m,getTargetLabelKey:b,drawElement:x,drawLabel:w,drawSourceLabel:E,drawTargetLabel:T,getElementBox:k,getLabelBox:C,getSourceLabelBox:P,getTargetLabelBox:S,getElementRotationPoint:B,getElementRotationOffset:N,getLabelRotationPoint:M,getSourceLabelRotationPoint:I,getTargetLabelRotationPoint:R,getLabelRotationOffset:z,getSourceLabelRotationOffset:L,getTargetLabelRotationOffset:O})}}_p.redrawHint=function(e,t){var r=this;switch(e){case"eles":r.data.canvasNeedsRedraw[_p.NODE]=t;break;case"drag":r.data.canvasNeedsRedraw[_p.DRAG]=t;break;case"select":r.data.canvasNeedsRedraw[_p.SELECT_BOX]=t;break;case"gc":r.data.gc=true;break}};var Ip=typeof Path2D!=="undefined";_p.path2dEnabled=function(e){if(e===undefined){return this.pathsEnabled}this.pathsEnabled=e?true:false};_p.usePaths=function(){return Ip&&this.pathsEnabled};_p.setImgSmoothing=function(e,t){if(e.imageSmoothingEnabled!=null){e.imageSmoothingEnabled=t}else{e.webkitImageSmoothingEnabled=t;e.mozImageSmoothingEnabled=t;e.msImageSmoothingEnabled=t}};_p.getImgSmoothing=function(e){if(e.imageSmoothingEnabled!=null){return e.imageSmoothingEnabled}else{return e.webkitImageSmoothingEnabled||e.mozImageSmoothingEnabled||e.msImageSmoothingEnabled}};_p.makeOffscreenCanvas=function(e,t){var r;if((typeof OffscreenCanvas==="undefined"?"undefined":b(OffscreenCanvas))!=="undefined"){r=new OffscreenCanvas(e,t)}else{var a=this.cy.window();var n=a.document;r=n.createElement("canvas");r.width=e;r.height=t}return r};[Hd,th,lh,vh,fh,dh,ph,ap,mp,Cp,Bp].forEach((function(e){se(_p,e)}));var Rp=[{name:"null",impl:ic},{name:"base",impl:rd},{name:"canvas",impl:Ap}];var Np=[{type:"layout",extensions:nc},{type:"renderer",extensions:Rp}];var Lp={};var Op={};function zp(e,t,r){var a=r;var n=function r(a){Lt("Can not register `"+t+"` for `"+e+"` since `"+a+"` already exists in the prototype and can not be overridden")};if(e==="core"){if(gf.prototype[t]){return n(t)}else{gf.prototype[t]=r}}else if(e==="collection"){if(Bv.prototype[t]){return n(t)}else{Bv.prototype[t]=r}}else if(e==="layout"){var i=function e(t){this.options=t;r.call(this,t);if(!_(this._private)){this._private={}}this._private.cy=t.cy;this._private.listeners=[];this.createEmitter()};var o=i.prototype=Object.create(r.prototype);var s=[];for(var l=0;l<s.length;l++){var u=s[l];o[u]=o[u]||function(){return this}}if(o.start&&!o.run){o.run=function(){this.start();return this}}else if(!o.start&&o.run){o.start=function(){this.run();return this}}var v=r.prototype.stop;o.stop=function(){var e=this.options;if(e&&e.animate){var t=this.animations;if(t){for(var r=0;r<t.length;r++){t[r].stop()}}}if(v){v.call(this)}else{this.emit("layoutstop")}return this};if(!o.destroy){o.destroy=function(){return this}}o.cy=function(){return this._private.cy};var f=function e(t){return t._private.cy};var c={addEventFields:function e(t,r){r.layout=t;r.cy=f(t);r.target=t},bubble:function e(){return true},parent:function e(t){return f(t)}};se(o,{createEmitter:function e(){this._private.emitter=new Uu(c,this);return this},emitter:function e(){return this._private.emitter},on:function e(t,r){this.emitter().on(t,r);return this},one:function e(t,r){this.emitter().one(t,r);return this},once:function e(t,r){this.emitter().one(t,r);return this},removeListener:function e(t,r){this.emitter().removeListener(t,r);return this},removeAllListeners:function e(){this.emitter().removeAllListeners();return this},emit:function e(t,r){this.emitter().emit(t,r);return this}});fl.eventAliasesOn(o);a=i}else if(e==="renderer"&&t!=="null"&&t!=="base"){var d=Fp("renderer","base");var h=d.prototype;var p=r;var g=r.prototype;var y=function e(){d.apply(this,arguments);p.apply(this,arguments)};var m=y.prototype;for(var b in h){var x=h[b];var w=g[b]!=null;if(w){return n(b)}m[b]=x}for(var E in g){m[E]=g[E]}h.clientFunctions.forEach((function(e){m[e]=m[e]||function(){Rt("Renderer does not implement `renderer."+e+"()` on its prototype")}}));a=y}else if(e==="__proto__"||e==="constructor"||e==="prototype"){return Rt(e+" is an illegal type to be registered, possibly lead to prototype pollutions")}return he({map:Lp,keys:[e,t],value:a})}function Fp(e,t){return pe({map:Lp,keys:[e,t]})}function Vp(e,t,r,a,n){return he({map:Op,keys:[e,t,r,a],value:n})}function jp(e,t,r,a){return pe({map:Op,keys:[e,t,r,a]})}var Xp=function e(){if(arguments.length===2){return Fp.apply(null,arguments)}else if(arguments.length===3){return zp.apply(null,arguments)}else if(arguments.length===4){return jp.apply(null,arguments)}else if(arguments.length===5){return Vp.apply(null,arguments)}else{Rt("Invalid extension access syntax")}};gf.prototype.extension=Xp;Np.forEach((function(e){e.extensions.forEach((function(t){zp(e.type,t.name,t.impl)}))}));var Yp=function e(){if(!(this instanceof Yp)){return new Yp}this.length=0};var qp=Yp.prototype;qp.instanceString=function(){return"stylesheet"};qp.selector=function(e){var t=this.length++;this[t]={selector:e,properties:[]};return this};qp.css=function(e,t){var r=this.length-1;if(D(e)){this[r].properties.push({name:e,value:t})}else if(_(e)){var a=e;var n=Object.keys(a);for(var i=0;i<n.length;i++){var o=n[i];var s=a[o];if(s==null){continue}var l=vf.properties[o]||vf.properties[K(o)];if(l==null){continue}var u=l.name;var v=s;this[r].properties.push({name:u,value:v})}}return this};qp.style=qp.css;qp.generateStyle=function(e){var t=new vf(e);return this.appendToStyle(t)};qp.appendToStyle=function(e){for(var t=0;t<this.length;t++){var r=this[t];var a=r.selector;var n=r.properties;e.selector(a);for(var i=0;i<n.length;i++){var o=n[i];e.css(o.name,o.value)}}return e};var Wp="3.31.1";var Up=function e(t){if(t===undefined){t={}}if(_(t)){return new gf(t)}else if(D(t)){return Xp.apply(Xp,arguments)}};Up.use=function(e){var t=Array.prototype.slice.call(arguments,1);t.unshift(Up);e.apply(null,t);return this};Up.warnings=function(e){return Nt(e)};Up.version=Wp;Up.stylesheet=Up.Stylesheet=Yp}}]);