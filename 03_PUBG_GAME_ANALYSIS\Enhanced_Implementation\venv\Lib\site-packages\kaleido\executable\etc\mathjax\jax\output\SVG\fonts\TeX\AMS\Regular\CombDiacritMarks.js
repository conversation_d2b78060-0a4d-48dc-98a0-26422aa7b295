/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/AMS/Regular/CombDiacritMarks.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_AMS'],
  {
    // COMBINING CIRCUMFLEX ACCENT
    0x302: [845,-561,0,-2347,13,'-2332 561Q-2336 563 -2340 577T-2346 604L-2347 618Q-2347 625 -2340 628T-2310 635Q-2302 636 -2297 637Q-2270 641 -1712 745Q-1185 845 -1168 845Q-1166 845 -581 739L5 630Q13 630 13 618Q7 565 -1 561Q-4 561 -584 654Q-716 675 -867 699T-1092 736T-1166 748Q-1168 748 -1240 737T-1466 700T-1750 654Q-2330 561 -2332 561'],

    // COMBINING TILDE
    0x303: [899,-628,0,-2332,-3,'-1529 788Q-1616 788 -1727 772T-1936 732T-2120 685T-2258 645T-2315 628Q-2322 628 -2322 632Q-2325 637 -2329 668T-2331 704Q-2331 713 -2297 732Q-2278 739 -2091 795Q-1711 898 -1507 898Q-1440 898 -1386 895Q-1324 887 -1277 872T-1146 819Q-1047 776 -977 758T-806 739Q-719 739 -608 755T-399 795T-215 842T-77 882T-20 899Q-13 899 -13 895Q-10 890 -6 860T-4 824Q-4 818 -37 795Q-60 787 -244 732Q-523 657 -735 632Q-771 629 -841 629Q-944 629 -1013 644T-1189 708Q-1285 751 -1356 769T-1529 788']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/AMS/Regular/CombDiacritMarks.js");
