{"version": 3, "file": "4498.4d8665e22c39c0b3f329.js?v=4d8665e22c39c0b3f329", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc,GAAG,uBAAuB,GAAG,6BAA6B,GAAG,qBAAqB,GAAG,mBAAmB,GAAG,sBAAsB,GAAG,cAAc,GAAG,YAAY,GAAG,YAAY,GAAG,iBAAiB,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,eAAe,GAAG,cAAc,GAAG,cAAc,GAAG,gBAAgB;AACnU,eAAe;AACf;AACA;AACA;AACA;AACA,gBAAgB;AAChB,cAAc;AACd,cAAc;AACd,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,2DAA2D,UAAU;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,iFAAiF,MAAM;AACvF;AACA,YAAY;AACZ;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE,6CAA6C;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,UAAU;AACrE;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA,kCAAkC,qCAAqC;AACvE;AACA;AACA,sBAAsB;AACtB;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA,kCAAkC,oCAAoC;AACtE;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA,oEAAoE,gBAAgB;AACpF;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA,gFAAgF,mBAAmB;AACnG;AACA,2BAA2B;AAC3B;AACA,+EAA+E,qBAAqB,UAAU;AAC9G;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,0BAA0B;AAC1B;AACA;AACA,cAAc;AACd", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/Options.js"], "sourcesContent": ["\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.lookup = exports.separateOptions = exports.selectOptionsFromKeys = exports.selectOptions = exports.userOptions = exports.defaultOptions = exports.insert = exports.copy = exports.keys = exports.makeArray = exports.expandable = exports.Expandable = exports.OPTIONS = exports.REMOVE = exports.APPEND = exports.isObject = void 0;\nvar OBJECT = {}.constructor;\nfunction isObject(obj) {\n    return typeof obj === 'object' && obj !== null &&\n        (obj.constructor === OBJECT || obj.constructor === Expandable);\n}\nexports.isObject = isObject;\nexports.APPEND = '[+]';\nexports.REMOVE = '[-]';\nexports.OPTIONS = {\n    invalidOption: 'warn',\n    optionError: function (message, _key) {\n        if (exports.OPTIONS.invalidOption === 'fatal') {\n            throw new Error(message);\n        }\n        console.warn('MathJax: ' + message);\n    }\n};\nvar Expandable = (function () {\n    function Expandable() {\n    }\n    return Expandable;\n}());\nexports.Expandable = Expandable;\nfunction expandable(def) {\n    return Object.assign(Object.create(Expandable.prototype), def);\n}\nexports.expandable = expandable;\nfunction makeArray(x) {\n    return Array.isArray(x) ? x : [x];\n}\nexports.makeArray = makeArray;\nfunction keys(def) {\n    if (!def) {\n        return [];\n    }\n    return Object.keys(def).concat(Object.getOwnPropertySymbols(def));\n}\nexports.keys = keys;\nfunction copy(def) {\n    var e_1, _a;\n    var props = {};\n    try {\n        for (var _b = __values(keys(def)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var key = _c.value;\n            var prop = Object.getOwnPropertyDescriptor(def, key);\n            var value = prop.value;\n            if (Array.isArray(value)) {\n                prop.value = insert([], value, false);\n            }\n            else if (isObject(value)) {\n                prop.value = copy(value);\n            }\n            if (prop.enumerable) {\n                props[key] = prop;\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    return Object.defineProperties(def.constructor === Expandable ? expandable({}) : {}, props);\n}\nexports.copy = copy;\nfunction insert(dst, src, warn) {\n    var e_2, _a;\n    if (warn === void 0) { warn = true; }\n    var _loop_1 = function (key) {\n        if (warn && dst[key] === undefined && dst.constructor !== Expandable) {\n            if (typeof key === 'symbol') {\n                key = key.toString();\n            }\n            exports.OPTIONS.optionError(\"Invalid option \\\"\".concat(key, \"\\\" (no default value).\"), key);\n            return \"continue\";\n        }\n        var sval = src[key], dval = dst[key];\n        if (isObject(sval) && dval !== null &&\n            (typeof dval === 'object' || typeof dval === 'function')) {\n            var ids = keys(sval);\n            if (Array.isArray(dval) &&\n                ((ids.length === 1 && (ids[0] === exports.APPEND || ids[0] === exports.REMOVE) && Array.isArray(sval[ids[0]])) ||\n                    (ids.length === 2 && ids.sort().join(',') === exports.APPEND + ',' + exports.REMOVE &&\n                        Array.isArray(sval[exports.APPEND]) && Array.isArray(sval[exports.REMOVE])))) {\n                if (sval[exports.REMOVE]) {\n                    dval = dst[key] = dval.filter(function (x) { return sval[exports.REMOVE].indexOf(x) < 0; });\n                }\n                if (sval[exports.APPEND]) {\n                    dst[key] = __spreadArray(__spreadArray([], __read(dval), false), __read(sval[exports.APPEND]), false);\n                }\n            }\n            else {\n                insert(dval, sval, warn);\n            }\n        }\n        else if (Array.isArray(sval)) {\n            dst[key] = [];\n            insert(dst[key], sval, false);\n        }\n        else if (isObject(sval)) {\n            dst[key] = copy(sval);\n        }\n        else {\n            dst[key] = sval;\n        }\n    };\n    try {\n        for (var _b = __values(keys(src)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var key = _c.value;\n            _loop_1(key);\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n    return dst;\n}\nexports.insert = insert;\nfunction defaultOptions(options) {\n    var defs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        defs[_i - 1] = arguments[_i];\n    }\n    defs.forEach(function (def) { return insert(options, def, false); });\n    return options;\n}\nexports.defaultOptions = defaultOptions;\nfunction userOptions(options) {\n    var defs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        defs[_i - 1] = arguments[_i];\n    }\n    defs.forEach(function (def) { return insert(options, def, true); });\n    return options;\n}\nexports.userOptions = userOptions;\nfunction selectOptions(options) {\n    var e_3, _a;\n    var keys = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        keys[_i - 1] = arguments[_i];\n    }\n    var subset = {};\n    try {\n        for (var keys_1 = __values(keys), keys_1_1 = keys_1.next(); !keys_1_1.done; keys_1_1 = keys_1.next()) {\n            var key = keys_1_1.value;\n            if (options.hasOwnProperty(key)) {\n                subset[key] = options[key];\n            }\n        }\n    }\n    catch (e_3_1) { e_3 = { error: e_3_1 }; }\n    finally {\n        try {\n            if (keys_1_1 && !keys_1_1.done && (_a = keys_1.return)) _a.call(keys_1);\n        }\n        finally { if (e_3) throw e_3.error; }\n    }\n    return subset;\n}\nexports.selectOptions = selectOptions;\nfunction selectOptionsFromKeys(options, object) {\n    return selectOptions.apply(void 0, __spreadArray([options], __read(Object.keys(object)), false));\n}\nexports.selectOptionsFromKeys = selectOptionsFromKeys;\nfunction separateOptions(options) {\n    var e_4, _a, e_5, _b;\n    var objects = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        objects[_i - 1] = arguments[_i];\n    }\n    var results = [];\n    try {\n        for (var objects_1 = __values(objects), objects_1_1 = objects_1.next(); !objects_1_1.done; objects_1_1 = objects_1.next()) {\n            var object = objects_1_1.value;\n            var exists = {}, missing = {};\n            try {\n                for (var _c = (e_5 = void 0, __values(Object.keys(options || {}))), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var key = _d.value;\n                    (object[key] === undefined ? missing : exists)[key] = options[key];\n                }\n            }\n            catch (e_5_1) { e_5 = { error: e_5_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_b = _c.return)) _b.call(_c);\n                }\n                finally { if (e_5) throw e_5.error; }\n            }\n            results.push(exists);\n            options = missing;\n        }\n    }\n    catch (e_4_1) { e_4 = { error: e_4_1 }; }\n    finally {\n        try {\n            if (objects_1_1 && !objects_1_1.done && (_a = objects_1.return)) _a.call(objects_1);\n        }\n        finally { if (e_4) throw e_4.error; }\n    }\n    results.unshift(options);\n    return results;\n}\nexports.separateOptions = separateOptions;\nfunction lookup(name, lookup, def) {\n    if (def === void 0) { def = null; }\n    return (lookup.hasOwnProperty(name) ? lookup[name] : def);\n}\nexports.lookup = lookup;\n//# sourceMappingURL=Options.js.map"], "names": [], "sourceRoot": ""}