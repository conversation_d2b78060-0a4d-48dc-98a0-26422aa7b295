/*************************************************************
 *
 *  MathJax/localization/br/TeX.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("br","TeX",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "Briataenn digeri\u00F1 a re pe briataenn serri\u00F1 diank",
          ExtraCloseMissingOpen: "Briataenn serri\u00F1 a re pe briataenn digeri\u00F1 diank",
          MissingLeftExtraRight: "\\left diank pe \\right a re",
          ExtraLeftMissingRight: "\\left a re pe \\right diank",
          Misplaced: "%1 lec'hiet fall",
          AmbiguousUseOf: "Implij forc'hellek eus %1",
          EnvBadEnd: "\\begin{%1} zo echuet gant \\end{%2}",
          EnvMissingEnd: "\\end{%1} a vank",
          MissingBoxFor: "Ar voest a vank evit %1",
          MissingCloseBrace: "Ar vriataenn serri\u00F1 a vank",
          MisplacedMiddle: "%1 a rank beza\u00F1 e-barzh \\left ha \\right",
          MisplacedMoveRoot: "%1 ne c'hall beza\u00F1 nemet e-barzh ur wrizienn",
          MultipleCommand: "Meur a %1",
          IntegerArg: "Arguzenn %1 a rank beza\u00F1 un niver anterin",
          InvalidMathMLAttr: "Doareenn MathML direizh : %1",
          MissingArgFor: "Arguzenn diank evit %1",
          InvalidEnv: "Anv endro \"%1\" direizh",
          UnknownEnv: "endro dianav \"%1\"",
          TokenNotFoundForCommand: "N'eus ket bet gallet kavout %1 evit %2",
          MathNotTerminated: "Formulenn diechu er voest testenn",
          CommandNotAllowedInEnv: "%1 n'eo ket aotreet en endro %2",
          MultipleLabel: "Label '%1' termenet meur a wech",
          CommandAtTheBeginingOfLine: "%1 a rank beza\u00F1 e dero\u00F9 al linenn",
          BadMathStyleFor: "Stil matematikel direizh evit %1",
          PositiveIntegerArg: "Arguzenn %1 a rank beza\u00F1 un niver pozitivel anterin",
          MultlineRowsOneCol: "Ar renko\u00F9 e-barzh an endro %1 a rank kaout ur bann dres",
          MultipleBBoxProperty: "%1 diferet div wech e-barzh %2",
          ExtraEndMissingBegin: "%1 a re pe \\begingroup diank",
          GlobalNotFollowedBy: "%1 n'eo ket heuliet gant \\let, \\def, pe \\newcommand",
          UndefinedColorModel: "N'eo ket termenet patrom liv \"%1\"",
          InvalidDecimalNumber: "Niver degel direizh",
          InvalidNumber: "Niver direizh",
          NewextarrowArg2: "Eil arguzenn %1 a rank beza\u00F1 daou niver anterin dispartiet gant ur virgulenn",
          NewextarrowArg3: "Trede arguzenn %1 a rank beza\u00F1 un niverenn arouezenn unicode",
          NoClosingChar: "Ne c'haller ket kavout an %1 serri\u00F1",
          CantUseHash2: "Implij direizh eus # er patrom evit %1",
          MismatchUseDef: "An implij eus %1 ne glot ket gant an termenadur anezha\u00F1",
          RunawayArgument: "Arguzenn diechu evit %1 ?",
          NoClosingDelim: "Ne c'haller ket kavout ur bevenner serri\u00F1 evit %1",
          MissingScript: "Arguzenn en usskrid pe en isskrid a vank",
          MissingOpenForSub: "Briataenn digeri\u00F1 a vank evit ar skrid en isskrid",
          MissingOpenForSup: "Briataenn digeri\u00F1 a vank evit ar skrid en usskrid",
          UndefinedControlSequence: "An urzh kontrolla\u00F1 %1 n'eo ket termenet",
          DoubleExponent: "Usskrid doubl : implijit briataenno\u00F9 evit sklaeraat",
          DoubleSubscripts: "Isskrid double : implijit briataenno\u00F9 evit sklaeraat",
          CantUseHash1: "Ne c'hallit ket implijout an arouezenn # er mod jedoniezh",
          MisplacedLimits: "N'eo aotreet an urzh %1 nemet gant an oberataerio\u00F9",
          NotMathMLToken: "N'eo ket an elfenn %1 un elfenn MathML elfennel",
          UnknownAttrForElement: "Dianav eo an doareenn %1 d'an elfenn %2",
          ExtraAlignTab: "Arouezenn steuda\u00F1 '\u0026' dic'hortoz evit testenn an urzh /cases",
          BracketMustBeDimension: "An argutezenn etre krochedo\u00F9 an urzh %1 a rank beza\u00F1 ur vent",
          ExtraCloseLooking: "Briataenn serri\u00F1 dic'hortoz pa'z eo bet enklasket %1",
          MissingCloseBracket: "Ne c'haller ket kavout ']' evit arguzenn an urzh %1",
          MissingOrUnrecognizedDelim: "Ar bevenner a vank pe n'eo ket anavezet gant an urzh %1",
          MissingDimOrUnits: "Ar Vent pe an unvez a vank evit an urzh %1",
          IllegalMacroParam: "N'eo ket aotreet an arventenn dave makro",
          IllegalAlign: "N'eo ket aotreet ar steudad evit an urzh %1",
          ErroneousNestingEq: "Empradur direizh ar frammo\u00F9 kevatalenno\u00F9",
          InvalidBBoxProperty: "Seblantout a ra n'eo ket an talvoud '%1' ul liv, ur ment marz bihanoc'h pe ur stil.",
          ModelArg1: "An talvoudo\u00F9 livio\u00F9 evit ar patrom %1 o deus ezhomm eus 3 niverenn",
          ModelArg2: "An talvoudo\u00F9 livio\u00F9 evit ar patrom %1 a rank beza\u00F1 etre %2 ha %3",
          NewextarrowArg1: "Arguzenn genta\u00F1 an urzhiad %1 a rank beza\u00F1 anv ur seka\u00F1s kontrolla\u00F1",
          IllegalControlSequenceName: "N'eo ket aotreet anv ar c'hontroll seka\u00F1s evit an urzh %1",
          IllegalParamNumber: "Niver direizh a arventenno\u00F9 evit an urzh %1",
          MissingCS: "%1 a rank beza\u00F1 heuliet gant ur seka\u00F1s kontrolla\u00F1",
          MissingReplacementString: "Ar chadennad arouezenno\u00F9 erlec'hia\u00F1 a vank evit an termenadur %1.",
          DoubleExponentPrime: "Un ask a zegas un usskrid doubl : implijit briataenno\u00F9 evit sklaeraat"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/br/TeX.js");
