/*************************************************************
 *
 *  MathJax/localization/es/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("es","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "Mostrar las f\u00F3rmulas como",
          MathMLcode: "C\u00F3digo de MathML",
          OriginalMathML: "MathML original",
          TeXCommands: "\u00D3rdenes de TeX",
          AsciiMathInput: "Entrada AsciiMathML",
          Original: "Forma original",
          ErrorMessage: "Mensaje de error",
          Annotation: "Anotaci\u00F3n",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "Contenido MathML",
          OpenMath: "OpenMath",
          texHints: "Mostrar sugerencias TeX en MathML",
          Settings: "Configuraci\u00F3n matem\u00E1tica",
          ZoomTrigger: "Activar zoom",
          Hover: "Apuntar con el rat\u00F3n",
          Click: "Pulsaci\u00F3n del rat\u00F3n",
          DoubleClick: "Pulsaci\u00F3n doble del rat\u00F3n",
          NoZoom: "Sin ampliaci\u00F3n",
          TriggerRequires: "La activaci\u00F3n requiere:",
          Option: "Opci\u00F3n",
          Alt: "Alt",
          Command: "Orden",
          Control: "Control",
          Shift: "May\u00FAs",
          ZoomFactor: "Factor de ampliaci\u00F3n",
          Renderer: "Renderizador matem\u00E1tico",
          MPHandles: "Permitir que MathPlayer gestione:",
          MenuEvents: "Eventos del men\u00FA",
          MouseEvents: "Eventos del rat\u00F3n",
          MenuAndMouse: "Eventos del men\u00FA y del rat\u00F3n",
          FontPrefs: "Preferencias tipogr\u00E1ficas",
          ForHTMLCSS: "Para HTML-CSS:",
          Auto: "Autom\u00E1tico",
          TeXLocal: "TeX (local)",
          TeXWeb: "TeX (web)",
          TeXImage: "TeX (imagen)",
          STIXLocal: "STIX (local)",
          STIXWeb: "STIX (web)",
          AsanaMathWeb: "Asana Math (web)",
          GyrePagellaWeb: "Gyre Pagella (web)",
          GyreTermesWeb: "Gyre Termes (web)",
          LatinModernWeb: "Latin Modern (web)",
          NeoEulerWeb: "Neo Euler (web)",
          ContextMenu: "Men\u00FA contextual",
          Browser: "Navegador",
          Scale: "Escalar todas las f\u00F3rmulas\u2026",
          Discoverable: "Resaltar al apuntar con el rat\u00F3n",
          Locale: "Idioma",
          LoadLocale: "Cargar a partir de URL\u2026",
          About: "Acerca de MathJax",
          Help: "Ayuda de MathJax",
          localTeXfonts: "usando tipos de letra locales TeX",
          webTeXfonts: "usando tipo de letra web TeX",
          imagefonts: "usando tipos de letra de imagen",
          localSTIXfonts: "usando tipos de letra locales STIX",
          webSVGfonts: "usando tipos de letra web SVG",
          genericfonts: "usando tipos de letra Unicode gen\u00E9ricas",
          wofforotffonts: "Tipos de letra WOFF u OTF",
          eotffonts: "Tipos de letra EOT",
          svgfonts: "Tipos de letra SVG",
          WebkitNativeMMLWarning: "Parece que tu navegador no admite MathML de manera nativa; si cambias a la salida MathML es posible que la notaci\u00F3n matem\u00E1tica de la p\u00E1gina se vuelva ilegible",
          MSIENativeMMLWarning: "Internet Explorer necesita el complemento MathPlayer para procesar la salida de MathML.",
          OperaNativeMMLWarning: "La compatibilidad de Opera con MathML es limitada, por lo que cambiar a la salida de MathML puede causar que algunas expresiones no se visualicen bien.",
          SafariNativeMMLWarning: "El MathML nativo de tu navegador no implementa todas las funciones utilizadas por MathJax, por lo que algunas expresiones pueden no visualizarse correctamente.",
          FirefoxNativeMMLWarning: "El MathML nativo de tu navegador no implementa todas las funciones utilizadas por MathJax, por lo que algunas expresiones pueden no visualizarse correctamente.",
          MSIESVGWarning: "SVG no est\u00E1 implementado en Internet Explorer anterior a IE9 o cuando se est\u00E1 emulando IE8 o anteriores. Cambiar a la salida SVG har\u00E1 que la notaci\u00F3n matem\u00E1tica no se visualice correctamente.",
          LoadURL: "Cargar datos de traducci\u00F3n desde esta direcci\u00F3n URL:",
          BadURL: "La direcci\u00F3n URL debe ser para un archivo JavaScript que define los datos de traducci\u00F3n de MathJax. Los nombres de los archivos de JavaScript deben terminar con \".js\"",
          BadData: "No se pudieron cargar los datos de traducci\u00F3n de %1",
          SwitchAnyway: "\u00BFCambiar al renderizador de todos modos?\n\n(Presiona OK para cambiar, CANCELAR para continuar con el renderizador actual)",
          ScaleMath: "Escalar toda la matem\u00E1tica (comparado con el texto circundante) por",
          NonZeroScale: "La escala no debe ser cero",
          PercentScale: "La escala debe ser un porcentaje (p. ej.: 120 %%)",
          IE8warning: "Esto deshabilitar\u00E1 las funciones de men\u00FA y zoom de MathJax, pero puedes pulsar Alt-Clic en una expresi\u00F3n para obtener el men\u00FA MathJax en su lugar.\n\n\u00BFRealmente quieres cambiar la configuraci\u00F3n de MathPlayer?",
          IE9warning: "El men\u00FA contextual de MathJax ser\u00E1 desactivado, pero puedes pulsar Alt-Clic en una expresi\u00F3n para obtener el men\u00FA MathJax en su lugar.",
          NoOriginalForm: "La forma original no est\u00E1 disponible",
          Close: "Cerrar",
          EqSource: "C\u00F3digo de la ecuaci\u00F3n de MathJax",
          CloseAboutDialog: "Cerrar el di\u00E1logo de acerca de MathJax",
          FastPreview: "Vista previa r\u00E1pida",
          AssistiveMML: "Asistente MathML",
          InTabOrder: "Incluir en el Orden de Pesta\u00F1as"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/es/MathMenu.js");
