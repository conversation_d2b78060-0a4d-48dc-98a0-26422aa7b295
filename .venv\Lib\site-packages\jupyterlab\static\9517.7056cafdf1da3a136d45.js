"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[9517],{79517:(e,t,i)=>{i.r(t);i.d(t,{properties:()=>n});const n={name:"properties",token:function(e,t){var i=e.sol()||t.afterSection;var n=e.eol();t.afterSection=false;if(i){if(t.nextMultiline){t.inMultiline=true;t.nextMultiline=false}else{t.position="def"}}if(n&&!t.nextMultiline){t.inMultiline=false;t.position="def"}if(i){while(e.eatSpace()){}}var l=e.next();if(i&&(l==="#"||l==="!"||l===";")){t.position="comment";e.skipToEnd();return"comment"}else if(i&&l==="["){t.afterSection=true;e.skipTo("]");e.eat("]");return"header"}else if(l==="="||l===":"){t.position="quote";return null}else if(l==="\\"&&t.position==="quote"){if(e.eol()){t.nextMultiline=true}}return t.position},startState:function(){return{position:"def",nextMultiline:false,inMultiline:false,afterSection:false}}}}}]);