/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/AMS/Regular/Dingbats.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_AMS'],
  {
    // CHECK MARK
    0x2713: [706,34,833,84,749,'84 231Q84 244 114 264T170 285Q176 285 183 274T224 205Q267 129 268 129Q271 141 279 163T318 250T389 378T502 523T662 673Q702 706 732 706H734Q749 706 749 695Q749 682 730 666T660 607T559 505Q387 299 328 29Q324 0 295 -17T245 -34H241Q234 -34 225 -21T185 46Q166 79 154 101Q84 223 84 231'],

    // MALTESE CROSS
    0x2720: [716,22,833,48,786,'195 702T195 706T201 716H632Q638 710 638 706T636 700T621 690Q436 581 427 374V357H430Q554 357 645 421Q682 447 711 483T755 542T770 567Q775 572 786 563V131Q777 125 774 125T762 139Q709 228 642 274T482 333Q452 337 430 337H427V320Q430 279 437 247T462 170T521 82T621 4Q630 -2 633 -4T637 -7T638 -12Q638 -16 632 -22H201Q195 -16 195 -12T197 -6T212 4Q397 113 406 320V337H403Q279 337 188 273Q151 247 122 211T78 152T63 127Q58 122 48 131V563Q54 569 59 569Q62 569 71 555Q124 466 191 420T351 361Q381 357 403 357H406V374Q403 415 396 447T371 525T312 613T212 690Q199 697 197 699']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/AMS/Regular/Dingbats.js");
