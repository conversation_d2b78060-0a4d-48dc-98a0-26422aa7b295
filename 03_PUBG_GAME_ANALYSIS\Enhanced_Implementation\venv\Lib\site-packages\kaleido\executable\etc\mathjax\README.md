# MathJax

## Beautiful math in all browsers

MathJax is an open-source JavaScript display engine for LaTeX, MathML, and
AsciiMath notation that works in all modern browsers.  It was designed with
the goal of consolidating the recent advances in web technologies into a
single, definitive, math-on-the-web platform supporting the major browsers
and operating systems.  It requires no setup on the part of the user (no
plugins to download or software to install), so the page author can write
web documents that include mathematics and be confident that users will be
able to view it naturally and easily.  Simply include MathJax and some
mathematics in a web page, and MathJax does the rest.

Some of the main features of MathJax include:

- High-quality display of LaTeX, MathML, and AsciiMath notation in HTML pages

- Supported in most browsers with no plug-ins, extra fonts, or special
  setup for the reader

- Easy for authors, flexible for publishers, extensible for developers

- Supports math accessibility, cut-and-paste interoperability, and other
  advanced functionality

- Powerful API for integration with other web applications

See <http://www.mathjax.org/> for additional details.


## Installation and Usage

The MathJax installation and usage documentation is available in a
separate GitHub repository at <https://github.com/mathjax/mathjax-docs>.
The HTML versions can now be viewed at <http://docs.mathjax.org/>, 
where it is possible for you to submit corrections and modifications
directly to the documentation on line.


## Community

The main MathJax website is <http://www.mathjax.org>, and it includes
announcements and other important information.  MathJax is maintained and
distributed on GitHub at <http://github.com/mathjax/MathJax>.  A user forum
for asking questions and getting assistance is hosted at Google, and the
bug tracker is hosted at GitHub:

Bug tracker:         <https://github.com/mathjax/MathJax/issues>  
MathJax-Users Group: <http://groups.google.com/group/mathjax-users>

Before reporting a bug, please check that it has not already been reported.
Also, please use the bug tracker for reporting bugs rather than the help forum.
