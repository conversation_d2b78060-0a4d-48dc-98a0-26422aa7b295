{"version": 3, "file": "6873.d5b12730d4556b6f37bf.js?v=d5b12730d4556b6f37bf", "mappings": ";;;;;AAAA,eAAe,KAAoD,oBAAoB,CAAqI,CAAC,kBAAkB,aAAa,qBAAqB,6BAA6B,qBAAqB,4BAA4B,0CAA0C,+GAA+G,IAAI,kBAAkB,UAAU,qCAAqC,gCAAgC,+BAA+B,kCAAkC,wCAAwC,sDAAsD,yDAAyD,sEAAsE,8CAA8C,8BAA8B,qCAAqC,6CAA6C,kBAAkB,GAAG,yBAAyB;;;;;;;ACAvkC,eAAe,KAAoD,oBAAoB,CAAwI,CAAC,kBAAkB,aAAa,OAAO,wHAAwH,qFAAqF,IAAI,+DAA+D,eAAe,8BAA8B,kBAAkB,mBAAmB,YAAY,sCAAsC,yBAAyB,sBAAsB,eAAe,oBAAoB,mDAAmD,+BAA+B,IAAI,gBAAgB,WAAW,wCAAwC,iBAAiB,mBAAmB,MAAM,YAAY,MAAM,gCAAgC,OAAO,OAAO,yBAAyB,SAAS,IAAI,iBAAiB,uBAAuB,mBAAmB,uBAAuB,mBAAmB,qBAAqB,mBAAmB,yBAAyB,oBAAoB,wBAAwB,WAAW,EAAE,cAAc,qBAAqB,uMAAuM,iCAAiC,+BAA+B,MAAM,iDAAiD,yFAAyF,0DAA0D,oBAAoB,iBAAiB,uBAAuB,mBAAmB,sBAAsB,+BAA+B,uBAAuB,mBAAmB,6CAA6C,eAAe,YAAY,EAAE,uBAAuB,cAAc,QAAQ,mBAAmB,4CAA4C,IAAI,GAAG,IAAI,qBAAqB,yBAAyB,qFAAqF,qBAAqB,GAAG,4BAA4B,IAAI,MAAM,sCAAsC,QAAQ,iBAAiB,0BAA0B,mBAAmB,YAAY,SAAS,IAAI,MAAM,WAAW,kCAAkC,KAAK,qDAAqD,+BAA+B,mBAAmB,kBAAkB,eAAe,cAAc,8DAA8D,OAAO,uBAAuB,yEAAyE,4BAA4B,oBAAoB,8BAA8B,UAAU,WAAW,uBAAuB,0CAA0C,0EAA0E,IAAI,8DAA8D,4KAA4K,gCAAgC,kCAAkC,0JAA0J,SAAS,qBAAqB,+GAA+G,kDAAkD,KAAK,MAAM,YAAY,sBAAsB,gBAAgB,sCAAsC,MAAM,8BAA8B,sBAAsB;;;;;;;ACApyH,eAAe,KAAoD,oBAAoB,CAA8H,CAAC,kBAAkB,aAAa,YAAY,uBAAuB,kBAAkB,iCAAiC,eAAe,yBAAyB,sBAAsB,uBAAuB,+DAA+D,wJAAwJ,0BAA0B,0BAA0B,sEAAsE,gBAAgB,wBAAwB,kCAAkC,yKAAyK;;;;;;;;;;;;;;;;;;;;;;;ACEt8B;AAcA;;AAE9B;AACA;AACA,0BAA0B,qEAAM;AAChC,sBAAsB,gBAAgB,KAAK;AAC3C;AACA,GAAG;AACH;AACA,2BAA2B,qEAAM;AACjC,KAAK;AACL,UAAU;AACV,gBAAgB,syBAAsyB;AACtzB,kBAAkB,woBAAwoB;AAC1pB;AACA,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,cAAc,iBAAiB,IAAI,QAAQ,mBAAmB,MAAM,KAAK,8RAA8R,mBAAmB,WAAW,qBAAqB,uPAAuP,+NAA+N,aAAa,IAAI,aAAa,uDAAuD,aAAa,2JAA2J,0BAA0B,uFAAuF,0BAA0B,qBAAqB,aAAa,qBAAqB,aAAa,sCAAsC,aAAa;AAC/2C,sBAAsB;AACtB,gCAAgC,qEAAM;AACtC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,KAAK;AACL,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ,iEAAiE;AACjE;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA,OAAO;AACP;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA,OAAO;AACP;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP,4DAA4D;AAC5D,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA,OAAO;AACP;AACA,sCAAsC,qEAAM;AAC5C;AACA,OAAO;AACP,iBAAiB,0BAA0B;AAC3C,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,uBAAuB,2HAA2H,gBAAgB,eAAe,mBAAmB,sBAAsB,gUAAgU,8EAA8E,gCAAgC,4BAA4B,4BAA4B,8BAA8B,6VAA6V,6DAA6D;AACxnC,oBAAoB,yBAAyB,qCAAqC,iBAAiB,kCAAkC,iBAAiB,kCAAkC,oBAAoB,uCAAuC,oBAAoB,2CAA2C,YAAY,uCAAuC,aAAa,uCAAuC,eAAe;AACxa;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACsD;AAC5B;AACyB;AACoB;AACN;AACjE,mDAAY,CAAC,gEAAY;AACzB,mDAAY,CAAC,0EAAsB;AACnC,mDAAY,CAAC,uEAAmB;AAChC,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,oEAAK;AACP;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA,CAAC;AACD,sCAAsC,qEAAM;AAC5C;AACA,CAAC;AACD,sCAAsC,qEAAM;AAC5C;AACA,CAAC;AACD,qCAAqC,qEAAM;AAC3C;AACA,CAAC;AACD,qCAAqC,qEAAM;AAC3C;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA,CAAC;AACD,8CAA8C,qEAAM;AACpD;AACA,CAAC;AACD,2CAA2C,qEAAM;AACjD;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA,CAAC;AACD,qCAAqC,qEAAM;AAC3C;AACA,CAAC;AACD,qCAAqC,qEAAM;AAC3C;AACA,CAAC;AACD,qCAAqC,qEAAM;AAC3C;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA,CAAC;AACD,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,gBAAgB,4CAAK;AACrB,IAAI;AACJ,gBAAgB,4CAAK;AACrB;AACA;AACA;AACA;AACA,sBAAsB,4CAAK;AAC3B,IAAI;AACJ,sBAAsB,4CAAK;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,4CAAK;AACnB;AACA;AACA,IAAI;AACJ,IAAI,8DAAG;AACP,IAAI,8DAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,4CAAK;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,4CAAK;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,kBAAkB,iBAAiB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,4CAAK;AAC9B;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,kBAAkB,iBAAiB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC,sCAAsC,qEAAM;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,4CAAK;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,8BAA8B,qEAAM;AACpC;AACA,MAAM,yEAAS;AACf,cAAc,6EAAW;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,kCAAkC,qEAAM;AACxC,MAAM,yEAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,EAAE;AACtD,oBAAoB,oBAAoB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,wEAAa;AACnB,KAAK;AACL;AACA,CAAC;AACD,8BAA8B,qEAAM;AACpC;AACA;AACA,kDAAkD,GAAG;AACrD;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA,kDAAkD,GAAG;AACrD;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA,6BAA6B,qEAAM,OAAO,yEAAS;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,aAAa;AACb,iBAAiB;AACjB,iBAAiB;AACjB;AACA;AACA,mBAAmB;AACnB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,qEAAM;;AAEN;AAC2B;AAwBf;AACZ,8BAA8B,qEAAM;AACpC,EAAE,8DAAG;AACL,CAAC;AACD;AACA,UAAU,qDAAU;AACpB,WAAW,sDAAW;AACtB,aAAa,wDAAa;AAC1B,YAAY,uDAAY;AACxB,UAAU,qDAAU;AACpB,YAAY,uDAAY;AACxB,UAAU,qDAAU;AACpB;AACA,0CAA0C,qEAAM;AAChD;AACA;AACA;AACA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,2BAA2B,qEAAM;AACjC,eAAe,yEAAS;AACxB,wBAAwB,yEAAS;AACjC;AACA;AACA,qBAAqB,oDAAM;AAC3B;AACA,6CAA6C,oDAAM,mDAAmD,oDAAM;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,GAAG;AACrC,oBAAoB,uDAAS;AAC7B,IAAI,kDAAG;AACP;AACA,KAAK;AACL,IAAI,kDAAG;AACP;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA,EAAE,+EAAgB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,0DAAW,4EAA4E,yDAAc;AAC5H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL,2BAA2B,yEAAS;AACpC;AACA;AACA,wBAAwB,oDAAM;AAC9B;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,qBAAqB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,4CAAM,eAAe,4CAAM;AACnC,MAAM,8DAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,4CAAM;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,EAAE,qEAAM;AACR;AACA,sBAAsB,yDAAU,6EAA6E,yDAAU;AACvH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,0DAAe;AAC3C;AACA;AACA,4BAA4B,qDAAU;AACtC;AACA;AACA,4BAA4B,qDAAU;AACtC;AACA;AACA,4BAA4B,mDAAQ;AACpC;AACA;AACA,4BAA4B,kDAAO;AACnC;AACA;AACA;AACA;AACA;AACA,4BAA4B,oDAAS;AACrC;AACA;AACA;AACA;AACA;AACA,qBAAqB,sDAAO,6EAA6E,yDAAU;AACnH;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,0DAAe;AAC1C;AACA;AACA,2BAA2B,qDAAU;AACrC;AACA;AACA,2BAA2B,qDAAU;AACrC;AACA;AACA,2BAA2B,mDAAQ;AACnC;AACA;AACA,2BAA2B,kDAAO;AAClC;AACA;AACA;AACA;AACA;AACA,2BAA2B,oDAAS;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA;AACA,8BAA8B,yEAAc;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,wBAAwB,OAAO;AAC/B;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,oCAAoC,OAAO;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA,gCAAgC,qEAAM;AACtC;AACA,uBAAuB;AACvB;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA;AACA;AACA;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA;AACA,YAAY;AACZ;AACA;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA;AACA,mBAAmB;AACnB;;;AAGA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,mBAAmB;AACnB,YAAY;AACZ;;AAEA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA,cAAc;AACd;AACA;;;AAGA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB;AACnB;;AAEA;AACA,YAAY;AACZ;AACA,mBAAmB;AACnB;;AAEA;AACA,YAAY;AACZ;AACA;;;AAGA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,YAAY,gCAAgC;AAC5C;AACA;;AAEA;AACA;AACA,YAAY,gCAAgC;AAC5C;AACA;;AAEA;AACA;AACA,YAAY,gCAAgC;AAC5C;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA,YAAY;AACZ;;AAEA;AACA;AACA;AACA;AACA,YAAY;AACZ,cAAc;AACd;;AAEA;AACA;AACA;AACA,YAAY;AACZ;;AAEA;AACA;AACA,YAAY;AACZ;;;AAGA;;AAEA;AACA;AACA;AACA;AACA,YAAY;AACZ,cAAc;AACd;;AAEA;AACA;AACA;AACA;AACA,YAAY,2BAA2B;AACvC;;;AAGA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA,YAAY,2BAA2B;AACvC;;;AAGA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd,YAAY;AACZ;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,2BAA2B;AACvC;;AAEA;AACA;AACA;AACA;AACA,YAAY,2BAA2B;AACvC;;AAEA;AACA;AACA;AACA,YAAY;AACZ,mBAAmB;AACnB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dayjs/plugin/advancedFormat.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dayjs/plugin/customParseFormat.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dayjs/plugin/isoWeek.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-APWFNJXF.mjs"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_advancedFormat=t()}(this,(function(){\"use strict\";return function(e,t){var r=t.prototype,n=r.format;r.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return n.bind(this)(e);var s=this.$utils(),a=(e||\"YYYY-MM-DDTHH:mm:ssZ\").replace(/\\[([^\\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,(function(e){switch(e){case\"Q\":return Math.ceil((t.$M+1)/3);case\"Do\":return r.ordinal(t.$D);case\"gggg\":return t.weekYear();case\"GGGG\":return t.isoWeekYear();case\"wo\":return r.ordinal(t.week(),\"W\");case\"w\":case\"ww\":return s.s(t.week(),\"w\"===e?1:2,\"0\");case\"W\":case\"WW\":return s.s(t.isoWeek(),\"W\"===e?1:2,\"0\");case\"k\":case\"kk\":return s.s(String(0===t.$H?24:t.$H),\"k\"===e?1:2,\"0\");case\"X\":return Math.floor(t.$d.getTime()/1e3);case\"x\":return t.$d.getTime();case\"z\":return\"[\"+t.offsetName()+\"]\";case\"zzz\":return\"[\"+t.offsetName(\"long\")+\"]\";default:return e}}));return n.bind(this)(a)}}}));", "!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_customParseFormat=t()}(this,(function(){\"use strict\";var e={LTS:\"h:mm:ss A\",LT:\"h:mm A\",L:\"MM/DD/YYYY\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY h:mm A\",LLLL:\"dddd, MMMM D, YYYY h:mm A\"},t=/(\\[[^[]*\\])|([-_:/.,()\\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\\d/,r=/\\d\\d/,i=/\\d\\d?/,o=/\\d*[^-_:/,()\\s\\d]+/,s={},a=function(e){return(e=+e)+(e>68?1900:2e3)};var f=function(e){return function(t){this[e]=+t}},h=[/[+-]\\d\\d:?(\\d\\d)?|Z/,function(e){(this.zone||(this.zone={})).offset=function(e){if(!e)return 0;if(\"Z\"===e)return 0;var t=e.match(/([+-]|\\d\\d)/g),n=60*t[1]+(+t[2]||0);return 0===n?0:\"+\"===t[0]?-n:n}(e)}],u=function(e){var t=s[e];return t&&(t.indexOf?t:t.s.concat(t.f))},d=function(e,t){var n,r=s.meridiem;if(r){for(var i=1;i<=24;i+=1)if(e.indexOf(r(i,0,t))>-1){n=i>12;break}}else n=e===(t?\"pm\":\"PM\");return n},c={A:[o,function(e){this.afternoon=d(e,!1)}],a:[o,function(e){this.afternoon=d(e,!0)}],Q:[n,function(e){this.month=3*(e-1)+1}],S:[n,function(e){this.milliseconds=100*+e}],SS:[r,function(e){this.milliseconds=10*+e}],SSS:[/\\d{3}/,function(e){this.milliseconds=+e}],s:[i,f(\"seconds\")],ss:[i,f(\"seconds\")],m:[i,f(\"minutes\")],mm:[i,f(\"minutes\")],H:[i,f(\"hours\")],h:[i,f(\"hours\")],HH:[i,f(\"hours\")],hh:[i,f(\"hours\")],D:[i,f(\"day\")],DD:[r,f(\"day\")],Do:[o,function(e){var t=s.ordinal,n=e.match(/\\d+/);if(this.day=n[0],t)for(var r=1;r<=31;r+=1)t(r).replace(/\\[|\\]/g,\"\")===e&&(this.day=r)}],w:[i,f(\"week\")],ww:[r,f(\"week\")],M:[i,f(\"month\")],MM:[r,f(\"month\")],MMM:[o,function(e){var t=u(\"months\"),n=(u(\"monthsShort\")||t.map((function(e){return e.slice(0,3)}))).indexOf(e)+1;if(n<1)throw new Error;this.month=n%12||n}],MMMM:[o,function(e){var t=u(\"months\").indexOf(e)+1;if(t<1)throw new Error;this.month=t%12||t}],Y:[/[+-]?\\d+/,f(\"year\")],YY:[r,function(e){this.year=a(e)}],YYYY:[/\\d{4}/,f(\"year\")],Z:h,ZZ:h};function l(n){var r,i;r=n,i=s&&s.formats;for(var o=(n=r.replace(/(\\[[^\\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(t,n,r){var o=r&&r.toUpperCase();return n||i[r]||e[r]||i[o].replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,n){return t||n.slice(1)}))}))).match(t),a=o.length,f=0;f<a;f+=1){var h=o[f],u=c[h],d=u&&u[0],l=u&&u[1];o[f]=l?{regex:d,parser:l}:h.replace(/^\\[|\\]$/g,\"\")}return function(e){for(var t={},n=0,r=0;n<a;n+=1){var i=o[n];if(\"string\"==typeof i)r+=i.length;else{var s=i.regex,f=i.parser,h=e.slice(r),u=s.exec(h)[0];f.call(t,u),e=e.replace(u,\"\")}}return function(e){var t=e.afternoon;if(void 0!==t){var n=e.hours;t?n<12&&(e.hours+=12):12===n&&(e.hours=0),delete e.afternoon}}(t),t}}return function(e,t,n){n.p.customParseFormat=!0,e&&e.parseTwoDigitYear&&(a=e.parseTwoDigitYear);var r=t.prototype,i=r.parse;r.parse=function(e){var t=e.date,r=e.utc,o=e.args;this.$u=r;var a=o[1];if(\"string\"==typeof a){var f=!0===o[2],h=!0===o[3],u=f||h,d=o[2];h&&(d=o[2]),s=this.$locale(),!f&&d&&(s=n.Ls[d]),this.$d=function(e,t,n,r){try{if([\"x\",\"X\"].indexOf(t)>-1)return new Date((\"X\"===t?1e3:1)*e);var i=l(t)(e),o=i.year,s=i.month,a=i.day,f=i.hours,h=i.minutes,u=i.seconds,d=i.milliseconds,c=i.zone,m=i.week,M=new Date,Y=a||(o||s?1:M.getDate()),p=o||M.getFullYear(),v=0;o&&!s||(v=s>0?s-1:M.getMonth());var D,w=f||0,g=h||0,y=u||0,L=d||0;return c?new Date(Date.UTC(p,v,Y,w,g,y,L+60*c.offset*1e3)):n?new Date(Date.UTC(p,v,Y,w,g,y,L)):(D=new Date(p,v,Y,w,g,y,L),m&&(D=r(D).week(m).toDate()),D)}catch(e){return new Date(\"\")}}(t,a,r,n),this.init(),d&&!0!==d&&(this.$L=this.locale(d).$L),u&&t!=this.format(a)&&(this.$d=new Date(\"\")),s={}}else if(a instanceof Array)for(var c=a.length,m=1;m<=c;m+=1){o[1]=a[m-1];var M=n.apply(this,o);if(M.isValid()){this.$d=M.$d,this.$L=M.$L,this.init();break}m===c&&(this.$d=new Date(\"\"))}else i.call(this,e)}}}));", "!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isoWeek=t()}(this,(function(){\"use strict\";var e=\"day\";return function(t,i,s){var a=function(t){return t.add(4-t.isoWeekday(),e)},d=i.prototype;d.isoWeekYear=function(){return a(this).year()},d.isoWeek=function(t){if(!this.$utils().u(t))return this.add(7*(t-this.isoWeek()),e);var i,d,n,o,r=a(this),u=(i=this.isoWeekYear(),d=this.$u,n=(d?s.utc:s)().year(i).startOf(\"year\"),o=4-n.isoWeekday(),n.isoWeekday()>4&&(o+=7),n.add(o,e));return r.diff(u,\"week\")+1},d.isoWeekday=function(e){return this.$utils().u(e)?this.day()||7:this.day(this.day()%7?e:e-7)};var n=d.startOf;d.startOf=function(e,t){var i=this.$utils(),s=!!i.u(t)||t;return\"isoweek\"===i.p(e)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf(\"day\"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf(\"day\"):n.bind(this)(e,t)}}}));", "import {\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  configureSvgSize,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/gantt/parser/gantt.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 35, 36, 38, 40], $V1 = [1, 26], $V2 = [1, 27], $V3 = [1, 28], $V4 = [1, 29], $V5 = [1, 30], $V6 = [1, 31], $V7 = [1, 32], $V8 = [1, 33], $V9 = [1, 34], $Va = [1, 9], $Vb = [1, 10], $Vc = [1, 11], $Vd = [1, 12], $Ve = [1, 13], $Vf = [1, 14], $Vg = [1, 15], $Vh = [1, 16], $Vi = [1, 19], $Vj = [1, 20], $Vk = [1, 21], $Vl = [1, 22], $Vm = [1, 23], $Vn = [1, 25], $Vo = [1, 35];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"gantt\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NL\": 10, \"weekday\": 11, \"weekday_monday\": 12, \"weekday_tuesday\": 13, \"weekday_wednesday\": 14, \"weekday_thursday\": 15, \"weekday_friday\": 16, \"weekday_saturday\": 17, \"weekday_sunday\": 18, \"weekend\": 19, \"weekend_friday\": 20, \"weekend_saturday\": 21, \"dateFormat\": 22, \"inclusiveEndDates\": 23, \"topAxis\": 24, \"axisFormat\": 25, \"tickInterval\": 26, \"excludes\": 27, \"includes\": 28, \"todayMarker\": 29, \"title\": 30, \"acc_title\": 31, \"acc_title_value\": 32, \"acc_descr\": 33, \"acc_descr_value\": 34, \"acc_descr_multiline_value\": 35, \"section\": 36, \"clickStatement\": 37, \"taskTxt\": 38, \"taskData\": 39, \"click\": 40, \"callbackname\": 41, \"callbackargs\": 42, \"href\": 43, \"clickStatementDebug\": 44, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"gantt\", 6: \"EOF\", 8: \"SPACE\", 10: \"NL\", 12: \"weekday_monday\", 13: \"weekday_tuesday\", 14: \"weekday_wednesday\", 15: \"weekday_thursday\", 16: \"weekday_friday\", 17: \"weekday_saturday\", 18: \"weekday_sunday\", 20: \"weekend_friday\", 21: \"weekend_saturday\", 22: \"dateFormat\", 23: \"inclusiveEndDates\", 24: \"topAxis\", 25: \"axisFormat\", 26: \"tickInterval\", 27: \"excludes\", 28: \"includes\", 29: \"todayMarker\", 30: \"title\", 31: \"acc_title\", 32: \"acc_title_value\", 33: \"acc_descr\", 34: \"acc_descr_value\", 35: \"acc_descr_multiline_value\", 36: \"section\", 38: \"taskTxt\", 39: \"taskData\", 40: \"click\", 41: \"callbackname\", 42: \"callbackargs\", 43: \"href\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [19, 1], [19, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 1], [9, 2], [37, 2], [37, 3], [37, 3], [37, 4], [37, 3], [37, 4], [37, 2], [44, 2], [44, 3], [44, 3], [44, 4], [44, 3], [44, 4], [44, 2]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.setWeekday(\"monday\");\n          break;\n        case 9:\n          yy.setWeekday(\"tuesday\");\n          break;\n        case 10:\n          yy.setWeekday(\"wednesday\");\n          break;\n        case 11:\n          yy.setWeekday(\"thursday\");\n          break;\n        case 12:\n          yy.setWeekday(\"friday\");\n          break;\n        case 13:\n          yy.setWeekday(\"saturday\");\n          break;\n        case 14:\n          yy.setWeekday(\"sunday\");\n          break;\n        case 15:\n          yy.setWeekend(\"friday\");\n          break;\n        case 16:\n          yy.setWeekend(\"saturday\");\n          break;\n        case 17:\n          yy.setDateFormat($$[$0].substr(11));\n          this.$ = $$[$0].substr(11);\n          break;\n        case 18:\n          yy.enableInclusiveEndDates();\n          this.$ = $$[$0].substr(18);\n          break;\n        case 19:\n          yy.TopAxis();\n          this.$ = $$[$0].substr(8);\n          break;\n        case 20:\n          yy.setAxisFormat($$[$0].substr(11));\n          this.$ = $$[$0].substr(11);\n          break;\n        case 21:\n          yy.setTickInterval($$[$0].substr(13));\n          this.$ = $$[$0].substr(13);\n          break;\n        case 22:\n          yy.setExcludes($$[$0].substr(9));\n          this.$ = $$[$0].substr(9);\n          break;\n        case 23:\n          yy.setIncludes($$[$0].substr(9));\n          this.$ = $$[$0].substr(9);\n          break;\n        case 24:\n          yy.setTodayMarker($$[$0].substr(12));\n          this.$ = $$[$0].substr(12);\n          break;\n        case 27:\n          yy.setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 28:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 29:\n        case 30:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 31:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 33:\n          yy.addTask($$[$0 - 1], $$[$0]);\n          this.$ = \"task\";\n          break;\n        case 34:\n          this.$ = $$[$0 - 1];\n          yy.setClickEvent($$[$0 - 1], $$[$0], null);\n          break;\n        case 35:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 36:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], null);\n          yy.setLink($$[$0 - 2], $$[$0]);\n          break;\n        case 37:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 2], $$[$0 - 1]);\n          yy.setLink($$[$0 - 3], $$[$0]);\n          break;\n        case 38:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0], null);\n          yy.setLink($$[$0 - 2], $$[$0 - 1]);\n          break;\n        case 39:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 1], $$[$0]);\n          yy.setLink($$[$0 - 3], $$[$0 - 2]);\n          break;\n        case 40:\n          this.$ = $$[$0 - 1];\n          yy.setLink($$[$0 - 1], $$[$0]);\n          break;\n        case 41:\n        case 47:\n          this.$ = $$[$0 - 1] + \" \" + $$[$0];\n          break;\n        case 42:\n        case 43:\n        case 45:\n          this.$ = $$[$0 - 2] + \" \" + $$[$0 - 1] + \" \" + $$[$0];\n          break;\n        case 44:\n        case 46:\n          this.$ = $$[$0 - 3] + \" \" + $$[$0 - 2] + \" \" + $$[$0 - 1] + \" \" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: 17, 12: $V1, 13: $V2, 14: $V3, 15: $V4, 16: $V5, 17: $V6, 18: $V7, 19: 18, 20: $V8, 21: $V9, 22: $Va, 23: $Vb, 24: $Vc, 25: $Vd, 26: $Ve, 27: $Vf, 28: $Vg, 29: $Vh, 30: $Vi, 31: $Vj, 33: $Vk, 35: $Vl, 36: $Vm, 37: 24, 38: $Vn, 40: $Vo }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 36, 11: 17, 12: $V1, 13: $V2, 14: $V3, 15: $V4, 16: $V5, 17: $V6, 18: $V7, 19: 18, 20: $V8, 21: $V9, 22: $Va, 23: $Vb, 24: $Vc, 25: $Vd, 26: $Ve, 27: $Vf, 28: $Vg, 29: $Vh, 30: $Vi, 31: $Vj, 33: $Vk, 35: $Vl, 36: $Vm, 37: 24, 38: $Vn, 40: $Vo }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 17]), o($V0, [2, 18]), o($V0, [2, 19]), o($V0, [2, 20]), o($V0, [2, 21]), o($V0, [2, 22]), o($V0, [2, 23]), o($V0, [2, 24]), o($V0, [2, 25]), o($V0, [2, 26]), o($V0, [2, 27]), { 32: [1, 37] }, { 34: [1, 38] }, o($V0, [2, 30]), o($V0, [2, 31]), o($V0, [2, 32]), { 39: [1, 39] }, o($V0, [2, 8]), o($V0, [2, 9]), o($V0, [2, 10]), o($V0, [2, 11]), o($V0, [2, 12]), o($V0, [2, 13]), o($V0, [2, 14]), o($V0, [2, 15]), o($V0, [2, 16]), { 41: [1, 40], 43: [1, 41] }, o($V0, [2, 4]), o($V0, [2, 28]), o($V0, [2, 29]), o($V0, [2, 33]), o($V0, [2, 34], { 42: [1, 42], 43: [1, 43] }), o($V0, [2, 40], { 41: [1, 44] }), o($V0, [2, 35], { 43: [1, 45] }), o($V0, [2, 36]), o($V0, [2, 38], { 42: [1, 46] }), o($V0, [2, 37]), o($V0, [2, 39])],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.begin(\"open_directive\");\n            return \"open_directive\";\n            break;\n          case 1:\n            this.begin(\"acc_title\");\n            return 31;\n            break;\n          case 2:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 3:\n            this.begin(\"acc_descr\");\n            return 33;\n            break;\n          case 4:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 5:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 6:\n            this.popState();\n            break;\n          case 7:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 8:\n            break;\n          case 9:\n            break;\n          case 10:\n            break;\n          case 11:\n            return 10;\n            break;\n          case 12:\n            break;\n          case 13:\n            break;\n          case 14:\n            this.begin(\"href\");\n            break;\n          case 15:\n            this.popState();\n            break;\n          case 16:\n            return 43;\n            break;\n          case 17:\n            this.begin(\"callbackname\");\n            break;\n          case 18:\n            this.popState();\n            break;\n          case 19:\n            this.popState();\n            this.begin(\"callbackargs\");\n            break;\n          case 20:\n            return 41;\n            break;\n          case 21:\n            this.popState();\n            break;\n          case 22:\n            return 42;\n            break;\n          case 23:\n            this.begin(\"click\");\n            break;\n          case 24:\n            this.popState();\n            break;\n          case 25:\n            return 40;\n            break;\n          case 26:\n            return 4;\n            break;\n          case 27:\n            return 22;\n            break;\n          case 28:\n            return 23;\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 25;\n            break;\n          case 31:\n            return 26;\n            break;\n          case 32:\n            return 28;\n            break;\n          case 33:\n            return 27;\n            break;\n          case 34:\n            return 29;\n            break;\n          case 35:\n            return 12;\n            break;\n          case 36:\n            return 13;\n            break;\n          case 37:\n            return 14;\n            break;\n          case 38:\n            return 15;\n            break;\n          case 39:\n            return 16;\n            break;\n          case 40:\n            return 17;\n            break;\n          case 41:\n            return 18;\n            break;\n          case 42:\n            return 20;\n            break;\n          case 43:\n            return 21;\n            break;\n          case 44:\n            return \"date\";\n            break;\n          case 45:\n            return 30;\n            break;\n          case 46:\n            return \"accDescription\";\n            break;\n          case 47:\n            return 36;\n            break;\n          case 48:\n            return 38;\n            break;\n          case 49:\n            return 39;\n            break;\n          case 50:\n            return \":\";\n            break;\n          case 51:\n            return 6;\n            break;\n          case 52:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%\\{)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:%%(?!\\{)*[^\\n]*)/i, /^(?:[^\\}]%%*[^\\n]*)/i, /^(?:%%*[^\\n]*[\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:%[^\\n]*)/i, /^(?:href[\\s]+[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:call[\\s]+)/i, /^(?:\\([\\s]*\\))/i, /^(?:\\()/i, /^(?:[^(]*)/i, /^(?:\\))/i, /^(?:[^)]*)/i, /^(?:click[\\s]+)/i, /^(?:[\\s\\n])/i, /^(?:[^\\s\\n]*)/i, /^(?:gantt\\b)/i, /^(?:dateFormat\\s[^#\\n;]+)/i, /^(?:inclusiveEndDates\\b)/i, /^(?:topAxis\\b)/i, /^(?:axisFormat\\s[^#\\n;]+)/i, /^(?:tickInterval\\s[^#\\n;]+)/i, /^(?:includes\\s[^#\\n;]+)/i, /^(?:excludes\\s[^#\\n;]+)/i, /^(?:todayMarker\\s[^\\n;]+)/i, /^(?:weekday\\s+monday\\b)/i, /^(?:weekday\\s+tuesday\\b)/i, /^(?:weekday\\s+wednesday\\b)/i, /^(?:weekday\\s+thursday\\b)/i, /^(?:weekday\\s+friday\\b)/i, /^(?:weekday\\s+saturday\\b)/i, /^(?:weekday\\s+sunday\\b)/i, /^(?:weekend\\s+friday\\b)/i, /^(?:weekend\\s+saturday\\b)/i, /^(?:\\d\\d\\d\\d-\\d\\d-\\d\\d\\b)/i, /^(?:title\\s[^\\n]+)/i, /^(?:accDescription\\s[^#\\n;]+)/i, /^(?:section\\s[^\\n]+)/i, /^(?:[^:\\n]+)/i, /^(?::[^#\\n;]+)/i, /^(?::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [6, 7], \"inclusive\": false }, \"acc_descr\": { \"rules\": [4], \"inclusive\": false }, \"acc_title\": { \"rules\": [2], \"inclusive\": false }, \"callbackargs\": { \"rules\": [21, 22], \"inclusive\": false }, \"callbackname\": { \"rules\": [18, 19, 20], \"inclusive\": false }, \"href\": { \"rules\": [15, 16], \"inclusive\": false }, \"click\": { \"rules\": [24, 25], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 3, 5, 8, 9, 10, 11, 12, 13, 14, 17, 23, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar gantt_default = parser;\n\n// src/diagrams/gantt/ganttDb.js\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nimport dayjs from \"dayjs\";\nimport dayjsIsoWeek from \"dayjs/plugin/isoWeek.js\";\nimport dayjsCustomParseFormat from \"dayjs/plugin/customParseFormat.js\";\nimport dayjsAdvancedFormat from \"dayjs/plugin/advancedFormat.js\";\ndayjs.extend(dayjsIsoWeek);\ndayjs.extend(dayjsCustomParseFormat);\ndayjs.extend(dayjsAdvancedFormat);\nvar WEEKEND_START_DAY = { friday: 5, saturday: 6 };\nvar dateFormat = \"\";\nvar axisFormat = \"\";\nvar tickInterval = void 0;\nvar todayMarker = \"\";\nvar includes = [];\nvar excludes = [];\nvar links = /* @__PURE__ */ new Map();\nvar sections = [];\nvar tasks = [];\nvar currentSection = \"\";\nvar displayMode = \"\";\nvar tags = [\"active\", \"done\", \"crit\", \"milestone\"];\nvar funs = [];\nvar inclusiveEndDates = false;\nvar topAxis = false;\nvar weekday = \"sunday\";\nvar weekend = \"saturday\";\nvar lastOrder = 0;\nvar clear2 = /* @__PURE__ */ __name(function() {\n  sections = [];\n  tasks = [];\n  currentSection = \"\";\n  funs = [];\n  taskCnt = 0;\n  lastTask = void 0;\n  lastTaskID = void 0;\n  rawTasks = [];\n  dateFormat = \"\";\n  axisFormat = \"\";\n  displayMode = \"\";\n  tickInterval = void 0;\n  todayMarker = \"\";\n  includes = [];\n  excludes = [];\n  inclusiveEndDates = false;\n  topAxis = false;\n  lastOrder = 0;\n  links = /* @__PURE__ */ new Map();\n  clear();\n  weekday = \"sunday\";\n  weekend = \"saturday\";\n}, \"clear\");\nvar setAxisFormat = /* @__PURE__ */ __name(function(txt) {\n  axisFormat = txt;\n}, \"setAxisFormat\");\nvar getAxisFormat = /* @__PURE__ */ __name(function() {\n  return axisFormat;\n}, \"getAxisFormat\");\nvar setTickInterval = /* @__PURE__ */ __name(function(txt) {\n  tickInterval = txt;\n}, \"setTickInterval\");\nvar getTickInterval = /* @__PURE__ */ __name(function() {\n  return tickInterval;\n}, \"getTickInterval\");\nvar setTodayMarker = /* @__PURE__ */ __name(function(txt) {\n  todayMarker = txt;\n}, \"setTodayMarker\");\nvar getTodayMarker = /* @__PURE__ */ __name(function() {\n  return todayMarker;\n}, \"getTodayMarker\");\nvar setDateFormat = /* @__PURE__ */ __name(function(txt) {\n  dateFormat = txt;\n}, \"setDateFormat\");\nvar enableInclusiveEndDates = /* @__PURE__ */ __name(function() {\n  inclusiveEndDates = true;\n}, \"enableInclusiveEndDates\");\nvar endDatesAreInclusive = /* @__PURE__ */ __name(function() {\n  return inclusiveEndDates;\n}, \"endDatesAreInclusive\");\nvar enableTopAxis = /* @__PURE__ */ __name(function() {\n  topAxis = true;\n}, \"enableTopAxis\");\nvar topAxisEnabled = /* @__PURE__ */ __name(function() {\n  return topAxis;\n}, \"topAxisEnabled\");\nvar setDisplayMode = /* @__PURE__ */ __name(function(txt) {\n  displayMode = txt;\n}, \"setDisplayMode\");\nvar getDisplayMode = /* @__PURE__ */ __name(function() {\n  return displayMode;\n}, \"getDisplayMode\");\nvar getDateFormat = /* @__PURE__ */ __name(function() {\n  return dateFormat;\n}, \"getDateFormat\");\nvar setIncludes = /* @__PURE__ */ __name(function(txt) {\n  includes = txt.toLowerCase().split(/[\\s,]+/);\n}, \"setIncludes\");\nvar getIncludes = /* @__PURE__ */ __name(function() {\n  return includes;\n}, \"getIncludes\");\nvar setExcludes = /* @__PURE__ */ __name(function(txt) {\n  excludes = txt.toLowerCase().split(/[\\s,]+/);\n}, \"setExcludes\");\nvar getExcludes = /* @__PURE__ */ __name(function() {\n  return excludes;\n}, \"getExcludes\");\nvar getLinks = /* @__PURE__ */ __name(function() {\n  return links;\n}, \"getLinks\");\nvar addSection = /* @__PURE__ */ __name(function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ __name(function() {\n  return sections;\n}, \"getSections\");\nvar getTasks = /* @__PURE__ */ __name(function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 10;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks = rawTasks;\n  return tasks;\n}, \"getTasks\");\nvar isInvalidDate = /* @__PURE__ */ __name(function(date, dateFormat2, excludes2, includes2) {\n  if (includes2.includes(date.format(dateFormat2.trim()))) {\n    return false;\n  }\n  if (excludes2.includes(\"weekends\") && (date.isoWeekday() === WEEKEND_START_DAY[weekend] || date.isoWeekday() === WEEKEND_START_DAY[weekend] + 1)) {\n    return true;\n  }\n  if (excludes2.includes(date.format(\"dddd\").toLowerCase())) {\n    return true;\n  }\n  return excludes2.includes(date.format(dateFormat2.trim()));\n}, \"isInvalidDate\");\nvar setWeekday = /* @__PURE__ */ __name(function(txt) {\n  weekday = txt;\n}, \"setWeekday\");\nvar getWeekday = /* @__PURE__ */ __name(function() {\n  return weekday;\n}, \"getWeekday\");\nvar setWeekend = /* @__PURE__ */ __name(function(startDay) {\n  weekend = startDay;\n}, \"setWeekend\");\nvar checkTaskDates = /* @__PURE__ */ __name(function(task, dateFormat2, excludes2, includes2) {\n  if (!excludes2.length || task.manualEndTime) {\n    return;\n  }\n  let startTime;\n  if (task.startTime instanceof Date) {\n    startTime = dayjs(task.startTime);\n  } else {\n    startTime = dayjs(task.startTime, dateFormat2, true);\n  }\n  startTime = startTime.add(1, \"d\");\n  let originalEndTime;\n  if (task.endTime instanceof Date) {\n    originalEndTime = dayjs(task.endTime);\n  } else {\n    originalEndTime = dayjs(task.endTime, dateFormat2, true);\n  }\n  const [fixedEndTime, renderEndTime] = fixTaskDates(\n    startTime,\n    originalEndTime,\n    dateFormat2,\n    excludes2,\n    includes2\n  );\n  task.endTime = fixedEndTime.toDate();\n  task.renderEndTime = renderEndTime;\n}, \"checkTaskDates\");\nvar fixTaskDates = /* @__PURE__ */ __name(function(startTime, endTime, dateFormat2, excludes2, includes2) {\n  let invalid = false;\n  let renderEndTime = null;\n  while (startTime <= endTime) {\n    if (!invalid) {\n      renderEndTime = endTime.toDate();\n    }\n    invalid = isInvalidDate(startTime, dateFormat2, excludes2, includes2);\n    if (invalid) {\n      endTime = endTime.add(1, \"d\");\n    }\n    startTime = startTime.add(1, \"d\");\n  }\n  return [endTime, renderEndTime];\n}, \"fixTaskDates\");\nvar getStartDate = /* @__PURE__ */ __name(function(prevTime, dateFormat2, str) {\n  str = str.trim();\n  const afterRePattern = /^after\\s+(?<ids>[\\d\\w- ]+)/;\n  const afterStatement = afterRePattern.exec(str);\n  if (afterStatement !== null) {\n    let latestTask = null;\n    for (const id of afterStatement.groups.ids.split(\" \")) {\n      let task = findTaskById(id);\n      if (task !== void 0 && (!latestTask || task.endTime > latestTask.endTime)) {\n        latestTask = task;\n      }\n    }\n    if (latestTask) {\n      return latestTask.endTime;\n    }\n    const today = /* @__PURE__ */ new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  let mDate = dayjs(str, dateFormat2.trim(), true);\n  if (mDate.isValid()) {\n    return mDate.toDate();\n  } else {\n    log.debug(\"Invalid date:\" + str);\n    log.debug(\"With date format:\" + dateFormat2.trim());\n    const d = new Date(str);\n    if (d === void 0 || isNaN(d.getTime()) || // WebKit browsers can mis-parse invalid dates to be ridiculously\n    // huge numbers, e.g. new Date('202304') gets parsed as January 1, 202304.\n    // This can cause virtually infinite loops while rendering, so for the\n    // purposes of Gantt charts we'll just treat any date beyond 10,000 AD/BC as\n    // invalid.\n    d.getFullYear() < -1e4 || d.getFullYear() > 1e4) {\n      throw new Error(\"Invalid date:\" + str);\n    }\n    return d;\n  }\n}, \"getStartDate\");\nvar parseDuration = /* @__PURE__ */ __name(function(str) {\n  const statement = /^(\\d+(?:\\.\\d+)?)([Mdhmswy]|ms)$/.exec(str.trim());\n  if (statement !== null) {\n    return [Number.parseFloat(statement[1]), statement[2]];\n  }\n  return [NaN, \"ms\"];\n}, \"parseDuration\");\nvar getEndDate = /* @__PURE__ */ __name(function(prevTime, dateFormat2, str, inclusive = false) {\n  str = str.trim();\n  const untilRePattern = /^until\\s+(?<ids>[\\d\\w- ]+)/;\n  const untilStatement = untilRePattern.exec(str);\n  if (untilStatement !== null) {\n    let earliestTask = null;\n    for (const id of untilStatement.groups.ids.split(\" \")) {\n      let task = findTaskById(id);\n      if (task !== void 0 && (!earliestTask || task.startTime < earliestTask.startTime)) {\n        earliestTask = task;\n      }\n    }\n    if (earliestTask) {\n      return earliestTask.startTime;\n    }\n    const today = /* @__PURE__ */ new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  let parsedDate = dayjs(str, dateFormat2.trim(), true);\n  if (parsedDate.isValid()) {\n    if (inclusive) {\n      parsedDate = parsedDate.add(1, \"d\");\n    }\n    return parsedDate.toDate();\n  }\n  let endTime = dayjs(prevTime);\n  const [durationValue, durationUnit] = parseDuration(str);\n  if (!Number.isNaN(durationValue)) {\n    const newEndTime = endTime.add(durationValue, durationUnit);\n    if (newEndTime.isValid()) {\n      endTime = newEndTime;\n    }\n  }\n  return endTime.toDate();\n}, \"getEndDate\");\nvar taskCnt = 0;\nvar parseId = /* @__PURE__ */ __name(function(idStr) {\n  if (idStr === void 0) {\n    taskCnt = taskCnt + 1;\n    return \"task\" + taskCnt;\n  }\n  return idStr;\n}, \"parseId\");\nvar compileData = /* @__PURE__ */ __name(function(prevTask, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === \":\") {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n  const data = ds.split(\",\");\n  const task = {};\n  getTaskTags(data, task, tags);\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n  let endTimeData = \"\";\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = prevTask.endTime;\n      endTimeData = data[0];\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = getStartDate(void 0, dateFormat, data[0]);\n      endTimeData = data[1];\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = getStartDate(void 0, dateFormat, data[1]);\n      endTimeData = data[2];\n      break;\n    default:\n  }\n  if (endTimeData) {\n    task.endTime = getEndDate(task.startTime, dateFormat, endTimeData, inclusiveEndDates);\n    task.manualEndTime = dayjs(endTimeData, \"YYYY-MM-DD\", true).isValid();\n    checkTaskDates(task, dateFormat, excludes, includes);\n  }\n  return task;\n}, \"compileData\");\nvar parseData = /* @__PURE__ */ __name(function(prevTaskId, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === \":\") {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n  const data = ds.split(\",\");\n  const task = {};\n  getTaskTags(data, task, tags);\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = {\n        type: \"prevTaskEnd\",\n        id: prevTaskId\n      };\n      task.endTime = {\n        data: data[0]\n      };\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = {\n        type: \"getStartDate\",\n        startData: data[0]\n      };\n      task.endTime = {\n        data: data[1]\n      };\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = {\n        type: \"getStartDate\",\n        startData: data[1]\n      };\n      task.endTime = {\n        data: data[2]\n      };\n      break;\n    default:\n  }\n  return task;\n}, \"parseData\");\nvar lastTask;\nvar lastTaskID;\nvar rawTasks = [];\nvar taskDb = {};\nvar addTask = /* @__PURE__ */ __name(function(descr, data) {\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    processed: false,\n    manualEndTime: false,\n    renderEndTime: null,\n    raw: { data },\n    task: descr,\n    classes: []\n  };\n  const taskInfo = parseData(lastTaskID, data);\n  rawTask.raw.startTime = taskInfo.startTime;\n  rawTask.raw.endTime = taskInfo.endTime;\n  rawTask.id = taskInfo.id;\n  rawTask.prevTaskId = lastTaskID;\n  rawTask.active = taskInfo.active;\n  rawTask.done = taskInfo.done;\n  rawTask.crit = taskInfo.crit;\n  rawTask.milestone = taskInfo.milestone;\n  rawTask.order = lastOrder;\n  lastOrder++;\n  const pos = rawTasks.push(rawTask);\n  lastTaskID = rawTask.id;\n  taskDb[rawTask.id] = pos - 1;\n}, \"addTask\");\nvar findTaskById = /* @__PURE__ */ __name(function(id) {\n  const pos = taskDb[id];\n  return rawTasks[pos];\n}, \"findTaskById\");\nvar addTaskOrg = /* @__PURE__ */ __name(function(descr, data) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  const taskInfo = compileData(lastTask, data);\n  newTask.startTime = taskInfo.startTime;\n  newTask.endTime = taskInfo.endTime;\n  newTask.id = taskInfo.id;\n  newTask.active = taskInfo.active;\n  newTask.done = taskInfo.done;\n  newTask.crit = taskInfo.crit;\n  newTask.milestone = taskInfo.milestone;\n  lastTask = newTask;\n  tasks.push(newTask);\n}, \"addTaskOrg\");\nvar compileTasks = /* @__PURE__ */ __name(function() {\n  const compileTask = /* @__PURE__ */ __name(function(pos) {\n    const task = rawTasks[pos];\n    let startTime = \"\";\n    switch (rawTasks[pos].raw.startTime.type) {\n      case \"prevTaskEnd\": {\n        const prevTask = findTaskById(task.prevTaskId);\n        task.startTime = prevTask.endTime;\n        break;\n      }\n      case \"getStartDate\":\n        startTime = getStartDate(void 0, dateFormat, rawTasks[pos].raw.startTime.startData);\n        if (startTime) {\n          rawTasks[pos].startTime = startTime;\n        }\n        break;\n    }\n    if (rawTasks[pos].startTime) {\n      rawTasks[pos].endTime = getEndDate(\n        rawTasks[pos].startTime,\n        dateFormat,\n        rawTasks[pos].raw.endTime.data,\n        inclusiveEndDates\n      );\n      if (rawTasks[pos].endTime) {\n        rawTasks[pos].processed = true;\n        rawTasks[pos].manualEndTime = dayjs(\n          rawTasks[pos].raw.endTime.data,\n          \"YYYY-MM-DD\",\n          true\n        ).isValid();\n        checkTaskDates(rawTasks[pos], dateFormat, excludes, includes);\n      }\n    }\n    return rawTasks[pos].processed;\n  }, \"compileTask\");\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n}, \"compileTasks\");\nvar setLink = /* @__PURE__ */ __name(function(ids, _linkStr) {\n  let linkStr = _linkStr;\n  if (getConfig().securityLevel !== \"loose\") {\n    linkStr = sanitizeUrl(_linkStr);\n  }\n  ids.split(\",\").forEach(function(id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== void 0) {\n      pushFun(id, () => {\n        window.open(linkStr, \"_self\");\n      });\n      links.set(id, linkStr);\n    }\n  });\n  setClass(ids, \"clickable\");\n}, \"setLink\");\nvar setClass = /* @__PURE__ */ __name(function(ids, className) {\n  ids.split(\",\").forEach(function(id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== void 0) {\n      rawTask.classes.push(className);\n    }\n  });\n}, \"setClass\");\nvar setClickFun = /* @__PURE__ */ __name(function(id, functionName, functionArgs) {\n  if (getConfig().securityLevel !== \"loose\") {\n    return;\n  }\n  if (functionName === void 0) {\n    return;\n  }\n  let argList = [];\n  if (typeof functionArgs === \"string\") {\n    argList = functionArgs.split(/,(?=(?:(?:[^\"]*\"){2})*[^\"]*$)/);\n    for (let i = 0; i < argList.length; i++) {\n      let item = argList[i].trim();\n      if (item.startsWith('\"') && item.endsWith('\"')) {\n        item = item.substr(1, item.length - 2);\n      }\n      argList[i] = item;\n    }\n  }\n  if (argList.length === 0) {\n    argList.push(id);\n  }\n  let rawTask = findTaskById(id);\n  if (rawTask !== void 0) {\n    pushFun(id, () => {\n      utils_default.runFunc(functionName, ...argList);\n    });\n  }\n}, \"setClickFun\");\nvar pushFun = /* @__PURE__ */ __name(function(id, callbackFunction) {\n  funs.push(\n    function() {\n      const elem = document.querySelector(`[id=\"${id}\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\"click\", function() {\n          callbackFunction();\n        });\n      }\n    },\n    function() {\n      const elem = document.querySelector(`[id=\"${id}-text\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\"click\", function() {\n          callbackFunction();\n        });\n      }\n    }\n  );\n}, \"pushFun\");\nvar setClickEvent = /* @__PURE__ */ __name(function(ids, functionName, functionArgs) {\n  ids.split(\",\").forEach(function(id) {\n    setClickFun(id, functionName, functionArgs);\n  });\n  setClass(ids, \"clickable\");\n}, \"setClickEvent\");\nvar bindFunctions = /* @__PURE__ */ __name(function(element) {\n  funs.forEach(function(fun) {\n    fun(element);\n  });\n}, \"bindFunctions\");\nvar ganttDb_default = {\n  getConfig: /* @__PURE__ */ __name(() => getConfig().gantt, \"getConfig\"),\n  clear: clear2,\n  setDateFormat,\n  getDateFormat,\n  enableInclusiveEndDates,\n  endDatesAreInclusive,\n  enableTopAxis,\n  topAxisEnabled,\n  setAxisFormat,\n  getAxisFormat,\n  setTickInterval,\n  getTickInterval,\n  setTodayMarker,\n  getTodayMarker,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  setDisplayMode,\n  getDisplayMode,\n  setAccDescription,\n  getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  findTaskById,\n  addTaskOrg,\n  setIncludes,\n  getIncludes,\n  setExcludes,\n  getExcludes,\n  setClickEvent,\n  setLink,\n  getLinks,\n  bindFunctions,\n  parseDuration,\n  isInvalidDate,\n  setWeekday,\n  getWeekday,\n  setWeekend\n};\nfunction getTaskTags(data, task, tags2) {\n  let matchFound = true;\n  while (matchFound) {\n    matchFound = false;\n    tags2.forEach(function(t) {\n      const pattern = \"^\\\\s*\" + t + \"\\\\s*$\";\n      const regex = new RegExp(pattern);\n      if (data[0].match(regex)) {\n        task[t] = true;\n        data.shift(1);\n        matchFound = true;\n      }\n    });\n  }\n}\n__name(getTaskTags, \"getTaskTags\");\n\n// src/diagrams/gantt/ganttRenderer.js\nimport dayjs2 from \"dayjs\";\nimport {\n  select,\n  scaleTime,\n  min,\n  max,\n  scaleLinear,\n  interpolateHcl,\n  axisBottom,\n  axisTop,\n  timeFormat,\n  timeMillisecond,\n  timeSecond,\n  timeMinute,\n  timeHour,\n  timeDay,\n  timeMonday,\n  timeTuesday,\n  timeWednesday,\n  timeThursday,\n  timeFriday,\n  timeSaturday,\n  timeSunday,\n  timeMonth\n} from \"d3\";\nvar setConf = /* @__PURE__ */ __name(function() {\n  log.debug(\"Something is calling, setConf, remove the call\");\n}, \"setConf\");\nvar mapWeekdayToTimeFunction = {\n  monday: timeMonday,\n  tuesday: timeTuesday,\n  wednesday: timeWednesday,\n  thursday: timeThursday,\n  friday: timeFriday,\n  saturday: timeSaturday,\n  sunday: timeSunday\n};\nvar getMaxIntersections = /* @__PURE__ */ __name((tasks2, orderOffset) => {\n  let timeline = [...tasks2].map(() => -Infinity);\n  let sorted = [...tasks2].sort((a, b) => a.startTime - b.startTime || a.order - b.order);\n  let maxIntersections = 0;\n  for (const element of sorted) {\n    for (let j = 0; j < timeline.length; j++) {\n      if (element.startTime >= timeline[j]) {\n        timeline[j] = element.endTime;\n        element.order = j + orderOffset;\n        if (j > maxIntersections) {\n          maxIntersections = j;\n        }\n        break;\n      }\n    }\n  }\n  return maxIntersections;\n}, \"getMaxIntersections\");\nvar w;\nvar draw = /* @__PURE__ */ __name(function(text, id, version, diagObj) {\n  const conf = getConfig().gantt;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  const elem = doc.getElementById(id);\n  w = elem.parentElement.offsetWidth;\n  if (w === void 0) {\n    w = 1200;\n  }\n  if (conf.useWidth !== void 0) {\n    w = conf.useWidth;\n  }\n  const taskArray = diagObj.db.getTasks();\n  let categories = [];\n  for (const element of taskArray) {\n    categories.push(element.type);\n  }\n  categories = checkUnique(categories);\n  const categoryHeights = {};\n  let h = 2 * conf.topPadding;\n  if (diagObj.db.getDisplayMode() === \"compact\" || conf.displayMode === \"compact\") {\n    const categoryElements = {};\n    for (const element of taskArray) {\n      if (categoryElements[element.section] === void 0) {\n        categoryElements[element.section] = [element];\n      } else {\n        categoryElements[element.section].push(element);\n      }\n    }\n    let intersections = 0;\n    for (const category of Object.keys(categoryElements)) {\n      const categoryHeight = getMaxIntersections(categoryElements[category], intersections) + 1;\n      intersections += categoryHeight;\n      h += categoryHeight * (conf.barHeight + conf.barGap);\n      categoryHeights[category] = categoryHeight;\n    }\n  } else {\n    h += taskArray.length * (conf.barHeight + conf.barGap);\n    for (const category of categories) {\n      categoryHeights[category] = taskArray.filter((task) => task.type === category).length;\n    }\n  }\n  elem.setAttribute(\"viewBox\", \"0 0 \" + w + \" \" + h);\n  const svg = root.select(`[id=\"${id}\"]`);\n  const timeScale = scaleTime().domain([\n    min(taskArray, function(d) {\n      return d.startTime;\n    }),\n    max(taskArray, function(d) {\n      return d.endTime;\n    })\n  ]).rangeRound([0, w - conf.leftPadding - conf.rightPadding]);\n  function taskCompare(a, b) {\n    const taskA = a.startTime;\n    const taskB = b.startTime;\n    let result = 0;\n    if (taskA > taskB) {\n      result = 1;\n    } else if (taskA < taskB) {\n      result = -1;\n    }\n    return result;\n  }\n  __name(taskCompare, \"taskCompare\");\n  taskArray.sort(taskCompare);\n  makeGantt(taskArray, w, h);\n  configureSvgSize(svg, h, w, conf.useMaxWidth);\n  svg.append(\"text\").text(diagObj.db.getDiagramTitle()).attr(\"x\", w / 2).attr(\"y\", conf.titleTopMargin).attr(\"class\", \"titleText\");\n  function makeGantt(tasks2, pageWidth, pageHeight) {\n    const barHeight = conf.barHeight;\n    const gap = barHeight + conf.barGap;\n    const topPadding = conf.topPadding;\n    const leftPadding = conf.leftPadding;\n    const colorScale = scaleLinear().domain([0, categories.length]).range([\"#00B9FA\", \"#F95002\"]).interpolate(interpolateHcl);\n    drawExcludeDays(\n      gap,\n      topPadding,\n      leftPadding,\n      pageWidth,\n      pageHeight,\n      tasks2,\n      diagObj.db.getExcludes(),\n      diagObj.db.getIncludes()\n    );\n    makeGrid(leftPadding, topPadding, pageWidth, pageHeight);\n    drawRects(tasks2, gap, topPadding, leftPadding, barHeight, colorScale, pageWidth, pageHeight);\n    vertLabels(gap, topPadding, leftPadding, barHeight, colorScale);\n    drawToday(leftPadding, topPadding, pageWidth, pageHeight);\n  }\n  __name(makeGantt, \"makeGantt\");\n  function drawRects(theArray, theGap, theTopPad, theSidePad, theBarHeight, theColorScale, w2) {\n    const uniqueTaskOrderIds = [...new Set(theArray.map((item) => item.order))];\n    const uniqueTasks = uniqueTaskOrderIds.map((id2) => theArray.find((item) => item.order === id2));\n    svg.append(\"g\").selectAll(\"rect\").data(uniqueTasks).enter().append(\"rect\").attr(\"x\", 0).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + theTopPad - 2;\n    }).attr(\"width\", function() {\n      return w2 - conf.rightPadding / 2;\n    }).attr(\"height\", theGap).attr(\"class\", function(d) {\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          return \"section section\" + i % conf.numberSectionStyles;\n        }\n      }\n      return \"section section0\";\n    });\n    const rectangles = svg.append(\"g\").selectAll(\"rect\").data(theArray).enter();\n    const links2 = diagObj.db.getLinks();\n    rectangles.append(\"rect\").attr(\"id\", function(d) {\n      return d.id;\n    }).attr(\"rx\", 3).attr(\"ry\", 3).attr(\"x\", function(d) {\n      if (d.milestone) {\n        return timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n      }\n      return timeScale(d.startTime) + theSidePad;\n    }).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + theTopPad;\n    }).attr(\"width\", function(d) {\n      if (d.milestone) {\n        return theBarHeight;\n      }\n      return timeScale(d.renderEndTime || d.endTime) - timeScale(d.startTime);\n    }).attr(\"height\", theBarHeight).attr(\"transform-origin\", function(d, i) {\n      i = d.order;\n      return (timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime))).toString() + \"px \" + (i * theGap + theTopPad + 0.5 * theBarHeight).toString() + \"px\";\n    }).attr(\"class\", function(d) {\n      const res = \"task\";\n      let classStr = \"\";\n      if (d.classes.length > 0) {\n        classStr = d.classes.join(\" \");\n      }\n      let secNum = 0;\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          secNum = i % conf.numberSectionStyles;\n        }\n      }\n      let taskClass = \"\";\n      if (d.active) {\n        if (d.crit) {\n          taskClass += \" activeCrit\";\n        } else {\n          taskClass = \" active\";\n        }\n      } else if (d.done) {\n        if (d.crit) {\n          taskClass = \" doneCrit\";\n        } else {\n          taskClass = \" done\";\n        }\n      } else {\n        if (d.crit) {\n          taskClass += \" crit\";\n        }\n      }\n      if (taskClass.length === 0) {\n        taskClass = \" task\";\n      }\n      if (d.milestone) {\n        taskClass = \" milestone \" + taskClass;\n      }\n      taskClass += secNum;\n      taskClass += \" \" + classStr;\n      return res + taskClass;\n    });\n    rectangles.append(\"text\").attr(\"id\", function(d) {\n      return d.id + \"-text\";\n    }).text(function(d) {\n      return d.task;\n    }).attr(\"font-size\", conf.fontSize).attr(\"x\", function(d) {\n      let startX = timeScale(d.startTime);\n      let endX = timeScale(d.renderEndTime || d.endTime);\n      if (d.milestone) {\n        startX += 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n      }\n      if (d.milestone) {\n        endX = startX + theBarHeight;\n      }\n      const textWidth = this.getBBox().width;\n      if (textWidth > endX - startX) {\n        if (endX + textWidth + 1.5 * conf.leftPadding > w2) {\n          return startX + theSidePad - 5;\n        } else {\n          return endX + theSidePad + 5;\n        }\n      } else {\n        return (endX - startX) / 2 + startX + theSidePad;\n      }\n    }).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + conf.barHeight / 2 + (conf.fontSize / 2 - 2) + theTopPad;\n    }).attr(\"text-height\", theBarHeight).attr(\"class\", function(d) {\n      const startX = timeScale(d.startTime);\n      let endX = timeScale(d.endTime);\n      if (d.milestone) {\n        endX = startX + theBarHeight;\n      }\n      const textWidth = this.getBBox().width;\n      let classStr = \"\";\n      if (d.classes.length > 0) {\n        classStr = d.classes.join(\" \");\n      }\n      let secNum = 0;\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          secNum = i % conf.numberSectionStyles;\n        }\n      }\n      let taskType = \"\";\n      if (d.active) {\n        if (d.crit) {\n          taskType = \"activeCritText\" + secNum;\n        } else {\n          taskType = \"activeText\" + secNum;\n        }\n      }\n      if (d.done) {\n        if (d.crit) {\n          taskType = taskType + \" doneCritText\" + secNum;\n        } else {\n          taskType = taskType + \" doneText\" + secNum;\n        }\n      } else {\n        if (d.crit) {\n          taskType = taskType + \" critText\" + secNum;\n        }\n      }\n      if (d.milestone) {\n        taskType += \" milestoneText\";\n      }\n      if (textWidth > endX - startX) {\n        if (endX + textWidth + 1.5 * conf.leftPadding > w2) {\n          return classStr + \" taskTextOutsideLeft taskTextOutside\" + secNum + \" \" + taskType;\n        } else {\n          return classStr + \" taskTextOutsideRight taskTextOutside\" + secNum + \" \" + taskType + \" width-\" + textWidth;\n        }\n      } else {\n        return classStr + \" taskText taskText\" + secNum + \" \" + taskType + \" width-\" + textWidth;\n      }\n    });\n    const securityLevel2 = getConfig().securityLevel;\n    if (securityLevel2 === \"sandbox\") {\n      let sandboxElement2;\n      sandboxElement2 = select(\"#i\" + id);\n      const doc2 = sandboxElement2.nodes()[0].contentDocument;\n      rectangles.filter(function(d) {\n        return links2.has(d.id);\n      }).each(function(o) {\n        var taskRect = doc2.querySelector(\"#\" + o.id);\n        var taskText = doc2.querySelector(\"#\" + o.id + \"-text\");\n        const oldParent = taskRect.parentNode;\n        var Link = doc2.createElement(\"a\");\n        Link.setAttribute(\"xlink:href\", links2.get(o.id));\n        Link.setAttribute(\"target\", \"_top\");\n        oldParent.appendChild(Link);\n        Link.appendChild(taskRect);\n        Link.appendChild(taskText);\n      });\n    }\n  }\n  __name(drawRects, \"drawRects\");\n  function drawExcludeDays(theGap, theTopPad, theSidePad, w2, h2, tasks2, excludes2, includes2) {\n    if (excludes2.length === 0 && includes2.length === 0) {\n      return;\n    }\n    let minTime;\n    let maxTime;\n    for (const { startTime, endTime } of tasks2) {\n      if (minTime === void 0 || startTime < minTime) {\n        minTime = startTime;\n      }\n      if (maxTime === void 0 || endTime > maxTime) {\n        maxTime = endTime;\n      }\n    }\n    if (!minTime || !maxTime) {\n      return;\n    }\n    if (dayjs2(maxTime).diff(dayjs2(minTime), \"year\") > 5) {\n      log.warn(\n        \"The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.\"\n      );\n      return;\n    }\n    const dateFormat2 = diagObj.db.getDateFormat();\n    const excludeRanges = [];\n    let range = null;\n    let d = dayjs2(minTime);\n    while (d.valueOf() <= maxTime) {\n      if (diagObj.db.isInvalidDate(d, dateFormat2, excludes2, includes2)) {\n        if (!range) {\n          range = {\n            start: d,\n            end: d\n          };\n        } else {\n          range.end = d;\n        }\n      } else {\n        if (range) {\n          excludeRanges.push(range);\n          range = null;\n        }\n      }\n      d = d.add(1, \"d\");\n    }\n    const rectangles = svg.append(\"g\").selectAll(\"rect\").data(excludeRanges).enter();\n    rectangles.append(\"rect\").attr(\"id\", function(d2) {\n      return \"exclude-\" + d2.start.format(\"YYYY-MM-DD\");\n    }).attr(\"x\", function(d2) {\n      return timeScale(d2.start) + theSidePad;\n    }).attr(\"y\", conf.gridLineStartPadding).attr(\"width\", function(d2) {\n      const renderEnd = d2.end.add(1, \"day\");\n      return timeScale(renderEnd) - timeScale(d2.start);\n    }).attr(\"height\", h2 - theTopPad - conf.gridLineStartPadding).attr(\"transform-origin\", function(d2, i) {\n      return (timeScale(d2.start) + theSidePad + 0.5 * (timeScale(d2.end) - timeScale(d2.start))).toString() + \"px \" + (i * theGap + 0.5 * h2).toString() + \"px\";\n    }).attr(\"class\", \"exclude-range\");\n  }\n  __name(drawExcludeDays, \"drawExcludeDays\");\n  function makeGrid(theSidePad, theTopPad, w2, h2) {\n    let bottomXAxis = axisBottom(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat(timeFormat(diagObj.db.getAxisFormat() || conf.axisFormat || \"%Y-%m-%d\"));\n    const reTickInterval = /^([1-9]\\d*)(millisecond|second|minute|hour|day|week|month)$/;\n    const resultTickInterval = reTickInterval.exec(\n      diagObj.db.getTickInterval() || conf.tickInterval\n    );\n    if (resultTickInterval !== null) {\n      const every = resultTickInterval[1];\n      const interval = resultTickInterval[2];\n      const weekday2 = diagObj.db.getWeekday() || conf.weekday;\n      switch (interval) {\n        case \"millisecond\":\n          bottomXAxis.ticks(timeMillisecond.every(every));\n          break;\n        case \"second\":\n          bottomXAxis.ticks(timeSecond.every(every));\n          break;\n        case \"minute\":\n          bottomXAxis.ticks(timeMinute.every(every));\n          break;\n        case \"hour\":\n          bottomXAxis.ticks(timeHour.every(every));\n          break;\n        case \"day\":\n          bottomXAxis.ticks(timeDay.every(every));\n          break;\n        case \"week\":\n          bottomXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));\n          break;\n        case \"month\":\n          bottomXAxis.ticks(timeMonth.every(every));\n          break;\n      }\n    }\n    svg.append(\"g\").attr(\"class\", \"grid\").attr(\"transform\", \"translate(\" + theSidePad + \", \" + (h2 - 50) + \")\").call(bottomXAxis).selectAll(\"text\").style(\"text-anchor\", \"middle\").attr(\"fill\", \"#000\").attr(\"stroke\", \"none\").attr(\"font-size\", 10).attr(\"dy\", \"1em\");\n    if (diagObj.db.topAxisEnabled() || conf.topAxis) {\n      let topXAxis = axisTop(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat(timeFormat(diagObj.db.getAxisFormat() || conf.axisFormat || \"%Y-%m-%d\"));\n      if (resultTickInterval !== null) {\n        const every = resultTickInterval[1];\n        const interval = resultTickInterval[2];\n        const weekday2 = diagObj.db.getWeekday() || conf.weekday;\n        switch (interval) {\n          case \"millisecond\":\n            topXAxis.ticks(timeMillisecond.every(every));\n            break;\n          case \"second\":\n            topXAxis.ticks(timeSecond.every(every));\n            break;\n          case \"minute\":\n            topXAxis.ticks(timeMinute.every(every));\n            break;\n          case \"hour\":\n            topXAxis.ticks(timeHour.every(every));\n            break;\n          case \"day\":\n            topXAxis.ticks(timeDay.every(every));\n            break;\n          case \"week\":\n            topXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));\n            break;\n          case \"month\":\n            topXAxis.ticks(timeMonth.every(every));\n            break;\n        }\n      }\n      svg.append(\"g\").attr(\"class\", \"grid\").attr(\"transform\", \"translate(\" + theSidePad + \", \" + theTopPad + \")\").call(topXAxis).selectAll(\"text\").style(\"text-anchor\", \"middle\").attr(\"fill\", \"#000\").attr(\"stroke\", \"none\").attr(\"font-size\", 10);\n    }\n  }\n  __name(makeGrid, \"makeGrid\");\n  function vertLabels(theGap, theTopPad) {\n    let prevGap = 0;\n    const numOccurrences = Object.keys(categoryHeights).map((d) => [d, categoryHeights[d]]);\n    svg.append(\"g\").selectAll(\"text\").data(numOccurrences).enter().append(function(d) {\n      const rows = d[0].split(common_default.lineBreakRegex);\n      const dy = -(rows.length - 1) / 2;\n      const svgLabel = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n      svgLabel.setAttribute(\"dy\", dy + \"em\");\n      for (const [j, row] of rows.entries()) {\n        const tspan = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n        tspan.setAttribute(\"alignment-baseline\", \"central\");\n        tspan.setAttribute(\"x\", \"10\");\n        if (j > 0) {\n          tspan.setAttribute(\"dy\", \"1em\");\n        }\n        tspan.textContent = row;\n        svgLabel.appendChild(tspan);\n      }\n      return svgLabel;\n    }).attr(\"x\", 10).attr(\"y\", function(d, i) {\n      if (i > 0) {\n        for (let j = 0; j < i; j++) {\n          prevGap += numOccurrences[i - 1][1];\n          return d[1] * theGap / 2 + prevGap * theGap + theTopPad;\n        }\n      } else {\n        return d[1] * theGap / 2 + theTopPad;\n      }\n    }).attr(\"font-size\", conf.sectionFontSize).attr(\"class\", function(d) {\n      for (const [i, category] of categories.entries()) {\n        if (d[0] === category) {\n          return \"sectionTitle sectionTitle\" + i % conf.numberSectionStyles;\n        }\n      }\n      return \"sectionTitle\";\n    });\n  }\n  __name(vertLabels, \"vertLabels\");\n  function drawToday(theSidePad, theTopPad, w2, h2) {\n    const todayMarker2 = diagObj.db.getTodayMarker();\n    if (todayMarker2 === \"off\") {\n      return;\n    }\n    const todayG = svg.append(\"g\").attr(\"class\", \"today\");\n    const today = /* @__PURE__ */ new Date();\n    const todayLine = todayG.append(\"line\");\n    todayLine.attr(\"x1\", timeScale(today) + theSidePad).attr(\"x2\", timeScale(today) + theSidePad).attr(\"y1\", conf.titleTopMargin).attr(\"y2\", h2 - conf.titleTopMargin).attr(\"class\", \"today\");\n    if (todayMarker2 !== \"\") {\n      todayLine.attr(\"style\", todayMarker2.replace(/,/g, \";\"));\n    }\n  }\n  __name(drawToday, \"drawToday\");\n  function checkUnique(arr) {\n    const hash = {};\n    const result = [];\n    for (let i = 0, l = arr.length; i < l; ++i) {\n      if (!Object.prototype.hasOwnProperty.call(hash, arr[i])) {\n        hash[arr[i]] = true;\n        result.push(arr[i]);\n      }\n    }\n    return result;\n  }\n  __name(checkUnique, \"checkUnique\");\n}, \"draw\");\nvar ganttRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/gantt/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .mermaid-main-font {\n        font-family: ${options.fontFamily};\n  }\n\n  .exclude-range {\n    fill: ${options.excludeBkgColor};\n  }\n\n  .section {\n    stroke: none;\n    opacity: 0.2;\n  }\n\n  .section0 {\n    fill: ${options.sectionBkgColor};\n  }\n\n  .section2 {\n    fill: ${options.sectionBkgColor2};\n  }\n\n  .section1,\n  .section3 {\n    fill: ${options.altSectionBkgColor};\n    opacity: 0.2;\n  }\n\n  .sectionTitle0 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle1 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle2 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle3 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle {\n    text-anchor: start;\n    font-family: ${options.fontFamily};\n  }\n\n\n  /* Grid and axis */\n\n  .grid .tick {\n    stroke: ${options.gridColor};\n    opacity: 0.8;\n    shape-rendering: crispEdges;\n  }\n\n  .grid .tick text {\n    font-family: ${options.fontFamily};\n    fill: ${options.textColor};\n  }\n\n  .grid path {\n    stroke-width: 0;\n  }\n\n\n  /* Today line */\n\n  .today {\n    fill: none;\n    stroke: ${options.todayLineColor};\n    stroke-width: 2px;\n  }\n\n\n  /* Task styling */\n\n  /* Default task */\n\n  .task {\n    stroke-width: 2;\n  }\n\n  .taskText {\n    text-anchor: middle;\n    font-family: ${options.fontFamily};\n  }\n\n  .taskTextOutsideRight {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: start;\n    font-family: ${options.fontFamily};\n  }\n\n  .taskTextOutsideLeft {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: end;\n  }\n\n\n  /* Special case clickable */\n\n  .task.clickable {\n    cursor: pointer;\n  }\n\n  .taskText.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideLeft.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideRight.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n\n  /* Specific task settings for the sections*/\n\n  .taskText0,\n  .taskText1,\n  .taskText2,\n  .taskText3 {\n    fill: ${options.taskTextColor};\n  }\n\n  .task0,\n  .task1,\n  .task2,\n  .task3 {\n    fill: ${options.taskBkgColor};\n    stroke: ${options.taskBorderColor};\n  }\n\n  .taskTextOutside0,\n  .taskTextOutside2\n  {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n  .taskTextOutside1,\n  .taskTextOutside3 {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n\n  /* Active task */\n\n  .active0,\n  .active1,\n  .active2,\n  .active3 {\n    fill: ${options.activeTaskBkgColor};\n    stroke: ${options.activeTaskBorderColor};\n  }\n\n  .activeText0,\n  .activeText1,\n  .activeText2,\n  .activeText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Completed task */\n\n  .done0,\n  .done1,\n  .done2,\n  .done3 {\n    stroke: ${options.doneTaskBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneText0,\n  .doneText1,\n  .doneText2,\n  .doneText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Tasks on the critical line */\n\n  .crit0,\n  .crit1,\n  .crit2,\n  .crit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.critBkgColor};\n    stroke-width: 2;\n  }\n\n  .activeCrit0,\n  .activeCrit1,\n  .activeCrit2,\n  .activeCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.activeTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneCrit0,\n  .doneCrit1,\n  .doneCrit2,\n  .doneCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n    cursor: pointer;\n    shape-rendering: crispEdges;\n  }\n\n  .milestone {\n    transform: rotate(45deg) scale(0.8,0.8);\n  }\n\n  .milestoneText {\n    font-style: italic;\n  }\n  .doneCritText0,\n  .doneCritText1,\n  .doneCritText2,\n  .doneCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .activeCritText0,\n  .activeCritText1,\n  .activeCritText2,\n  .activeCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .titleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.titleColor || options.textColor};\n    font-family: ${options.fontFamily};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/gantt/ganttDiagram.ts\nvar diagram = {\n  parser: gantt_default,\n  db: ganttDb_default,\n  renderer: ganttRenderer_default,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}