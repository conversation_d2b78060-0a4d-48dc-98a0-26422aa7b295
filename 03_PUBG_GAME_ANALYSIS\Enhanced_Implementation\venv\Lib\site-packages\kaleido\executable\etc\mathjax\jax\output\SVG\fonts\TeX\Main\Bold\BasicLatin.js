/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/Main/Bold/BasicLatin.js
 *
 *  Copyright (c) 2011-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_Main-bold'],
  {
    // SPACE
    0x20: [0,0,250,0,0,''],

    // EXCLAMATION MARK
    0x21: [705,-1,350,89,260,'89 629Q89 663 116 684T171 705Q215 705 237 681T260 634Q260 619 233 434T204 244Q201 237 175 237Q150 237 146 244Q144 248 117 433T89 629ZM90 86Q90 125 116 148T177 171Q211 169 235 146T259 86Q259 48 235 25T175 1Q138 1 114 24T90 86'],

    // QUOTATION MARK
    0x22: [694,-328,603,38,492,'38 572T38 608T61 669T121 694Q167 694 196 657T225 559Q225 520 214 482T186 418T151 370T119 339T99 329T82 340T70 360Q70 365 74 369T92 385T122 414Q142 441 154 471T170 518L172 535L166 532Q160 530 148 527T122 523Q85 523 62 547ZM305 572T305 608T328 669T388 694Q434 694 463 657T492 559Q492 520 481 482T453 418T418 370T386 339T366 329T349 340T337 360Q337 365 341 369T359 385T389 414Q409 441 421 471T436 518L439 535L433 532Q427 530 415 527T389 523Q352 523 329 547'],

    // NUMBER SIGN
    0x23: [694,193,958,64,893,'64 362Q64 380 87 393H363L366 404Q379 443 390 480T409 542T424 590T435 628T443 655T451 674T458 686T467 692T478 694Q490 694 499 686T509 662Q505 643 427 395Q427 393 523 393H620L623 404Q630 426 652 498T691 624T711 681Q718 694 735 694Q748 694 757 685T766 662Q762 643 684 395Q684 393 777 393H871Q872 392 875 390T881 386T887 381T891 374T893 363Q893 345 871 333L767 332H664L660 319Q660 318 638 245T614 171Q614 169 742 169H871L877 165Q883 161 885 159T890 151T893 138Q893 120 871 109L732 108H594L590 95Q587 84 546 -46Q508 -175 505 -178Q498 -193 478 -193Q462 -193 455 -183T448 -164Q448 -156 530 106Q530 108 434 108H337L333 95Q330 84 289 -46Q251 -175 248 -178Q240 -193 222 -193Q206 -193 199 -183T191 -164Q191 -154 273 106Q273 108 180 108L87 109Q64 117 64 139Q64 156 87 169H293L321 262Q326 277 331 294T340 321L343 330Q343 332 215 332L87 333Q64 343 64 362ZM600 330Q600 332 504 332H407L403 319Q403 318 381 245T357 171Q357 169 453 169H550L578 262Q583 277 588 294T597 321L600 330'],

    // DOLLAR SIGN
    0x24: [750,56,575,64,510,'64 494Q64 541 80 579T120 638T171 674T219 693T253 698H256V750H318V699H323Q355 694 380 686T433 663T480 620T506 556Q510 539 510 520Q510 480 488 463T440 445L422 447Q407 451 398 459Q370 478 370 515Q370 542 384 559T412 580L427 584Q424 589 418 596T386 617T324 636H318V434Q411 419 460 355T510 217Q510 196 507 175T492 122T461 67T404 23T318 -4V-56H256V-5H254Q252 -3 240 -3Q194 4 160 23T108 64T80 112T67 156T64 190Q64 218 81 240T134 262Q171 262 187 240T204 193T190 150T153 125Q146 125 144 123Q142 123 149 113T174 89T218 66Q247 58 255 58Q256 58 256 173V287L239 291Q160 308 112 365T64 494ZM255 636Q246 635 236 632T206 620T173 591T160 543Q160 472 256 448V542Q256 636 255 636ZM320 59Q324 59 333 61T356 70T384 89T406 120T415 167Q415 200 395 225T356 260T318 274V59H320'],

    // PERCENT SIGN
    0x25: [751,56,958,65,893,'65 549Q65 609 84 652T132 714T187 742T236 750Q265 750 296 734T355 697T431 661T541 644Q662 644 736 730Q751 749 767 749T790 739T797 719Q797 710 768 672T504 329Q212 -47 211 -48Q203 -55 191 -55Q161 -55 161 -25Q161 -17 163 -12L642 609Q608 595 542 595Q515 595 488 599T441 608T405 619T381 628L373 632Q373 630 375 619T380 589T383 548Q383 455 343 401T235 347Q217 347 198 351T154 368T110 403T78 462T65 549ZM320 549Q320 633 295 665T235 698H234Q214 698 196 674Q182 650 182 549Q182 509 183 486T190 441T207 409T238 399Q269 399 294 431T320 549ZM745 -56Q727 -56 708 -52T664 -35T620 0T588 59T575 146T588 232T620 291T663 325T708 343T747 347Q810 347 851 294T893 146Q893 89 879 48T841 -15T794 -46T745 -56ZM830 146Q830 230 805 262T745 295H744Q724 295 706 271Q692 247 692 146Q692 106 693 83T700 38T717 6T748 -4Q779 -4 804 28T830 146'],

    // AMPERSAND
    0x26: [705,11,894,48,836,'255 -11Q209 -11 164 4T84 56T48 146Q48 159 50 171Q57 197 72 218T99 249T152 292Q204 333 204 334L194 356Q185 379 176 421T166 511Q166 538 168 551Q182 613 226 654T332 704Q334 704 343 704T358 705Q412 702 444 661T476 565V559Q476 489 334 371L330 368L335 357Q382 272 485 165L496 154L506 163Q543 200 597 273L671 382H601V444H610L732 441Q821 441 830 444H836V382H741L709 335Q702 324 687 302T665 270T646 244T625 216T605 191T581 162T553 132L537 116Q544 109 557 98T605 69T673 51Q711 51 739 70T767 115V118H829V114Q829 70 786 30T668 -11Q570 -11 474 37L451 49L441 43Q352 -11 255 -11ZM415 564Q415 596 400 625T356 654Q329 654 310 634T285 588Q283 580 283 554Q283 475 309 417L325 431Q415 512 415 564ZM192 182Q192 126 213 89T279 51Q348 51 400 83L389 91Q362 112 338 137T295 186T264 229T240 265T227 286Q226 285 222 280T217 272T211 263T205 251T200 238T196 222T193 204T192 182'],

    // APOSTROPHE
    0x27: [694,-329,319,74,261,'74 572T74 608T97 669T157 694Q203 694 232 657T261 559Q261 520 250 482T222 418T187 370T155 339T135 329Q128 329 117 340T106 359Q106 365 117 375T144 399T176 440T203 505Q204 511 205 518T208 530V535L202 532Q196 530 184 527T158 523Q121 523 98 547'],

    // ASTERISK
    0x2A: [750,-306,575,72,502,'235 706Q235 724 251 737T287 750Q306 750 322 738T339 706Q339 685 318 580V579Q429 663 436 666Q441 668 449 668Q471 668 486 650T501 612Q501 582 478 572Q476 570 414 549L354 528L414 507Q420 505 430 502T445 497T458 492T470 488T479 483T487 478T493 471T497 463T500 454T501 443Q501 423 486 406T449 388H446Q435 388 370 437Q339 461 318 477V476Q339 371 339 350Q339 332 323 319T287 306T251 319T235 350Q235 371 256 476V477Q145 393 138 390Q133 388 125 388Q103 388 88 406T73 444Q73 474 96 484Q98 486 160 507L220 528L160 549Q154 551 144 554T129 559T116 564T104 568T95 573T87 578T81 585T77 593T74 602T73 613Q73 633 88 650T125 668H128Q139 668 204 619Q235 595 256 579V580Q235 685 235 706'],

    // HYPHEN-MINUS
    0x2D: [278,-166,383,13,318,'13 166V278H318V166H13'],

    // QUESTION MARK
    0x3F: [700,-1,543,65,478,'65 570Q65 628 119 664T259 700Q326 700 372 688T440 654T469 613T478 569Q478 505 412 465Q287 391 287 294V283Q287 250 284 244T263 237H256H249Q232 237 229 242T225 272V287Q227 364 253 418Q274 463 311 504Q335 530 335 575Q335 622 323 635T259 648Q231 648 209 644T179 636T170 630L172 628Q174 627 177 625T183 620T190 611T197 601T202 587T204 570Q204 539 185 519T134 499Q105 499 85 517T65 570ZM171 86Q171 125 197 148T258 171Q292 169 316 146T340 86Q340 48 316 25T256 1Q218 1 195 24T171 86'],

    // COMMERCIAL AT
    0x40: [699,6,894,64,829,'64 347Q64 511 171 605T434 699Q487 699 500 698Q624 684 703 621T811 464Q828 414 828 344Q828 232 788 179T691 125Q673 125 657 127T628 132T606 140T588 148T576 156T568 162L566 164Q565 164 549 154T504 135T444 125Q349 125 284 183T218 347Q218 455 284 512T448 569Q554 569 610 479H638Q670 479 674 471Q676 468 676 340V258Q676 213 679 199T694 178Q701 174 713 177Q767 187 767 340Q767 489 678 569T446 649Q299 649 213 566T126 346Q126 307 134 269T166 189T225 116T320 65T455 45H463Q606 51 721 91L746 99H782H801Q829 99 829 85Q829 78 825 75T804 65Q800 63 797 62Q625 -6 451 -6Q271 -6 168 91T64 347ZM547 468Q526 493 504 505T444 517T377 476T346 347Q346 306 354 271T386 206T448 177Q505 177 547 226V468'],

    // LATIN CAPITAL LETTER A
    0x41: [698,0,869,40,828,'296 0Q278 3 164 3Q58 3 49 0H40V62H92Q144 62 144 64Q388 682 397 689Q403 698 434 698Q463 698 471 689Q475 686 538 530T663 218L724 64Q724 62 776 62H828V0H817Q796 3 658 3Q509 3 485 0H472V62H517Q561 62 561 63L517 175H262L240 120Q218 65 217 64Q217 62 261 62H306V0H296ZM390 237L492 238L440 365Q390 491 388 491Q287 239 287 237H390'],

    // LATIN CAPITAL LETTER B
    0x42: [686,0,818,39,752,'720 510Q720 476 704 448T665 404T619 377T580 362L564 359L583 356Q602 353 632 342T690 312Q712 292 725 276Q752 235 752 189V183Q752 160 741 125Q698 18 547 2Q543 1 288 0H39V62H147V624H39V686H264H409Q502 686 542 681T624 655Q720 607 720 510ZM563 513Q563 553 548 578T518 611T486 622Q479 624 385 624H293V382H375Q458 383 467 385Q563 405 563 513ZM590 192Q590 307 505 329Q504 330 503 330L398 331H293V62H391H400H444Q496 62 528 75T580 131Q590 155 590 192'],

    // LATIN CAPITAL LETTER C
    0x43: [697,11,831,64,766,'64 343Q64 502 174 599T468 697Q502 697 533 691T586 674T623 655T647 639T657 632L694 663Q703 670 711 677T723 687T730 692T735 695T740 696T746 697Q759 697 762 692T766 668V627V489V449Q766 428 762 424T742 419H732H720Q699 419 697 436Q690 498 657 545Q611 618 532 632Q522 634 496 634Q356 634 286 553Q232 488 232 343T286 133Q355 52 497 52Q597 52 650 112T704 237Q704 248 709 251T729 254H735Q750 254 755 253T763 248T766 234Q766 136 680 63T469 -11Q285 -11 175 86T64 343'],

    // LATIN CAPITAL LETTER D
    0x44: [686,0,882,39,817,'39 624V686H270H310H408Q500 686 545 680T638 649Q768 584 805 438Q817 388 817 338Q817 171 702 75Q628 17 515 2Q504 1 270 0H39V62H147V624H39ZM655 337Q655 370 655 390T650 442T639 494T616 540T580 580T526 607T451 623Q443 624 368 624H298V62H377H387H407Q445 62 472 65T540 83T606 129Q629 156 640 195T653 262T655 337'],

    // LATIN CAPITAL LETTER E
    0x45: [680,0,756,39,723,'723 286Q721 284 700 145T677 3V0H39V62H147V618H39V680H660V676Q662 670 675 552T691 428V424H629V428Q629 429 627 448T618 494T601 541Q574 593 527 605T382 618H374H304V384H336Q338 384 347 384T361 384T376 386T392 390T407 397T421 407T432 423Q442 444 443 482V501H505V205H443V224Q442 258 435 278T411 307T380 318T336 322H304V62H375H394Q429 62 449 62T497 66T541 76T577 95T609 126T632 170T651 232Q661 287 661 289H723V286'],

    // LATIN CAPITAL LETTER F
    0x46: [680,0,724,39,675,'425 0L228 3Q63 3 51 0H39V62H147V618H39V680H644V676Q647 670 659 552T675 428V424H613Q613 433 605 477Q599 511 589 535T562 574T530 599T488 612T441 617T387 618H368H304V371H333Q389 373 411 390T437 468V488H499V192H437V212Q436 244 430 263T408 292T378 305T333 309H304V62H439V0H425'],

    // LATIN CAPITAL LETTER G
    0x47: [697,10,904,64,845,'465 -10Q281 -10 173 88T64 343Q64 413 85 471T143 568T217 631T298 670Q371 697 449 697Q452 697 459 697T470 696Q502 696 531 690T582 675T618 658T644 641T656 632L732 695Q734 697 745 697Q758 697 761 692T765 668V627V489V449Q765 428 761 424T741 419H731H724Q705 419 702 422T695 444Q683 520 631 577T495 635Q364 635 295 563Q261 528 247 477T232 343Q232 296 236 260T256 185T296 120T366 76T472 52Q481 51 498 51Q544 51 573 67T607 108Q608 111 608 164V214H464V276H479Q506 273 680 273Q816 273 834 276H845V214H765V113V51Q765 16 763 8T750 0Q742 2 709 16T658 40L648 46Q592 -10 465 -10'],

    // LATIN CAPITAL LETTER H
    0x48: [686,0,900,39,860,'400 0Q376 3 226 3Q75 3 51 0H39V62H147V624H39V686H51Q75 683 226 683Q376 683 400 686H412V624H304V388H595V624H487V686H499Q523 683 673 683Q824 683 848 686H860V624H752V62H860V0H848Q824 3 674 3Q523 3 499 0H487V62H595V326H304V62H412V0H400'],

    // LATIN CAPITAL LETTER I
    0x49: [686,0,436,25,410,'397 0Q370 3 218 3Q65 3 38 0H25V62H139V624H25V686H38Q65 683 218 683Q370 683 397 686H410V624H296V62H410V0H397'],

    // LATIN CAPITAL LETTER J
    0x4A: [686,11,594,8,527,'174 114Q174 96 169 82T159 63T144 47L155 45Q183 40 203 40Q271 40 290 104Q294 118 294 150T295 380V624H154V686H169Q196 683 365 683Q499 683 517 686H527V624H446V379Q446 183 446 153T441 108Q413 32 315 2Q266 -11 208 -11Q160 -11 118 -2T42 37T8 114V122Q8 150 30 174T91 198T152 174T174 122V114'],

    // LATIN CAPITAL LETTER K
    0x4B: [686,0,901,39,852,'400 0Q376 3 226 3Q75 3 51 0H39V62H147V624H39V686H51Q75 683 226 683Q376 683 400 686H412V624H304V338L472 483L634 624H565V686H576Q597 683 728 683Q814 683 829 686H836V624H730L614 524Q507 432 497 422Q496 422 498 418T514 395T553 342T627 241L759 63L805 62H852V0H842Q830 3 701 3Q550 3 526 0H513V62H549Q584 62 584 63Q583 65 486 196T388 328L304 256V62H412V0H400'],

    // LATIN CAPITAL LETTER L
    0x4C: [686,0,692,39,643,'643 285Q641 280 629 148T612 4V0H39V62H147V624H39V686H51Q75 683 228 683Q415 685 425 686H439V624H304V62H352H378Q492 62 539 138Q551 156 558 178T569 214T576 255T581 289H643V285'],

    // LATIN CAPITAL LETTER M
    0x4D: [686,0,1092,39,1052,'314 0Q296 3 181 3T48 0H39V62H147V624H39V686H305Q316 679 323 667Q330 653 434 414L546 157L658 414Q766 662 773 674Q778 681 788 686H1052V624H944V62H1052V0H1040Q1016 3 874 3T708 0H696V62H804V341L803 618L786 580Q770 543 735 462T671 315Q540 13 536 9Q528 1 507 1Q485 1 477 9Q472 14 408 162T281 457T217 603Q215 603 215 334V62H323V0H314'],

    // LATIN CAPITAL LETTER N
    0x4E: [686,0,900,39,860,'314 0Q296 3 181 3T48 0H39V62H147V624H39V686H171H265Q288 686 297 686T309 684T315 679Q317 676 500 455T684 233V624H576V686H585Q603 683 718 683T851 686H860V624H752V319Q752 15 750 11Q747 4 742 2T718 0H712Q708 0 706 0T700 0T696 1T693 2T690 4T687 7T684 11T679 16T674 23Q671 27 437 311L215 579V62H323V0H314'],

    // LATIN CAPITAL LETTER O
    0x4F: [696,10,864,64,798,'64 339Q64 431 96 502T182 614T295 675T420 696Q469 696 481 695Q620 680 709 589T798 339Q798 173 697 82T432 -10Q262 -10 163 85T64 339ZM625 454Q618 502 600 538T562 593T515 624T469 639T431 642Q331 642 276 563Q232 493 232 353Q232 315 234 285T244 216T267 148T308 94T372 56Q405 46 432 46Q517 46 567 106T627 267Q631 299 631 353Q631 418 625 454'],

    // LATIN CAPITAL LETTER P
    0x50: [686,0,786,39,722,'400 0Q376 3 226 3Q75 3 51 0H39V62H147V624H39V686H253Q435 686 470 685T536 678Q585 668 621 648T675 605T705 557T718 514T721 483T718 451T704 409T673 362T616 322T530 293Q500 288 399 287H304V62H412V0H400ZM553 475Q553 554 537 582T459 622Q451 623 373 624H298V343H372Q457 344 480 350Q527 362 540 390T553 475'],

    // LATIN CAPITAL LETTER Q
    0x51: [696,193,864,64,805,'64 339Q64 431 96 502T182 614T295 675T420 696Q469 696 481 695Q620 680 709 589T798 339Q798 255 768 184Q720 77 611 26L600 21Q635 -26 682 -26H696Q769 -26 769 0Q769 7 774 12T787 18Q805 18 805 -7V-13Q803 -64 785 -106T737 -171Q720 -183 697 -191Q687 -193 668 -193Q636 -193 613 -182T575 -144T552 -94T532 -27Q531 -23 530 -16T528 -6T526 -3L512 -5Q499 -7 477 -8T431 -10Q393 -10 382 -9Q238 8 151 97T64 339ZM326 80Q326 113 356 138T430 163Q492 163 542 100L553 86Q554 85 561 91T578 108Q637 179 637 330Q637 430 619 498T548 604Q500 641 425 641Q408 641 390 637T347 623T299 590T259 535Q226 469 226 338Q226 244 246 180T318 79L325 74Q326 74 326 80ZM506 58Q480 112 433 112Q412 112 395 104T378 77Q378 44 431 44Q480 44 506 58'],

    // LATIN CAPITAL LETTER R
    0x52: [687,11,862,39,858,'394 0Q370 3 222 3Q75 3 51 0H39V62H147V624H39V686H234Q256 686 299 686T362 687Q479 687 554 669T681 593Q716 550 716 497Q716 390 568 338Q569 337 572 336T577 332Q605 317 623 300T650 258T662 218T668 172Q678 98 689 76Q707 40 748 40Q770 40 780 54T795 88T801 111Q805 117 827 117H831Q846 117 852 113T858 92Q857 78 852 63T834 30T797 1T739 -11Q630 -11 580 12T511 87Q506 104 506 168Q506 170 506 178T507 194Q507 289 438 313Q424 318 356 318H298V62H406V0H394ZM366 369Q459 370 490 381Q548 402 548 476V498V517Q548 578 513 600Q479 624 392 624H358H298V369H366'],

    // LATIN CAPITAL LETTER S
    0x53: [698,12,639,63,574,'64 493Q64 582 120 636T264 696H272Q280 697 285 697Q380 697 454 645L480 669Q484 672 488 676T495 683T500 688T504 691T508 693T511 695T514 696T517 697T522 697Q536 697 539 691T542 652V577Q542 557 542 532T543 500Q543 472 540 465T524 458H511H505Q489 458 485 461T479 478Q472 529 449 564T393 614T336 634T287 639Q228 639 203 610T177 544Q177 517 195 493T247 457Q253 454 343 436T475 391Q574 326 574 207V200Q574 163 559 120Q517 12 389 -9Q380 -10 346 -10Q308 -10 275 -5T221 7T184 22T160 35T151 40L126 17Q122 14 118 10T111 3T106 -2T102 -5T98 -7T95 -9T92 -10T89 -11T84 -11Q70 -11 67 -4T64 35V108Q64 128 64 153T63 185Q63 203 63 211T69 223T77 227T94 228H100Q118 228 122 225T126 205Q130 125 193 88T345 51Q408 51 434 82T460 157Q460 196 439 221T388 257Q384 259 305 276T221 295Q155 313 110 366T64 493'],

    // LATIN CAPITAL LETTER T
    0x54: [675,0,800,41,758,'41 425Q41 426 51 545T62 669V675H737V669Q738 665 748 546T758 425V419H696V425Q687 517 669 555T595 607Q578 612 522 613H478V62H631V0H615Q585 3 399 3Q214 3 184 0H168V62H321V613H277H263Q164 613 134 561Q113 527 103 425V419H41V425'],

    // LATIN CAPITAL LETTER U
    0x55: [686,11,885,39,845,'570 686Q588 683 703 683T836 686H845V624H737V420Q737 390 737 345T738 284Q738 205 729 164T689 83Q614 -11 465 -11Q321 -11 240 51T148 207Q147 214 147 421V624H39V686H51Q75 683 226 683Q376 683 400 686H412V624H304V405V370V268Q304 181 311 146T346 87Q387 52 466 52Q642 52 667 195Q668 204 669 415V624H561V686H570'],

    // LATIN CAPITAL LETTER V
    0x56: [686,7,869,25,843,'592 686H604Q615 685 631 685T666 684T700 684T724 683Q829 683 835 686H843V624H744L611 315Q584 254 546 165Q492 40 482 19T461 -6L460 -7H409Q398 -4 391 9Q385 20 257 315L124 624H25V686H36Q57 683 190 683Q340 683 364 686H377V624H289L384 403L480 185L492 212Q504 240 529 298T575 405L670 624H582V686H592'],

    // LATIN CAPITAL LETTER W
    0x57: [686,7,1189,24,1164,'915 686L1052 683Q1142 683 1157 686H1164V624H1073L957 320Q930 249 900 170T855 52T839 10Q834 0 826 -5Q821 -7 799 -7H792Q777 -7 772 -5T759 10Q759 11 748 39T716 122T676 228L594 442L512 228Q486 159 455 78Q433 19 428 9T416 -5Q411 -7 389 -7H379Q356 -7 349 10Q349 12 334 51T288 170T231 320L116 624H24V686H35Q44 683 183 683Q331 683 355 686H368V624H323Q278 624 278 623L437 207L499 369L561 531L526 624H434V686H445Q454 683 593 683Q741 683 765 686H778V624H733Q688 624 688 623L847 207Q848 207 927 415T1006 624H905V686H915'],

    // LATIN CAPITAL LETTER X
    0x58: [686,0,869,33,835,'327 0Q306 3 174 3Q52 3 43 0H33V62H98L162 63L360 333L157 624H48V686H59Q80 683 217 683Q368 683 395 686H408V624H335L393 540L452 458L573 623Q573 624 528 624H483V686H494Q515 683 646 683Q769 683 778 686H787V624H658L575 511Q493 398 493 397L508 376Q522 356 553 312T611 229L727 62H835V0H824Q803 3 667 3Q516 3 489 0H476V62H513L549 63L401 274L247 63Q247 62 292 62H338V0H327'],

    // LATIN CAPITAL LETTER Y
    0x59: [686,0,869,19,849,'605 0Q581 3 434 3Q286 3 262 0H250V62H358V275L126 624H19V686H30Q54 683 189 683Q361 685 370 686H383V624H308L319 608Q330 591 353 556T396 491L484 359L660 623Q660 624 623 624H585V686H595Q613 683 728 683Q832 683 841 686H849V624H742L509 274V62H618V0H605'],

    // LATIN CAPITAL LETTER Z
    0x5A: [686,0,703,64,645,'80 430L92 686H358Q624 686 628 684Q638 679 638 656Q638 640 637 639Q637 638 445 353Q401 288 351 214T277 103L253 67L256 66Q258 66 265 66T279 66T298 66H343Q380 66 406 68T464 81T518 110T557 164T579 250Q583 278 583 298Q583 299 614 299H645V291Q643 281 636 150T627 8V0H353Q79 0 75 2Q64 7 64 31Q64 48 66 52L259 340L451 623Q451 624 384 624Q294 623 259 612Q155 581 143 446Q142 440 142 432V430H80'],

    // LOW LINE
    0x5F: [-10,61,575,0,574,'0 -61V-10H574V-61H0'],

    // GRAVE ACCENT
    0x60: [706,-503,575,113,338,'114 634Q114 663 136 684T183 706Q191 706 196 705T208 700T219 693T232 681T245 666T262 645T282 620Q332 558 337 553Q338 552 318 527L299 503L223 543Q215 547 202 553T183 563T167 571T153 580T141 587T131 595T124 603T118 612T115 622T114 634'],

    // TILDE
    0x7E: [344,-202,575,96,478,'343 202Q320 202 278 225T215 249Q181 249 146 214L134 202L115 219Q111 222 106 226T98 234L96 236Q158 306 165 313Q199 344 230 344Q239 344 244 343Q262 339 300 318T359 297Q393 297 428 332L440 344L459 327Q463 324 468 320T476 312L478 310Q416 240 409 233Q375 202 343 202']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/BasicLatin.js");
