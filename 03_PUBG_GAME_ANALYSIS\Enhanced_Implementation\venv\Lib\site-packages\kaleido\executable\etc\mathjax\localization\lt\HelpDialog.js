/*************************************************************
 *
 *  MathJax/localization/lt/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("lt","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "MathJax Pagalba",
          MathJax: "*\u201EMathJax\u201C* \u2013 tai \u201EJavaScript\u201C biblioteka, leid\u017Eianti autoriams tinklalapiuose ra\u0161yti matematikos tekst\u0105. Skaitytojams papildom\u0173 veiksm\u0173 atlikti nereikia.",
          Browsers: "*Nar\u0161ykl\u0117s*: \u201EMathJax\u201C veikia visose dabartin\u0117se nar\u0161ykl\u0117je, \u012Fskaitant \u201EIE\u201C 6+, \u201EFirefox\u201C 3+, \u201EChrome\u201C 0.2+, \u201ESafari\u201C 2+, \u201EOpera\u201C 9.6+ ir daugum\u0105 mobili\u0173j\u0173 nar\u0161ykli\u0173.",
          Menu: "*Matematikos meniu*: \u201EMathJax\u201C \u012Fdiegtas kontekstinis lyg\u010Di\u0173 meniu. Meniu atver\u010Diamas spustel\u0117jus de\u0161iniuoju pel\u0117s klavi\u0161u arba nuspaudus Vald ir spustel\u0117jus matematikos \u017Eenkl\u0105.",
          ShowMath: "*Rodiniu* per\u017Ei\u016Brima ir kopijuojama formul\u0117s \u0161altinio \u017Eym\u0117jimo sintaks\u0117 (\u201EMathML\u201C arba pradiniu formatu).",
          Settings: "*Nuostatomis* valdomos \u201EMathJax\u201C ypatyb\u0117s, pavyzd\u017Eiui, matematikos \u017Eenkl\u0173 dydis arba lyg\u010Di\u0173 vaizdavimo mechanizmas.",
          Language: "*Kalba* nustatoma \u201EMathJax\u201C meniu ir \u012Fsp\u0117jam\u0173j\u0173 prane\u0161im\u0173 kalba.",
          Zoom: "*Masteliu* didinamas lygties rodinys.",
          Accessibilty: "*Pritaikymas ne\u012Fgaliesiems*: \u201EMathJax\u201C prisitaiko prie ekrano skaitykli\u0173, kad matematikos tekstas b\u016Bt\u0173 \u012Fskaitomas silpnaregiams.",
          Fonts: "*\u0160riftai*: \u201EMathJax\u201C telkiasi naudotojo kompiuteryje \u012Fdiegtus \u0161riftus, jei \u0161ie yra tinkami; kitu atveju \u2013 tinkle saugomus \u0161riftus. Naudotojo kompiuteryje \u012Fdiegti \u0161riftai, nors ir neb\u016Btini, greitina \u017Eenkl\u0173 rinkim\u0105. Patariama \u012Fdiegti [\u201ESTIX\u201C \u0161riftus](%1).",
          CloseDialog: "Nutraukti \u017Einyno dialog\u0105"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/lt/HelpDialog.js");
