/*************************************************************
 *
 *  MathJax/localization/ca/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ca","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "Carregant font web %1",
          CantLoadWebFont: "No es pot carregar la font web %1",
          FirefoxCantLoadWebFont: "Firefox no pot carregar fonts web des d'un servidor remot",
          CantFindFontUsing: "No es pot trobar una font v\u00E0lida usant %1",
          WebFontsNotAvailable: "Fonts web no disponibles. S'estan usant fonts d'imatge"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ca/HTML-CSS.js");
