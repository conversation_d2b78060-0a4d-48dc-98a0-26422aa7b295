[{"locale": "en"}, {"category": "Trigonometric", "mappings": {"default": {"default": "cosine function", "short": "cosine"}}, "key": "cos", "names": ["cos", "cosine"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "cotangent function", "short": "cotangent"}}, "key": "cot", "names": ["cot"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "cosecant function", "short": "cosecant"}}, "key": "csc", "names": ["csc"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "secant function", "short": "secant"}}, "key": "sec", "names": ["sec"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "sine function", "alternative": "sine function", "short": "sine"}}, "key": "sin", "names": ["sin", "sine"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "tangent function", "short": "tangent"}}, "key": "tan", "names": ["tan"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "inverse cosine function", "alternative": "arc cosine function", "short": "arc cosine"}}, "key": "arccos", "names": ["arccos"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "inverse cotangent function", "alternative": "arc cotangent function", "short": "arc cotangent"}}, "key": "<PERSON><PERSON>", "names": ["<PERSON><PERSON>"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "inverse cosecant function", "alternative": "arc cosecant function", "short": "arc cosecant"}}, "key": "arccsc", "names": ["arccsc"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "inverse secant function", "alternative": "arc secant function", "short": "arc secant"}}, "key": "arcsec", "names": ["arcsec"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "inverse sine function", "alternative": "arc sine function", "short": "arc sine"}}, "key": "arcsin", "names": ["arcsin"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "inverse tangent function", "alternative": "arc tangent function", "short": "arc tangent"}}, "key": "arctan", "names": ["arctan"]}]