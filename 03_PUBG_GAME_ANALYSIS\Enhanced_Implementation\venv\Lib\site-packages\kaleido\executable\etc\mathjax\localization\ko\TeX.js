/*************************************************************
 *
 *  MathJax/localization/ko/TeX.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ko","TeX",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "\uB2EB\uD788\uC9C0 \uC54A\uC740 \uC5EC\uB294 \uAD04\uD638\uAC00 \uC788\uC2B5\uB2C8\uB2E4.",
          ExtraCloseMissingOpen: "\uB2EB\uB294 \uAD04\uD638\uAC00 \uCD94\uAC00\uB85C \uB0A8\uC544\uC788\uC2B5\uB2C8\uB2E4.",
          MissingLeftExtraRight: "\\left\uAC00 \uC5C6\uAC70\uB098 \\right\uAC00 \uCD94\uAC00\uB85C \uC788\uC2B5\uB2C8\uB2E4",
          ExtraLeftMissingRight: "\\left\uAC00 \uCD94\uAC00\uB85C \uC788\uAC70\uB098 \\right\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4",
          Misplaced: "%1\uC758 \uC704\uCE58\uAC00 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4",
          MissingOpenForSub: "\uC544\uB798 \uCCA8\uC790\uC758 \uC5EC\uB294 \uAD04\uD638\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4",
          MissingOpenForSup: "\uC704 \uCCA8\uC790\uC758 \uC5EC\uB294 \uAD04\uD638\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4",
          EnvBadEnd: "\\begin{%1}\uC774 \\end{%2}(\uC73C)\uB85C \uB05D\uB0A9\uB2C8\uB2E4",
          EnvMissingEnd: "\\end{%1}\uC774 \uBE60\uC84C\uC2B5\uB2C8\uB2E4",
          MissingBoxFor: "%1 \uBC15\uC2A4\uB97C \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
          MissingCloseBrace: "\uB2EB\uB294 \uAD04\uD638\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4",
          DoubleExponent: "\uC774\uC911 \uC9C0\uC218: \uBA85\uD655\uC131\uC744 \uC704\uD574 \uAD04\uD638\uB97C \uC0AC\uC6A9\uD558\uC138\uC694",
          DoubleSubscripts: "\uC774\uC911 \uC544\uB798 \uCCA8\uC790: \uBA85\uD655\uC131\uC744 \uC704\uD574 \uAD04\uD638\uB97C \uC0AC\uC6A9\uD558\uC138\uC694",
          MisplacedMiddle: "%1\uC740 \\left\uC640 \\right \uC0AC\uC774\uC5D0 \uC788\uC5B4\uC57C \uD569\uB2C8\uB2E4",
          MisplacedLimits: "%1\uC740 \uC5F0\uC0B0\uC790\uB9CC \uD5C8\uC6A9\uB429\uB2C8\uB2E4.",
          MisplacedMoveRoot: "%1\uC740 \uB8E8\uD2B8 \uC548\uC5D0\uC11C\uB9CC \uB098\uD0C0\uB0A9\uB2C8\uB2E4.",
          MultipleCommand: "%1\uC774 \uC911\uBCF5\uB418\uC5C8\uC2B5\uB2C8\uB2E4",
          IntegerArg: "%1\uC5D0 \uB300\uD55C \uC778\uC790\uB294 \uC815\uC218\uC5EC\uC57C \uD569\uB2C8\uB2E4.",
          MissingArgFor: "%1 \uC778\uC790\uB97C \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
          InvalidEnv: "\uC62C\uBC14\uB974\uC9C0 \uC54A\uC740 \uD658\uACBD \uC774\uB984 '%1'",
          UnknownEnv: "\uC54C \uC218 \uC5C6\uB294 \uD658\uACBD '%1'",
          ExtraCloseLooking: "%1\uC744 \uCC3E\uB294 \uB3C4\uC911\uC5D0 \uC5EC\uBD84\uC758 \uB2EB\uB294 \uC911\uAD04\uD638\uAC00 \uBC1C\uACAC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
          MissingCloseBracket: "%1\uC758 \uC778\uC790\uC5D0 \uB300\uD574 \uB2EB\uB294 ']' \uAE30\uD638\uB97C \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
          CommandNotAllowedInEnv: "%1\uC740 %2 \uD658\uACBD\uC5D0\uC11C \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4",
          MultipleLabel: "'%1' label\uC774 \uC911\uBCF5\uC73C\uB85C \uC815\uC758\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
          CommandAtTheBeginingOfLine: "%1\uC740 \uC904\uC758 \uCC98\uC74C\uC5D0 \uC640\uC57C \uD569\uB2C8\uB2E4.",
          IllegalAlign: "%1\uC5D0 \uC9C0\uC815\uB41C \uC778\uC218\uB294 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4",
          UndefinedColorModel: "'%1' \uC0C9\uC0C1 \uBAA8\uB378\uC774 \uC815\uC758\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.",
          ModelArg1: "%1 \uBAA8\uB378\uC5D0 \uB300\uD55C \uC0C9\uC0C1 \uAC12\uC740 3\uAC1C\uC758 \uC218\uAC00 \uD544\uC694\uD569\uB2C8\uB2E4.",
          InvalidDecimalNumber: "\uC62C\uBC14\uB974\uC9C0 \uC54A\uC740 \uC2ED\uC9C4\uC218",
          InvalidNumber: "\uC798\uBABB\uB41C \uC22B\uC790\uC785\uB2C8\uB2E4",
          NoClosingChar: "\uB2EB\uB294 \uAD04\uD638 %1\uB97C \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4",
          IllegalControlSequenceName: "%1\uC5D0 \uB300\uD55C \uC81C\uC5B4 \uC2DC\uD000\uC2A4 \uC774\uB984\uC774 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4",
          IllegalParamNumber: "%1\uC5D0 \uC9C0\uC815\uB41C \uBCC0\uC218 \uC218\uAC00 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4",
          CantUseHash2: "%1\uC5D0 \uB300\uD55C \uD15C\uD50C\uB9BF\uC5D0 #\uC758 \uC0AC\uC6A9\uC774 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4",
          UndefinedControlSequence: "\uC815\uC758\uB418\uC9C0 \uC54A\uC740 \uCEE8\uD2B8\uB864 \uC2DC\uD000\uC2A4 %1",
          MathNotTerminated: "\uD14D\uC2A4\uD2B8 \uC0C1\uC790\uC5D0\uC11C \uC218\uC2DD\uC774 \uC644\uC131\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4",
          MissingCS: "%1 \uC55E\uC5D0 \uCEE8\uD2B8\uB864 \uC2DC\uD000\uC2A4\uAC00 \uC640\uC57C \uD569\uB2C8\uB2E4",
          MismatchUseDef: "%1\uC758 \uC0AC\uC6A9\uC774 \uADF8\uAC83\uC758 \uC815\uC758\uC640 \uC77C\uCE58\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ko/TeX.js");
