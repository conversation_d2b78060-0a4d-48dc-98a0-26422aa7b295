/*************************************************************
 *
 *  MathJax/localization/zh-hant/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("zh-hant","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "\u7DB2\u9801\u5B57\u578B%1\u8F09\u5165\u4E2D",
          CantLoadWebFont: "\u7121\u6CD5\u8F09\u5165\u7DB2\u9801\u5B57\u578B%1",
          FirefoxCantLoadWebFont: "Firefox\u700F\u89BD\u5668\u7121\u6CD5\u5F9E\u9060\u7AEF\u4E3B\u6A5F\u8F09\u5165\u7DB2\u9801\u5B57\u578B",
          CantFindFontUsing: "\u7121\u6CD5\u627E\u5230\u4F7F\u7528%1\u7684\u6709\u6548\u5B57\u578B",
          WebFontsNotAvailable: "\u7DB2\u9801\u5B57\u578B\u7121\u6CD5\u4F7F\u7528\u3002\u6539\u7528\u5716\u50CF\u5B57\u578B\u53D6\u4EE3"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/zh-hant/HTML-CSS.js");
