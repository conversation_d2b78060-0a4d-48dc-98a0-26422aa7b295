{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 👥 PUBG Team Dynamics - Squad Performance Analysis\n", "\n", "## 🎯 Overview\n", "Analysis of team coordination, squad performance metrics, and collaborative gameplay patterns in PUBG team matches.\n", "\n", "## 📊 Team Metrics\n", "- **Squad Coordination** - Team synergy analysis\n", "- **Role Distribution** - Player specialization patterns\n", "- **Collective Performance** - Team effectiveness metrics\n", "- **Communication Patterns** - Team interaction analysis\n", "- **Support Dynamics** - Assists and revives analysis\n", "- **Team Survival** - Group longevity patterns\n", "\n", "## 🔧 Small Column Organization\n", "- **Column 1**: Setup & Team Data\n", "- **Column 2**: Team Performance Analysis\n", "- **Column 3**: Coordination Metrics\n", "- **Column 4**: Team Visualizations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Column 1: Setup & Team Data\n", "\n", "### Essential imports and team data preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 1: <PERSON><PERSON><PERSON><PERSON><PERSON> IMPORTS FOR TEAM ANALYSIS\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure plotting\n", "%matplotlib inline\n", "plt.style.use('default')\n", "sns.set_palette(\"Set1\")\n", "\n", "print(\"👥 PUBG Team Dynamics Analysis - Setup Complete\")\n", "print(\"✅ All libraries imported successfully\")\n", "print(\"🤝 Ready for team coordination analysis\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 1: LOAD TEAM DATA\n", "def load_team_data(sample_size=70000):\n", "    \"\"\"\n", "    Load PUBG data with focus on team dynamics\n", "    \"\"\"\n", "    print(\"👥 Loading PUBG team data...\")\n", "    \n", "    # Try different file paths\n", "    possible_paths = [\n", "        '../data/pubg.csv',\n", "        '../../data/pubg.csv',\n", "        'data/pubg.csv',\n", "        'pubg.csv'\n", "    ]\n", "    \n", "    for path in possible_paths:\n", "        if Path(path).exists():\n", "            try:\n", "                data = pd.read_csv(path, nrows=sample_size)\n", "                print(f\"✅ Loaded {len(data):,} team records\")\n", "                return data\n", "            except Exception as e:\n", "                continue\n", "    \n", "    # Create sample team data\n", "    print(\"🔧 Creating sample team data...\")\n", "    np.random.seed(42)\n", "    \n", "    team_data = pd.DataFrame({\n", "        'kills': np.random.poisson(2, sample_size),\n", "        'assists': np.random.poisson(1.5, sample_size),\n", "        'revives': np.random.poisson(1, sample_size),\n", "        'teamKills': np.random.poisson(6, sample_size),\n", "        'DBNOs': np.random.poisson(1, sample_size),\n", "        'damageDealt': np.random.gamma(2, 100, sample_size),\n", "        'heals': np.random.poisson(3, sample_size),\n", "        'boosts': np.random.poisson(2, sample_size),\n", "        'winPlacePerc': np.random.beta(2, 5, sample_size),\n", "        'walkDistance': np.random.gamma(3, 500, sample_size),\n", "        'weaponsAcquired': np.random.poisson(4, sample_size)\n", "    })\n", "    \n", "    # Add team identifiers\n", "    team_data['groupId'] = [f'team_{i//4}' for i in range(sample_size)]  # 4 players per team\n", "    team_data['matchId'] = [f'match_{i//100}' for i in range(sample_size)]  # 100 players per match\n", "    \n", "    # Calculate team metrics\n", "    team_data['support_ratio'] = team_data['assists'] / (team_data['kills'] + team_data['assists'] + 1)\n", "    team_data['team_contribution'] = team_data['kills'] / (team_data['teamKills'] + 1)\n", "    team_data['medic_score'] = team_data['revives'] + team_data['heals'] * 0.5\n", "    \n", "    print(f\"✅ Team data created: {team_data.shape}\")\n", "    print(f\"👥 Unique teams: {team_data['groupId'].nunique():,}\")\n", "    return team_data\n", "\n", "# Load team data\n", "team_data = load_team_data()\n", "print(f\"\\n👥 Team data loaded: {team_data.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Column 2: Team Performance Analysis\n", "\n", "### Core team dynamics and performance patterns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 2: TEAM ROLE ANALYSIS\n", "print(\"🎯 TEAM ROLE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Define player roles based on performance patterns\n", "def categorize_role(row):\n", "    kills = row['kills']\n", "    assists = row['assists']\n", "    revives = row['revives']\n", "    \n", "    if kills >= 4:\n", "        return '<PERSON><PERSON> (High Kills)'\n", "    elif assists >= 3:\n", "        return 'Support (High Assists)'\n", "    elif revives >= 2:\n", "        return 'Medic (High Revives)'\n", "    elif kills <= 1 and assists <= 1:\n", "        return 'Passive Player'\n", "    else:\n", "        return 'Balanced Player'\n", "\n", "team_data['player_role'] = team_data.apply(categorize_role, axis=1)\n", "\n", "# Role distribution\n", "role_distribution = team_data['player_role'].value_counts()\n", "print(f\"📊 Player Role Distribution:\")\n", "for role, count in role_distribution.items():\n", "    percentage = (count / len(team_data)) * 100\n", "    print(f\"   {role}: {count:,} players ({percentage:.1f}%)\")\n", "\n", "# Role performance analysis\n", "role_performance = team_data.groupby('player_role').agg({\n", "    'winPlacePerc': 'mean',\n", "    'kills': 'mean',\n", "    'assists': 'mean',\n", "    'revives': 'mean',\n", "    'damageDealt': 'mean'\n", "}).round(3)\n", "\n", "print(f\"\\n🎯 Performance by Role:\")\n", "for role in role_performance.index:\n", "    stats = role_performance.loc[role]\n", "    print(f\"\\n👤 {role}:\")\n", "    print(f\"   Avg Win Rate: {stats['winPlacePerc']:.3f}\")\n", "    print(f\"   Avg Kills: {stats['kills']:.2f}\")\n", "    print(f\"   Avg Assists: {stats['assists']:.2f}\")\n", "    print(f\"   Avg Revives: {stats['revives']:.2f}\")\n", "\n", "print(\"\\n✅ Team role analysis completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 2: TEAM COORDINATION METRICS\n", "print(\"🤝 TEAM COORDINATION ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Calculate team-level statistics\n", "team_stats = team_data.groupby('groupId').agg({\n", "    'kills': ['sum', 'mean', 'std'],\n", "    'assists': ['sum', 'mean'],\n", "    'revives': ['sum', 'mean'],\n", "    'damageDealt': ['sum', 'mean'],\n", "    'winPlacePerc': ['mean', 'std'],\n", "    'support_ratio': 'mean',\n", "    'medic_score': 'mean'\n", "}).round(3)\n", "\n", "# Flatten column names\n", "team_stats.columns = ['_'.join(col).strip() for col in team_stats.columns]\n", "\n", "# Team coordination categories\n", "def categorize_team_coordination(row):\n", "    assist_ratio = row['assists_sum'] / (row['kills_sum'] + 1)\n", "    revive_ratio = row['revives_sum'] / 4  # Assuming 4 players per team\n", "    \n", "    if assist_ratio >= 0.5 and revive_ratio >= 0.5:\n", "        return 'Highly Coordinated'\n", "    elif assist_ratio >= 0.3 or revive_ratio >= 0.3:\n", "        return 'Moderately Coordinated'\n", "    else:\n", "        return 'Low Coordination'\n", "\n", "team_stats['coordination_level'] = team_stats.apply(categorize_team_coordination, axis=1)\n", "\n", "# Coordination analysis\n", "coordination_dist = team_stats['coordination_level'].value_counts()\n", "print(f\"📊 Team Coordination Levels:\")\n", "for level, count in coordination_dist.items():\n", "    percentage = (count / len(team_stats)) * 100\n", "    print(f\"   {level}: {count:,} teams ({percentage:.1f}%)\")\n", "\n", "# Performance by coordination level\n", "coord_performance = team_stats.groupby('coordination_level').agg({\n", "    'winPlacePerc_mean': 'mean',\n", "    'kills_sum': 'mean',\n", "    'assists_sum': 'mean',\n", "    'revives_sum': 'mean'\n", "}).round(3)\n", "\n", "print(f\"\\n🎯 Performance by Coordination Level:\")\n", "for level in coord_performance.index:\n", "    stats = coord_performance.loc[level]\n", "    print(f\"\\n🤝 {level}:\")\n", "    print(f\"   Avg Team Win Rate: {stats['winPlacePerc_mean']:.3f}\")\n", "    print(f\"   Avg Team Kills: {stats['kills_sum']:.1f}\")\n", "    print(f\"   Avg Team Assists: {stats['assists_sum']:.1f}\")\n", "    print(f\"   Avg Team Revives: {stats['revives_sum']:.1f}\")\n", "\n", "print(\"\\n✅ Team coordination analysis completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 Column 3: Team Visualizations\n", "\n", "### Visual analysis of team dynamics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COLUMN 3: TEAM DYNAMICS VISUALIZATIONS\n", "print(\"📊 CREATING TEAM DYNAMICS VISUALIZATIONS\")\n", "print(\"=\" * 50)\n", "\n", "# Create 2x2 subplot for team analysis\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('PUBG Team Dynamics - Squad Performance Analysis', fontsize=16, fontweight='bold')\n", "\n", "# 1. Player Role Distribution (Top-left)\n", "role_counts = team_data['player_role'].value_counts()\n", "colors = ['red', 'blue', 'green', 'orange', 'purple']\n", "bars = axes[0, 0].bar(range(len(role_counts)), role_counts.values, \n", "                     color=colors[:len(role_counts)], alpha=0.7, edgecolor='black')\n", "axes[0, 0].set_title('Player Role Distribution', fontweight='bold')\n", "axes[0, 0].set_xlabel('Player Roles')\n", "axes[0, 0].set_ylabel('Number of Players')\n", "axes[0, 0].set_xticks(range(len(role_counts)))\n", "axes[0, 0].set_xticklabels(role_counts.index, rotation=45, ha='right')\n", "axes[0, 0].grid(True, alpha=0.3, axis='y')\n", "\n", "# Add percentage labels\n", "total = role_counts.sum()\n", "for bar, count in zip(bars, role_counts.values):\n", "    percentage = (count/total)*100\n", "    axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(role_counts.values)*0.01,\n", "                   f'{percentage:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "# 2. <PERSON><PERSON> vs <PERSON><PERSON><PERSON> (Top-right)\n", "scatter = axes[0, 1].scatter(team_data['kills'], team_data['assists'], \n", "                           alpha=0.6, c=team_data['winPlacePerc'], cmap='viridis')\n", "axes[0, 1].set_title('Kills vs Assists (colored by Win Rate)', fontweight='bold')\n", "axes[0, 1].set_xlabel('Kills')\n", "axes[0, 1].set_ylabel('Assists')\n", "plt.colorbar(scatter, ax=axes[0, 1], label='Win Placement %')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# 3. Team Coordination Levels (Bottom-left)\n", "coord_counts = team_stats['coordination_level'].value_counts()\n", "colors = ['gold', 'silver', 'lightcoral']\n", "bars = axes[1, 0].bar(range(len(coord_counts)), coord_counts.values, \n", "                     color=colors[:len(coord_counts)], alpha=0.7, edgecolor='black')\n", "axes[1, 0].set_title('Team Coordination Levels', fontweight='bold')\n", "axes[1, 0].set_xlabel('Coordination Level')\n", "axes[1, 0].set_ylabel('Number of Teams')\n", "axes[1, 0].set_xticks(range(len(coord_counts)))\n", "axes[1, 0].set_xticklabels(coord_counts.index, rotation=45, ha='right')\n", "axes[1, 0].grid(True, alpha=0.3, axis='y')\n", "\n", "# Add percentage labels\n", "total = coord_counts.sum()\n", "for bar, count in zip(bars, coord_counts.values):\n", "    percentage = (count/total)*100\n", "    axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(coord_counts.values)*0.01,\n", "                   f'{percentage:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "# 4. Support vs Medic Score (Bottom-right)\n", "axes[1, 1].scatter(team_data['support_ratio'], team_data['medic_score'], \n", "                  alpha=0.6, c='purple')\n", "axes[1, 1].set_title('Support vs Medic Score', fontweight='bold')\n", "axes[1, 1].set_xlabel('Support Ratio (Assists/Total)')\n", "axes[1, 1].set_ylabel('Medic Score (Revives + 0.5*Heals)')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Team dynamics visualizations completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Team Dynamics Summary\n", "\n", "### Key team insights and coordination patterns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FINAL TEAM SUMMARY\n", "print(\"📋 PUBG TEAM DYNAMICS ANALYSIS - SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"👥 Team Overview:\")\n", "print(f\"   Total Players Analyzed: {len(team_data):,}\")\n", "print(f\"   Total Teams: {team_data['groupId'].nunique():,}\")\n", "print(f\"   Average Team Size: {len(team_data) / team_data['groupId'].nunique():.1f}\")\n", "\n", "# Role analysis\n", "most_common_role = team_data['player_role'].value_counts().index[0]\n", "print(f\"\\n🎯 Role Analysis:\")\n", "print(f\"   Most Common Role: {most_common_role}\")\n", "print(f\"   Fraggers (High Kills): {len(team_data[team_data['player_role'] == '<PERSON><PERSON> (High Kills)'])/len(team_data)*100:.1f}%\")\n", "print(f\"   Support Players: {len(team_data[team_data['player_role'] == 'Support (High Assists)'])/len(team_data)*100:.1f}%\")\n", "print(f\"   Medics: {len(team_data[team_data['player_role'] == 'Medic (High Revives)'])/len(team_data)*100:.1f}%\")\n", "\n", "# Coordination analysis\n", "if 'coordination_level' in team_stats.columns:\n", "    highly_coord = len(team_stats[team_stats['coordination_level'] == 'Highly Coordinated'])\n", "    print(f\"\\n🤝 Coordination Analysis:\")\n", "    print(f\"   Highly Coordinated Teams: {highly_coord/len(team_stats)*100:.1f}%\")\n", "    print(f\"   Average Team Assists: {team_data['assists'].mean():.2f}\")\n", "    print(f\"   Average Team Revives: {team_data['revives'].mean():.2f}\")\n", "\n", "# Performance insights\n", "print(f\"\\n📊 Performance Insights:\")\n", "print(f\"   Average Support Ratio: {team_data['support_ratio'].mean():.3f}\")\n", "print(f\"   Average Medic Score: {team_data['medic_score'].mean():.2f}\")\n", "print(f\"   Team Contribution Balance: {team_data['team_contribution'].mean():.3f}\")\n", "\n", "# Correlation insights\n", "correlations = team_data[['winPlacePerc', 'assists', 'revives', 'support_ratio', 'medic_score']].corr()['winPlacePerc']\n", "print(f\"\\n🔍 Team Success Correlations:\")\n", "print(f\"   Assists ↔ Win Rate: {correlations['assists']:.3f}\")\n", "print(f\"   Revives ↔ Win Rate: {correlations['revives']:.3f}\")\n", "print(f\"   Support Ratio ↔ Win Rate: {correlations['support_ratio']:.3f}\")\n", "print(f\"   Medic Score ↔ Win Rate: {correlations['medic_score']:.3f}\")\n", "\n", "print(f\"\\n✅ Team Dynamics Analysis Complete!\")\n", "print(f\"📊 All team visualizations display inline\")\n", "print(f\"👥 Team coordination patterns analyzed\")\n", "print(f\"🎯 Player roles and team dynamics identified\")\n", "\n", "print(f\"\\n🎉 TEAM DYNAMICS ANALYSIS COMPLETED SUCCESSFULLY!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}