/*************************************************************
 *
 *  MathJax/localization/uk/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("uk","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0443 \u044F\u043A",
          MathMLcode: "\u041A\u043E\u0434 MathML",
          OriginalMathML: "\u041E\u0440\u0438\u0433\u0456\u043D\u0430\u043B\u044C\u043D\u0438\u0439 MathML",
          TeXCommands: "\u041A\u043E\u043C\u0430\u043D\u0434\u0438 TeX",
          AsciiMathInput: "\u0412\u0432\u0456\u0434 AsciiMathML",
          Original: "\u041E\u0440\u0438\u0433\u0456\u043D\u0430\u043B\u044C\u043D\u0430 \u0444\u043E\u0440\u043C\u0430",
          ErrorMessage: "\u041F\u043E\u0432\u0456\u0434\u043E\u043C\u043B\u0435\u043D\u043D\u044F \u043F\u0440\u043E \u043F\u043E\u043C\u0438\u043B\u043A\u0443",
          Annotation: "\u0410\u043D\u043E\u0442\u0430\u0446\u0456\u044F",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "\u0417\u043C\u0456\u0441\u0442 MathML",
          OpenMath: "OpenMath",
          texHints: "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u0438 \u043F\u043E\u0440\u0430\u0434\u0438 TeX \u0432 MathML",
          Settings: "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0438",
          ZoomTrigger: "\u041F\u0443\u0441\u043A \u043C\u0430\u0441\u0448\u0442\u0430\u0431\u0443",
          Hover: "\u041F\u0456\u0434\u043D\u0435\u0441\u0435\u043D\u043D\u044F \u043A\u0443\u0440\u0441\u043E\u0440\u0430",
          Click: "\u041A\u043B\u0456\u043A",
          DoubleClick: "\u041F\u043E\u0434\u0432\u0456\u0439\u043D\u0438\u0439 \u043A\u043B\u0456\u043A",
          NoZoom: "\u0411\u0435\u0437 \u043C\u0430\u0441\u0448\u0442\u0430\u0431\u0443\u0432\u0430\u043D\u043D\u044F",
          TriggerRequires: "\u041D\u0435\u043E\u0431\u0445\u0456\u0434\u043D\u0456 \u043A\u043B\u044E\u0447\u0456:",
          Option: "\u041F\u0430\u0440\u0430\u043C\u0435\u0442\u0440",
          Alt: "Alt",
          Command: "\u041A\u043E\u043C\u0430\u043D\u0434\u0430",
          Control: "\u041A\u0435\u0440\u0443\u0432\u0430\u043D\u043D\u044F",
          Shift: "Shift",
          ZoomFactor: "\u041C\u043D\u043E\u0436\u043D\u0438\u043A \u043C\u0430\u0441\u0448\u0442\u0430\u0431\u0443\u0432\u0430\u043D\u043D\u044F",
          Renderer: "\u0412\u0456\u0437\u0443\u0430\u043B\u0456\u0437\u0430\u0446\u0456\u044F \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0438",
          MPHandles: "MathPlayer \u043E\u043F\u0440\u0430\u0446\u044C\u043E\u0432\u0443\u0454:",
          MenuEvents: "\u041F\u043E\u0434\u0456\u0457 \u043C\u0435\u043D\u044E",
          MouseEvents: "\u041F\u043E\u0434\u0456\u0457 \u043C\u0438\u0448\u043A\u0438",
          MenuAndMouse: "\u041F\u043E\u0434\u0456\u0457 \u043C\u0435\u043D\u044E \u0456 \u043C\u0438\u0448\u043A\u0438",
          FontPrefs: "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u043A\u0438 \u0448\u0442\u0440\u0438\u0444\u0442\u0443",
          ForHTMLCSS: "\u0414\u043B\u044F HTML-CSS:",
          Auto: "\u0410\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u043D\u043E",
          TeXLocal: "TeX (\u043B\u043E\u043A\u0430\u043B\u044C\u043D\u0438\u0439)",
          TeXWeb: "TeX (\u0432\u0435\u0431)",
          TeXImage: "TeX (\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F)",
          STIXLocal: "STIX (\u043B\u043E\u043A\u0430\u043B\u044C\u043D\u0438\u0439)",
          STIXWeb: "STIX (\u0432\u0435\u0431)",
          AsanaMathWeb: "Asana Math (\u0432\u0435\u0431)",
          GyrePagellaWeb: "Gyre Pagella (\u0432\u0435\u0431)",
          GyreTermesWeb: "Gyre Termes (\u0432\u0435\u0431)",
          LatinModernWeb: "Latin Modern (\u0432\u0435\u0431)",
          NeoEulerWeb: "Neo Euler (\u0432\u0435\u0431)",
          ContextMenu: "\u041A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u0435 \u043C\u0435\u043D\u044E",
          Browser: "\u041E\u0433\u043B\u044F\u0434",
          Scale: "\u041C\u0430\u0441\u0448\u0442\u0430\u0431\u0443\u0432\u0430\u0442\u0438 \u0443\u0441\u044E \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0443\u2026",
          Discoverable: "\u041F\u0456\u0434\u0441\u0432\u0456\u0442\u043A\u0430 \u043F\u0440\u0438 \u043D\u0430\u0432\u0435\u0434\u0435\u043D\u043D\u0456",
          Locale: "\u041C\u043E\u0432\u0430",
          LoadLocale: "\u0417\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0438\u0442\u0438 \u0437 URL...",
          About: "\u041F\u0440\u043E MathJax",
          Help: "\u0414\u043E\u043F\u043E\u043C\u043E\u0433\u0430 MathJax",
          localTeXfonts: "\u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u044E\u0442\u044C\u0441\u044F \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u0456 \u0448\u0440\u0438\u0444\u0442\u0438 TeX",
          webTeXfonts: "\u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u044E\u0442\u044C\u0441\u044F \u0432\u0435\u0431-\u0448\u0440\u0438\u0444\u0442\u0438 TeX",
          imagefonts: "\u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u044E\u0442\u044C\u0441\u044F \u0448\u0440\u0438\u0444\u0442\u0438 \u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u044C",
          localSTIXfonts: "\u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u044E\u0447\u0438 \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u0456 STIX-\u0448\u0440\u0438\u0444\u0442\u0438",
          webSVGfonts: "\u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u044E\u0447\u0438 SVG \u0432\u0435\u0431-\u0448\u0440\u0438\u0444\u0442\u0438",
          genericfonts: "\u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u044E\u0447\u0438 \u0437\u0433\u0435\u043D\u0435\u0440\u043E\u0432\u0430\u043D\u0456 \u044E\u043D\u0456\u043A\u043E\u0434\u043D\u0456 \u0448\u0440\u0438\u0444\u0442\u0438",
          wofforotffonts: "\u0448\u0440\u0438\u0444\u0442\u0438 woff \u0430\u0431\u043E otf",
          eotffonts: "\u0448\u0440\u0438\u0444\u0442\u0438 eot",
          svgfonts: "\u0448\u0440\u0438\u0444\u0442\u0438 svg",
          WebkitNativeMMLWarning: "\u0412\u0430\u0448 \u0431\u0440\u0430\u0443\u0437\u0435\u0440 \u043D\u0435 \u043F\u0456\u0434\u0442\u0440\u0438\u043C\u0443\u0454 MathML, \u0442\u043E\u043C\u0443 \u043F\u0435\u0440\u0435\u043C\u0438\u043A\u0430\u043D\u043D\u044F \u0432\u0438\u0432\u043E\u0434\u0443 \u0434\u043E MathML \u043C\u043E\u0436\u0435 \u043F\u0440\u0438\u0437\u0432\u0435\u0441\u0442\u0438, \u0449\u043E \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0443 \u043D\u0430 \u0446\u0456\u0439 \u0441\u0442\u043E\u0440\u0456\u043D\u0446\u0456 \u0441\u0442\u0430\u043D\u0435 \u043D\u0435\u043C\u043E\u0436\u043B\u0438\u0432\u043E \u043F\u0440\u043E\u0447\u0438\u0442\u0430\u0442\u0438.",
          MSIENativeMMLWarning: "\u0412\u0435\u0431-\u043F\u0435\u0440\u0435\u0433\u043B\u044F\u0434\u0430\u0447 Internet Explorer \u043F\u043E\u0442\u0440\u0435\u0431\u0443\u0454 \u043C\u043E\u0434\u0443\u043B\u044C MathPlayer \u0434\u043B\u044F \u043E\u0431\u0440\u043E\u0431\u043A\u0438 \u0432\u0438\u0432\u043E\u0434\u0443 MathML.",
          OperaNativeMMLWarning: "\u041E\u0431\u043C\u0435\u0436\u0435\u043D\u0430 \u043F\u0456\u0434\u0442\u0440\u0438\u043C\u043A\u0430 MathML \u0432 Opera, \u0442\u043E\u043C\u0443 \u043F\u0435\u0440\u0435\u043C\u0438\u043A\u0430\u043D\u043D\u044F \u0432\u0438\u0432\u043E\u0434\u0443 \u043D\u0430 MathML \u043C\u043E\u0436\u0443\u0442\u044C \u0432\u0438\u043A\u043B\u0438\u043A\u0430\u0442\u0438, \u0449\u043E \u0434\u0435\u044F\u043A\u0456 \u0432\u0438\u0440\u0430\u0437\u0438 \u043C\u043E\u0436\u0443\u0442\u044C \u043F\u043E\u0433\u0430\u043D\u043E \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0430\u0442\u0438\u043C\u0443\u0442\u044C\u0441\u044F.",
          SafariNativeMMLWarning: "MathML \u0432\u0430\u0448\u043E\u0433\u043E \u0432\u0435\u0431-\u043F\u0435\u0440\u0435\u0433\u043B\u044F\u0434\u0430\u0447\u0430 \u043D\u0435 \u0440\u0435\u0430\u043B\u0456\u0437\u0443\u0454 \u0432\u0441\u0456 \u0444\u0443\u043D\u043A\u0446\u0456\u0457, \u044F\u043A\u0456 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u044E\u0442\u044C\u0441\u044F MathJax. \u0422\u0430\u043A\u0438\u043C \u0447\u0438\u043D\u043E\u043C, \u0434\u0435\u044F\u043A\u0456 \u0432\u0438\u0440\u0430\u0437\u0438 \u043C\u043E\u0436\u0443\u0442\u044C \u043D\u0435\u043F\u0440\u0430\u0432\u0438\u043B\u044C\u043D\u043E \u0432\u0456\u0437\u0443\u0430\u043B\u0456\u0437\u0443\u0432\u0430\u0442\u0438\u0441\u044F.",
          FirefoxNativeMMLWarning: "MathML \u0432\u0430\u0448\u043E\u0433\u043E \u0432\u0435\u0431-\u043F\u0435\u0440\u0435\u0433\u043B\u044F\u0434\u0430\u0447\u0430 \u043D\u0435 \u0440\u0435\u0430\u043B\u0456\u0437\u0443\u0454 \u0432\u0441\u0456 \u0444\u0443\u043D\u043A\u0446\u0456\u0457, \u044F\u043A\u0456 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u044E\u0442\u044C\u0441\u044F MathJax. \u0422\u0430\u043A\u0438\u043C \u0447\u0438\u043D\u043E\u043C, \u0434\u0435\u044F\u043A\u0456 \u0432\u0438\u0440\u0430\u0437\u0438 \u043C\u043E\u0436\u0443\u0442\u044C \u043D\u0435\u043F\u0440\u0430\u0432\u0438\u043B\u044C\u043D\u043E \u0432\u0456\u0437\u0443\u0430\u043B\u0456\u0437\u0443\u0432\u0430\u0442\u0438\u0441\u044F.",
          MSIESVGWarning: "SVG \u043D\u0435 \u0440\u0435\u0430\u043B\u0456\u0437\u043E\u0432\u0430\u043D\u043E \u0443 \u0432\u0435\u0431-\u043F\u0435\u0440\u0435\u0433\u043B\u044F\u0434\u0430\u0447\u0456 Internet Explorer \u0432\u0435\u0440\u0441\u0456\u0457 9 \u0430\u0431\u043E \u0435\u043C\u0443\u043B\u044E\u0454\u0442\u044C\u0441\u044F \u0432 IE8 \u0430\u0431\u043E \u043D\u0438\u0436\u0447\u0435, \u0442\u043E\u043C\u0443 \u043F\u0435\u0440\u0435\u043C\u0438\u043A\u0430\u043D\u043D\u044F \u043D\u0430 \u0432\u0438\u0432\u0456\u0434 SVG \u043C\u043E\u0436\u0435 \u043F\u0440\u0438\u0437\u0432\u0435\u0441\u0442\u0438, \u0449\u043E \u0446\u044E \u0441\u0442\u043E\u0440\u0456\u043D\u043A\u0443 \u0441\u0442\u0430\u043D\u0435 \u043D\u0435\u043C\u043E\u0436\u043B\u0438\u0432\u043E \u043F\u0440\u043E\u0447\u0438\u0442\u0430\u0442\u0438.",
          LoadURL: "\u0417\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0438\u0442\u0438 \u0434\u0430\u043D\u0456 \u043F\u0435\u0440\u0435\u043A\u043B\u0430\u0434\u0443 \u0437 \u0446\u044C\u043E\u0433\u043E URL:",
          BadURL: "URL-\u0430\u0434\u0440\u0435\u0441\u0430 \u043C\u0430\u0454 \u0431\u0443\u0442\u0438 \u0434\u043B\u044F JavaScript \u0444\u0430\u0439\u043B\u0443, \u044F\u043A\u0438\u0439 \u0432\u0438\u0437\u043D\u0430\u0447\u0430\u0454 MathJax \u0434\u0430\u043D\u0456 \u043F\u0435\u0440\u0435\u043A\u043B\u0430\u0434\u0443. \u0406\u043C'\u044F \u0444\u0430\u0439\u043B\u0443 JavaScript \u043F\u043E\u0432\u0438\u043D\u043D\u043E \u0437\u0430\u043A\u0456\u043D\u0447\u0443\u0432\u0430\u0442\u0438\u0441\u044F \u043D\u0430 \".js\"",
          BadData: "\u041D\u0435 \u0432\u0434\u0430\u043B\u043E\u0441\u044F \u0437\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0438\u0442\u0438 \u043F\u0435\u0440\u0435\u043A\u043B\u0430\u0434\u0438 \u0437 %1",
          SwitchAnyway: "\u0417\u043C\u0456\u043D\u0438\u0442\u0438 \u0432\u0456\u0437\u0443\u0430\u043B\u0456\u0437\u0430\u0442\u043E\u0440?\n\n(\u0412\u0438\u0431\u0435\u0440\u0456\u0442\u044C \u0413\u0430\u0440\u0430\u0437\u0434, \u0449\u043E\u0431 \u0437\u043C\u0456\u043D\u0438\u0442\u0438, \u0430\u0431\u043E \u0441\u043A\u0430\u0441\u0443\u0432\u0430\u0442\u0438 \u0434\u043B\u044F \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0435\u043D\u043D\u044F \u043F\u043E\u0442\u043E\u0447\u043D\u043E\u0433\u043E \u0432\u0456\u0437\u0443\u0430\u043B\u0456\u0437\u0430\u0442\u043E\u0440\u0430)",
          ScaleMath: "\u041C\u0430\u0441\u0448\u0442\u0430\u0431\u0443\u0432\u0430\u0442\u0438 \u0432\u0441\u0456 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043D\u0456 \u0432\u0438\u0440\u0430\u0437\u0438 (\u0443 \u043F\u043E\u0440\u0456\u0432\u043D\u044F\u043D\u043D\u0456 \u0437 \u043E\u0442\u043E\u0447\u0443\u044E\u0447\u0438\u043C \u0442\u0435\u043A\u0441\u0442\u043E\u043C)",
          NonZeroScale: "\u041C\u0430\u0441\u0448\u0442\u0430\u0431 \u043D\u0435 \u043F\u043E\u0432\u0438\u043D\u0435\u043D \u0431\u0443\u0442\u0438 \u043D\u0443\u043B\u0435\u043C",
          PercentScale: "\u041C\u0430\u0441\u0448\u0442\u0430\u0431\u0443\u0432\u0430\u043D\u043D\u044F \u043C\u0430\u0454 \u0431\u0443\u0442\u0438 \u0443 \u0432\u0456\u0434\u0441\u043E\u0442\u043A\u0430\u0445 (\u043D\u0430\u043F\u0440\u0438\u043A\u043B\u0430\u0434, 120%%)",
          IE8warning: "\u0426\u0435 \u0432\u0438\u043C\u0438\u043A\u0430\u0454 MathJax \u043C\u0435\u043D\u044E \u0442\u0430 \u0444\u0443\u043D\u043A\u0446\u0456\u0457 \u043C\u0430\u0441\u0448\u0442\u0430\u0431\u0443\u0432\u0430\u043D\u043D\u044F, \u0430\u043B\u0435 \u0432\u0438 \u043C\u043E\u0436\u0435\u0442\u0435 \u0443\u0442\u0440\u0438\u043C\u0443\u0432\u0430\u0442\u0438 ALT \u043F\u0456\u0434 \u0447\u0430\u0441 \u043A\u043B\u0456\u043A\u0456\u0432 \u043C\u0438\u0448\u0456 \u043D\u0430 \u0432\u0438\u0440\u0430\u0437\u0456 \u0434\u043B\u044F \u043E\u0442\u0440\u0438\u043C\u0430\u043D\u043D\u044F \u043D\u0430\u0442\u043E\u043C\u0456\u0441\u0442\u044C \u043C\u0435\u043D\u044E MathJax.\n\n\u0414\u0456\u0439\u0441\u043D\u043E \u0437\u043C\u0456\u043D\u0438\u0442\u0438 \u043D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F MathPlayer?",
          IE9warning: "MathJax \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u0435 \u043C\u0435\u043D\u044E \u0432\u0456\u0434\u043A\u043B\u044E\u0447\u0435\u043D\u043E, \u0430\u043B\u0435 \u0432\u0438 \u043C\u043E\u0436\u0435\u0442\u0435 \u043D\u0430\u0442\u0438\u0441\u043D\u0443\u0442\u0438 ALT \u043F\u0456\u0434 \u0447\u0430\u0441 \u043A\u043B\u0456\u043A\u0456\u0432 \u043C\u0438\u0448\u0456 \u0434\u043B\u044F \u043E\u0442\u0440\u0438\u043C\u0430\u043D\u044F MathJax \u043C\u0435\u043D\u044E.",
          NoOriginalForm: "\u041D\u0435 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u0430 \u043E\u0440\u0438\u0433\u0456\u043D\u0430\u043B\u044C\u043D\u0430 \u0444\u043E\u0440\u043C\u0430",
          Close: "\u0417\u0430\u043A\u0440\u0438\u0442\u0438",
          EqSource: "\u0414\u0436\u0435\u0440\u0435\u043B\u043E \u0440\u0456\u0432\u043D\u044F\u043D\u043D\u044F MathJax",
          CloseAboutDialog: "\u0417\u0430\u043A\u0440\u0438\u0442\u0438 \u0434\u0456\u0430\u043B\u043E\u0433 \u043F\u0440\u043E MathJax",
          FastPreview: "\u0428\u0432\u0438\u0434\u043A\u0438\u0439 \u043F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u0456\u0439 \u043F\u0435\u0440\u0435\u0433\u043B\u044F\u0434",
          AssistiveMML: "\u0414\u043E\u043F\u043E\u043C\u0456\u0436\u043D\u0438\u0439 MathML",
          InTabOrder: "\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u0438 \u0432 \u043F\u043E\u0440\u044F\u0434\u043E\u043A \u0432\u043A\u043B\u0430\u0434\u043E\u043A"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/uk/MathMenu.js");
